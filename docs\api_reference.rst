API Reference
=============

This section provides comprehensive API documentation for the Neural Symbolic Language Model,
including OpenAI-compatible endpoints, Hybrid Retrieval system, and multi-modal capabilities
with Ollama gemma3n:e2b integration.

**Prerequisites:**

.. code-block:: bash

   # Install and configure Ollama (http://localhost:11434)
   curl -fsSL https://ollama.com/install.sh | sh
   ollama serve
   ollama pull gemma3n:e2b

**Configuration:**

.. code-block:: bash

   # Set environment variables
   MODEL_REASONING_ENGINE=ollama
   MODEL_REASONING_MODEL=gemma3n:e2b

REST API Endpoints
------------------

The application provides a RESTful API with full OpenAI compatibility and enhanced features.

Base URL
~~~~~~~~

.. code-block:: text

   http://localhost:8080

Authentication
~~~~~~~~~~~~~~

All API endpoints require authentication using Bearer tokens:

.. code-block:: http

   Authorization: Bearer your-api-key-here

**API Key Configuration:**

.. code-block:: bash

   # Environment variable
   SECURITY_API_KEYS_JSON='{"user": {"key": "your-secure-key", "permissions": ["read", "write"], "rate_limit": 1000}}'

**Supported Authentication Methods:**

* Bearer tokens (recommended)
* API key headers
* Query parameters (development only)

Chat Completions API
--------------------

OpenAI-Compatible Endpoint
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /v1/chat/completions

   Create a chat completion response using the gemma3n:e2b model with enhanced symbolic reasoning and multi-modal capabilities.

   **Supported Models:**

   * ``gemma3n:e2b`` -- Primary model with multi-modal and symbolic reasoning capabilities (recommended)
   * ``gemma3n`` -- Base model variant
   * Auto-detection of available Ollama models

   **Request Headers:**

   * ``Authorization`` -- Bearer token for authentication
   * ``Content-Type`` -- application/json

   **Basic Request Body:**

   .. code-block:: json

      {
        "model": "gemma3n:e2b",
        "messages": [
          {
            "role": "system",
            "content": "You are an expert in symbolic reasoning and logic."
          },
          {
            "role": "user",
            "content": "Explain the difference between deductive and inductive reasoning."
          }
        ],
        "max_tokens": 1000,
        "temperature": 0.7,
        "top_p": 1.0,
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0,
        "stream": false
      }

   **Multi-Modal Request (Code Analysis):**

   .. code-block:: json

      {
        "model": "gemma3n:e2b",
        "messages": [
          {
            "role": "user",
            "content": [
              {
                "type": "text",
                "text": "Analyze this Python function and explain its logic:"
              },
              {
                "type": "code",
                "language": "python",
                "content": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)"
              }
            ]
          }
        ],
        "temperature": 0.3
      }

   **Response:**

   .. code-block:: json

      {
        "id": "chatcmpl-abc123",
        "object": "chat.completion",
        "created": 1677652288,
        "model": "gemma3n:e2b",
        "choices": [
          {
            "index": 0,
            "message": {
              "role": "assistant",
              "content": "Deductive reasoning starts with general principles and derives specific conclusions, while inductive reasoning starts with specific observations and forms general principles. For example, deductive: 'All birds have wings, penguins are birds, therefore penguins have wings.' Inductive: 'I've seen 100 swans and they were all white, therefore all swans are white.'"
            },
            "finish_reason": "stop"
          }
        ],
        "usage": {
          "prompt_tokens": 45,
          "completion_tokens": 78,
          "total_tokens": 123
        },
        "performance": {
          "response_time": 2.34,
          "cache_hit": false,
          "reasoning_steps": 3,
          "model_version": "gemma3n:e2b"
        }
      }

   :statuscode 200: Success
   :statuscode 400: Bad Request - Invalid parameters
   :statuscode 401: Unauthorized - Invalid API key
   :statuscode 429: Too Many Requests - Rate limit exceeded
   :statuscode 500: Internal Server Error

Streaming Responses
~~~~~~~~~~~~~~~~~~~

Enable streaming by setting ``"stream": true`` in the request:

.. code-block:: json

   {
     "model": "local",
     "messages": [...],
     "stream": true
   }

**Streaming Response Format:**

.. code-block:: text

   data: {"id":"chatcmpl-123","object":"chat.completion.chunk",...}
   data: {"id":"chatcmpl-123","object":"chat.completion.chunk",...}
   data: [DONE]

Models API
----------

List Available Models
~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /v1/models

   List all available models in OpenAI-compatible format.

   **Response:**

   .. code-block:: json

      {
        "object": "list",
        "data": [
          {
            "id": "gemma3n:e2b",
            "object": "model",
            "created": 1677652288,
            "owned_by": "ollama",
            "permission": [],
            "root": "gemma3n:e2b",
            "parent": null
          }
        ]
      }

System Information API
----------------------

System Info
~~~~~~~~~~~

.. http:get:: /system/info

   Get system information and status.

   **Response:**

   .. code-block:: json

      {
        "reasoner": {
          "status": "operational",
          "engine": "local",
          "model": "llama"
        },
        "retriever": {
          "status": "operational",
          "vector_db": "faiss",
          "index_size": 1000
        },
        "gpu_optimized": true,
        "version": "0.1.0"
      }

Health Check
~~~~~~~~~~~~

.. http:get:: /health

   Health check endpoint for load balancers.

   **Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "timestamp": "2025-06-29T12:00:00Z"
      }

Readiness Check
~~~~~~~~~~~~~~~

.. http:get:: /ready

   Readiness check endpoint for Kubernetes.

   **Response:**

   .. code-block:: json

      {
        "status": "ready",
        "components": {
          "reasoner": "ready",
          "retriever": "ready",
          "cache": "ready"
        }
      }

Performance Monitoring API
--------------------------

Performance Metrics
~~~~~~~~~~~~~~~~~~~

.. http:get:: /performance

   Get detailed performance metrics.

   **Response:**

   .. code-block:: json

      {
        "cache": {
          "size": 100,
          "max_size": 1000,
          "hits": 80,
          "misses": 20,
          "hit_rate": 0.8
        },
        "system": {
          "cpu_percent": 25.5,
          "memory_percent": 60.2,
          "gpu_available": true,
          "gpu_name": "NVIDIA RTX 4090",
          "active_requests": 5
        },
        "requests": {
          "total": 1000,
          "avg_duration": 0.5,
          "error_rate": 0.01,
          "cache_hit_rate": 0.8
        }
      }

Request Parameters
------------------

Common Parameters
~~~~~~~~~~~~~~~~~

All chat completion requests support these parameters:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Type
     - Description
   * - ``model``
     - string
     - Model identifier (required)
   * - ``messages``
     - array
     - List of conversation messages (required)
   * - ``max_tokens``
     - integer
     - Maximum tokens in response (optional, default: 150)
   * - ``temperature``
     - number
     - Response randomness 0.0-1.0 (optional, default: 0.7)
   * - ``top_p``
     - number
     - Nucleus sampling parameter (optional, default: 1.0)
   * - ``stream``
     - boolean
     - Enable streaming response (optional, default: false)
   * - ``stop``
     - array
     - Stop sequences (optional)

Message Format
~~~~~~~~~~~~~~

Each message in the ``messages`` array has this format:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Field
     - Type
     - Description
   * - ``role``
     - string
     - Message role: "user", "assistant", or "system"
   * - ``content``
     - string
     - Message content text
   * - ``name``
     - string
     - Optional message author name

Error Responses
---------------

Error Format
~~~~~~~~~~~~

All errors return a structured response:

.. code-block:: json

   {
     "error": {
       "code": "ERROR_CODE",
       "message": "Human-readable error message",
       "details": {
         "field": "additional_context"
       }
     }
   }

Hybrid Retrieval API
--------------------

The Hybrid Retrieval system enables adding and searching various content types including documents, websites, and GitHub repositories.

Add Document
~~~~~~~~~~~~

.. http:post:: /documents/add

   Add text content to the retrieval system.

   **Request Body:**

   .. code-block:: json

      {
        "content": "Neural networks are computational models inspired by biological neural networks...",
        "metadata": {
          "title": "Neural Network Basics",
          "category": "ai_concepts",
          "tags": ["neural-networks", "machine-learning"],
          "author": "AI Researcher",
          "difficulty": "intermediate"
        }
      }

   **Response:**

   .. code-block:: json

      {
        "document_id": "doc_abc123",
        "status": "added",
        "message": "Document successfully added to retrieval system"
      }

Add Website Content
~~~~~~~~~~~~~~~~~~~

.. http:post:: /documents/add-url

   Extract and add content from web pages.

   **Request Body:**

   .. code-block:: json

      {
        "url": "https://arxiv.org/abs/2301.00001",
        "extract_links": true,
        "max_depth": 2,
        "include_images": false,
        "metadata": {
          "category": "research_paper",
          "source": "arxiv"
        }
      }

   **Response:**

   .. code-block:: json

      {
        "document_id": "doc_web456",
        "status": "added",
        "extracted_links": ["https://arxiv.org/abs/2301.00002"],
        "message": "Website content successfully processed"
      }

Add GitHub Repository
~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /documents/add-github

   Clone and process GitHub repository content.

   **Request Body:**

   .. code-block:: json

      {
        "repository_url": "https://github.com/openai/gpt-3",
        "include_code": true,
        "include_docs": true,
        "include_issues": false,
        "file_extensions": [".py", ".md", ".txt"],
        "exclude_paths": ["node_modules", ".git", "__pycache__"],
        "metadata": {
          "category": "source_code",
          "project_type": "ai_model",
          "language": "python"
        }
      }

   **Response:**

   .. code-block:: json

      {
        "document_id": "doc_github789",
        "status": "added",
        "files_processed": 42,
        "repository_info": {
          "name": "gpt-3",
          "owner": "openai",
          "language": "Python",
          "stars": 15420
        }
      }

Search Documents
~~~~~~~~~~~~~~~~

.. http:post:: /documents/search

   Search for relevant documents using various methods.

   **Request Body:**

   .. code-block:: json

      {
        "query": "neural network training algorithms",
        "method": "hybrid",
        "limit": 10,
        "filters": {
          "category": "ai_concepts",
          "tags": ["neural-networks"],
          "difficulty": "intermediate"
        },
        "min_score": 0.5
      }

   **Search Methods:**

   * ``vector`` -- Semantic similarity using embeddings
   * ``keyword`` -- Traditional text-based search
   * ``hybrid`` -- Combines vector and keyword search (recommended)

   **Response:**

   .. code-block:: json

      {
        "results": [
          {
            "document_id": "doc_abc123",
            "score": 0.95,
            "content": "Neural networks are computational models...",
            "metadata": {
              "title": "Neural Network Basics",
              "category": "ai_concepts",
              "tags": ["neural-networks", "machine-learning"]
            }
          }
        ],
        "total_results": 1,
        "search_time_ms": 45,
        "method_used": "hybrid"
      }

Document Management
~~~~~~~~~~~~~~~~~~~

.. http:get:: /documents/count

   Get statistics about documents in the system.

   **Response:**

   .. code-block:: json

      {
        "total_documents": 1250,
        "indexed_documents": 1248,
        "pending_documents": 2,
        "categories": {
          "ai_concepts": 400,
          "source_code": 350,
          "research_paper": 300,
          "documentation": 200
        }
      }

.. http:get:: /documents/(document_id)

   Retrieve a specific document by ID.

.. http:delete:: /documents/(document_id)

   Remove a document from the system.

Error Codes
~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 30 20 50

   * - Error Code
     - HTTP Status
     - Description
   * - ``VALIDATION_ERROR``
     - 400
     - Invalid request format or parameters
   * - ``AUTHENTICATION_ERROR``
     - 401
     - Invalid or missing API key
   * - ``AUTHORIZATION_ERROR``
     - 403
     - Access denied or IP blocked
   * - ``RATE_LIMIT_ERROR``
     - 429
     - Rate limit exceeded
   * - ``REASONING_ERROR``
     - 500
     - Symbolic reasoning operation failed
   * - ``RETRIEVAL_ERROR``
     - 500
     - Vector retrieval operation failed
   * - ``INTERNAL_ERROR``
     - 500
     - Unexpected server error

Rate Limiting
-------------

Rate Limit Headers
~~~~~~~~~~~~~~~~~~

Responses include rate limiting information:

.. code-block:: http

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1677652348

Default Limits
~~~~~~~~~~~~~~

* **Requests per minute:** 100 (configurable)
* **Request size limit:** 10MB (configurable)
* **Concurrent requests:** 10 per client

CORS Support
------------

The API supports Cross-Origin Resource Sharing (CORS) with configurable origins.

**Allowed Methods:** GET, POST, PUT, DELETE, OPTIONS
**Allowed Headers:** Authorization, Content-Type, X-API-Key
**Credentials:** Supported

OpenAPI Documentation
---------------------

Interactive API documentation is available at:

* **Swagger UI:** ``/docs``
* **ReDoc:** ``/redoc``
* **OpenAPI JSON:** ``/openapi.json``

These endpoints provide interactive documentation where you can test API calls directly.
