Main Application Module
========================

.. automodule:: main
   :members:
   :undoc-members:
   :show-inheritance:

FastAPI Application
-------------------

The main FastAPI application provides OpenAI-compatible endpoints for chat completions,
performance monitoring, and system information.

Key Features:
* OpenAI-compatible chat completions API
* Streaming response support
* Performance monitoring endpoints
* Health check endpoints
* Automatic API documentation

Endpoints
---------

Chat Completions
~~~~~~~~~~~~~~~~

.. autofunction:: main.chat_completions

Performance Monitoring
~~~~~~~~~~~~~~~~~~~~~~

.. autofunction:: main.get_performance_metrics

System Information
~~~~~~~~~~~~~~~~~~

.. autofunction:: main.get_system_info

Health Checks
~~~~~~~~~~~~~

.. autofunction:: main.health_check
.. autofunction:: main.readiness_check

Configuration
-------------

The application uses environment-based configuration with the following key settings:

* ``APP_HOST``: Server host address (default: 0.0.0.0)
* ``APP_PORT``: Server port (default: 8000)
* ``APP_DEBUG``: Debug mode (default: False)
* ``APP_WORKERS``: Number of worker processes (default: 1)

Security
--------

All endpoints are protected by:

* API key authentication
* Rate limiting
* Input validation
* Request size limits
* CORS configuration

Example Usage
-------------

.. code-block:: python

   import requests
   
   # Chat completion request
   response = requests.post(
       "http://localhost:8000/v1/chat/completions",
       headers={"Authorization": "Bearer your-api-key"},
       json={
           "model": "local",
           "messages": [
               {"role": "user", "content": "Explain symbolic reasoning"}
           ],
           "max_tokens": 150,
           "temperature": 0.7
       }
   )
   
   result = response.json()
   print(result["choices"][0]["message"]["content"])

.. code-block:: python

   # Streaming response
   response = requests.post(
       "http://localhost:8000/v1/chat/completions",
       headers={"Authorization": "Bearer your-api-key"},
       json={
           "model": "local",
           "messages": [
               {"role": "user", "content": "What is AI?"}
           ],
           "stream": True
       },
       stream=True
   )
   
   for line in response.iter_lines():
       if line:
           print(line.decode())

Error Handling
--------------

The API returns structured error responses:

.. code-block:: json

   {
       "error": {
           "code": "VALIDATION_ERROR",
           "message": "Invalid request format",
           "details": {
               "field": "messages",
               "reason": "Messages array cannot be empty"
           }
       }
   }

Common error codes:

* ``VALIDATION_ERROR`` (400): Invalid request format or parameters
* ``AUTHENTICATION_ERROR`` (401): Invalid or missing API key
* ``RATE_LIMIT_ERROR`` (429): Rate limit exceeded
* ``INTERNAL_ERROR`` (500): Server error
