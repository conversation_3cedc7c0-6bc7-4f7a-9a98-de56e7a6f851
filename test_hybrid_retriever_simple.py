"""
Simplified test script for HybridRetriever with a mock LightRAG implementation.
"""

import sys
import os
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

# Add project root to Python path
project_root = str(Path(__file__).parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Mock LightRAG class for testing
class MockLightRAG:
    def __init__(self, model_name: str = "mock-model", use_gpu: bool = False):
        self.model_name = model_name
        self.use_gpu = use_gpu
        self.embeddings = {}
    
    def encode(self, texts: List[str], **kwargs) -> np.ndarray:
        """Generate deterministic embeddings for testing."""
        # Simple hash-based embedding for testing
        embeddings = []
        for text in texts:
            # Simple hash-based embedding
            vec = np.zeros(768, dtype=np.float32)
            for i, c in enumerate(text.lower()):
                if i >= 768:
                    break
                vec[i] = (ord(c) % 100) / 100.0  # Simple deterministic values
            embeddings.append(vec)
        return np.array(embeddings)
    
    def rerank(self, query: str, documents: List[str], top_k: int = 5) -> List[float]:
        """Simple reranking that prefers longer documents."""
        # Just return scores based on document length for testing
        return [len(doc) / 1000.0 for doc in documents][:top_k]

# Replace the LightRAG import with our mock
import sys
import importlib
sys.modules['lightrag'] = type(sys)('lightrag')
sys.modules['lightrag'].LightRAG = MockLightRAG

# Now import the HybridRetriever
from src.hybrid_retriever import HybridRetriever

def main():
    print("Testing HybridRetriever with mock LightRAG implementation")
    print("=" * 60)
    
    # Initialize the retriever with mock LightRAG
    retriever = HybridRetriever(
        vector_db="torch",  # Use torch for this test to avoid FAISS dependency issues
        use_gpu=False,      # Disable GPU for this test
        first_stage_k=10,
        rerank_k=5,
        model_name="mock-model"
    )
    
    # Add test documents
    print("\nAdding test documents...")
    documents = [
        {"id": "doc1", "text": "The quick brown fox jumps over the lazy dog.", "category": "animals"},
        {"id": "doc2", "text": "Machine learning is a subset of artificial intelligence.", "category": "ai"},
        {"id": "doc3", "text": "The capital of France is Paris.", "category": "geography"},
        {"id": "doc4", "text": "Python is a popular programming language for data science.", "category": "programming"},
        {"id": "doc5", "text": "Deep learning requires large amounts of data and computational resources.", "category": "ai"},
        {"id": "doc6", "text": "Paris is known as the City of Light.", "category": "geography"},
    ]
    retriever.add_documents(documents)
    
    # Test search with reranking
    test_queries = [
        "What is machine learning?",
        "Tell me about Paris",
        "Programming languages"
    ]
    
    for query in test_queries:
        print(f"\n{'='*60}")
        print(f"QUERY: {query}")
        print("-" * 60)
        
        # Search with reranking
        print("\nWith Reranking:")
        results = retriever.search(query, rerank=True)
        for i, result in enumerate(results, 1):
            print(f"{i}. [{result.metadata.get('category', 'N/A')}] {result.text} (Score: {result.score:.4f})")
        
        # Search without reranking for comparison
        print("\nWithout Reranking:")
        results = retriever.search(query, rerank=False)
        for i, result in enumerate(results, 1):
            print(f"{i}. [{result.metadata.get('category', 'N/A')}] {result.text} (Score: {result.score:.4f})")
    
    # Print system information
    print("\n" + "="*60)
    print("System Information:")
    print("-" * 60)
    for key, value in retriever.get_system_info().items():
        print(f"{key}: {value}")
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
