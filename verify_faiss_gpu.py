"""
FAISS-GPU Verification Script

This script verifies that FAISS-GPU is working correctly in your environment.
Run this from your Conda environment after fixing the installation issues.
"""

import os
import sys
import numpy as np
import time

def verify_faiss_basic():
    """Basic verification of FAISS installation."""
    print("\n1. Verifying basic FAISS import:")
    try:
        import faiss
        print("✅ Successfully imported FAISS")
        print(f"   Version: {getattr(faiss, '__version__', 'Unknown')}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import FAISS: {e}")
        return False

def verify_faiss_gpu():
    """Verify FAISS GPU capabilities."""
    print("\n2. Verifying FAISS GPU support:")
    try:
        import faiss
        if hasattr(faiss, 'StandardGpuResources'):
            print("✅ FAISS has GPU support")
            return True
        else:
            print("❌ FAISS does not have GPU support")
            return False
    except ImportError:
        print("❌ Failed to import FAISS")
        return False

def verify_gpu_search():
    """Verify FAISS GPU search functionality."""
    print("\n3. Testing FAISS GPU search functionality:")
    try:
        import faiss
        import numpy as np

        # Check GPU availability
        if not hasattr(faiss, 'StandardGpuResources'):
            print("❌ FAISS GPU support not available")
            return False

        # Create GPU resources
        print("   Creating GPU resources...")
        res = faiss.StandardGpuResources()

        # Create a small test index
        print("   Creating test index...")
        dimension = 128
        nb = 10000  # Database size
        nq = 100    # Number of queries
        np.random.seed(42)

        # Generate random data
        print("   Generating test data...")
        xb = np.random.random((nb, dimension)).astype('float32')
        xq = np.random.random((nq, dimension)).astype('float32')

        # Create CPU index
        print("   Creating CPU index...")
        cpu_index = faiss.IndexFlatL2(dimension)
        cpu_index.add(xb)

        # Create GPU index
        print("   Creating GPU index...")
        gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)

        # Search on CPU
        print("   Running CPU search...")
        start_time = time.time()
        cpu_distances, cpu_indices = cpu_index.search(xq, 5)
        cpu_time = time.time() - start_time
        print(f"   CPU search time: {cpu_time:.4f} seconds")

        # Search on GPU
        print("   Running GPU search...")
        start_time = time.time()
        gpu_distances, gpu_indices = gpu_index.search(xq, 5)
        gpu_time = time.time() - start_time
        print(f"   GPU search time: {gpu_time:.4f} seconds")
        
        # Verify results
        if np.array_equal(cpu_indices, gpu_indices):
            print("✅ CPU and GPU search results match")
        else:
            print("⚠️ CPU and GPU search results differ (this can happen due to floating point precision)")
        
        # Compare speed
        speedup = cpu_time / gpu_time
        print(f"   GPU speedup: {speedup:.2f}x")
        
        if speedup > 1:
            print("✅ GPU search is faster than CPU search")
        else:
            print("⚠️ GPU search is not faster than CPU search (might indicate GPU issues)")
        
        return True
    except Exception as e:
        print(f"❌ Error during GPU search test: {e}")
        return False

def main():
    """Run all verification tests."""
    print("=" * 60)
    print("FAISS-GPU Verification")
    print("=" * 60)
    print(f"Python executable: {sys.executable}")
    print(f"Working directory: {os.getcwd()}")
    
    # Run verification tests
    basic_import = verify_faiss_basic()
    gpu_support = verify_faiss_gpu() if basic_import else False
    gpu_search = verify_gpu_search() if gpu_support else False
    
    # Summary
    print("\n" + "=" * 60)
    print("Verification Summary")
    print("=" * 60)
    print(f"Basic FAISS import: {'✅ PASS' if basic_import else '❌ FAIL'}")
    print(f"FAISS GPU support: {'✅ PASS' if gpu_support else '❌ FAIL'}")
    print(f"FAISS GPU search: {'✅ PASS' if gpu_search else '❌ FAIL'}")
    
    if basic_import and gpu_support and gpu_search:
        print("\n✅ FAISS-GPU is working correctly!")
    else:
        print("\n❌ FAISS-GPU verification failed.")
        
        if not basic_import:
            print("\nTo fix basic FAISS import issues:")
            print("1. Make sure FAISS is installed: conda install -c conda-forge faiss-gpu")
            print("2. Check for conflicting FAISS installations")
        
        if basic_import and not gpu_support:
            print("\nTo fix FAISS GPU support issues:")
            print("1. Make sure you've installed faiss-gpu, not faiss-cpu")
            print("2. Reinstall: conda install -c conda-forge faiss-gpu")
            print("3. Install Microsoft Visual C++ Redistributable")
        
        if gpu_support and not gpu_search:
            print("\nTo fix FAISS GPU search issues:")
            print("1. Check your GPU drivers")
            print("2. Verify CUDA installation")
            print("3. Ensure your GPU is compatible with FAISS-GPU")

if __name__ == "__main__":
    main()
