"""Test GPU functionality for both symbolic reasoning and retrieval components."""

import sys
import os
import unittest
import torch
import numpy as np
from pathlib import Path
import time
import site

# Add src directory to Python path
src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Print Python environment diagnostics
print("--- Python Environment Diagnostics ---")
print(f"Python Executable: {sys.executable}")
print("sys.path:")
for p in sys.path:
    print(f"  {p}")
print("Site packages:")
print(f"  {os.path.dirname(sys.executable)}")
print(f"  {os.path.join(os.path.dirname(sys.executable), 'lib', 'site-packages')}")
print(f"User site packages: {os.path.expanduser('~/.local/lib/python3.10/site-packages')}")

# Attempt to import FAISS and set a flag
FAISS_AVAILABLE = False
FAISS_GPU_AVAILABLE = False

try:
    print("Attempting to import faiss...")
    import faiss
    print(f"Successfully imported faiss. faiss.__file__: {getattr(faiss, '__file__', 'N/A')}")
    print(f"faiss.__path__: {getattr(faiss, '__path__', 'N/A')}")
    
    faiss_module_path = None
    if hasattr(faiss, '__file__') and faiss.__file__:
        faiss_module_path = os.path.dirname(faiss.__file__)
    elif hasattr(faiss, '__path__') and faiss.__path__:
        faiss_module_path = faiss.__path__[0] # Use the first path in the list for namespace packages
        
    if faiss_module_path:
        print(f"Directory of faiss module: {faiss_module_path}")
        print(f"Contents of {faiss_module_path}:")
        try:
            for item in os.listdir(faiss_module_path):
                print(f"  - {item}")
        except Exception as e:
            print(f"    Could not list directory: {e}")
    else:
        print("Could not determine faiss module path.")

    FAISS_AVAILABLE = True
    # Check if it's faiss-gpu by looking for StandardGpuResources
    if hasattr(faiss, 'StandardGpuResources'):
        print("faiss.StandardGpuResources found. Attempting to use GPU.")
        try:
            res = faiss.StandardGpuResources()
            FAISS_GPU_AVAILABLE = True
            print("FAISS GPU resources initialized successfully.")
        except Exception as e:
            print(f"Failed to initialize FAISS GPU resources: {e}")
            FAISS_GPU_AVAILABLE = False
    else:
        print("faiss.StandardGpuResources not found. This is faiss-cpu or an incomplete install.")
        FAISS_GPU_AVAILABLE = False
except ImportError as e:
    print(f"Failed to import faiss: {e}")
    FAISS_AVAILABLE = False
except Exception as e:
    print(f"An unexpected error occurred during FAISS import/check: {e}")
    FAISS_AVAILABLE = False

print(f"Initial FAISS_AVAILABLE: {FAISS_AVAILABLE}")
print(f"Initial FAISS_GPU_AVAILABLE: {FAISS_GPU_AVAILABLE}")

from symbolic_reasoning import SymbolicReasoner
from retrieval import Retriever
from vector_store import TorchVectorStore


class TestGPUSupport(unittest.TestCase):
    """Test the GPU functionality of the system components."""
    
    def setUp(self):
        """Set up test environment."""
        self.gpu_available = torch.cuda.is_available()
        if not self.gpu_available:
            self.skipTest("CUDA is not available on this system")
        print(f"Testing on GPU: {torch.cuda.get_device_name(0)}")
    
    def test_symbolic_reasoner_gpu(self):
        """Test that the symbolic reasoner can use the GPU."""
        reasoner = SymbolicReasoner(use_gpu=True)
        self.assertTrue(reasoner.use_gpu)
        
        # Test system info includes GPU details
        sys_info = reasoner.get_system_info()
        self.assertTrue(sys_info['gpu_available'])
        self.assertTrue(sys_info['gpu_enabled'])
        self.assertIsNotNone(sys_info['gpu_name'])
        print(f"Symbolic Reasoner GPU info: {sys_info}")
        
        # Test query processing with GPU
        response = reasoner.process_query("What is symbolic reasoning?")
        self.assertIsNotNone(response)
        print(f"Query response length: {len(response)}")

    def test_retriever_gpu(self):
        print("\n--- Running test_retriever_gpu ---")
        
        # Initialize retriever with GPU support
        retriever = Retriever(vector_db="faiss", use_gpu=True)
        self.assertIsNotNone(retriever.index, "Retriever index should be initialized.")
        
        # Verify GPU is being used
        self.assertTrue(retriever.use_gpu, "Retriever should be using GPU when available")
        
        # Get system info
        sys_info = retriever.get_system_info()
        print(f"Retriever system info: {sys_info}")
        
        # Test basic vector operations
        test_vectors = np.random.rand(10, retriever.dimension).astype('float32')
        test_docs = [{'id': str(i), 'text': f'test_{i}'} for i in range(10)]
        
        # Add vectors and verify
        retriever.add_documents(test_docs, test_vectors)
        
        # Search and verify results
        query_vector = np.random.rand(1, retriever.dimension).astype('float32')
        results = retriever.search(query_vector, k=3)
        
        self.assertEqual(len(results), 3, "Search should return k results")
        for result in results:
            self.assertIn('text', result, "Each result should have text")
            self.assertIn('score', result, "Each result should have a score")
        
        print("Vector search test completed successfully.")
        print(f"Using {'TorchVectorStore' if retriever.using_torch_fallback else 'FAISS'} backend")
        if retriever.using_torch_fallback:
            print("Note: Using TorchVectorStore fallback with GPU support")
            self.assertTrue(retriever.index.use_gpu, "TorchVectorStore should be using GPU when requested")

    def test_cuda_tensor_operations(self):
        """Test basic CUDA tensor operations to verify GPU functionality."""
        # Create tensors on GPU
        a = torch.rand(1000, 1000).cuda()
        b = torch.rand(1000, 1000).cuda()
        
        # Perform matrix multiplication (computationally intensive)
        start = torch.cuda.Event(enable_timing=True)
        end = torch.cuda.Event(enable_timing=True)
        
        start.record()
        result = torch.matmul(a, b)
        end.record()
        
        # Synchronize to get accurate timing
        torch.cuda.synchronize()
        
        # Get elapsed time
        elapsed_time = start.elapsed_time(end)
        print(f"GPU matrix multiplication elapsed time: {elapsed_time} ms")
        
        # Verify the result shape
        self.assertEqual(result.shape, (1000, 1000))
        self.assertTrue(result.is_cuda)


if __name__ == "__main__":
    unittest.main()
