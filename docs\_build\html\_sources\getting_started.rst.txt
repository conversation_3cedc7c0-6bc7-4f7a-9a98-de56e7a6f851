Getting Started
===============

Welcome to the Neural Symbolic Language Model! This guide will help you get up and running quickly.

Overview
--------

The Neural Symbolic Language Model is a production-ready AI system that combines:

* **Symbolic Reasoning**: Advanced logical reasoning capabilities
* **Neural Language Processing**: Modern transformer-based language understanding
* **Vector Retrieval**: Efficient semantic search and document retrieval
* **OpenAI Compatibility**: Drop-in replacement for OpenAI's chat completions API

Key Features
------------

🧠 **Hybrid AI Architecture**
   Combines symbolic reasoning with neural language processing for enhanced explainability

🔍 **Retrieval-Augmented Generation**
   Grounds responses in factual information through vector-based document retrieval

🚀 **Production Ready**
   Enterprise-grade security, monitoring, and scalability features

🔌 **OpenAI Compatible**
   Drop-in replacement for OpenAI's chat completions API

🛡️ **Secure by Design**
   Comprehensive authentication, rate limiting, and input validation

📊 **Full Observability**
   Built-in monitoring, logging, and performance metrics

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~~

**Minimum Requirements:**
* Python 3.8 or higher
* 4GB RAM
* 2 CPU cores
* 10GB disk space

**Recommended for Production:**
* Python 3.10+
* 8GB+ RAM
* 4+ CPU cores
* NVIDIA GPU with 8GB+ VRAM
* 50GB+ disk space

**Operating Systems:**
* Linux (Ubuntu 20.04+, CentOS 8+)
* macOS (10.15+)
* Windows 10/11

Dependencies
~~~~~~~~~~~~

The system requires several Python packages which will be installed automatically:

* **FastAPI** - Modern web framework
* **PyTorch** - Neural network framework
* **FAISS** - Vector similarity search
* **Pydantic** - Data validation
* **Uvicorn** - ASGI server

Quick Installation
------------------

1. **Clone the Repository**

   .. code-block:: bash

      git clone https://github.com/your-org/neural-symbolic-language-model.git
      cd neural-symbolic-language-model

2. **Create Virtual Environment**

   .. code-block:: bash

      # Using venv
      python -m venv env
      source env/bin/activate  # On Windows: env\Scripts\activate

      # Or using conda
      conda create -n neural-symbolic python=3.10
      conda activate neural-symbolic

3. **Install Dependencies**

   .. code-block:: bash

      pip install -r requirements.txt

4. **Configure Environment**

   .. code-block:: bash

      # Copy environment template
      cp .env.example .env
      
      # Edit configuration (see Configuration Guide for details)
      nano .env

5. **Start the Server**

   .. code-block:: bash

      python src/main.py

   The server will start on ``http://localhost:8000``

First API Call
--------------

Once the server is running, you can make your first API call:

**Using curl:**

.. code-block:: bash

   curl -X POST "http://localhost:8000/v1/chat/completions" \
        -H "Authorization: Bearer your-api-key" \
        -H "Content-Type: application/json" \
        -d '{
          "model": "local",
          "messages": [
            {
              "role": "user", 
              "content": "What is symbolic reasoning and how does it differ from neural approaches?"
            }
          ],
          "max_tokens": 150,
          "temperature": 0.7
        }'

**Using Python:**

.. code-block:: python

   import requests

   response = requests.post(
       "http://localhost:8000/v1/chat/completions",
       headers={
           "Authorization": "Bearer your-api-key",
           "Content-Type": "application/json"
       },
       json={
           "model": "local",
           "messages": [
               {
                   "role": "user",
                   "content": "Explain the benefits of hybrid AI systems."
               }
           ],
           "max_tokens": 150,
           "temperature": 0.7
       }
   )

   result = response.json()
   print(result["choices"][0]["message"]["content"])

**Using JavaScript:**

.. code-block:: javascript

   const response = await fetch('http://localhost:8000/v1/chat/completions', {
     method: 'POST',
     headers: {
       'Authorization': 'Bearer your-api-key',
       'Content-Type': 'application/json'
     },
     body: JSON.stringify({
       model: 'local',
       messages: [
         {
           role: 'user',
           content: 'How does retrieval-augmented generation work?'
         }
       ],
       max_tokens: 150,
       temperature: 0.7
     })
   });

   const result = await response.json();
   console.log(result.choices[0].message.content);

Verification
------------

Health Check
~~~~~~~~~~~~

Verify the system is running correctly:

.. code-block:: bash

   curl http://localhost:8000/health

Expected response:

.. code-block:: json

   {
     "status": "healthy",
     "timestamp": "2025-06-29T12:00:00Z"
   }

System Information
~~~~~~~~~~~~~~~~~~

Get detailed system information:

.. code-block:: bash

   curl -H "Authorization: Bearer your-api-key" \
        http://localhost:8000/system/info

Expected response:

.. code-block:: json

   {
     "reasoner": {
       "status": "operational",
       "engine": "local",
       "model": "llama"
     },
     "retriever": {
       "status": "operational",
       "vector_db": "faiss",
       "index_size": 0
     },
     "gpu_optimized": true,
     "version": "0.1.0"
   }

Performance Metrics
~~~~~~~~~~~~~~~~~~~

Monitor system performance:

.. code-block:: bash

   curl -H "Authorization: Bearer your-api-key" \
        http://localhost:8000/performance

Next Steps
----------

Now that you have the system running, explore these topics:

📖 **Configuration**
   Learn how to configure the system for different environments
   
   * :doc:`configuration` - Detailed configuration guide
   * Environment-specific settings
   * Security configuration

🔌 **API Usage**
   Explore the full API capabilities
   
   * :doc:`api_reference` - Complete API documentation
   * OpenAI compatibility features
   * Streaming responses

🏗️ **Architecture**
   Understand the system architecture
   
   * :doc:`architecture` - System design and components
   * Symbolic reasoning engine
   * Vector retrieval system

🧪 **Development**
   Set up development environment
   
   * :doc:`contributing` - Development guidelines
   * :doc:`testing` - Running tests
   * Code quality tools

Common Issues
-------------

Port Already in Use
~~~~~~~~~~~~~~~~~~~

If port 8000 is already in use:

.. code-block:: bash

   # Change port in .env file
   APP_PORT=8001
   
   # Or specify when running
   python src/main.py --port 8001

GPU Not Available
~~~~~~~~~~~~~~~~~

If you don't have a GPU or it's not detected:

.. code-block:: bash

   # Disable GPU in .env file
   MODEL_USE_GPU=false

Missing Dependencies
~~~~~~~~~~~~~~~~~~~~

If you encounter import errors:

.. code-block:: bash

   # Reinstall dependencies
   pip install -r requirements.txt --force-reinstall
   
   # Or install specific packages
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

API Key Issues
~~~~~~~~~~~~~~

If authentication fails:

1. Check your API key in the ``.env`` file
2. Ensure the ``Authorization`` header is correctly formatted
3. Verify the API key is not expired or blocked

Getting Help
------------

If you need assistance:

* **Documentation**: Browse the complete documentation
* **GitHub Issues**: Report bugs or request features
* **Community**: Join our community discussions
* **Support**: Contact our support team

**Useful Links:**

* 📚 **Full Documentation**: Complete user and developer guides
* 🐛 **Issue Tracker**: Report bugs and request features  
* 💬 **Discussions**: Community support and questions
* 📧 **Contact**: Direct support contact
