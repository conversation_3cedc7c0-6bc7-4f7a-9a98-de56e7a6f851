Data Models Module
===================

.. automodule:: models
   :members:
   :undoc-members:
   :show-inheritance:

Overview
--------

The models module defines Pydantic data models for request/response validation,
configuration management, and data serialization throughout the application.

Key Features:
* Pydantic v2 models with comprehensive validation
* OpenAI-compatible request/response formats
* Type safety with Python type hints
* Automatic JSON serialization/deserialization
* Field validation with custom validators

Request Models
--------------

ChatCompletionRequest
~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: models.ChatCompletionRequest
   :members:
   :undoc-members:
   :show-inheritance:

Main request model for chat completions:

.. code-block:: python

   from models import ChatCompletionRequest, Message
   
   # Create a chat completion request
   request = ChatCompletionRequest(
       model="local",
       messages=[
           Message(role="user", content="What is symbolic reasoning?")
       ],
       max_tokens=150,
       temperature=0.7,
       stream=False
   )

Fields:

* ``model``: Model identifier (required)
* ``messages``: List of conversation messages (required)
* ``max_tokens``: Maximum tokens in response (optional)
* ``temperature``: Response randomness 0.0-1.0 (optional)
* ``top_p``: Nucleus sampling parameter (optional)
* ``stream``: Enable streaming response (optional)
* ``stop``: Stop sequences (optional)

Message
~~~~~~~

.. autoclass:: models.Message
   :members:
   :undoc-members:
   :show-inheritance:

Individual message in a conversation:

.. code-block:: python

   from models import Message
   
   # User message
   user_msg = Message(role="user", content="Hello, AI!")
   
   # Assistant message
   assistant_msg = Message(role="assistant", content="Hello! How can I help?")
   
   # System message
   system_msg = Message(role="system", content="You are a helpful assistant.")

Fields:

* ``role``: Message role ("user", "assistant", "system")
* ``content``: Message content text
* ``name``: Optional message author name

Response Models
---------------

ChatCompletionResponse
~~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: models.ChatCompletionResponse
   :members:
   :undoc-members:
   :show-inheritance:

Response model for chat completions:

.. code-block:: python

   from models import ChatCompletionResponse, Choice, Usage
   
   # Create response
   response = ChatCompletionResponse(
       id="chatcmpl-123",
       object="chat.completion",
       created=1677652288,
       model="local",
       choices=[
           Choice(
               index=0,
               message=Message(role="assistant", content="Response text"),
               finish_reason="stop"
           )
       ],
       usage=Usage(
           prompt_tokens=10,
           completion_tokens=20,
           total_tokens=30
       )
   )

Fields:

* ``id``: Unique response identifier
* ``object``: Object type ("chat.completion")
* ``created``: Unix timestamp
* ``model``: Model used for generation
* ``choices``: List of response choices
* ``usage``: Token usage information

Choice
~~~~~~

.. autoclass:: models.Choice
   :members:
   :undoc-members:
   :show-inheritance:

Individual choice in a response:

Fields:

* ``index``: Choice index in the list
* ``message``: Response message
* ``finish_reason``: Reason for completion ("stop", "length", "content_filter")

Usage
~~~~~

.. autoclass:: models.Usage
   :members:
   :undoc-members:
   :show-inheritance:

Token usage information:

Fields:

* ``prompt_tokens``: Tokens in the prompt
* ``completion_tokens``: Tokens in the completion
* ``total_tokens``: Total tokens used

Streaming Models
----------------

ChatCompletionChunk
~~~~~~~~~~~~~~~~~~~

.. autoclass:: models.ChatCompletionChunk
   :members:
   :undoc-members:
   :show-inheritance:

Streaming response chunk:

.. code-block:: python

   from models import ChatCompletionChunk, ChoiceDelta
   
   # Create streaming chunk
   chunk = ChatCompletionChunk(
       id="chatcmpl-123",
       object="chat.completion.chunk",
       created=1677652288,
       model="local",
       choices=[
           ChoiceDelta(
               index=0,
               delta={"content": "Hello"},
               finish_reason=None
           )
       ]
   )

ChoiceDelta
~~~~~~~~~~~

.. autoclass:: models.ChoiceDelta
   :members:
   :undoc-members:
   :show-inheritance:

Delta information for streaming:

Fields:

* ``index``: Choice index
* ``delta``: Incremental content
* ``finish_reason``: Completion reason (when finished)

Error Models
------------

ErrorResponse
~~~~~~~~~~~~~

.. autoclass:: models.ErrorResponse
   :members:
   :undoc-members:
   :show-inheritance:

Structured error response:

.. code-block:: python

   from models import ErrorResponse, ErrorDetail
   
   # Create error response
   error = ErrorResponse(
       error=ErrorDetail(
           code="VALIDATION_ERROR",
           message="Invalid request format",
           details={"field": "messages"}
       )
   )

ErrorDetail
~~~~~~~~~~~

.. autoclass:: models.ErrorDetail
   :members:
   :undoc-members:
   :show-inheritance:

Detailed error information:

Fields:

* ``code``: Machine-readable error code
* ``message``: Human-readable error message
* ``details``: Additional error context

System Models
-------------

SystemInfo
~~~~~~~~~~

.. autoclass:: models.SystemInfo
   :members:
   :undoc-members:
   :show-inheritance:

System information response:

.. code-block:: python

   from models import SystemInfo
   
   info = SystemInfo(
       reasoner={"status": "operational", "model": "llama"},
       retriever={"status": "operational", "index_size": 1000},
       gpu_optimized=True,
       version="0.1.0"
   )

PerformanceMetrics
~~~~~~~~~~~~~~~~~~

.. autoclass:: models.PerformanceMetrics
   :members:
   :undoc-members:
   :show-inheritance:

Performance monitoring data:

.. code-block:: python

   from models import PerformanceMetrics, CacheMetrics, SystemMetrics
   
   metrics = PerformanceMetrics(
       cache=CacheMetrics(
           size=100,
           max_size=1000,
           hits=80,
           misses=20,
           hit_rate=0.8
       ),
       system=SystemMetrics(
           cpu_percent=25.5,
           memory_percent=60.2,
           gpu_available=True,
           active_requests=5
       )
   )

Validation Features
-------------------

Field Validation
~~~~~~~~~~~~~~~~

Models include comprehensive field validation:

.. code-block:: python

   from models import ChatCompletionRequest
   from pydantic import ValidationError
   
   try:
       # This will raise ValidationError
       request = ChatCompletionRequest(
           model="",  # Empty model name
           messages=[],  # Empty messages list
           temperature=2.0  # Invalid temperature > 1.0
       )
   except ValidationError as e:
       print(e.errors())

Custom Validators
~~~~~~~~~~~~~~~~~

Models include custom validation logic:

.. code-block:: python

   # Temperature validation
   @field_validator('temperature')
   @classmethod
   def validate_temperature(cls, v):
       if v < 0.0 or v > 1.0:
           raise ValueError('Temperature must be between 0.0 and 1.0')
       return v
   
   # Messages validation
   @field_validator('messages')
   @classmethod
   def validate_messages(cls, v):
       if not v:
           raise ValueError('Messages list cannot be empty')
       if not any(msg.role == 'user' for msg in v):
           raise ValueError('At least one user message is required')
       return v

Serialization
-------------

JSON Serialization
~~~~~~~~~~~~~~~~~~

All models support JSON serialization:

.. code-block:: python

   from models import ChatCompletionRequest, Message
   
   # Create model
   request = ChatCompletionRequest(
       model="local",
       messages=[Message(role="user", content="Hello")]
   )
   
   # Serialize to JSON
   json_data = request.model_dump_json()
   
   # Deserialize from JSON
   request_copy = ChatCompletionRequest.model_validate_json(json_data)

Dictionary Conversion
~~~~~~~~~~~~~~~~~~~~~

Models can be converted to/from dictionaries:

.. code-block:: python

   # To dictionary
   data = request.model_dump()
   
   # From dictionary
   request_copy = ChatCompletionRequest.model_validate(data)
   
   # Exclude certain fields
   public_data = request.model_dump(exclude={'internal_field'})

Example Usage
-------------

Complete Request/Response Cycle
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from models import (
       ChatCompletionRequest, ChatCompletionResponse,
       Message, Choice, Usage
   )
   
   # Parse incoming request
   request_data = {
       "model": "local",
       "messages": [
           {"role": "user", "content": "What is AI?"}
       ],
       "max_tokens": 100,
       "temperature": 0.7
   }
   
   request = ChatCompletionRequest.model_validate(request_data)
   
   # Process request (business logic)
   response_content = process_chat_request(request)
   
   # Create response
   response = ChatCompletionResponse(
       id="chatcmpl-123",
       object="chat.completion",
       created=int(time.time()),
       model=request.model,
       choices=[
           Choice(
               index=0,
               message=Message(role="assistant", content=response_content),
               finish_reason="stop"
           )
       ],
       usage=Usage(
           prompt_tokens=len(request.messages[0].content.split()),
           completion_tokens=len(response_content.split()),
           total_tokens=len(request.messages[0].content.split()) + len(response_content.split())
       )
   )
   
   # Return JSON response
   return response.model_dump_json()

Best Practices
--------------

Model Design
~~~~~~~~~~~~

* Use descriptive field names and types
* Include comprehensive docstrings
* Add validation for business rules
* Use optional fields with sensible defaults

Validation
~~~~~~~~~~

* Validate inputs at API boundaries
* Use custom validators for complex rules
* Provide clear error messages
* Handle validation errors gracefully

Performance
~~~~~~~~~~~

* Use model_dump() for serialization
* Cache validated models when appropriate
* Consider using model_validate() for trusted data
* Profile serialization performance for high-throughput scenarios
