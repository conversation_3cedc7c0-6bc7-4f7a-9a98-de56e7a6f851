#!/usr/bin/env python3
"""
Comprehensive benchmark runner for Neural Symbolic Language Model with Ollama gemma3n:e2b.
This script runs all available benchmarks and performance tests using the Ollama backend.
"""

import os
import sys
import time
import asyncio
import subprocess
import signal
import json
from datetime import datetime
from pathlib import Path

# Set environment for Ollama testing
os.environ['APP_ENVIRONMENT'] = 'test'
os.environ['MODEL_REASONING_ENGINE'] = 'ollama'
os.environ['MODEL_REASONING_MODEL'] = 'gemma3n:e2b'
os.environ['MODEL_USE_GPU'] = 'false'
os.environ['APP_PORT'] = '8080'

print("🚀 Neural Symbolic Language Model - Ollama Benchmark Suite")
print("=" * 70)
print(f"🤖 Model: gemma3n:e2b")
print(f"🔧 Backend: Ollama")
print(f"📅 Timestamp: {datetime.now().isoformat()}")
print("=" * 70)


class BenchmarkRunner:
    """Manages benchmark execution and API server lifecycle."""
    
    def __init__(self):
        self.api_process = None
        self.project_root = Path(__file__).parent
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "model": "gemma3n:e2b",
            "backend": "ollama",
            "benchmarks": {}
        }
    
    def start_api_server(self):
        """Start the API server for benchmarking."""
        print("🔄 Starting API server...")
        
        try:
            # Start the server
            cmd = [
                sys.executable, 
                "src/main.py"
            ]
            
            self.api_process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for server to start
            print("⏳ Waiting for server to initialize...")
            time.sleep(10)  # Give server time to start
            
            # Test if server is responding
            import requests
            for attempt in range(5):
                try:
                    response = requests.get("http://localhost:8080/health", timeout=5)
                    if response.status_code == 200:
                        print("✅ API server is running and healthy")
                        return True
                except:
                    print(f"⏳ Attempt {attempt + 1}/5: Waiting for server...")
                    time.sleep(3)
            
            print("❌ Failed to start API server")
            return False
            
        except Exception as e:
            print(f"❌ Error starting server: {e}")
            return False
    
    def stop_api_server(self):
        """Stop the API server."""
        if self.api_process:
            print("🛑 Stopping API server...")
            self.api_process.terminate()
            try:
                self.api_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.api_process.kill()
            print("✅ API server stopped")
    
    def run_benchmark_script(self, script_name, description):
        """Run a specific benchmark script."""
        print(f"\n🧪 Running {description}...")
        print("-" * 50)
        
        script_path = self.project_root / "scripts" / script_name
        
        if not script_path.exists():
            print(f"⚠️  Script not found: {script_path}")
            return {"status": "skipped", "reason": "script not found"}
        
        try:
            start_time = time.time()
            
            result = subprocess.run(
                [sys.executable, str(script_path)],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ {description} completed successfully ({duration:.2f}s)")
                return {
                    "status": "success",
                    "duration": duration,
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
            else:
                print(f"❌ {description} failed (exit code: {result.returncode})")
                print(f"Error output: {result.stderr}")
                return {
                    "status": "failed",
                    "duration": duration,
                    "exit_code": result.returncode,
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} timed out after 5 minutes")
            return {"status": "timeout", "duration": 300}
        except Exception as e:
            print(f"💥 {description} crashed: {e}")
            return {"status": "error", "error": str(e)}
    
    def run_performance_tests(self):
        """Run pytest performance tests."""
        print(f"\n🧪 Running Performance Unit Tests...")
        print("-" * 50)
        
        try:
            start_time = time.time()
            
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                "tests/test_performance.py", 
                "-v", "--tb=short"
            ], cwd=self.project_root, capture_output=True, text=True, timeout=300)
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ Performance tests completed successfully ({duration:.2f}s)")
                return {
                    "status": "success",
                    "duration": duration,
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
            else:
                print(f"❌ Performance tests failed")
                print(f"Output: {result.stdout}")
                return {
                    "status": "failed",
                    "duration": duration,
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
                
        except Exception as e:
            print(f"💥 Performance tests crashed: {e}")
            return {"status": "error", "error": str(e)}
    
    def run_api_load_test(self):
        """Run API load testing."""
        print(f"\n🧪 Running API Load Test...")
        print("-" * 50)
        
        try:
            # Simple load test using requests
            import requests
            
            test_queries = [
                "What is artificial intelligence?",
                "Explain machine learning briefly.",
                "What is 2 + 2?",
                "How does symbolic reasoning work?",
                "What is the difference between AI and ML?"
            ]
            
            results = []
            total_start = time.time()
            
            for i, query in enumerate(test_queries, 1):
                print(f"📝 Query {i}/{len(test_queries)}: {query[:50]}...")
                
                start_time = time.time()
                
                response = requests.post(
                    "http://localhost:8080/v1/chat/completions",
                    json={
                        "model": "gemma3n:e2b",
                        "messages": [{"role": "user", "content": query}]
                    },
                    timeout=60
                )
                
                duration = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                    print(f"✅ Response {i} ({duration:.2f}s): {response_text[:80]}...")
                    
                    results.append({
                        "query": query,
                        "duration": duration,
                        "status": "success",
                        "response_length": len(response_text)
                    })
                else:
                    print(f"❌ Query {i} failed: {response.status_code}")
                    results.append({
                        "query": query,
                        "duration": duration,
                        "status": "failed",
                        "status_code": response.status_code
                    })
            
            total_duration = time.time() - total_start
            
            # Calculate statistics
            successful = [r for r in results if r["status"] == "success"]
            if successful:
                avg_duration = sum(r["duration"] for r in successful) / len(successful)
                min_duration = min(r["duration"] for r in successful)
                max_duration = max(r["duration"] for r in successful)
                
                print(f"\n📊 Load Test Results:")
                print(f"   Total queries: {len(test_queries)}")
                print(f"   Successful: {len(successful)}")
                print(f"   Success rate: {len(successful)/len(test_queries)*100:.1f}%")
                print(f"   Average response time: {avg_duration:.2f}s")
                print(f"   Min response time: {min_duration:.2f}s")
                print(f"   Max response time: {max_duration:.2f}s")
                print(f"   Total test duration: {total_duration:.2f}s")
            
            return {
                "status": "success",
                "total_duration": total_duration,
                "results": results,
                "statistics": {
                    "total_queries": len(test_queries),
                    "successful": len(successful),
                    "success_rate": len(successful)/len(test_queries)*100,
                    "avg_duration": avg_duration if successful else 0,
                    "min_duration": min_duration if successful else 0,
                    "max_duration": max_duration if successful else 0
                }
            }
            
        except Exception as e:
            print(f"💥 Load test crashed: {e}")
            return {"status": "error", "error": str(e)}
    
    def run_all_benchmarks(self):
        """Run all available benchmarks."""
        print("\n🎯 Starting Comprehensive Benchmark Suite")
        print("=" * 70)
        
        # Start API server
        if not self.start_api_server():
            print("❌ Failed to start API server. Aborting benchmarks.")
            return False
        
        try:
            # Define benchmarks to run
            benchmarks = [
                ("benchmark.py", "Main API Benchmark"),
                ("quick_benchmark.py", "Quick Performance Benchmark"),
                ("test_api.py", "API Functionality Test"),
                ("test_symbolic.py", "Symbolic Reasoning Test"),
                ("test_retrieval.py", "Retrieval System Test"),
            ]
            
            # Run script-based benchmarks
            for script, description in benchmarks:
                result = self.run_benchmark_script(script, description)
                self.results["benchmarks"][script] = result
            
            # Run performance unit tests
            perf_result = self.run_performance_tests()
            self.results["benchmarks"]["performance_tests"] = perf_result
            
            # Run API load test
            load_result = self.run_api_load_test()
            self.results["benchmarks"]["api_load_test"] = load_result
            
            return True
            
        finally:
            # Always stop the server
            self.stop_api_server()
    
    def save_results(self):
        """Save benchmark results to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ollama_benchmark_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Benchmark results saved to: {filename}")
        return filename
    
    def print_summary(self):
        """Print benchmark summary."""
        print("\n" + "=" * 70)
        print("📊 BENCHMARK SUMMARY")
        print("=" * 70)
        
        total_benchmarks = len(self.results["benchmarks"])
        successful = sum(1 for r in self.results["benchmarks"].values() 
                        if r.get("status") == "success")
        
        print(f"🎯 Model: {self.results['model']}")
        print(f"🔧 Backend: {self.results['backend']}")
        print(f"📅 Timestamp: {self.results['timestamp']}")
        print(f"📈 Success Rate: {successful}/{total_benchmarks} ({successful/total_benchmarks*100:.1f}%)")
        
        print("\n📋 Individual Results:")
        for name, result in self.results["benchmarks"].items():
            status = result.get("status", "unknown")
            duration = result.get("duration", 0)
            
            if status == "success":
                print(f"   ✅ {name}: {status} ({duration:.2f}s)")
            elif status == "failed":
                print(f"   ❌ {name}: {status}")
            elif status == "timeout":
                print(f"   ⏰ {name}: {status}")
            else:
                print(f"   ⚠️  {name}: {status}")
        
        # Special handling for load test results
        if "api_load_test" in self.results["benchmarks"]:
            load_result = self.results["benchmarks"]["api_load_test"]
            if load_result.get("status") == "success" and "statistics" in load_result:
                stats = load_result["statistics"]
                print(f"\n🚀 API Performance Highlights:")
                print(f"   Success Rate: {stats['success_rate']:.1f}%")
                print(f"   Average Response Time: {stats['avg_duration']:.2f}s")
                print(f"   Fastest Response: {stats['min_duration']:.2f}s")
                print(f"   Slowest Response: {stats['max_duration']:.2f}s")


def main():
    """Main benchmark execution."""
    runner = BenchmarkRunner()
    
    try:
        print("🔍 Checking Ollama availability...")
        
        # Check if Ollama is running
        import requests
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [m["name"] for m in models]
                if "gemma3n:e2b" in model_names:
                    print("✅ Ollama is running and gemma3n:e2b model is available")
                else:
                    print(f"❌ gemma3n:e2b model not found. Available: {model_names}")
                    return False
            else:
                print("❌ Ollama API not responding")
                return False
        except:
            print("❌ Cannot connect to Ollama. Please ensure it's running.")
            return False
        
        # Run all benchmarks
        success = runner.run_all_benchmarks()
        
        # Save and display results
        runner.save_results()
        runner.print_summary()
        
        if success:
            print("\n🎉 Benchmark suite completed successfully!")
        else:
            print("\n⚠️  Benchmark suite completed with some issues.")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️  Benchmarks interrupted by user")
        runner.stop_api_server()
        return False
    except Exception as e:
        print(f"\n💥 Benchmark suite crashed: {e}")
        runner.stop_api_server()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
