#!/usr/bin/env python3
"""
Simple benchmark for Neural Symbolic Language Model with Ollama gemma3n:e2b.
This script tests the API performance without complex dependencies.
"""

import time
import json
import asyncio
from datetime import datetime

# Set environment for Ollama testing
import os
os.environ['APP_ENVIRONMENT'] = 'test'
os.environ['MODEL_REASONING_ENGINE'] = 'ollama'
os.environ['MODEL_REASONING_MODEL'] = 'gemma3n:e2b'

print("🚀 Simple Ollama Benchmark with gemma3n:e2b")
print("=" * 50)


async def test_direct_ollama():
    """Test Ollama directly without API server."""
    print("\n🔍 Testing Direct Ollama Performance...")
    
    try:
        import ollama
        
        client = ollama.AsyncClient()
        
        test_queries = [
            "What is 2 + 2?",
            "Explain AI in one sentence.",
            "What is symbolic reasoning?",
            "How does machine learning work?",
            "What is the difference between AI and ML?"
        ]
        
        results = []
        total_start = time.time()
        
        for i, query in enumerate(test_queries, 1):
            print(f"📝 Query {i}/{len(test_queries)}: {query}")
            
            # Measure response time
            start_time = time.time()
            
            response = await client.chat(
                model='gemma3n:e2b',
                messages=[
                    {'role': 'user', 'content': query}
                ]
            )
            
            duration = time.time() - start_time
            
            if response and 'message' in response:
                content = response['message']['content']
                word_count = len(content.split())
                
                print(f"✅ Response ({duration:.2f}s, {word_count} words): {content[:80]}...")
                
                results.append({
                    "query": query,
                    "duration": duration,
                    "word_count": word_count,
                    "response_length": len(content),
                    "status": "success"
                })
            else:
                print(f"❌ Query {i} failed")
                results.append({
                    "query": query,
                    "duration": duration,
                    "status": "failed"
                })
        
        total_duration = time.time() - total_start
        
        # Calculate statistics
        successful = [r for r in results if r["status"] == "success"]
        
        if successful:
            avg_duration = sum(r["duration"] for r in successful) / len(successful)
            min_duration = min(r["duration"] for r in successful)
            max_duration = max(r["duration"] for r in successful)
            avg_words = sum(r["word_count"] for r in successful) / len(successful)
            total_words = sum(r["word_count"] for r in successful)
            
            print(f"\n📊 Direct Ollama Performance Results:")
            print(f"   Total queries: {len(test_queries)}")
            print(f"   Successful: {len(successful)}")
            print(f"   Success rate: {len(successful)/len(test_queries)*100:.1f}%")
            print(f"   Average response time: {avg_duration:.2f}s")
            print(f"   Fastest response: {min_duration:.2f}s")
            print(f"   Slowest response: {max_duration:.2f}s")
            print(f"   Average words per response: {avg_words:.1f}")
            print(f"   Total words generated: {total_words}")
            print(f"   Words per second: {total_words/total_duration:.1f}")
            print(f"   Total benchmark time: {total_duration:.2f}s")
            
            return {
                "status": "success",
                "total_duration": total_duration,
                "results": results,
                "statistics": {
                    "total_queries": len(test_queries),
                    "successful": len(successful),
                    "success_rate": len(successful)/len(test_queries)*100,
                    "avg_duration": avg_duration,
                    "min_duration": min_duration,
                    "max_duration": max_duration,
                    "avg_words": avg_words,
                    "total_words": total_words,
                    "words_per_second": total_words/total_duration
                }
            }
        else:
            print("❌ No successful queries")
            return {"status": "failed", "results": results}
            
    except Exception as e:
        print(f"❌ Direct Ollama test failed: {e}")
        return {"status": "error", "error": str(e)}


async def test_reasoning_performance():
    """Test reasoning-specific performance."""
    print("\n🧠 Testing Reasoning Performance...")
    
    try:
        import ollama
        
        client = ollama.AsyncClient()
        
        reasoning_queries = [
            "If A implies B and B implies C, what can we conclude?",
            "What is the logical relationship between premises and conclusions?",
            "Solve: All cats are animals. Fluffy is a cat. What follows?",
            "Compare deductive and inductive reasoning.",
            "What is a syllogism? Give an example."
        ]
        
        results = []
        
        for i, query in enumerate(reasoning_queries, 1):
            print(f"🧠 Reasoning Query {i}/{len(reasoning_queries)}: {query[:50]}...")
            
            start_time = time.time()
            
            response = await client.chat(
                model='gemma3n:e2b',
                messages=[
                    {'role': 'system', 'content': 'You are an expert in logic and reasoning. Provide clear, structured answers.'},
                    {'role': 'user', 'content': query}
                ]
            )
            
            duration = time.time() - start_time
            
            if response and 'message' in response:
                content = response['message']['content']
                
                # Check for reasoning keywords
                reasoning_keywords = ['therefore', 'thus', 'conclude', 'implies', 'follows', 'logic', 'premise']
                keyword_count = sum(1 for keyword in reasoning_keywords if keyword.lower() in content.lower())
                
                print(f"✅ Reasoning Response ({duration:.2f}s, {keyword_count} reasoning terms)")
                print(f"   Preview: {content[:100]}...")
                
                results.append({
                    "query": query,
                    "duration": duration,
                    "reasoning_keywords": keyword_count,
                    "response_length": len(content),
                    "status": "success"
                })
            else:
                print(f"❌ Reasoning query {i} failed")
                results.append({
                    "query": query,
                    "duration": duration,
                    "status": "failed"
                })
        
        # Calculate reasoning-specific statistics
        successful = [r for r in results if r["status"] == "success"]
        
        if successful:
            avg_duration = sum(r["duration"] for r in successful) / len(successful)
            avg_keywords = sum(r["reasoning_keywords"] for r in successful) / len(successful)
            
            print(f"\n🎯 Reasoning Performance Results:")
            print(f"   Reasoning queries: {len(reasoning_queries)}")
            print(f"   Successful: {len(successful)}")
            print(f"   Average response time: {avg_duration:.2f}s")
            print(f"   Average reasoning terms per response: {avg_keywords:.1f}")
            
            return {
                "status": "success",
                "results": results,
                "statistics": {
                    "avg_duration": avg_duration,
                    "avg_reasoning_keywords": avg_keywords,
                    "success_rate": len(successful)/len(reasoning_queries)*100
                }
            }
        else:
            return {"status": "failed", "results": results}
            
    except Exception as e:
        print(f"❌ Reasoning performance test failed: {e}")
        return {"status": "error", "error": str(e)}


async def test_concurrent_performance():
    """Test concurrent query performance."""
    print("\n⚡ Testing Concurrent Performance...")
    
    try:
        import ollama
        
        client = ollama.AsyncClient()
        
        concurrent_queries = [
            "What is AI?",
            "What is ML?", 
            "What is 5 * 7?",
            "Define logic.",
            "Explain reasoning."
        ]
        
        print(f"🔄 Running {len(concurrent_queries)} queries concurrently...")
        
        start_time = time.time()
        
        # Run all queries concurrently
        tasks = []
        for query in concurrent_queries:
            task = client.chat(
                model='gemma3n:e2b',
                messages=[{'role': 'user', 'content': query}]
            )
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_duration = time.time() - start_time
        
        # Process results
        successful = 0
        total_words = 0
        
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                print(f"❌ Concurrent query {i+1} failed: {response}")
            elif response and 'message' in response:
                content = response['message']['content']
                words = len(content.split())
                total_words += words
                successful += 1
                print(f"✅ Concurrent query {i+1}: {words} words")
        
        print(f"\n⚡ Concurrent Performance Results:")
        print(f"   Total queries: {len(concurrent_queries)}")
        print(f"   Successful: {successful}")
        print(f"   Success rate: {successful/len(concurrent_queries)*100:.1f}%")
        print(f"   Total time: {total_duration:.2f}s")
        print(f"   Average time per query: {total_duration/len(concurrent_queries):.2f}s")
        print(f"   Total words generated: {total_words}")
        print(f"   Concurrent throughput: {total_words/total_duration:.1f} words/sec")
        
        return {
            "status": "success",
            "statistics": {
                "total_queries": len(concurrent_queries),
                "successful": successful,
                "success_rate": successful/len(concurrent_queries)*100,
                "total_duration": total_duration,
                "avg_time_per_query": total_duration/len(concurrent_queries),
                "total_words": total_words,
                "concurrent_throughput": total_words/total_duration
            }
        }
        
    except Exception as e:
        print(f"❌ Concurrent performance test failed: {e}")
        return {"status": "error", "error": str(e)}


async def main():
    """Run all benchmark tests."""
    print("🎯 Starting Ollama gemma3n:e2b Benchmark Suite")
    
    # Check Ollama availability
    try:
        import ollama
        print("✅ Ollama module available")
    except ImportError:
        print("❌ Ollama module not available")
        return False
    
    # Run benchmark tests
    benchmark_results = {
        "timestamp": datetime.now().isoformat(),
        "model": "gemma3n:e2b",
        "backend": "ollama",
        "tests": {}
    }
    
    tests = [
        ("direct_ollama", "Direct Ollama Performance", test_direct_ollama),
        ("reasoning", "Reasoning Performance", test_reasoning_performance),
        ("concurrent", "Concurrent Performance", test_concurrent_performance)
    ]
    
    for test_key, test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = await test_func()
        benchmark_results["tests"][test_key] = result
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"ollama_benchmark_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(benchmark_results, f, indent=2)
    
    print(f"\n{'='*60}")
    print("📊 BENCHMARK SUMMARY")
    print("="*60)
    
    successful_tests = sum(1 for test in benchmark_results["tests"].values() 
                          if test.get("status") == "success")
    total_tests = len(benchmark_results["tests"])
    
    print(f"🎯 Model: {benchmark_results['model']}")
    print(f"🔧 Backend: {benchmark_results['backend']}")
    print(f"📅 Timestamp: {benchmark_results['timestamp']}")
    print(f"📈 Success Rate: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    
    # Print key performance metrics
    if "direct_ollama" in benchmark_results["tests"]:
        direct_stats = benchmark_results["tests"]["direct_ollama"].get("statistics", {})
        if direct_stats:
            print(f"\n🚀 Key Performance Metrics:")
            print(f"   Average Response Time: {direct_stats.get('avg_duration', 0):.2f}s")
            print(f"   Words per Second: {direct_stats.get('words_per_second', 0):.1f}")
            print(f"   Success Rate: {direct_stats.get('success_rate', 0):.1f}%")
    
    print(f"\n💾 Results saved to: {filename}")
    
    if successful_tests == total_tests:
        print("🎉 All benchmarks completed successfully!")
        return True
    else:
        print("⚠️  Some benchmarks failed.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Benchmark interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n💥 Benchmark crashed: {e}")
        exit(1)
