"""
Configuration management for the Neural Symbolic Language Model.

This module provides centralized configuration management using Pydantic BaseSettings
with environment variable support and validation.

Author: AI Assistant
Date: 2025-06-29
"""

import os
from typing import List, Optional, Dict, Any
from pathlib import Path

from pydantic import BaseModel, Field, field_validator, ConfigDict
from pydantic.types import PositiveInt, NonNegativeFloat
from typing import Annotated


class SecuritySettings(BaseModel):
    """Security-related configuration settings.

    This class defines security-related configuration parameters with
    comprehensive validation and environment variable support.

    Attributes:
        api_keys: Dictionary of API keys for authentication
        rate_limit_requests: Maximum requests per minute per IP
        rate_limit_window: Rate limit window in seconds
        max_request_size: Maximum request size in bytes
        cors_origins: List of allowed CORS origins
        block_duration: IP block duration in seconds after failed attempts
        max_failed_attempts: Maximum failed attempts before blocking IP
    """

    model_config = ConfigDict(
        env_prefix="SECURITY_",
        case_sensitive=False,
        validate_assignment=True,
        extra='forbid',
        str_strip_whitespace=True
    )

    api_keys: Dict[str, str] = Field(
        default_factory=dict,
        description="API keys for authentication"
    )
    rate_limit_requests: PositiveInt = Field(
        default=100,
        description="Maximum requests per minute per IP"
    )
    rate_limit_window: PositiveInt = Field(
        default=60,
        description="Rate limit window in seconds"
    )
    max_request_size: PositiveInt = Field(
        default=10 * 1024 * 1024,  # 10MB
        description="Maximum request size in bytes"
    )
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="Allowed CORS origins"
    )
    block_duration: PositiveInt = Field(
        default=3600,  # 1 hour
        description="IP block duration in seconds"
    )
    max_failed_attempts: PositiveInt = Field(
        default=5,
        description="Maximum failed attempts before blocking IP"
    )


class ModelSettings(BaseModel):
    """Model and AI-related configuration settings.

    This class defines model and AI-related configuration parameters with
    comprehensive validation and environment variable support.

    Attributes:
        reasoning_engine: Symbolic reasoning engine to use
        reasoning_model: Model for symbolic reasoning
        embedding_model: Embedding model for vector search
        embedding_dimension: Embedding vector dimension
        use_gpu: Whether to use GPU acceleration
        gpu_memory_fraction: Fraction of GPU memory to use
        ollama_host: Ollama server host URL
        ollama_timeout: Ollama request timeout in seconds
        vector_db_backend: Vector database backend
    """

    model_config = ConfigDict(
        env_prefix="MODEL_",
        case_sensitive=False,
        validate_assignment=True,
        extra='forbid',
        str_strip_whitespace=True
    )

    reasoning_engine: str = Field(
        default="local",
        description="Symbolic reasoning engine to use"
    )
    reasoning_model: str = Field(
        default="llama",
        description="Model for symbolic reasoning"
    )
    embedding_model: str = Field(
        default="BAAI/bge-small-en-v1.5",
        description="Embedding model for vector search"
    )
    embedding_dimension: PositiveInt = Field(
        default=768,
        description="Embedding vector dimension"
    )
    use_gpu: bool = Field(
        default=True,
        description="Whether to use GPU acceleration"
    )
    gpu_memory_fraction: NonNegativeFloat = Field(
        default=0.8,
        ge=0.1,
        le=1.0,
        description="Fraction of GPU memory to use"
    )
    ollama_host: str = Field(
        default="http://localhost:11434",
        description="Ollama server host URL"
    )
    ollama_timeout: PositiveInt = Field(
        default=300,
        description="Ollama request timeout in seconds"
    )
    vector_db_backend: str = Field(
        default="faiss",
        description="Vector database backend (faiss, torch)"
    )

    @field_validator('reasoning_engine')
    @classmethod
    def validate_reasoning_engine(cls, v: str) -> str:
        """Validate reasoning engine choice.

        Args:
            v: The reasoning engine name to validate

        Returns:
            The validated reasoning engine name

        Raises:
            ValueError: If reasoning engine is not supported
        """
        allowed_engines = ["local", "openai", "anthropic", "ollama"]
        if v not in allowed_engines:
            raise ValueError(
                f"Reasoning engine '{v}' not supported. "
                f"Must be one of: {', '.join(allowed_engines)}"
            )
        return v

    @field_validator('vector_db_backend')
    @classmethod
    def validate_vector_db_backend(cls, v: str) -> str:
        """Validate vector database backend choice.

        Args:
            v: The vector database backend name to validate

        Returns:
            The validated backend name

        Raises:
            ValueError: If backend is not supported
        """
        allowed_backends = ["faiss", "torch", "chromadb"]
        if v not in allowed_backends:
            raise ValueError(
                f"Vector database backend '{v}' not supported. "
                f"Must be one of: {', '.join(allowed_backends)}"
            )
        return v


class CacheSettings(BaseModel):
    """Cache configuration settings.

    This class defines cache-related configuration parameters with
    comprehensive validation and environment variable support.

    Attributes:
        enabled: Whether caching is enabled
        ttl: Time-to-live for cache entries in seconds
        max_size: Maximum number of entries in cache
        backend: Cache backend to use
    """

    model_config = ConfigDict(
        env_prefix="CACHE_",
        case_sensitive=False,
        validate_assignment=True,
        extra='forbid',
        str_strip_whitespace=True
    )

    max_size: PositiveInt = Field(
        default=1000,
        description="Maximum number of cached responses"
    )
    ttl_seconds: PositiveInt = Field(
        default=3600,  # 1 hour
        description="Cache TTL in seconds"
    )
    cleanup_interval: PositiveInt = Field(
        default=300,  # 5 minutes
        description="Cache cleanup interval in seconds"
    )
    redis_url: Optional[str] = Field(
        default=None,
        description="Redis URL for distributed caching"
    )


class LoggingSettings(BaseModel):
    """Logging configuration settings.

    This class defines logging-related configuration parameters with
    comprehensive validation and environment variable support.

    Attributes:
        level: Logging level
        format: Log format string
        file_enabled: Whether to enable file logging
        file_path: Log file directory
        max_file_size: Maximum log file size in bytes
        backup_count: Number of backup log files to keep
        structured: Whether to use structured JSON logging
    """

    model_config = ConfigDict(
        env_prefix="LOG_",
        case_sensitive=False,
        validate_assignment=True,
        extra='forbid',
        str_strip_whitespace=True
    )

    level: str = Field(
        default="INFO",
        description="Logging level"
    )
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )
    file_enabled: bool = Field(
        default=True,
        description="Whether to enable file logging"
    )
    file_path: str = Field(
        default="logs",
        description="Log file directory"
    )
    max_file_size: PositiveInt = Field(
        default=10 * 1024 * 1024,  # 10MB
        description="Maximum log file size in bytes"
    )
    backup_count: PositiveInt = Field(
        default=5,
        description="Number of backup log files to keep"
    )
    structured_logging: bool = Field(
        default=False,
        description="Whether to use structured JSON logging"
    )

    @field_validator('level')
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level.

        Args:
            v: The log level to validate

        Returns:
            The validated log level in uppercase

        Raises:
            ValueError: If log level is not supported
        """
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        v_upper = v.upper()
        if v_upper not in allowed_levels:
            raise ValueError(
                f"Log level '{v}' not supported. "
                f"Must be one of: {', '.join(allowed_levels)}"
            )
        return v_upper


class AppSettings(BaseModel):
    """Main application configuration settings.

    This class defines the main application configuration parameters with
    comprehensive validation and environment variable support.

    Attributes:
        title: Application title
        version: Application version
        debug: Whether debug mode is enabled
        environment: Deployment environment
        host: Host address to bind to
        port: Port number to bind to
        workers: Number of worker processes
        reload: Whether to enable auto-reload
        security: Security configuration
        model: Model configuration
        cache: Cache configuration
        logging: Logging configuration
    """

    model_config = ConfigDict(
        env_prefix="APP_",
        case_sensitive=False,
        validate_assignment=True,
        extra='forbid',
        str_strip_whitespace=True
    )

    # Basic app settings
    title: str = Field(
        default="Neural Symbolic Language Model API",
        description="Application title"
    )
    version: str = Field(
        default="0.1.0",
        description="Application version"
    )
    debug: bool = Field(
        default=False,
        description="Debug mode"
    )
    environment: str = Field(
        default="development",
        description="Environment (development, staging, production)"
    )

    # Server settings
    host: str = Field(
        default="127.0.0.1",
        description="Server host"
    )
    port: PositiveInt = Field(
        default=8080,
        description="Server port"
    )
    workers: PositiveInt = Field(
        default=1,
        description="Number of worker processes"
    )

    # Data directories
    data_dir: str = Field(
        default="data",
        description="Data directory path"
    )
    static_dir: str = Field(
        default="src/static",
        description="Static files directory"
    )

    # Component settings
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    model: ModelSettings = Field(default_factory=ModelSettings)
    cache: CacheSettings = Field(default_factory=CacheSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)

    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment setting.

        Args:
            v: The environment name to validate

        Returns:
            The validated environment name

        Raises:
            ValueError: If environment is not supported
        """
        allowed_envs = ["development", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(
                f"Environment '{v}' not supported. "
                f"Must be one of: {', '.join(allowed_envs)}"
            )
        return v

    def get_data_path(self) -> Path:
        """Get the data directory path as a Path object."""
        return Path(self.data_dir)

    def get_static_path(self) -> Path:
        """Get the static directory path as a Path object."""
        return Path(self.static_dir)

    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == "production"

    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == "development"


# Global settings instance
_settings: Optional[AppSettings] = None


def get_settings() -> AppSettings:
    """Get the global settings instance.

    This function implements a singleton pattern to ensure that settings
    are loaded only once and reused throughout the application lifecycle.
    It uses the ConfigurationLoader for environment-specific configuration.

    Returns:
        AppSettings: The global application settings instance

    Example:
        >>> settings = get_settings()
        >>> print(f"Running on {settings.host}:{settings.port}")
        Running on 0.0.0.0:8080
    """
    global _settings
    if _settings is None:
        try:
            from .config_loader import load_settings
            _settings = load_settings()
        except ImportError:
            # Fallback to basic settings if config_loader is not available
            _settings = AppSettings()
    return _settings


def reload_settings() -> AppSettings:
    """Reload settings from configuration sources.

    This function forces a reload of the configuration, useful for
    testing or when configuration changes need to be picked up.

    Returns:
        AppSettings: The reloaded application settings instance
    """
    global _settings
    _settings = None
    return get_settings()


def create_env_template(output_path: str = ".env.template") -> None:
    """Create a template .env file with all available settings."""
    template_content = """# Neural Symbolic Language Model Configuration
# Copy this file to .env and customize the values

# Application Settings
APP_TITLE=Neural Symbolic Language Model API
APP_VERSION=0.1.0
APP_DEBUG=false
APP_ENVIRONMENT=development
APP_HOST=0.0.0.0
APP_PORT=8000
APP_WORKERS=1
APP_RELOAD=true

# Model Configuration
MODEL_REASONING_ENGINE=local
MODEL_REASONING_MODEL=llama
MODEL_EMBEDDING_MODEL=BAAI/bge-small-en-v1.5
MODEL_EMBEDDING_DIMENSION=768
MODEL_USE_GPU=true
MODEL_GPU_MEMORY_FRACTION=0.8
MODEL_VECTOR_DB_BACKEND=faiss

# Security Configuration
SECURITY_API_KEYS={}
SECURITY_RATE_LIMIT_REQUESTS=100
SECURITY_RATE_LIMIT_WINDOW=60
SECURITY_MAX_REQUEST_SIZE=10485760
SECURITY_CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
SECURITY_BLOCK_DURATION=3600
SECURITY_MAX_FAILED_ATTEMPTS=5

# Cache Configuration
CACHE_MAX_SIZE=1000
CACHE_TTL_SECONDS=3600
CACHE_CLEANUP_INTERVAL=300
CACHE_REDIS_URL=

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs
LOG_MAX_FILE_SIZE=10485760
LOG_BACKUP_COUNT=5
LOG_STRUCTURED_LOGGING=false
"""

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(template_content)

    print(f"Environment template created: {output_path}")


def validate_configuration(settings: AppSettings) -> List[str]:
    """Validate configuration and return list of warnings/errors."""
    warnings = []

    # Production-specific validations
    if settings.environment == 'production':
        if settings.debug:
            warnings.append("Debug mode should be disabled in production")

        if settings.reload:
            warnings.append("Auto-reload should be disabled in production")

        if not settings.security.api_keys:
            warnings.append("API keys should be configured in production")

        if "*" in settings.security.cors_origins:
            warnings.append("CORS should not allow all origins in production")

    # GPU validations
    if settings.model.use_gpu:
        try:
            import torch
            if not torch.cuda.is_available():
                warnings.append("GPU requested but CUDA not available")
        except ImportError:
            warnings.append("GPU requested but PyTorch not installed")

    # Cache validations
    if settings.cache.redis_url and not settings.cache.redis_url.startswith(('redis://', 'rediss://')):
        warnings.append("Redis URL should start with redis:// or rediss://")

    return warnings


def load_config_from_file(config_path: str) -> Dict[str, Any]:
    """Load configuration from a file (JSON or YAML)."""
    import json

    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    with open(config_path, 'r', encoding='utf-8') as f:
        if config_path.endswith('.json'):
            return json.load(f)
        elif config_path.endswith(('.yml', '.yaml')):
            try:
                import yaml
                return yaml.safe_load(f)
            except ImportError:
                raise ImportError("PyYAML is required to load YAML configuration files")
        else:
            raise ValueError("Configuration file must be JSON or YAML")


def get_uvicorn_config(settings: AppSettings) -> Dict[str, Any]:
    """Get Uvicorn server configuration."""
    return {
        "host": settings.host,
        "port": settings.port,
        "workers": settings.workers if not settings.debug else 1,
        "reload": settings.reload and settings.environment != 'production',
        "log_level": settings.logging.level.lower(),
        "access_log": True,
    }


def get_cors_config(settings: AppSettings) -> Dict[str, Any]:
    """Get CORS configuration."""
    return {
        "allow_origins": settings.security.cors_origins,
        "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["*"],
        "allow_credentials": True,
    }