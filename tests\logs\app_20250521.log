2025-05-21 21:11:34,239 - root - INFO - Logging system initialized
2025-05-21 21:11:34,277 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\tests\logs
2025-05-21 21:11:34,341 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:11:34,412 - main - INFO - Initializing components...
2025-05-21 21:11:34,436 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 21:11:34,485 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 21:11:34,527 - main - INFO - Components initialized successfully
2025-05-21 21:11:34,590 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-21 21:11:34,663 - main - WARNING - No documents found in data directory
2025-05-21 21:11:35,073 - src.retrieval - WARNING - FAISS not available. Using fallback vector storage.
2025-05-21 21:11:35,322 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 422 Unprocessable Entity"
2025-05-21 21:11:35,347 - main - INFO - Received chat request for model: local
2025-05-21 21:11:35,366 - main - WARNING - No user message found in request
2025-05-21 21:11:35,405 - monitoring - WARNING - Request chat-bd86898b-69b8-4574-9578-3f005318739a failed after 0.06s: No user message found
2025-05-21 21:11:35,490 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-21 21:11:35,568 - main - INFO - Received chat request for model: local
2025-05-21 21:11:35,639 - main - WARNING - No user message found in request
2025-05-21 21:11:35,684 - monitoring - WARNING - Request chat-ca604bcb-e95e-4ff5-a789-83b1d37e9ac7 failed after 0.12s: No user message found
2025-05-21 21:11:35,877 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-21 21:11:35,953 - main - INFO - Received chat request for model: local
2025-05-21 21:11:36,016 - main - INFO - Performing retrieval operation
2025-05-21 21:11:36,056 - main - INFO - Retrieved relevant context
2025-05-21 21:11:36,124 - main - INFO - Processing with symbolic reasoning
2025-05-21 21:11:36,164 - main - INFO - Symbolic reasoning completed
2025-05-21 21:11:36,210 - main - INFO - Successfully generated response
2025-05-21 21:11:36,255 - monitoring - INFO - Request chat-35104279-0e4b-4be2-861a-322fa4ff49bc completed in 0.30s
2025-05-21 21:11:36,313 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-21 21:11:36,396 - main - INFO - Received chat request for model: local
2025-05-21 21:11:36,457 - main - INFO - Found response in cache
2025-05-21 21:11:36,518 - monitoring - INFO - Request chat-fde958f6-642a-4879-a4e0-d2f4e1aab3db completed in 0.12s
2025-05-21 21:11:36,573 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-21 21:11:36,650 - main - INFO - Retrieving performance statistics
2025-05-21 21:11:36,722 - main - INFO - Successfully retrieved performance statistics
2025-05-21 21:11:36,784 - httpx - INFO - HTTP Request: GET http://testserver/performance "HTTP/1.1 200 OK"
2025-05-21 21:11:36,854 - main - INFO - Received chat request for model: local
2025-05-21 21:11:36,894 - main - INFO - Request requires streaming, forwarding to streaming endpoint
2025-05-21 21:11:36,937 - monitoring - INFO - Request chat-cf79556e-e2f8-47e7-b0e8-aee61d841d1e completed in 0.08s
2025-05-21 21:11:36,976 - main - INFO - Received streaming chat request for model: local
2025-05-21 21:11:37,020 - main - INFO - Found response in cache for streaming request
2025-05-21 21:11:37,060 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-21 21:11:37,122 - main - INFO - Retrieving system configuration information
2025-05-21 21:11:37,161 - main - INFO - Successfully retrieved system configuration
2025-05-21 21:11:37,190 - httpx - INFO - HTTP Request: GET http://testserver/system/info "HTTP/1.1 200 OK"
2025-05-21 21:11:37,227 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:11:37,282 - monitoring - INFO - Request test-456 completed in 0.00s
2025-05-21 21:11:38,231 - monitoring - INFO - Performance monitoring shutdown
2025-05-21 21:11:38,248 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:11:38,270 - monitoring - WARNING - Request test-789 failed after 0.00s: Test error
2025-05-21 21:11:38,291 - monitoring - INFO - Request test-790 completed in 0.00s
2025-05-21 21:11:39,257 - monitoring - INFO - Performance monitoring shutdown
2025-05-21 21:11:39,289 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:11:39,310 - monitoring - INFO - Request recent completed in 0.00s
2025-05-21 21:11:40,295 - monitoring - INFO - Performance monitoring shutdown
2025-05-21 21:11:40,324 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:11:40,347 - monitoring - INFO - Request test-123 completed in 0.00s
2025-05-21 21:11:41,327 - monitoring - INFO - Performance monitoring shutdown
2025-05-21 21:11:41,352 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:11:43,375 - monitoring - INFO - Performance monitoring shutdown
2025-05-21 21:11:43,392 - src.retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 21:11:43,417 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 21:11:43,435 - src.retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 21:11:43,456 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 21:11:43,477 - src.retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 21:11:43,500 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 21:11:43,523 - src.retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 21:11:43,542 - vector_store - INFO - TorchVectorStore using CPU
