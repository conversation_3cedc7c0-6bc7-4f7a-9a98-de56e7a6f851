[tool:pytest]
# Pytest configuration for Neural Symbolic Language Model

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output options
addopts =
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --durations=10
    --maxfail=5

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests (may take more than 1 second)
    gpu: Tests requiring GPU
    network: Tests requiring network access
    security: Security-related tests
    performance: Performance tests

# Minimum version
minversion = 7.0

# Ignore warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:torch.*
    ignore::UserWarning:faiss.*

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# addopts = -n auto  # Uncomment to enable parallel execution with pytest-xdist