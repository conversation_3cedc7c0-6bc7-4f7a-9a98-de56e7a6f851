"""
Setup script to properly configure FAISS-GPU for the Symbolic Language Model.
This ensures the correct version of FAISS-GPU is installed to match your CUDA version.
"""

import os
import subprocess
import sys
import torch
import platform

def run_command(cmd):
    """Run a command and return its output."""
    print(f"Running: {cmd}")
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        shell=True
    )
    stdout, stderr = process.communicate()
    if process.returncode != 0:
        print(f"Error executing command: {cmd}")
        print(f"Error: {stderr.decode('utf-8')}")
        return False, stderr.decode('utf-8')
    return True, stdout.decode('utf-8')

def get_cuda_version():
    """Get the CUDA version from PyTorch."""
    if torch.cuda.is_available():
        cuda_version = torch.version.cuda
        print(f"CUDA version detected: {cuda_version}")
        return cuda_version
    else:
        print("CUDA not available")
        return None

def get_python_version():
    """Get the Python version."""
    major, minor, _ = platform.python_version_tuple()
    return f"{major}{minor}"

def setup_faiss_gpu():
    """Install the correct version of FAISS-GPU."""
    # Check CUDA availability
    if not torch.cuda.is_available():
        print("CUDA is not available. Please install CUDA first.")
        return False
    
    # Get CUDA version
    cuda_version = get_cuda_version()
    if not cuda_version:
        return False
    
    # Get Python version
    python_version = get_python_version()
    
    # Parse CUDA version
    major, minor = cuda_version.split('.')[:2]
    cuda_short = f"{major}{minor}"
    
    # Clean existing FAISS installations
    print("Removing any existing FAISS installations...")
    run_command("pip uninstall -y faiss-cpu")
    run_command("pip uninstall -y faiss-gpu")
    
    # Check if we have CUDA 11.x or 12.x
    if int(major) == 11:
        # For CUDA 11.x
        if int(minor) >= 0:
            faiss_version = f"faiss-gpu==1.7.3+cu{cuda_short}"
    elif int(major) == 12:
        # For CUDA 12.x
        faiss_version = f"faiss-gpu"  # Use the latest for CUDA 12.x
    else:
        print(f"Unsupported CUDA version: {cuda_version}")
        print("Trying generic faiss-gpu installation...")
        faiss_version = "faiss-gpu"
    
    # Install FAISS-GPU
    print(f"Installing {faiss_version}...")
    success, output = run_command(f"pip install {faiss_version}")
    
    if success:
        print("FAISS-GPU successfully installed!")
        
        # Verify installation
        verify_faiss()
        return True
    else:
        print("Failed to install FAISS-GPU.")
        print("Trying alternative installation method...")
        
        # Try conda installation if pip fails
        conda_cmd = f"conda install -c conda-forge faiss-gpu cudatoolkit={cuda_version}"
        print(f"You can try installing with conda: {conda_cmd}")
        
        # Try installing from PyPI index with specific CUDA version
        print("Trying pip with specific CUDA version...")
        alt_cmd = f"pip install faiss-gpu"
        success, output = run_command(alt_cmd)
        
        if success:
            print("FAISS-GPU installed with alternative method!")
            verify_faiss()
            return True
        else:
            print("All installation methods failed.")
            return False

def verify_faiss():
    """Verify that FAISS-GPU is properly installed and can use the GPU."""
    try:
        import faiss
        print("FAISS imported successfully!")
        
        # Check if GPU version is available
        has_gpu = hasattr(faiss, 'StandardGpuResources')
        if has_gpu:
            print("FAISS GPU support is available!")
            
            # Test GPU functionality
            try:
                res = faiss.StandardGpuResources()
                index = faiss.IndexFlatL2(128)
                gpu_index = faiss.index_cpu_to_gpu(res, 0, index)
                print("Successfully created GPU index!")
                return True
            except Exception as e:
                print(f"Error creating GPU index: {str(e)}")
                return False
        else:
            print("FAISS is installed but does not have GPU support.")
            return False
    except ImportError:
        print("Failed to import FAISS.")
        return False

if __name__ == "__main__":
    print("Setting up FAISS-GPU for the Symbolic Language Model...")
    print(f"PyTorch version: {torch.__version__}")
    print(f"Python version: {platform.python_version()}")
    
    if torch.cuda.is_available():
        print(f"GPU available: {torch.cuda.get_device_name(0)}")
    else:
        print("No GPU detected! CUDA is not available.")
        sys.exit(1)
    
    # Setup FAISS-GPU
    if setup_faiss_gpu():
        print("FAISS-GPU setup completed successfully!")
    else:
        print("FAISS-GPU setup failed.")
