"""Comprehensive security tests for the Neural Symbolic Language Model.

This test suite provides extensive security testing including authentication,
authorization, input validation, rate limiting, and security headers.
"""

import unittest
import time
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import Request

# Import the modules under test
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from security import SecurityManager, verify_api_key, rate_limiter
from exceptions import AuthenticationError, ValidationError
from main import app


class TestSecurityComprehensive(unittest.TestCase):
    """Comprehensive security test suite."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.security_manager = SecurityManager()
        self.client = TestClient(app)
    
    def test_api_key_validation_valid_key(self):
        """Test API key validation with valid key."""
        # The security manager should have a default key
        api_keys = list(self.security_manager.api_keys.values())
        if api_keys:
            valid_key = api_keys[0]
            
            # This should not raise an exception
            try:
                verify_api_key(valid_key)
            except AuthenticationError:
                self.fail("Valid API key was rejected")
    
    def test_api_key_validation_invalid_key(self):
        """Test API key validation with invalid key."""
        invalid_key = "invalid_key_12345"
        
        with self.assertRaises(AuthenticationError):
            verify_api_key(invalid_key)
    
    def test_api_key_validation_empty_key(self):
        """Test API key validation with empty key."""
        with self.assertRaises(AuthenticationError):
            verify_api_key("")
    
    def test_api_key_validation_none_key(self):
        """Test API key validation with None key."""
        with self.assertRaises(AuthenticationError):
            verify_api_key(None)
    
    def test_input_sanitization_basic(self):
        """Test basic input sanitization."""
        test_input = "Hello <script>alert('xss')</script> World"
        
        sanitized = self.security_manager.sanitize_input(test_input)
        
        self.assertNotIn("<script>", sanitized)
        self.assertNotIn("</script>", sanitized)
        self.assertIn("Hello", sanitized)
        self.assertIn("World", sanitized)
    
    def test_input_sanitization_sql_injection(self):
        """Test input sanitization against SQL injection attempts."""
        sql_injection_attempts = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM passwords --"
        ]
        
        for attempt in sql_injection_attempts:
            sanitized = self.security_manager.sanitize_input(attempt)
            self.assertNotIn("'", sanitized)
            self.assertNotIn("--", sanitized)
    
    def test_input_sanitization_xss_attempts(self):
        """Test input sanitization against XSS attempts."""
        xss_attempts = [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "<svg onload=alert('xss')>",
            "<iframe src='javascript:alert(\"xss\")'></iframe>"
        ]
        
        for attempt in xss_attempts:
            sanitized = self.security_manager.sanitize_input(attempt)
            self.assertNotIn("<script>", sanitized)
            self.assertNotIn("<img", sanitized)
            self.assertNotIn("javascript:", sanitized)
            self.assertNotIn("<svg", sanitized)
            self.assertNotIn("<iframe", sanitized)
    
    def test_input_sanitization_length_limit(self):
        """Test input sanitization respects length limits."""
        long_input = "A" * 20000  # Exceeds default 10000 limit
        
        with self.assertRaises(ValueError):
            self.security_manager.sanitize_input(long_input)
    
    def test_input_sanitization_custom_length_limit(self):
        """Test input sanitization with custom length limit."""
        test_input = "A" * 100
        
        # Should work with higher limit
        sanitized = self.security_manager.sanitize_input(test_input, max_length=200)
        self.assertEqual(len(sanitized), 100)
        
        # Should fail with lower limit
        with self.assertRaises(ValueError):
            self.security_manager.sanitize_input(test_input, max_length=50)
    
    def test_content_type_validation_allowed(self):
        """Test content type validation with allowed types."""
        allowed_types = [
            "application/json",
            "text/plain",
            "multipart/form-data",
            "application/x-www-form-urlencoded"
        ]
        
        for content_type in allowed_types:
            self.assertTrue(self.security_manager.validate_content_type(content_type))
    
    def test_content_type_validation_with_charset(self):
        """Test content type validation with charset parameter."""
        content_type = "application/json; charset=utf-8"
        
        self.assertTrue(self.security_manager.validate_content_type(content_type))
    
    def test_content_type_validation_disallowed(self):
        """Test content type validation with disallowed types."""
        disallowed_types = [
            "application/octet-stream",
            "text/html",
            "application/xml",
            "image/jpeg",
            "video/mp4"
        ]
        
        for content_type in disallowed_types:
            self.assertFalse(self.security_manager.validate_content_type(content_type))
    
    def test_request_headers_validation_normal(self):
        """Test request headers validation with normal headers."""
        normal_headers = {
            "content-type": "application/json",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
            "accept": "application/json",
            "authorization": "Bearer token123"
        }
        
        warnings = self.security_manager.check_request_headers(normal_headers)
        
        # Should have minimal or no warnings for normal headers
        self.assertIsInstance(warnings, dict)
    
    def test_request_headers_validation_suspicious(self):
        """Test request headers validation with suspicious headers."""
        suspicious_headers = {
            "x-forwarded-for": "***********",
            "x-real-ip": "********",
            "user-agent": "bot crawler spider"
        }
        
        warnings = self.security_manager.check_request_headers(suspicious_headers)
        
        self.assertGreater(len(warnings), 0)
        self.assertIn("x-forwarded-for", warnings)
        self.assertIn("user-agent", warnings)
    
    def test_request_headers_validation_missing_user_agent(self):
        """Test request headers validation with missing user agent."""
        headers_no_ua = {
            "content-type": "application/json",
            "accept": "application/json"
        }
        
        warnings = self.security_manager.check_request_headers(headers_no_ua)
        
        self.assertIn("user-agent", warnings)
    
    def test_rate_limiting_basic(self):
        """Test basic rate limiting functionality."""
        client_ip = "***********00"
        
        # Should allow initial requests
        for i in range(5):
            result = rate_limiter.is_allowed(client_ip)
            self.assertTrue(result)
    
    def test_rate_limiting_exceeded(self):
        """Test rate limiting when limit is exceeded."""
        client_ip = "*************"
        
        # Exhaust the rate limit
        for i in range(100):  # Exceed typical rate limit
            rate_limiter.is_allowed(client_ip)
        
        # Next request should be blocked
        result = rate_limiter.is_allowed(client_ip)
        self.assertFalse(result)
    
    def test_security_headers_in_response(self):
        """Test that security headers are present in API responses."""
        response = self.client.get("/api/info")
        
        # Check for essential security headers
        expected_headers = [
            "x-content-type-options",
            "x-frame-options",
            "x-xss-protection",
            "strict-transport-security",
            "content-security-policy"
        ]
        
        for header in expected_headers:
            self.assertIn(header, response.headers)
    
    def test_cors_configuration(self):
        """Test CORS configuration."""
        # Test preflight request
        response = self.client.options(
            "/v1/chat/completions",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertIn("access-control-allow-origin", response.headers)
    
    def test_request_size_limits(self):
        """Test request size limiting."""
        # Create a large payload
        large_payload = {
            "model": "local",
            "messages": [
                {"role": "user", "content": "A" * 100000}  # Very large content
            ]
        }
        
        response = self.client.post("/v1/chat/completions", json=large_payload)
        
        # Should either succeed or fail gracefully with appropriate error
        self.assertIn(response.status_code, [200, 400, 413, 422])
    
    def test_authentication_required_endpoints(self):
        """Test that protected endpoints require authentication."""
        protected_endpoints = [
            ("/v1/chat/completions", "POST"),
            ("/documents/add", "POST"),
        ]
        
        for endpoint, method in protected_endpoints:
            if method == "POST":
                response = self.client.post(endpoint, json={})
            else:
                response = self.client.get(endpoint)
            
            # Should require authentication (401) or have validation error (422)
            self.assertIn(response.status_code, [401, 422])
    
    def test_sql_injection_protection_in_api(self):
        """Test SQL injection protection in API endpoints."""
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--"
        ]
        
        for payload in sql_injection_payloads:
            response = self.client.post(
                "/documents/add",
                json={"content": payload},
                headers={"Authorization": "Bearer test_key"}
            )
            
            # Should not cause server error (500)
            self.assertNotEqual(response.status_code, 500)
    
    def test_xss_protection_in_api(self):
        """Test XSS protection in API endpoints."""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')"
        ]
        
        for payload in xss_payloads:
            response = self.client.post(
                "/documents/add",
                json={"content": payload},
                headers={"Authorization": "Bearer test_key"}
            )
            
            # Should not cause server error and should sanitize input
            self.assertNotEqual(response.status_code, 500)
            if response.status_code == 200:
                # If successful, response should not contain script tags
                response_text = response.text
                self.assertNotIn("<script>", response_text)


if __name__ == '__main__':
    unittest.main()
