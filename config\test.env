# Test Environment Configuration for Unit Testing
# This file contains configuration settings for running unit tests with Ollama

# Application Settings
APP_TITLE="Neural Symbolic Language Model (Test)"
APP_VERSION="0.1.0-test"
APP_DEBUG=true
APP_ENVIRONMENT="test"
APP_HOST="127.0.0.1"
APP_PORT=8081
APP_WORKERS=1
APP_RELOAD=false

# Security Settings
SECURITY_API_KEYS_JSON='{"test_key": {"key": "test_api_key_12345", "permissions": ["read", "write"], "rate_limit": 10000, "created_at": "2025-06-29T00:00:00"}}'
SECURITY_RATE_LIMIT_REQUESTS=10000
SECURITY_RATE_LIMIT_WINDOW=60
SECURITY_MAX_REQUEST_SIZE=10485760
SECURITY_CORS_ORIGINS='["http://localhost:3000", "http://localhost:8081", "http://127.0.0.1:3000"]'
SECURITY_BLOCK_DURATION=60
SECURITY_MAX_FAILED_ATTEMPTS=100

# Model Settings for Testing with Ollama
MODEL_REASONING_ENGINE="ollama"
MODEL_REASONING_MODEL="gemma3n:e2b"
MODEL_EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
MODEL_EMBEDDING_DIMENSION=384
MODEL_USE_GPU=false
MODEL_GPU_MEMORY_FRACTION=0.3
MODEL_OLLAMA_HOST="http://localhost:11434"
MODEL_OLLAMA_TIMEOUT=60
MODEL_VECTOR_DB_BACKEND="torch"

# Cache Settings
CACHE_ENABLED=true
CACHE_TTL=300
CACHE_MAX_SIZE=100
CACHE_BACKEND="memory"

# Logging Settings
LOG_LEVEL="DEBUG"
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE_ENABLED=true
LOG_FILE_PATH="tests/logs"
LOG_MAX_FILE_SIZE=5242880
LOG_BACKUP_COUNT=3
LOG_STRUCTURED=false

# Test-specific settings
TEST_ENABLE_OLLAMA=true
TEST_MOCK_EXTERNAL_APIS=false
TEST_TIMEOUT=120
TEST_VERBOSE=true
