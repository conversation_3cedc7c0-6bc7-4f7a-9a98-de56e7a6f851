2025-05-24 08:17:02,034 - root - INFO - Logging system initialized
2025-05-24 08:17:02,068 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-24 08:17:02,145 - monitoring - INFO - Performance monitoring initialized
2025-05-24 08:17:02,208 - __main__ - INFO - Initializing components...
2025-05-24 08:17:02,245 - __main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-24 08:17:02,284 - __main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-24 08:17:02,322 - retrieval - INFO - Using CPU FAISS
2025-05-24 08:17:02,359 - __main__ - INFO - Components initialized successfully
2025-05-24 08:17:02,426 - __main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-24 08:17:02,525 - __main__ - WARNING - No documents found in data directory
2025-05-24 08:42:34,617 - root - INFO - Logging system initialized
2025-05-24 08:42:34,663 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-24 08:42:34,690 - monitoring - INFO - Performance monitoring initialized
2025-05-24 08:42:34,726 - main - INFO - Initializing components...
2025-05-24 08:42:34,731 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-24 08:42:34,737 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-24 08:42:34,745 - main - INFO - Components initialized successfully
2025-05-24 08:42:34,751 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-24 08:42:34,777 - main - WARNING - No documents found in data directory
2025-05-24 08:42:35,090 - src.retrieval - INFO - Imported FAISS successfully
2025-05-24 08:42:35,095 - src.retrieval - INFO - Using CPU version of FAISS
2025-05-24 08:42:35,545 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 422 Unprocessable Entity"
2025-05-24 08:42:35,555 - main - INFO - Received chat request for model: local
2025-05-24 08:42:35,564 - main - WARNING - No user message found in request
2025-05-24 08:42:35,570 - monitoring - WARNING - Request chat-a71776b5-f66f-4492-8957-0318671c9ef6 failed after 0.01s: No user message found
2025-05-24 08:42:35,576 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-24 08:42:35,586 - main - INFO - Received chat request for model: local
2025-05-24 08:42:35,594 - main - WARNING - No user message found in request
2025-05-24 08:42:35,601 - monitoring - WARNING - Request chat-c60e5158-8a3c-4e8b-8a3a-e7d1a60a5d72 failed after 0.01s: No user message found
2025-05-24 08:42:35,613 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-24 08:42:35,624 - main - INFO - Received chat request for model: local
2025-05-24 08:42:35,629 - main - INFO - Performing retrieval operation
2025-05-24 08:42:35,637 - main - INFO - Retrieved relevant context
2025-05-24 08:42:35,643 - main - INFO - Processing with symbolic reasoning
2025-05-24 08:42:35,651 - main - INFO - Symbolic reasoning completed
2025-05-24 08:42:35,658 - main - INFO - Successfully generated response
2025-05-24 08:42:35,665 - monitoring - INFO - Request chat-7f4890d8-fd70-488b-b609-2cbe8cf36c66 completed in 0.04s
2025-05-24 08:42:35,674 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-24 08:42:35,681 - main - INFO - Received chat request for model: local
2025-05-24 08:42:35,689 - main - INFO - Found response in cache
2025-05-24 08:42:35,696 - monitoring - INFO - Request chat-4906b34a-0180-4000-a908-423bdb8c40f1 completed in 0.01s
2025-05-24 08:42:35,703 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-24 08:42:35,720 - main - INFO - Retrieving performance statistics
2025-05-24 08:42:35,728 - main - INFO - Successfully retrieved performance statistics
2025-05-24 08:42:35,736 - httpx - INFO - HTTP Request: GET http://testserver/performance "HTTP/1.1 200 OK"
2025-05-24 08:42:35,747 - main - INFO - Received chat request for model: local
2025-05-24 08:42:35,757 - main - INFO - Request requires streaming, forwarding to streaming endpoint
2025-05-24 08:42:35,763 - monitoring - INFO - Request chat-23e4ec93-1a30-42c7-83d1-aa8907e7cbb3 completed in 0.02s
2025-05-24 08:42:35,768 - main - INFO - Received streaming chat request for model: local
2025-05-24 08:42:35,776 - main - INFO - Found response in cache for streaming request
2025-05-24 08:42:35,781 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-24 08:42:35,793 - main - INFO - Retrieving system configuration information
2025-05-24 08:42:35,797 - main - INFO - Successfully retrieved system configuration
2025-05-24 08:42:35,806 - httpx - INFO - HTTP Request: GET http://testserver/system/info "HTTP/1.1 200 OK"
2025-05-24 08:42:35,819 - monitoring - INFO - Performance monitoring initialized
2025-05-24 08:42:35,828 - monitoring - INFO - Request test-456 completed in 0.00s
2025-05-24 08:42:36,823 - monitoring - INFO - Performance monitoring shutdown
2025-05-24 08:42:36,890 - monitoring - INFO - Performance monitoring initialized
2025-05-24 08:42:36,929 - monitoring - WARNING - Request test-789 failed after 0.00s: Test error
2025-05-24 08:42:36,973 - monitoring - INFO - Request test-790 completed in 0.00s
2025-05-24 08:42:37,900 - monitoring - INFO - Performance monitoring shutdown
2025-05-24 08:42:37,954 - monitoring - INFO - Performance monitoring initialized
2025-05-24 08:42:37,995 - monitoring - INFO - Request recent completed in 0.00s
2025-05-24 08:42:38,964 - monitoring - INFO - Performance monitoring shutdown
2025-05-24 08:42:38,976 - monitoring - INFO - Performance monitoring initialized
2025-05-24 08:42:38,986 - monitoring - INFO - Request test-123 completed in 0.00s
2025-05-24 08:42:39,984 - monitoring - INFO - Performance monitoring shutdown
2025-05-24 08:42:39,996 - monitoring - INFO - Performance monitoring initialized
2025-05-24 08:42:42,012 - monitoring - INFO - Performance monitoring shutdown
2025-05-24 08:42:42,020 - src.retrieval - INFO - Using CPU FAISS
