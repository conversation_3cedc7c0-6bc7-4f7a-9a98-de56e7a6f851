

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vector Retrieval Module &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Security Module" href="security.html" />
    <link rel="prev" title="Symbolic Reasoning Module" href="symbolic_reasoning.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../modules.html">API Reference</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="../modules.html#core-modules">Core Modules</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="main.html">Main Application Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">Vector Retrieval Module</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#retrieval.Retriever"><code class="docutils literal notranslate"><span class="pre">Retriever</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="#retriever-class">Retriever Class</a></li>
<li class="toctree-l4"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#supported-backends">Supported Backends</a></li>
<li class="toctree-l4"><a class="reference internal" href="#embedding-models">Embedding Models</a></li>
<li class="toctree-l4"><a class="reference internal" href="#example-usage">Example Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l4"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l4"><a class="reference internal" href="#best-practices">Best Practices</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="security.html">Security Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="models.html">Data Models Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="../modules.html#module-main">Core Components</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#utility-modules">Utility Modules</a></li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#api-routes">API Routes</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Vector Retrieval Module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#retrieval.Retriever"><code class="docutils literal notranslate"><span class="pre">Retriever</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.use_gpu"><code class="docutils literal notranslate"><span class="pre">Retriever.use_gpu</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.vector_db"><code class="docutils literal notranslate"><span class="pre">Retriever.vector_db</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.index"><code class="docutils literal notranslate"><span class="pre">Retriever.index</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.documents"><code class="docutils literal notranslate"><span class="pre">Retriever.documents</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.dimension"><code class="docutils literal notranslate"><span class="pre">Retriever.dimension</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.using_torch_fallback"><code class="docutils literal notranslate"><span class="pre">Retriever.using_torch_fallback</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.__init__"><code class="docutils literal notranslate"><span class="pre">Retriever.__init__()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.setup_vector_db"><code class="docutils literal notranslate"><span class="pre">Retriever.setup_vector_db()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.add_documents"><code class="docutils literal notranslate"><span class="pre">Retriever.add_documents()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.search"><code class="docutils literal notranslate"><span class="pre">Retriever.search()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.get_system_info"><code class="docutils literal notranslate"><span class="pre">Retriever.get_system_info()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.optimize_index"><code class="docutils literal notranslate"><span class="pre">Retriever.optimize_index()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#retrieval.Retriever.batch_add_documents"><code class="docutils literal notranslate"><span class="pre">Retriever.batch_add_documents()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#retriever-class">Retriever Class</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id0"><code class="docutils literal notranslate"><span class="pre">Retriever</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id1"><code class="docutils literal notranslate"><span class="pre">Retriever.use_gpu</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2"><code class="docutils literal notranslate"><span class="pre">Retriever.vector_db</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3"><code class="docutils literal notranslate"><span class="pre">Retriever.index</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4"><code class="docutils literal notranslate"><span class="pre">Retriever.documents</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5"><code class="docutils literal notranslate"><span class="pre">Retriever.dimension</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id6"><code class="docutils literal notranslate"><span class="pre">Retriever.using_torch_fallback</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7"><code class="docutils literal notranslate"><span class="pre">Retriever.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id8"><code class="docutils literal notranslate"><span class="pre">Retriever.setup_vector_db()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id9"><code class="docutils literal notranslate"><span class="pre">Retriever.add_documents()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id10"><code class="docutils literal notranslate"><span class="pre">Retriever.search()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id11"><code class="docutils literal notranslate"><span class="pre">Retriever.get_system_info()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id12"><code class="docutils literal notranslate"><span class="pre">Retriever.optimize_index()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id13"><code class="docutils literal notranslate"><span class="pre">Retriever.batch_add_documents()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#initialization">Initialization</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id14"><code class="docutils literal notranslate"><span class="pre">Retriever.__init__()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#core-methods">Core Methods</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id15"><code class="docutils literal notranslate"><span class="pre">Retriever.add_documents()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id16"><code class="docutils literal notranslate"><span class="pre">Retriever.batch_add_documents()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id17"><code class="docutils literal notranslate"><span class="pre">Retriever.search()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id18"><code class="docutils literal notranslate"><span class="pre">Retriever.get_system_info()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="#supported-backends">Supported Backends</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#faiss-backend">FAISS Backend</a></li>
<li class="toctree-l3"><a class="reference internal" href="#chromadb-backend">ChromaDB Backend</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#embedding-models">Embedding Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#basic-document-retrieval">Basic Document Retrieval</a></li>
<li class="toctree-l3"><a class="reference internal" href="#batch-processing">Batch Processing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#advanced-search">Advanced Search</a></li>
<li class="toctree-l3"><a class="reference internal" href="#system-information">System Information</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#performance-optimization">Performance Optimization</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#gpu-acceleration">GPU Acceleration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id19">Batch Processing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#index-optimization">Index Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#caching">Caching</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#document-preparation">Document Preparation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#query-optimization">Query Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#scaling-considerations">Scaling Considerations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../modules.html">API Reference</a></li>
      <li class="breadcrumb-item active">Vector Retrieval Module</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api/retrieval.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-retrieval">
<span id="vector-retrieval-module"></span><h1>Vector Retrieval Module<a class="headerlink" href="#module-retrieval" title="Link to this heading"></a></h1>
<p>LightRAG-based retrieval module for the Neural Symbolic Language Model.
This module provides a wrapper around LightRAG for efficient retrieval-augmented generation.</p>
<dl class="py class">
<dt class="sig sig-object py" id="retrieval.Retriever">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">retrieval.</span></span><span class="sig-name descname"><span class="pre">Retriever</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">vector_db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'faiss'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#retrieval.Retriever" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Hybrid retrieval system for neural-symbolic language models.</p>
<p>This class implements a sophisticated retrieval system that combines
vector similarity search with symbolic reasoning for enhanced document
retrieval and context generation. It supports multiple vector database
backends and GPU acceleration for optimal performance.</p>
<p>The retriever uses dense vector embeddings for fast similarity search
and can be extended with sparse retrieval methods and reranking for
improved accuracy in domain-specific applications.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="retrieval.Retriever.use_gpu">
<span class="sig-name descname"><span class="pre">use_gpu</span></span><a class="headerlink" href="#retrieval.Retriever.use_gpu" title="Link to this definition"></a></dt>
<dd><p>Whether GPU acceleration is enabled</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="retrieval.Retriever.vector_db">
<span class="sig-name descname"><span class="pre">vector_db</span></span><a class="headerlink" href="#retrieval.Retriever.vector_db" title="Link to this definition"></a></dt>
<dd><p>The vector database backend being used</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="retrieval.Retriever.index">
<span class="sig-name descname"><span class="pre">index</span></span><a class="headerlink" href="#retrieval.Retriever.index" title="Link to this definition"></a></dt>
<dd><p>The vector index for similarity search</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="retrieval.Retriever.documents">
<span class="sig-name descname"><span class="pre">documents</span></span><a class="headerlink" href="#retrieval.Retriever.documents" title="Link to this definition"></a></dt>
<dd><p>Storage for document content and metadata</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="retrieval.Retriever.dimension">
<span class="sig-name descname"><span class="pre">dimension</span></span><a class="headerlink" href="#retrieval.Retriever.dimension" title="Link to this definition"></a></dt>
<dd><p>Vector embedding dimension</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="retrieval.Retriever.using_torch_fallback">
<span class="sig-name descname"><span class="pre">using_torch_fallback</span></span><a class="headerlink" href="#retrieval.Retriever.using_torch_fallback" title="Link to this definition"></a></dt>
<dd><p>Whether using PyTorch fallback implementation</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<p class="rubric">Example</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span> <span class="o">=</span> <span class="n">Retriever</span><span class="p">(</span><span class="n">vector_db</span><span class="o">=</span><span class="s2">&quot;faiss&quot;</span><span class="p">,</span> <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span><span class="o">.</span><span class="n">add_document</span><span class="p">(</span><span class="s2">&quot;Neural networks are powerful.&quot;</span><span class="p">,</span> <span class="s2">&quot;doc1&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;What are neural networks?&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">3</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">results</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Score: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;score&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">.3f</span><span class="si">}</span><span class="s2">, Text: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">][:</span><span class="mi">50</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This implementation provides a foundation for hybrid retrieval.
In production, this would integrate with more sophisticated
embedding models and reranking systems.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="retrieval.Retriever.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">vector_db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'faiss'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#retrieval.Retriever.__init__" title="Link to this definition"></a></dt>
<dd><p>Initialize the retrieval system.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>vector_db</strong> – The vector database to use (e.g., “faiss”, “chromadb”)</p></li>
<li><p><strong>use_gpu</strong> – Whether to use GPU acceleration if available</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="../modules.html#exceptions.ConfigurationError" title="exceptions.ConfigurationError"><strong>ConfigurationError</strong></a> – If initialization fails</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="retrieval.Retriever.setup_vector_db">
<span class="sig-name descname"><span class="pre">setup_vector_db</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.setup_vector_db"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#retrieval.Retriever.setup_vector_db" title="Link to this definition"></a></dt>
<dd><p>Set up the vector database based on the selected backend.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="retrieval.Retriever.add_documents">
<span class="sig-name descname"><span class="pre">add_documents</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">documents</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">embeddings</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://numpy.org/doc/stable/reference/generated/numpy.ndarray.html#numpy.ndarray" title="(in NumPy v2.3)"><span class="pre">ndarray</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.add_documents"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#retrieval.Retriever.add_documents" title="Link to this definition"></a></dt>
<dd><p>Add documents to the retrieval system.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>documents</strong> – List of document dictionaries with ‘id’ and ‘text’ keys</p></li>
<li><p><strong>embeddings</strong> – Optional pre-computed embeddings for the documents</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="retrieval.Retriever.search">
<span class="sig-name descname"><span class="pre">search</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">k</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">5</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/retrieval.html#Retriever.search"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#retrieval.Retriever.search" title="Link to this definition"></a></dt>
<dd><p>Search for relevant documents using hybrid retrieval methods.</p>
<p>This method performs a two-stage search process: first using dense vector
similarity search for fast retrieval, then optionally applying reranking
for improved accuracy. The search supports both FAISS and PyTorch backends
with automatic fallback handling.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>query</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The search query text. Must be a non-empty string containing
the question or topic to search for. Maximum length is 10,000
characters. The query will be embedded into a dense vector
for similarity comparison.</p></li>
<li><p><strong>k</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – Number of top results to return. Must be a positive integer.
Higher values return more results but may include less relevant
documents. Typical values range from 3-10 for most applications.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><p>List of dictionaries containing search results, sorted by
relevance score in descending order. Each dictionary contains:</p>
<ul class="simple">
<li><p><strong>id</strong> (str): Unique document identifier</p></li>
<li><p><strong>text</strong> (str): Full document content</p></li>
<li><p><strong>score</strong> (float): Relevance score between 0.0 and 1.0,
where 1.0 indicates perfect relevance</p></li>
</ul>
</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List[Dict[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, Union[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)">float</a>]]]</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><a class="reference internal" href="../modules.html#exceptions.RetrievalError" title="exceptions.RetrievalError"><strong>RetrievalError</strong></a> – If the search operation fails due to:
- Empty or invalid query
- Vector database errors
- Embedding generation failures</p></li>
<li><p><a class="reference internal" href="../modules.html#exceptions.ValidationError" title="exceptions.ValidationError"><strong>ValidationError</strong></a> – If input validation fails due to:
- Non-positive k value
- Query exceeding maximum length
- Invalid query format</p></li>
</ul>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span> <span class="o">=</span> <span class="n">Retriever</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span><span class="o">.</span><span class="n">add_document</span><span class="p">(</span><span class="s2">&quot;Neural networks learn patterns.&quot;</span><span class="p">,</span> <span class="s2">&quot;doc1&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span><span class="o">.</span><span class="n">add_document</span><span class="p">(</span><span class="s2">&quot;Symbolic AI uses logical rules.&quot;</span><span class="p">,</span> <span class="s2">&quot;doc2&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Basic search</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;machine learning&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">results</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Score: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;score&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">.3f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Text: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">][:</span><span class="mi">50</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
<span class="go">Score: 0.856</span>
<span class="go">Text: Neural networks learn patterns...</span>
<span class="go">Score: 0.234</span>
<span class="go">Text: Symbolic AI uses logical rules...</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Search with specific number of results</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;neural networks&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Found </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">)</span><span class="si">}</span><span class="s2"> result(s)&quot;</span><span class="p">)</span>
<span class="go">Found 1 result(s)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Search performance depends on the vector database backend and
the quality of the embedding model. GPU acceleration significantly
improves search speed for large document collections.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This implementation uses random vectors for testing. In production,
replace with proper embedding models like BERT, Sentence-BERT,
or domain-specific embeddings.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_document()</span></code> for adding documents to search
<a class="reference internal" href="../modules.html#retrieval.Retriever.get_system_info" title="retrieval.Retriever.get_system_info"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_system_info()</span></code></a> for checking backend capabilities</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="retrieval.Retriever.get_system_info">
<span class="sig-name descname"><span class="pre">get_system_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/retrieval.html#Retriever.get_system_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#retrieval.Retriever.get_system_info" title="Link to this definition"></a></dt>
<dd><p>Get information about the system configuration.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>System configuration information</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="retrieval.Retriever.optimize_index">
<span class="sig-name descname"><span class="pre">optimize_index</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.optimize_index"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#retrieval.Retriever.optimize_index" title="Link to this definition"></a></dt>
<dd><p>Optimize the FAISS index for better performance.</p>
<p>This should be called after adding a significant number of documents.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="retrieval.Retriever.batch_add_documents">
<span class="sig-name descname"><span class="pre">batch_add_documents</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">documents</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">batch_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">32</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.batch_add_documents"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#retrieval.Retriever.batch_add_documents" title="Link to this definition"></a></dt>
<dd><p>Add multiple documents in batches for better performance.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>documents</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a>) – List of documents to add</p></li>
<li><p><strong>batch_size</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – Size of each batch</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if successful</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The retrieval module provides efficient semantic search and document retrieval
capabilities using vector embeddings and similarity search.</p>
<p>Key Features:
* Multiple vector database backends (FAISS, ChromaDB)
* GPU-accelerated embeddings
* Batch document processing
* Similarity search with configurable parameters
* Automatic fallback mechanisms</p>
</section>
<section id="retriever-class">
<h2>Retriever Class<a class="headerlink" href="#retriever-class" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="id0">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">retrieval.</span></span><span class="sig-name descname"><span class="pre">Retriever</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">vector_db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'faiss'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id0" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Hybrid retrieval system for neural-symbolic language models.</p>
<p>This class implements a sophisticated retrieval system that combines
vector similarity search with symbolic reasoning for enhanced document
retrieval and context generation. It supports multiple vector database
backends and GPU acceleration for optimal performance.</p>
<p>The retriever uses dense vector embeddings for fast similarity search
and can be extended with sparse retrieval methods and reranking for
improved accuracy in domain-specific applications.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="id1">
<span class="sig-name descname"><span class="pre">use_gpu</span></span><a class="headerlink" href="#id1" title="Link to this definition"></a></dt>
<dd><p>Whether GPU acceleration is enabled</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id2">
<span class="sig-name descname"><span class="pre">vector_db</span></span><a class="headerlink" href="#id2" title="Link to this definition"></a></dt>
<dd><p>The vector database backend being used</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id3">
<span class="sig-name descname"><span class="pre">index</span></span><a class="headerlink" href="#id3" title="Link to this definition"></a></dt>
<dd><p>The vector index for similarity search</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id4">
<span class="sig-name descname"><span class="pre">documents</span></span><a class="headerlink" href="#id4" title="Link to this definition"></a></dt>
<dd><p>Storage for document content and metadata</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id5">
<span class="sig-name descname"><span class="pre">dimension</span></span><a class="headerlink" href="#id5" title="Link to this definition"></a></dt>
<dd><p>Vector embedding dimension</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id6">
<span class="sig-name descname"><span class="pre">using_torch_fallback</span></span><a class="headerlink" href="#id6" title="Link to this definition"></a></dt>
<dd><p>Whether using PyTorch fallback implementation</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<p class="rubric">Example</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span> <span class="o">=</span> <span class="n">Retriever</span><span class="p">(</span><span class="n">vector_db</span><span class="o">=</span><span class="s2">&quot;faiss&quot;</span><span class="p">,</span> <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span><span class="o">.</span><span class="n">add_document</span><span class="p">(</span><span class="s2">&quot;Neural networks are powerful.&quot;</span><span class="p">,</span> <span class="s2">&quot;doc1&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;What are neural networks?&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">3</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">results</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Score: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;score&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">.3f</span><span class="si">}</span><span class="s2">, Text: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">][:</span><span class="mi">50</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This implementation provides a foundation for hybrid retrieval.
In production, this would integrate with more sophisticated
embedding models and reranking systems.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="id7">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">vector_db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'faiss'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id7" title="Link to this definition"></a></dt>
<dd><p>Initialize the retrieval system.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>vector_db</strong> – The vector database to use (e.g., “faiss”, “chromadb”)</p></li>
<li><p><strong>use_gpu</strong> – Whether to use GPU acceleration if available</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="../modules.html#exceptions.ConfigurationError" title="exceptions.ConfigurationError"><strong>ConfigurationError</strong></a> – If initialization fails</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id8">
<span class="sig-name descname"><span class="pre">setup_vector_db</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.setup_vector_db"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id8" title="Link to this definition"></a></dt>
<dd><p>Set up the vector database based on the selected backend.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id9">
<span class="sig-name descname"><span class="pre">add_documents</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">documents</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">embeddings</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://numpy.org/doc/stable/reference/generated/numpy.ndarray.html#numpy.ndarray" title="(in NumPy v2.3)"><span class="pre">ndarray</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.add_documents"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id9" title="Link to this definition"></a></dt>
<dd><p>Add documents to the retrieval system.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>documents</strong> – List of document dictionaries with ‘id’ and ‘text’ keys</p></li>
<li><p><strong>embeddings</strong> – Optional pre-computed embeddings for the documents</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id10">
<span class="sig-name descname"><span class="pre">search</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">k</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">5</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/retrieval.html#Retriever.search"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id10" title="Link to this definition"></a></dt>
<dd><p>Search for relevant documents using hybrid retrieval methods.</p>
<p>This method performs a two-stage search process: first using dense vector
similarity search for fast retrieval, then optionally applying reranking
for improved accuracy. The search supports both FAISS and PyTorch backends
with automatic fallback handling.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>query</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The search query text. Must be a non-empty string containing
the question or topic to search for. Maximum length is 10,000
characters. The query will be embedded into a dense vector
for similarity comparison.</p></li>
<li><p><strong>k</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – Number of top results to return. Must be a positive integer.
Higher values return more results but may include less relevant
documents. Typical values range from 3-10 for most applications.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><p>List of dictionaries containing search results, sorted by
relevance score in descending order. Each dictionary contains:</p>
<ul class="simple">
<li><p><strong>id</strong> (str): Unique document identifier</p></li>
<li><p><strong>text</strong> (str): Full document content</p></li>
<li><p><strong>score</strong> (float): Relevance score between 0.0 and 1.0,
where 1.0 indicates perfect relevance</p></li>
</ul>
</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List[Dict[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, Union[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)">float</a>]]]</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><a class="reference internal" href="../modules.html#exceptions.RetrievalError" title="exceptions.RetrievalError"><strong>RetrievalError</strong></a> – If the search operation fails due to:
- Empty or invalid query
- Vector database errors
- Embedding generation failures</p></li>
<li><p><a class="reference internal" href="../modules.html#exceptions.ValidationError" title="exceptions.ValidationError"><strong>ValidationError</strong></a> – If input validation fails due to:
- Non-positive k value
- Query exceeding maximum length
- Invalid query format</p></li>
</ul>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span> <span class="o">=</span> <span class="n">Retriever</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span><span class="o">.</span><span class="n">add_document</span><span class="p">(</span><span class="s2">&quot;Neural networks learn patterns.&quot;</span><span class="p">,</span> <span class="s2">&quot;doc1&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span><span class="o">.</span><span class="n">add_document</span><span class="p">(</span><span class="s2">&quot;Symbolic AI uses logical rules.&quot;</span><span class="p">,</span> <span class="s2">&quot;doc2&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Basic search</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;machine learning&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">results</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Score: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;score&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">.3f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Text: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">][:</span><span class="mi">50</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
<span class="go">Score: 0.856</span>
<span class="go">Text: Neural networks learn patterns...</span>
<span class="go">Score: 0.234</span>
<span class="go">Text: Symbolic AI uses logical rules...</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Search with specific number of results</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;neural networks&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Found </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">)</span><span class="si">}</span><span class="s2"> result(s)&quot;</span><span class="p">)</span>
<span class="go">Found 1 result(s)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Search performance depends on the vector database backend and
the quality of the embedding model. GPU acceleration significantly
improves search speed for large document collections.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This implementation uses random vectors for testing. In production,
replace with proper embedding models like BERT, Sentence-BERT,
or domain-specific embeddings.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_document()</span></code> for adding documents to search
<a class="reference internal" href="../modules.html#retrieval.Retriever.get_system_info" title="retrieval.Retriever.get_system_info"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_system_info()</span></code></a> for checking backend capabilities</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id11">
<span class="sig-name descname"><span class="pre">get_system_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/retrieval.html#Retriever.get_system_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id11" title="Link to this definition"></a></dt>
<dd><p>Get information about the system configuration.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>System configuration information</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id12">
<span class="sig-name descname"><span class="pre">optimize_index</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.optimize_index"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id12" title="Link to this definition"></a></dt>
<dd><p>Optimize the FAISS index for better performance.</p>
<p>This should be called after adding a significant number of documents.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id13">
<span class="sig-name descname"><span class="pre">batch_add_documents</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">documents</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">batch_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">32</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.batch_add_documents"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id13" title="Link to this definition"></a></dt>
<dd><p>Add multiple documents in batches for better performance.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>documents</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a>) – List of documents to add</p></li>
<li><p><strong>batch_size</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – Size of each batch</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if successful</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<p>The main class for vector retrieval operations.</p>
<section id="initialization">
<h3>Initialization<a class="headerlink" href="#initialization" title="Link to this heading"></a></h3>
<dl class="py method">
<dt class="sig sig-object py" id="id14">
<span class="sig-prename descclassname"><span class="pre">Retriever.</span></span><span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">vector_db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'faiss'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id14" title="Link to this definition"></a></dt>
<dd><p>Initialize the retrieval system.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>vector_db</strong> – The vector database to use (e.g., “faiss”, “chromadb”)</p></li>
<li><p><strong>use_gpu</strong> – Whether to use GPU acceleration if available</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="../modules.html#exceptions.ConfigurationError" title="exceptions.ConfigurationError"><strong>ConfigurationError</strong></a> – If initialization fails</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="core-methods">
<h3>Core Methods<a class="headerlink" href="#core-methods" title="Link to this heading"></a></h3>
<dl class="py method">
<dt class="sig sig-object py" id="id15">
<span class="sig-prename descclassname"><span class="pre">Retriever.</span></span><span class="sig-name descname"><span class="pre">add_documents</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">documents</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">embeddings</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://numpy.org/doc/stable/reference/generated/numpy.ndarray.html#numpy.ndarray" title="(in NumPy v2.3)"><span class="pre">ndarray</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.add_documents"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id15" title="Link to this definition"></a></dt>
<dd><p>Add documents to the retrieval system.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>documents</strong> – List of document dictionaries with ‘id’ and ‘text’ keys</p></li>
<li><p><strong>embeddings</strong> – Optional pre-computed embeddings for the documents</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id16">
<span class="sig-prename descclassname"><span class="pre">Retriever.</span></span><span class="sig-name descname"><span class="pre">batch_add_documents</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">documents</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">batch_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">32</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/retrieval.html#Retriever.batch_add_documents"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id16" title="Link to this definition"></a></dt>
<dd><p>Add multiple documents in batches for better performance.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>documents</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a>) – List of documents to add</p></li>
<li><p><strong>batch_size</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – Size of each batch</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if successful</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id17">
<span class="sig-prename descclassname"><span class="pre">Retriever.</span></span><span class="sig-name descname"><span class="pre">search</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">k</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">5</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/retrieval.html#Retriever.search"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id17" title="Link to this definition"></a></dt>
<dd><p>Search for relevant documents using hybrid retrieval methods.</p>
<p>This method performs a two-stage search process: first using dense vector
similarity search for fast retrieval, then optionally applying reranking
for improved accuracy. The search supports both FAISS and PyTorch backends
with automatic fallback handling.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>query</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The search query text. Must be a non-empty string containing
the question or topic to search for. Maximum length is 10,000
characters. The query will be embedded into a dense vector
for similarity comparison.</p></li>
<li><p><strong>k</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><em>int</em></a>) – Number of top results to return. Must be a positive integer.
Higher values return more results but may include less relevant
documents. Typical values range from 3-10 for most applications.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><p>List of dictionaries containing search results, sorted by
relevance score in descending order. Each dictionary contains:</p>
<ul class="simple">
<li><p><strong>id</strong> (str): Unique document identifier</p></li>
<li><p><strong>text</strong> (str): Full document content</p></li>
<li><p><strong>score</strong> (float): Relevance score between 0.0 and 1.0,
where 1.0 indicates perfect relevance</p></li>
</ul>
</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List[Dict[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, Union[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, <a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)">float</a>]]]</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><a class="reference internal" href="../modules.html#exceptions.RetrievalError" title="exceptions.RetrievalError"><strong>RetrievalError</strong></a> – If the search operation fails due to:
- Empty or invalid query
- Vector database errors
- Embedding generation failures</p></li>
<li><p><a class="reference internal" href="../modules.html#exceptions.ValidationError" title="exceptions.ValidationError"><strong>ValidationError</strong></a> – If input validation fails due to:
- Non-positive k value
- Query exceeding maximum length
- Invalid query format</p></li>
</ul>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span> <span class="o">=</span> <span class="n">Retriever</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span><span class="o">.</span><span class="n">add_document</span><span class="p">(</span><span class="s2">&quot;Neural networks learn patterns.&quot;</span><span class="p">,</span> <span class="s2">&quot;doc1&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">retriever</span><span class="o">.</span><span class="n">add_document</span><span class="p">(</span><span class="s2">&quot;Symbolic AI uses logical rules.&quot;</span><span class="p">,</span> <span class="s2">&quot;doc2&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Basic search</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;machine learning&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">results</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Score: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;score&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">.3f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Text: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">][:</span><span class="mi">50</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
<span class="go">Score: 0.856</span>
<span class="go">Text: Neural networks learn patterns...</span>
<span class="go">Score: 0.234</span>
<span class="go">Text: Symbolic AI uses logical rules...</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Search with specific number of results</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;neural networks&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Found </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">)</span><span class="si">}</span><span class="s2"> result(s)&quot;</span><span class="p">)</span>
<span class="go">Found 1 result(s)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Search performance depends on the vector database backend and
the quality of the embedding model. GPU acceleration significantly
improves search speed for large document collections.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This implementation uses random vectors for testing. In production,
replace with proper embedding models like BERT, Sentence-BERT,
or domain-specific embeddings.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_document()</span></code> for adding documents to search
<a class="reference internal" href="../modules.html#retrieval.Retriever.get_system_info" title="retrieval.Retriever.get_system_info"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_system_info()</span></code></a> for checking backend capabilities</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id18">
<span class="sig-prename descclassname"><span class="pre">Retriever.</span></span><span class="sig-name descname"><span class="pre">get_system_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/retrieval.html#Retriever.get_system_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id18" title="Link to this definition"></a></dt>
<dd><p>Get information about the system configuration.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>System configuration information</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

</section>
</section>
<section id="configuration">
<h2>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<p>The retriever can be configured with the following parameters:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">vector_db</span></code>: Vector database backend (“faiss”, “chromadb”)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">embedding_model</span></code>: Embedding model name</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">use_gpu</span></code>: Enable GPU acceleration (default: True)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">dimension</span></code>: Vector dimension (default: 768)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">index_type</span></code>: FAISS index type for optimization</p></li>
</ul>
</section>
<section id="supported-backends">
<h2>Supported Backends<a class="headerlink" href="#supported-backends" title="Link to this heading"></a></h2>
<section id="faiss-backend">
<h3>FAISS Backend<a class="headerlink" href="#faiss-backend" title="Link to this heading"></a></h3>
<p>Facebook AI Similarity Search for efficient vector operations:</p>
<ul class="simple">
<li><p><strong>Performance</strong>: Highly optimized C++ implementation</p></li>
<li><p><strong>GPU Support</strong>: Full GPU acceleration available</p></li>
<li><p><strong>Scalability</strong>: Handles millions of vectors efficiently</p></li>
<li><p><strong>Index Types</strong>: Multiple index types for different use cases</p></li>
</ul>
</section>
<section id="chromadb-backend">
<h3>ChromaDB Backend<a class="headerlink" href="#chromadb-backend" title="Link to this heading"></a></h3>
<p>Modern vector database with advanced features:</p>
<ul class="simple">
<li><p><strong>Metadata</strong>: Rich metadata support for documents</p></li>
<li><p><strong>Filtering</strong>: Advanced filtering capabilities</p></li>
<li><p><strong>Collections</strong>: Organized document collections</p></li>
<li><p><strong>Persistence</strong>: Built-in data persistence</p></li>
</ul>
</section>
</section>
<section id="embedding-models">
<h2>Embedding Models<a class="headerlink" href="#embedding-models" title="Link to this heading"></a></h2>
<p>Supported embedding models:</p>
<ul class="simple">
<li><p><strong>BAAI/bge-small-en-v1.5</strong>: Lightweight, fast embeddings</p></li>
<li><p><strong>BAAI/bge-large-en-v1.5</strong>: High-quality embeddings</p></li>
<li><p><strong>sentence-transformers/all-MiniLM-L6-v2</strong>: Balanced performance</p></li>
<li><p><strong>Custom models</strong>: Support for custom embedding models</p></li>
</ul>
</section>
<section id="example-usage">
<h2>Example Usage<a class="headerlink" href="#example-usage" title="Link to this heading"></a></h2>
<section id="basic-document-retrieval">
<h3>Basic Document Retrieval<a class="headerlink" href="#basic-document-retrieval" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">retrieval</span><span class="w"> </span><span class="kn">import</span> <span class="n">Retriever</span>

<span class="c1"># Initialize retriever</span>
<span class="n">retriever</span> <span class="o">=</span> <span class="n">Retriever</span><span class="p">(</span>
    <span class="n">vector_db</span><span class="o">=</span><span class="s2">&quot;faiss&quot;</span><span class="p">,</span>
    <span class="n">embedding_model</span><span class="o">=</span><span class="s2">&quot;BAAI/bge-small-en-v1.5&quot;</span><span class="p">,</span>
    <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>

<span class="c1"># Add documents</span>
<span class="n">documents</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">{</span>
        <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="s2">&quot;doc1&quot;</span><span class="p">,</span>
        <span class="s2">&quot;text&quot;</span><span class="p">:</span> <span class="s2">&quot;Symbolic reasoning uses formal logic and rules.&quot;</span><span class="p">,</span>
        <span class="s2">&quot;metadata&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;category&quot;</span><span class="p">:</span> <span class="s2">&quot;AI&quot;</span><span class="p">,</span> <span class="s2">&quot;source&quot;</span><span class="p">:</span> <span class="s2">&quot;textbook&quot;</span><span class="p">}</span>
    <span class="p">},</span>
    <span class="p">{</span>
        <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="s2">&quot;doc2&quot;</span><span class="p">,</span>
        <span class="s2">&quot;text&quot;</span><span class="p">:</span> <span class="s2">&quot;Neural networks learn patterns from data.&quot;</span><span class="p">,</span>
        <span class="s2">&quot;metadata&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;category&quot;</span><span class="p">:</span> <span class="s2">&quot;ML&quot;</span><span class="p">,</span> <span class="s2">&quot;source&quot;</span><span class="p">:</span> <span class="s2">&quot;paper&quot;</span><span class="p">}</span>
    <span class="p">}</span>
<span class="p">]</span>

<span class="n">retriever</span><span class="o">.</span><span class="n">add_documents</span><span class="p">(</span><span class="n">documents</span><span class="p">)</span>

<span class="c1"># Search for similar documents</span>
<span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;What is symbolic AI?&quot;</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>
<span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">results</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Score: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;score&#39;</span><span class="p">]</span><span class="si">:</span><span class="s2">.3f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Text: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Metadata: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;metadata&#39;</span><span class="p">]</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="batch-processing">
<h3>Batch Processing<a class="headerlink" href="#batch-processing" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Process large document collections</span>
<span class="n">large_document_set</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">{</span><span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;doc_</span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="s2">&quot;text&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Document content </span><span class="si">{</span><span class="n">i</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">}</span>
    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">10000</span><span class="p">)</span>
<span class="p">]</span>

<span class="c1"># Add documents in batches</span>
<span class="n">retriever</span><span class="o">.</span><span class="n">batch_add_documents</span><span class="p">(</span>
    <span class="n">large_document_set</span><span class="p">,</span>
    <span class="n">batch_size</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
    <span class="n">show_progress</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="advanced-search">
<h3>Advanced Search<a class="headerlink" href="#advanced-search" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Advanced search with parameters</span>
<span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span>
    <span class="n">query</span><span class="o">=</span><span class="s2">&quot;machine learning algorithms&quot;</span><span class="p">,</span>
    <span class="n">k</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span>
    <span class="n">score_threshold</span><span class="o">=</span><span class="mf">0.7</span><span class="p">,</span>
    <span class="n">include_metadata</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>

<span class="c1"># Filter results by metadata</span>
<span class="n">filtered_results</span> <span class="o">=</span> <span class="p">[</span>
    <span class="n">r</span> <span class="k">for</span> <span class="n">r</span> <span class="ow">in</span> <span class="n">results</span>
    <span class="k">if</span> <span class="n">r</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;metadata&#39;</span><span class="p">,</span> <span class="p">{})</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;category&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="s1">&#39;ML&#39;</span>
<span class="p">]</span>
</pre></div>
</div>
</section>
<section id="system-information">
<h3>System Information<a class="headerlink" href="#system-information" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get system information</span>
<span class="n">info</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Vector DB: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;vector_db&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Index Size: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;index_size&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU Enabled: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_enabled&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Embedding Model: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;embedding_model&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-optimization">
<h2>Performance Optimization<a class="headerlink" href="#performance-optimization" title="Link to this heading"></a></h2>
<section id="gpu-acceleration">
<h3>GPU Acceleration<a class="headerlink" href="#gpu-acceleration" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Enable GPU acceleration for faster embedding computation</p></li>
<li><p>Automatic memory management prevents GPU memory leaks</p></li>
<li><p>Fallback to CPU if GPU is not available or runs out of memory</p></li>
</ul>
</section>
<section id="id19">
<h3>Batch Processing<a class="headerlink" href="#id19" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Use batch processing for large document collections</p></li>
<li><p>Configurable batch sizes optimize memory usage</p></li>
<li><p>Progress tracking for long-running operations</p></li>
</ul>
</section>
<section id="index-optimization">
<h3>Index Optimization<a class="headerlink" href="#index-optimization" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Choose appropriate FAISS index types for your use case:
* <code class="docutils literal notranslate"><span class="pre">IndexFlatIP</span></code>: Exact search, good for small datasets
* <code class="docutils literal notranslate"><span class="pre">IndexIVFFlat</span></code>: Approximate search, good for medium datasets
* <code class="docutils literal notranslate"><span class="pre">IndexHNSW</span></code>: Graph-based search, good for large datasets</p></li>
</ul>
</section>
<section id="caching">
<h3>Caching<a class="headerlink" href="#caching" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Embedding results are cached to avoid recomputation</p></li>
<li><p>Configurable cache size and TTL</p></li>
<li><p>LRU eviction policy for memory management</p></li>
</ul>
</section>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>The module provides comprehensive error handling:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">retrieval</span><span class="w"> </span><span class="kn">import</span> <span class="n">Retriever</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">exceptions</span><span class="w"> </span><span class="kn">import</span> <span class="n">RetrievalError</span><span class="p">,</span> <span class="n">VectorStoreError</span>

<span class="k">try</span><span class="p">:</span>
    <span class="n">retriever</span> <span class="o">=</span> <span class="n">Retriever</span><span class="p">(</span><span class="n">vector_db</span><span class="o">=</span><span class="s2">&quot;faiss&quot;</span><span class="p">)</span>
    <span class="n">retriever</span><span class="o">.</span><span class="n">add_documents</span><span class="p">(</span><span class="n">documents</span><span class="p">)</span>
    <span class="n">results</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="s2">&quot;query&quot;</span><span class="p">)</span>
<span class="k">except</span> <span class="n">RetrievalError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Retrieval failed: </span><span class="si">{</span><span class="n">e</span><span class="o">.</span><span class="n">message</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Operation: </span><span class="si">{</span><span class="n">e</span><span class="o">.</span><span class="n">details</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;operation&#39;</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="k">except</span> <span class="n">VectorStoreError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Vector store error: </span><span class="si">{</span><span class="n">e</span><span class="o">.</span><span class="n">message</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="document-preparation">
<h3>Document Preparation<a class="headerlink" href="#document-preparation" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Clean and preprocess text before adding to the index</p></li>
<li><p>Include relevant metadata for filtering and organization</p></li>
<li><p>Use consistent document IDs for updates and deletions</p></li>
</ul>
</section>
<section id="query-optimization">
<h3>Query Optimization<a class="headerlink" href="#query-optimization" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Use specific, well-formed queries for better results</p></li>
<li><p>Experiment with different similarity thresholds</p></li>
<li><p>Consider query expansion for better recall</p></li>
</ul>
</section>
<section id="scaling-considerations">
<h3>Scaling Considerations<a class="headerlink" href="#scaling-considerations" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Monitor index size and performance metrics</p></li>
<li><p>Consider distributed setups for very large datasets</p></li>
<li><p>Regular index optimization and maintenance</p></li>
</ul>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="symbolic_reasoning.html" class="btn btn-neutral float-left" title="Symbolic Reasoning Module" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="security.html" class="btn btn-neutral float-right" title="Security Module" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>