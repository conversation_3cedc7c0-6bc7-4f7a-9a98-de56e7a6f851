Deployment Guide
================

This guide covers deploying the Neural Symbolic Language Model in production environments.

Overview
--------

The Neural Symbolic Language Model is designed for production deployment with:

* **Containerized deployment** with Docker
* **Kubernetes orchestration** support
* **Load balancing** and auto-scaling
* **Monitoring and observability** integration
* **Security hardening** for production use

Deployment Options
------------------

Local Development
~~~~~~~~~~~~~~~~~

For development and testing:

.. code-block:: bash

   # Start development server
   python src/main.py
   
   # With auto-reload
   uvicorn main:app --reload --host 0.0.0.0 --port 8000

Single Server Deployment
~~~~~~~~~~~~~~~~~~~~~~~~

For small-scale production deployments:

.. code-block:: bash

   # Production server with multiple workers
   uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4

Docker Deployment
~~~~~~~~~~~~~~~~~

**Dockerfile:**

.. code-block:: dockerfile

   FROM python:3.10-slim

   # Set working directory
   WORKDIR /app

   # Install system dependencies
   RUN apt-get update && apt-get install -y \
       build-essential \
       curl \
       && rm -rf /var/lib/apt/lists/*

   # Copy requirements and install Python dependencies
   COPY requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt

   # Copy application code
   COPY src/ ./src/
   COPY config/ ./config/

   # Create non-root user
   RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
   USER appuser

   # Expose port
   EXPOSE 8000

   # Health check
   HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
     CMD curl -f http://localhost:8000/health || exit 1

   # Start application
   CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]

**Build and run:**

.. code-block:: bash

   # Build image
   docker build -t neural-symbolic-ai:latest .
   
   # Run container
   docker run -d \
     --name neural-symbolic-api \
     -p 8000:8000 \
     -e APP_ENVIRONMENT=production \
     -e SECURITY_API_KEYS='{"prod": "your-secure-api-key"}' \
     neural-symbolic-ai:latest

Docker Compose
~~~~~~~~~~~~~~

**docker-compose.yml:**

.. code-block:: yaml

   version: '3.8'
   
   services:
     neural-symbolic-api:
       build: .
       ports:
         - "8000:8000"
       environment:
         - APP_ENVIRONMENT=production
         - APP_WORKERS=4
         - CACHE_REDIS_URL=redis://redis:6379/0
         - LOG_LEVEL=INFO
       depends_on:
         - redis
       restart: unless-stopped
       healthcheck:
         test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
         interval: 30s
         timeout: 10s
         retries: 3
   
     redis:
       image: redis:7-alpine
       ports:
         - "6379:6379"
       restart: unless-stopped
       healthcheck:
         test: ["CMD", "redis-cli", "ping"]
         interval: 30s
         timeout: 10s
         retries: 3
   
     nginx:
       image: nginx:alpine
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx.conf:/etc/nginx/nginx.conf
         - ./ssl:/etc/nginx/ssl
       depends_on:
         - neural-symbolic-api
       restart: unless-stopped

**Start services:**

.. code-block:: bash

   docker-compose up -d

Kubernetes Deployment
~~~~~~~~~~~~~~~~~~~~~

**deployment.yaml:**

.. code-block:: yaml

   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: neural-symbolic-api
     labels:
       app: neural-symbolic-api
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: neural-symbolic-api
     template:
       metadata:
         labels:
           app: neural-symbolic-api
       spec:
         containers:
         - name: neural-symbolic-api
           image: neural-symbolic-ai:latest
           ports:
           - containerPort: 8000
           env:
           - name: APP_ENVIRONMENT
             value: "production"
           - name: APP_WORKERS
             value: "4"
           - name: CACHE_REDIS_URL
             value: "redis://redis-service:6379/0"
           resources:
             requests:
               memory: "2Gi"
               cpu: "1000m"
             limits:
               memory: "4Gi"
               cpu: "2000m"
           livenessProbe:
             httpGet:
               path: /health
               port: 8000
             initialDelaySeconds: 30
             periodSeconds: 30
           readinessProbe:
             httpGet:
               path: /ready
               port: 8000
             initialDelaySeconds: 5
             periodSeconds: 10

**service.yaml:**

.. code-block:: yaml

   apiVersion: v1
   kind: Service
   metadata:
     name: neural-symbolic-service
   spec:
     selector:
       app: neural-symbolic-api
     ports:
     - protocol: TCP
       port: 80
       targetPort: 8000
     type: LoadBalancer

**Deploy to Kubernetes:**

.. code-block:: bash

   kubectl apply -f deployment.yaml
   kubectl apply -f service.yaml

Production Configuration
------------------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

**Production .env file:**

.. code-block:: bash

   # Application
   APP_ENVIRONMENT=production
   APP_DEBUG=false
   APP_WORKERS=4
   APP_HOST=0.0.0.0
   APP_PORT=8000

   # Security
   SECURITY_API_KEYS='{"prod": "your-very-secure-api-key-here"}'
   SECURITY_CORS_ORIGINS='["https://yourdomain.com"]'
   SECURITY_RATE_LIMIT_REQUESTS=100

   # Performance
   MODEL_USE_GPU=true
   CACHE_REDIS_URL=redis://redis-server:6379/0
   CACHE_MAX_SIZE=10000

   # Logging
   LOG_LEVEL=INFO
   LOG_STRUCTURED_LOGGING=true
   LOG_FILE_PATH=/var/log/neural-symbolic

Database Configuration
~~~~~~~~~~~~~~~~~~~~~~

For production, use PostgreSQL instead of SQLite:

.. code-block:: bash

   # Database connection
   DATABASE_URL=*****************************************/neural_symbolic
   DATABASE_POOL_SIZE=20
   DATABASE_MAX_OVERFLOW=30

Load Balancing
--------------

Nginx Configuration
~~~~~~~~~~~~~~~~~~~

**nginx.conf:**

.. code-block:: nginx

   upstream neural_symbolic_backend {
       server neural-symbolic-api-1:8000;
       server neural-symbolic-api-2:8000;
       server neural-symbolic-api-3:8000;
   }

   server {
       listen 80;
       server_name yourdomain.com;
       return 301 https://$server_name$request_uri;
   }

   server {
       listen 443 ssl http2;
       server_name yourdomain.com;

       ssl_certificate /etc/nginx/ssl/cert.pem;
       ssl_certificate_key /etc/nginx/ssl/key.pem;

       # Security headers
       add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
       add_header X-Frame-Options DENY always;
       add_header X-Content-Type-Options nosniff always;

       # Rate limiting
       limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

       location / {
           limit_req zone=api burst=20 nodelay;
           
           proxy_pass http://neural_symbolic_backend;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           
           # Timeouts
           proxy_connect_timeout 30s;
           proxy_send_timeout 30s;
           proxy_read_timeout 30s;
       }

       location /health {
           access_log off;
           proxy_pass http://neural_symbolic_backend;
       }
   }

HAProxy Configuration
~~~~~~~~~~~~~~~~~~~~

**haproxy.cfg:**

.. code-block:: text

   global
       daemon
       maxconn 4096

   defaults
       mode http
       timeout connect 5000ms
       timeout client 50000ms
       timeout server 50000ms

   frontend neural_symbolic_frontend
       bind *:80
       bind *:443 ssl crt /etc/ssl/certs/yourdomain.pem
       redirect scheme https if !{ ssl_fc }
       default_backend neural_symbolic_backend

   backend neural_symbolic_backend
       balance roundrobin
       option httpchk GET /health
       server api1 neural-symbolic-api-1:8000 check
       server api2 neural-symbolic-api-2:8000 check
       server api3 neural-symbolic-api-3:8000 check

Monitoring and Observability
----------------------------

Prometheus Metrics
~~~~~~~~~~~~~~~~~~

The application exposes Prometheus-compatible metrics:

.. code-block:: bash

   # Scrape configuration
   curl http://localhost:8000/metrics

**prometheus.yml:**

.. code-block:: yaml

   global:
     scrape_interval: 15s

   scrape_configs:
     - job_name: 'neural-symbolic-api'
       static_configs:
         - targets: ['neural-symbolic-api:8000']
       metrics_path: /metrics
       scrape_interval: 30s

Grafana Dashboard
~~~~~~~~~~~~~~~~

Import the provided Grafana dashboard for monitoring:

* **Request rate and latency**
* **Error rates and status codes**
* **Cache hit rates and performance**
* **GPU utilization and memory**
* **System resources (CPU, memory)**

Logging
~~~~~~~

**Centralized logging with ELK stack:**

.. code-block:: yaml

   # docker-compose.logging.yml
   version: '3.8'
   
   services:
     elasticsearch:
       image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
       environment:
         - discovery.type=single-node
         - xpack.security.enabled=false
       ports:
         - "9200:9200"

     logstash:
       image: docker.elastic.co/logstash/logstash:8.8.0
       volumes:
         - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
       ports:
         - "5044:5044"
       depends_on:
         - elasticsearch

     kibana:
       image: docker.elastic.co/kibana/kibana:8.8.0
       ports:
         - "5601:5601"
       depends_on:
         - elasticsearch

Security Hardening
------------------

SSL/TLS Configuration
~~~~~~~~~~~~~~~~~~~~

**Generate SSL certificates:**

.. code-block:: bash

   # Self-signed certificate (development)
   openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

   # Let's Encrypt (production)
   certbot certonly --standalone -d yourdomain.com

API Security
~~~~~~~~~~~~

**Security checklist:**

* ✅ Use strong API keys (32+ characters)
* ✅ Enable HTTPS only in production
* ✅ Configure CORS properly
* ✅ Set up rate limiting
* ✅ Enable request size limits
* ✅ Use security headers
* ✅ Regular security updates

**Environment security:**

.. code-block:: bash

   # Secure file permissions
   chmod 600 .env
   chmod 600 ssl/key.pem

   # Use secrets management
   kubectl create secret generic api-keys \
     --from-literal=production-key=your-secure-key

Auto-scaling
------------

Kubernetes HPA
~~~~~~~~~~~~~~

**hpa.yaml:**

.. code-block:: yaml

   apiVersion: autoscaling/v2
   kind: HorizontalPodAutoscaler
   metadata:
     name: neural-symbolic-hpa
   spec:
     scaleTargetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: neural-symbolic-api
     minReplicas: 3
     maxReplicas: 10
     metrics:
     - type: Resource
       resource:
         name: cpu
         target:
           type: Utilization
           averageUtilization: 70
     - type: Resource
       resource:
         name: memory
         target:
           type: Utilization
           averageUtilization: 80

Docker Swarm
~~~~~~~~~~~~

.. code-block:: yaml

   version: '3.8'
   
   services:
     neural-symbolic-api:
       image: neural-symbolic-ai:latest
       deploy:
         replicas: 3
         update_config:
           parallelism: 1
           delay: 10s
         restart_policy:
           condition: on-failure
         resources:
           limits:
             cpus: '2'
             memory: 4G
           reservations:
             cpus: '1'
             memory: 2G

Backup and Recovery
------------------

Data Backup
~~~~~~~~~~~

.. code-block:: bash

   # Backup vector indices
   tar -czf vector_indices_backup.tar.gz data/indices/

   # Backup configuration
   tar -czf config_backup.tar.gz config/ .env

   # Automated backup script
   #!/bin/bash
   DATE=$(date +%Y%m%d_%H%M%S)
   docker exec neural-symbolic-api tar -czf /tmp/backup_$DATE.tar.gz /app/data
   docker cp neural-symbolic-api:/tmp/backup_$DATE.tar.gz ./backups/

Disaster Recovery
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Restore from backup
   tar -xzf vector_indices_backup.tar.gz -C data/
   
   # Restart services
   docker-compose restart

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**High memory usage:**

.. code-block:: bash

   # Monitor memory usage
   docker stats neural-symbolic-api
   
   # Reduce cache size
   CACHE_MAX_SIZE=1000

**Slow response times:**

.. code-block:: bash

   # Check GPU utilization
   nvidia-smi
   
   # Enable GPU acceleration
   MODEL_USE_GPU=true

**Connection timeouts:**

.. code-block:: bash

   # Increase timeout values
   proxy_read_timeout 60s;
   
   # Check network connectivity
   curl -v http://neural-symbolic-api:8000/health

Performance Tuning
~~~~~~~~~~~~~~~~~~

**Optimize for your workload:**

.. code-block:: bash

   # CPU-intensive workloads
   APP_WORKERS=8  # 2x CPU cores
   
   # Memory-intensive workloads
   CACHE_MAX_SIZE=50000
   
   # GPU workloads
   MODEL_GPU_MEMORY_FRACTION=0.9
