# Client Application Integration Guide

This comprehensive guide shows how to integrate the Neural Symbolic Language Model with popular LLM client applications and frameworks.

## Overview

The Neural Symbolic Language Model provides an OpenAI-compatible API that works seamlessly with any application that supports OpenAI's API format. This includes:

- **Web UIs**: Open WebUI, LibreChat, Chatbot UI
- **Development Tools**: Continue.dev, Cursor, GitHub Copilot alternatives
- **Programming SDKs**: OpenAI Python/JavaScript SDKs
- **Custom Applications**: Any application using OpenAI API format

## Prerequisites

Before integrating with client applications, ensure:

1. **Neural Symbolic Language Model is running**:
   ```bash
   python src/main.py
   ```

2. **API is accessible**:
   ```bash
   curl http://localhost:8080/health
   ```

3. **API key is configured** (see Configuration section)

## Configuration

### API Key Setup

Set up API authentication in your environment:

```bash
# In .env file
SECURITY_API_KEYS_JSON='{"client": {"key": "your-secure-api-key", "permissions": ["read", "write"], "rate_limit": 1000}}'
```

### Model Configuration

Ensure the gemma3n:e2b model is available:

```bash
# Check Ollama models
ollama list

# Pull model if needed
ollama pull gemma3n:e2b
```

## Web UI Integrations

### Open WebUI

[Open WebUI](https://github.com/open-webui/open-webui) provides a ChatGPT-like interface.

#### Installation and Setup

```bash
# Method 1: pip installation
pip install open-webui
open-webui serve --port 3000

# Method 2: Docker
docker run -d -p 3000:8080 \
  -e OPENAI_API_BASE_URL=http://host.docker.internal:8080/v1 \
  -e OPENAI_API_KEY=your-api-key \
  --name open-webui \
  ghcr.io/open-webui/open-webui:main
```

#### Configuration

1. **Access Open WebUI**: http://localhost:3000
2. **Go to Settings** → **Connections**
3. **Add Connection**:
   - **Name**: Neural Symbolic Language Model
   - **API Base URL**: `http://localhost:8080/v1`
   - **API Key**: `your-api-key`
4. **Select Model**: Choose `gemma3n:e2b` from the dropdown

#### Advanced Configuration

```yaml
# docker-compose.yml for Open WebUI
version: '3.8'
services:
  neural-symbolic-lm:
    build: .
    ports:
      - "8080:8080"
    environment:
      - MODEL_REASONING_ENGINE=ollama
      - MODEL_REASONING_MODEL=gemma3n:e2b
      - SECURITY_API_KEYS_JSON={"webui": {"key": "webui-api-key", "permissions": ["read", "write"]}}

  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    ports:
      - "3000:8080"
    environment:
      - OPENAI_API_BASE_URL=http://neural-symbolic-lm:8080/v1
      - OPENAI_API_KEY=webui-api-key
      - DEFAULT_MODELS=gemma3n:e2b
      - WEBUI_NAME=Neural Symbolic Chat
    volumes:
      - open-webui:/app/backend/data
    depends_on:
      - neural-symbolic-lm

volumes:
  open-webui:
```

### LibreChat

[LibreChat](https://github.com/danny-avila/LibreChat) is an enhanced ChatGPT clone.

#### Configuration File

```yaml
# librechat.yaml
version: 1.0.5
cache: true

endpoints:
  custom:
    - name: "Neural Symbolic LM"
      apiKey: "your-api-key"
      baseURL: "http://localhost:8080/v1"
      models:
        default: ["gemma3n:e2b"]
        fetch: false
      titleConvo: true
      titleModel: "gemma3n:e2b"
      summarize: false
      summaryModel: "gemma3n:e2b"
      forcePrompt: false
      modelDisplayLabel: "Neural Symbolic Language Model"
      iconURL: "https://example.com/neural-symbolic-icon.png"
      
      # Advanced settings
      headers:
        "User-Agent": "LibreChat/1.0"
      
      # Model parameters
      modelOptions:
        temperature:
          range: [0, 2]
          step: 0.1
        top_p:
          range: [0, 1]
          step: 0.1
        max_tokens:
          range: [1, 4096]
          step: 1
```

#### Docker Deployment

```yaml
# docker-compose.yml for LibreChat
version: '3.8'
services:
  neural-symbolic-lm:
    build: .
    ports:
      - "8080:8080"
    environment:
      - MODEL_REASONING_ENGINE=ollama
      - MODEL_REASONING_MODEL=gemma3n:e2b

  librechat:
    image: ghcr.io/danny-avila/librechat:latest
    ports:
      - "3000:3080"
    environment:
      - CUSTOM_CONFIG_PATH=/app/librechat.yaml
    volumes:
      - ./librechat.yaml:/app/librechat.yaml
      - librechat_data:/app/client/public/images
    depends_on:
      - neural-symbolic-lm

volumes:
  librechat_data:
```

### Chatbot UI

[Chatbot UI](https://github.com/mckaywrigley/chatbot-ui) is a simple, customizable chat interface.

#### Environment Configuration

```bash
# .env.local
OPENAI_API_KEY=your-api-key
OPENAI_API_HOST=http://localhost:8080/v1
DEFAULT_MODEL=gemma3n:e2b
DEFAULT_SYSTEM_PROMPT="You are a helpful AI assistant with advanced symbolic reasoning capabilities."

# Optional: Custom branding
NEXT_PUBLIC_DEFAULT_SYSTEM_PROMPT="You are a Neural Symbolic Language Model with enhanced reasoning capabilities."
NEXT_PUBLIC_DEFAULT_TEMPERATURE=0.7
```

## Development Tool Integrations

### Continue.dev (VS Code)

[Continue.dev](https://continue.dev/) provides AI-powered code assistance in VS Code.

#### Configuration

```json
{
  "models": [
    {
      "title": "Neural Symbolic Language Model",
      "provider": "openai",
      "model": "gemma3n:e2b",
      "apiKey": "your-api-key",
      "apiBase": "http://localhost:8080/v1",
      "contextLength": 4096,
      "completionOptions": {
        "temperature": 0.7,
        "topP": 1.0,
        "presencePenalty": 0.0,
        "frequencyPenalty": 0.0,
        "maxTokens": 1000
      },
      "systemMessage": "You are an expert programmer with advanced symbolic reasoning capabilities. Provide clear, well-commented code solutions."
    }
  ],
  "tabAutocompleteModel": {
    "title": "Neural Symbolic Language Model",
    "provider": "openai",
    "model": "gemma3n:e2b",
    "apiKey": "your-api-key",
    "apiBase": "http://localhost:8080/v1",
    "completionOptions": {
      "temperature": 0.3,
      "maxTokens": 100
    }
  },
  "embeddingsProvider": {
    "provider": "openai",
    "model": "text-embedding-ada-002",
    "apiKey": "your-api-key",
    "apiBase": "http://localhost:8080/v1"
  }
}
```

### Cursor IDE

For [Cursor](https://cursor.sh/) IDE integration:

#### Settings Configuration

```json
{
  "cursor.general.enableCodeActions": true,
  "cursor.cpp.disabledLanguages": [],
  "cursor.chat.openaiApiKey": "your-api-key",
  "cursor.chat.openaiBaseUrl": "http://localhost:8080/v1",
  "cursor.chat.defaultModel": "gemma3n:e2b",
  "cursor.chat.systemPrompt": "You are an AI programming assistant with advanced symbolic reasoning. Help write clean, efficient, and well-documented code."
}
```

## Programming SDK Integrations

### Python OpenAI SDK

```python
import openai
from typing import List, Dict, Any

class NeuralSymbolicClient:
    """Client wrapper for Neural Symbolic Language Model."""
    
    def __init__(self, api_key: str, base_url: str = "http://localhost:8080/v1"):
        """Initialize the client.
        
        Args:
            api_key: API key for authentication
            base_url: Base URL for the API
        """
        openai.api_key = api_key
        openai.api_base = base_url
        self.model = "gemma3n:e2b"
    
    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Send a chat completion request.
        
        Args:
            messages: List of message dictionaries
            **kwargs: Additional parameters for the API call
            
        Returns:
            Response content as string
        """
        response = openai.ChatCompletion.create(
            model=self.model,
            messages=messages,
            temperature=kwargs.get('temperature', 0.7),
            max_tokens=kwargs.get('max_tokens', 1000),
            **kwargs
        )
        return response.choices[0].message.content
    
    def reasoning_chat(self, query: str, context: str = None) -> str:
        """Specialized method for reasoning tasks.
        
        Args:
            query: The reasoning question or problem
            context: Optional context for the reasoning task
            
        Returns:
            Reasoning response
        """
        messages = [
            {
                "role": "system", 
                "content": "You are an expert in symbolic reasoning and logic. Provide step-by-step explanations for your reasoning process."
            }
        ]
        
        if context:
            messages.append({"role": "user", "content": f"Context: {context}"})
        
        messages.append({"role": "user", "content": query})
        
        return self.chat(messages, temperature=0.3)

# Usage example
client = NeuralSymbolicClient("your-api-key")

# Basic chat
response = client.chat([
    {"role": "user", "content": "Explain the concept of symbolic AI"}
])
print(response)

# Reasoning task
reasoning_response = client.reasoning_chat(
    "If all birds can fly and penguins are birds, what can we conclude about penguins?",
    "Consider both the logical structure and real-world knowledge."
)
print(reasoning_response)
```

### JavaScript/Node.js SDK

```javascript
const { Configuration, OpenAIApi } = require("openai");

class NeuralSymbolicClient {
  constructor(apiKey, baseURL = "http://localhost:8080/v1") {
    const configuration = new Configuration({
      apiKey: apiKey,
      basePath: baseURL
    });
    this.openai = new OpenAIApi(configuration);
    this.model = "gemma3n:e2b";
  }

  async chat(messages, options = {}) {
    try {
      const response = await this.openai.createChatCompletion({
        model: this.model,
        messages: messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
        ...options
      });
      
      return response.data.choices[0].message.content;
    } catch (error) {
      console.error("Error in chat completion:", error);
      throw error;
    }
  }

  async reasoningChat(query, context = null) {
    const messages = [
      {
        role: "system",
        content: "You are an expert in symbolic reasoning and logic. Provide step-by-step explanations."
      }
    ];

    if (context) {
      messages.push({ role: "user", content: `Context: ${context}` });
    }

    messages.push({ role: "user", content: query });

    return await this.chat(messages, { temperature: 0.3 });
  }

  async streamChat(messages, onChunk, options = {}) {
    try {
      const response = await this.openai.createChatCompletion({
        model: this.model,
        messages: messages,
        stream: true,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 1000,
        ...options
      });

      response.data.on('data', (chunk) => {
        const lines = chunk.toString().split('\n').filter(line => line.trim() !== '');
        
        for (const line of lines) {
          const message = line.replace(/^data: /, '');
          if (message === '[DONE]') return;
          
          try {
            const parsed = JSON.parse(message);
            const content = parsed.choices[0]?.delta?.content;
            if (content) {
              onChunk(content);
            }
          } catch (error) {
            // Skip invalid JSON
          }
        }
      });
    } catch (error) {
      console.error("Error in streaming chat:", error);
      throw error;
    }
  }
}

// Usage example
const client = new NeuralSymbolicClient("your-api-key");

// Basic chat
client.chat([
  { role: "user", content: "What is machine learning?" }
]).then(response => {
  console.log(response);
});

// Streaming chat
client.streamChat([
  { role: "user", content: "Explain neural networks step by step" }
], (chunk) => {
  process.stdout.write(chunk);
});

// Reasoning task
client.reasoningChat(
  "Compare deductive and inductive reasoning with examples"
).then(response => {
  console.log("Reasoning Response:", response);
});
```

## Custom Application Integration

### REST API Integration

For applications that don't use OpenAI SDKs:

```python
import requests
import json

class CustomNeuralSymbolicClient:
    """Custom client for direct REST API integration."""
    
    def __init__(self, api_key: str, base_url: str = "http://localhost:8080"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def health_check(self) -> bool:
        """Check if the API is healthy."""
        try:
            response = requests.get(f"{self.base_url}/health")
            return response.status_code == 200
        except:
            return False
    
    def chat_completion(self, messages: list, **kwargs) -> dict:
        """Send a chat completion request."""
        payload = {
            "model": "gemma3n:e2b",
            "messages": messages,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/v1/chat/completions",
            headers=self.headers,
            json=payload
        )
        
        response.raise_for_status()
        return response.json()
    
    def add_document(self, content: str, metadata: dict = None) -> dict:
        """Add a document to the knowledge base."""
        payload = {
            "content": content,
            "metadata": metadata or {}
        }
        
        response = requests.post(
            f"{self.base_url}/documents/add",
            headers=self.headers,
            json=payload
        )
        
        response.raise_for_status()
        return response.json()
    
    def get_performance_metrics(self) -> dict:
        """Get performance metrics."""
        response = requests.get(
            f"{self.base_url}/performance",
            headers=self.headers
        )
        
        response.raise_for_status()
        return response.json()

# Usage example
client = CustomNeuralSymbolicClient("your-api-key")

# Health check
if client.health_check():
    print("✅ API is healthy")
    
    # Chat completion
    response = client.chat_completion([
        {"role": "user", "content": "Explain symbolic reasoning"}
    ])
    print(response["choices"][0]["message"]["content"])
    
    # Add document
    doc_response = client.add_document(
        "Symbolic reasoning combines logic with AI to enable explainable decision-making.",
        {"category": "ai_concepts", "source": "documentation"}
    )
    print(f"Document added: {doc_response['document_id']}")
    
    # Get metrics
    metrics = client.get_performance_metrics()
    print(f"Cache hit rate: {metrics['cache_stats']['hit_rate']:.2%}")
else:
    print("❌ API is not accessible")
```

## Troubleshooting

### Common Issues

**1. Connection Refused**
```bash
# Check if the API is running
curl http://localhost:8080/health

# Check if port is in use
netstat -an | grep 8080
```

**2. Authentication Errors**
```bash
# Verify API key format
echo $SECURITY_API_KEYS_JSON | jq .

# Test authentication
curl -H "Authorization: Bearer your-api-key" http://localhost:8080/v1/models
```

**3. Model Not Found**
```bash
# Check available models
ollama list

# Pull model if missing
ollama pull gemma3n:e2b
```

**4. Performance Issues**
```bash
# Check system resources
htop
nvidia-smi

# Monitor API performance
curl http://localhost:8080/performance
```

### Best Practices

1. **Use connection pooling** for high-throughput applications
2. **Implement retry logic** with exponential backoff
3. **Monitor API health** and performance metrics
4. **Cache responses** when appropriate
5. **Use streaming** for long responses
6. **Set appropriate timeouts** for your use case
7. **Handle rate limits** gracefully

## Support

For integration issues:
1. Check the [API documentation](api.md)
2. Review the [troubleshooting guide](deployment_guide.md#troubleshooting)
3. Test with the [validation suite](../scripts/validation_suite.py)
4. Open an issue on GitHub with integration details
