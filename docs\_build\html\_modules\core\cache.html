

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>core.cache &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../../_static/custom.css?v=39bd3b11" />

  
      <script src="../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../../_static/documentation_options.js?v=01f34227"></script>
      <script src="../../_static/doctools.js?v=9bcbadda"></script>
      <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../index.html">Module code</a></li>
      <li class="breadcrumb-item active">core.cache</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for core.cache</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Advanced caching system for the Neural Symbolic Language Model.</span>

<span class="sd">This module provides LRU cache with TTL support, memory management,</span>
<span class="sd">and optional Redis backend for distributed caching.</span>

<span class="sd">Author: AI Assistant</span>
<span class="sd">Date: 2025-06-29</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">threading</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Any</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Tuple</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">collections</span><span class="w"> </span><span class="kn">import</span> <span class="n">OrderedDict</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">hashlib</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">json</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>


<div class="viewcode-block" id="LRUCache">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">LRUCache</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Thread-safe LRU cache with TTL support.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="LRUCache.__init__">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">max_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1000</span><span class="p">,</span> <span class="n">ttl_seconds</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">3600</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the LRU cache.</span>

<span class="sd">        Args:</span>
<span class="sd">            max_size: Maximum number of items to cache</span>
<span class="sd">            ttl_seconds: Time-to-live for cache entries in seconds</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">max_size</span> <span class="o">=</span> <span class="n">max_size</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">ttl_seconds</span> <span class="o">=</span> <span class="n">ttl_seconds</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">:</span> <span class="n">OrderedDict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Tuple</span><span class="p">[</span><span class="n">Any</span><span class="p">,</span> <span class="nb">float</span><span class="p">]]</span> <span class="o">=</span> <span class="n">OrderedDict</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">RLock</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_hits</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_misses</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;LRU Cache initialized: max_size=</span><span class="si">{</span><span class="n">max_size</span><span class="si">}</span><span class="s2">, ttl=</span><span class="si">{</span><span class="n">ttl_seconds</span><span class="si">}</span><span class="s2">s&quot;</span><span class="p">)</span></div>


    <span class="k">def</span><span class="w"> </span><span class="nf">_is_expired</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">timestamp</span><span class="p">:</span> <span class="nb">float</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check if a cache entry is expired.</span>

<span class="sd">        Args:</span>
<span class="sd">            timestamp: Entry timestamp</span>

<span class="sd">        Returns:</span>
<span class="sd">            True if expired, False otherwise</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">timestamp</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">ttl_seconds</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_generate_key</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Generate a string key from any hashable object.</span>

<span class="sd">        Args:</span>
<span class="sd">            key: The key to hash</span>

<span class="sd">        Returns:</span>
<span class="sd">            String representation of the key</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">key</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="p">(</span><span class="nb">dict</span><span class="p">,</span> <span class="nb">list</span><span class="p">)):</span>
            <span class="c1"># For complex objects, create a hash</span>
            <span class="n">key_str</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">sort_keys</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">md5</span><span class="p">(</span><span class="n">key_str</span><span class="o">.</span><span class="n">encode</span><span class="p">())</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>

<div class="viewcode-block" id="LRUCache.get">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache.get">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Any</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get a value from the cache.</span>

<span class="sd">        Args:</span>
<span class="sd">            key: The cache key</span>

<span class="sd">        Returns:</span>
<span class="sd">            The cached value or None if not found/expired</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="n">str_key</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_generate_key</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">str_key</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_misses</span> <span class="o">+=</span> <span class="mi">1</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache miss for key: </span><span class="si">{</span><span class="n">str_key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="kc">None</span>

            <span class="n">value</span><span class="p">,</span> <span class="n">timestamp</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">[</span><span class="n">str_key</span><span class="p">]</span>

            <span class="c1"># Check if expired</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_is_expired</span><span class="p">(</span><span class="n">timestamp</span><span class="p">):</span>
                <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">[</span><span class="n">str_key</span><span class="p">]</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_misses</span> <span class="o">+=</span> <span class="mi">1</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache expired for key: </span><span class="si">{</span><span class="n">str_key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="kc">None</span>

            <span class="c1"># Move to end (most recently used)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="o">.</span><span class="n">move_to_end</span><span class="p">(</span><span class="n">str_key</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_hits</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache hit for key: </span><span class="si">{</span><span class="n">str_key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">value</span></div>


<div class="viewcode-block" id="LRUCache.set">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache.set">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">set</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="n">Any</span><span class="p">,</span> <span class="n">value</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Set a value in the cache.</span>

<span class="sd">        Args:</span>
<span class="sd">            key: The cache key</span>
<span class="sd">            value: The value to cache</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="n">str_key</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_generate_key</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
            <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

            <span class="c1"># If key exists, update it</span>
            <span class="k">if</span> <span class="n">str_key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">[</span><span class="n">str_key</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">current_time</span><span class="p">)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="o">.</span><span class="n">move_to_end</span><span class="p">(</span><span class="n">str_key</span><span class="p">)</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache updated for key: </span><span class="si">{</span><span class="n">str_key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                <span class="k">return</span>

            <span class="c1"># If at capacity, remove least recently used</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_size</span><span class="p">:</span>
                <span class="n">oldest_key</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="nb">iter</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">))</span>
                <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">[</span><span class="n">oldest_key</span><span class="p">]</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache evicted LRU key: </span><span class="si">{</span><span class="n">oldest_key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="c1"># Add new entry</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">[</span><span class="n">str_key</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">current_time</span><span class="p">)</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache set for key: </span><span class="si">{</span><span class="n">str_key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="LRUCache.delete">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache.delete">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">delete</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Delete a key from the cache.</span>

<span class="sd">        Args:</span>
<span class="sd">            key: The cache key to delete</span>

<span class="sd">        Returns:</span>
<span class="sd">            True if key was deleted, False if not found</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="n">str_key</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_generate_key</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">str_key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">:</span>
                <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">[</span><span class="n">str_key</span><span class="p">]</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache deleted key: </span><span class="si">{</span><span class="n">str_key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="kc">True</span>
            <span class="k">return</span> <span class="kc">False</span></div>


<div class="viewcode-block" id="LRUCache.clear">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache.clear">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">clear</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Clear all entries from the cache.&quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_hits</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_misses</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Cache cleared&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="LRUCache.cleanup_expired">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache.cleanup_expired">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">cleanup_expired</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Remove expired entries from the cache.</span>

<span class="sd">        Returns:</span>
<span class="sd">            Number of entries removed</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
            <span class="n">expired_keys</span> <span class="o">=</span> <span class="p">[]</span>

            <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="p">(</span><span class="n">_</span><span class="p">,</span> <span class="n">timestamp</span><span class="p">)</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
                <span class="k">if</span> <span class="n">current_time</span> <span class="o">-</span> <span class="n">timestamp</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">ttl_seconds</span><span class="p">:</span>
                    <span class="n">expired_keys</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>

            <span class="k">for</span> <span class="n">key</span> <span class="ow">in</span> <span class="n">expired_keys</span><span class="p">:</span>
                <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">[</span><span class="n">key</span><span class="p">]</span>

            <span class="k">if</span> <span class="n">expired_keys</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache cleanup removed </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">expired_keys</span><span class="p">)</span><span class="si">}</span><span class="s2"> expired entries&quot;</span><span class="p">)</span>

            <span class="k">return</span> <span class="nb">len</span><span class="p">(</span><span class="n">expired_keys</span><span class="p">)</span></div>


<div class="viewcode-block" id="LRUCache.size">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache.size">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">size</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get the current cache size.</span>

<span class="sd">        Returns:</span>
<span class="sd">            Number of entries in the cache</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">)</span></div>


<div class="viewcode-block" id="LRUCache.stats">
<a class="viewcode-back" href="../../modules.html#core.cache.LRUCache.stats">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">stats</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get cache statistics.</span>

<span class="sd">        Returns:</span>
<span class="sd">            Dictionary with cache statistics</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="n">total_requests</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_hits</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">_misses</span>
            <span class="n">hit_rate</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_hits</span> <span class="o">/</span> <span class="n">total_requests</span> <span class="k">if</span> <span class="n">total_requests</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="mi">0</span>

            <span class="k">return</span> <span class="p">{</span>
                <span class="s2">&quot;size&quot;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_cache</span><span class="p">),</span>
                <span class="s2">&quot;max_size&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_size</span><span class="p">,</span>
                <span class="s2">&quot;hits&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">_hits</span><span class="p">,</span>
                <span class="s2">&quot;misses&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">_misses</span><span class="p">,</span>
                <span class="s2">&quot;hit_rate&quot;</span><span class="p">:</span> <span class="n">hit_rate</span><span class="p">,</span>
                <span class="s2">&quot;ttl_seconds&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">ttl_seconds</span>
            <span class="p">}</span></div>
</div>



<div class="viewcode-block" id="CacheManager">
<a class="viewcode-back" href="../../modules.html#core.cache.CacheManager">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">CacheManager</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Manages multiple cache instances and provides unified interface.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="CacheManager.__init__">
<a class="viewcode-back" href="../../modules.html#core.cache.CacheManager.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">default_max_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1000</span><span class="p">,</span> <span class="n">default_ttl</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">3600</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the cache manager.</span>

<span class="sd">        Args:</span>
<span class="sd">            default_max_size: Default maximum size for new caches</span>
<span class="sd">            default_ttl: Default TTL for new caches</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">default_max_size</span> <span class="o">=</span> <span class="n">default_max_size</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">default_ttl</span> <span class="o">=</span> <span class="n">default_ttl</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">LRUCache</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">RLock</span><span class="p">()</span>

        <span class="c1"># Create default cache</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="p">[</span><span class="s2">&quot;default&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">LRUCache</span><span class="p">(</span><span class="n">default_max_size</span><span class="p">,</span> <span class="n">default_ttl</span><span class="p">)</span>

        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Cache manager initialized&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="CacheManager.get_cache">
<a class="viewcode-back" href="../../modules.html#core.cache.CacheManager.get_cache">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_cache</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;default&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">LRUCache</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get a named cache instance.</span>

<span class="sd">        Args:</span>
<span class="sd">            name: Cache name</span>

<span class="sd">        Returns:</span>
<span class="sd">            LRUCache instance</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">name</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="p">[</span><span class="n">name</span><span class="p">]</span> <span class="o">=</span> <span class="n">LRUCache</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">default_max_size</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">default_ttl</span><span class="p">)</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Created new cache: </span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="p">[</span><span class="n">name</span><span class="p">]</span></div>


<div class="viewcode-block" id="CacheManager.create_cache">
<a class="viewcode-back" href="../../modules.html#core.cache.CacheManager.create_cache">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">create_cache</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">max_size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">ttl_seconds</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">LRUCache</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create a new named cache with specific settings.</span>

<span class="sd">        Args:</span>
<span class="sd">            name: Cache name</span>
<span class="sd">            max_size: Maximum cache size</span>
<span class="sd">            ttl_seconds: TTL in seconds</span>

<span class="sd">        Returns:</span>
<span class="sd">            LRUCache instance</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="p">[</span><span class="n">name</span><span class="p">]</span> <span class="o">=</span> <span class="n">LRUCache</span><span class="p">(</span><span class="n">max_size</span><span class="p">,</span> <span class="n">ttl_seconds</span><span class="p">)</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Created cache &#39;</span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2">&#39;: max_size=</span><span class="si">{</span><span class="n">max_size</span><span class="si">}</span><span class="s2">, ttl=</span><span class="si">{</span><span class="n">ttl_seconds</span><span class="si">}</span><span class="s2">s&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="p">[</span><span class="n">name</span><span class="p">]</span></div>


<div class="viewcode-block" id="CacheManager.cleanup_all">
<a class="viewcode-back" href="../../modules.html#core.cache.CacheManager.cleanup_all">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">cleanup_all</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Cleanup expired entries from all caches.</span>

<span class="sd">        Returns:</span>
<span class="sd">            Dictionary mapping cache names to number of entries removed</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">results</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">name</span><span class="p">,</span> <span class="n">cache</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
                <span class="n">removed</span> <span class="o">=</span> <span class="n">cache</span><span class="o">.</span><span class="n">cleanup_expired</span><span class="p">()</span>
                <span class="n">results</span><span class="p">[</span><span class="n">name</span><span class="p">]</span> <span class="o">=</span> <span class="n">removed</span>

        <span class="n">total_removed</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">results</span><span class="o">.</span><span class="n">values</span><span class="p">())</span>
        <span class="k">if</span> <span class="n">total_removed</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache cleanup removed </span><span class="si">{</span><span class="n">total_removed</span><span class="si">}</span><span class="s2"> total expired entries&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">results</span></div>


<div class="viewcode-block" id="CacheManager.get_all_stats">
<a class="viewcode-back" href="../../modules.html#core.cache.CacheManager.get_all_stats">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_all_stats</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get statistics for all caches.</span>

<span class="sd">        Returns:</span>
<span class="sd">            Dictionary mapping cache names to their statistics</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="k">return</span> <span class="p">{</span><span class="n">name</span><span class="p">:</span> <span class="n">cache</span><span class="o">.</span><span class="n">stats</span><span class="p">()</span> <span class="k">for</span> <span class="n">name</span><span class="p">,</span> <span class="n">cache</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_caches</span><span class="o">.</span><span class="n">items</span><span class="p">()}</span></div>
</div>



<span class="c1"># Global cache manager instance</span>
<span class="n">cache_manager</span> <span class="o">=</span> <span class="n">CacheManager</span><span class="p">()</span>


<div class="viewcode-block" id="get_cache">
<a class="viewcode-back" href="../../modules.html#core.cache.get_cache">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_cache</span><span class="p">(</span><span class="n">name</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;default&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">LRUCache</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get a cache instance by name.</span>

<span class="sd">    Args:</span>
<span class="sd">        name: Cache name</span>

<span class="sd">    Returns:</span>
<span class="sd">        LRUCache instance</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">cache_manager</span><span class="o">.</span><span class="n">get_cache</span><span class="p">(</span><span class="n">name</span><span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>