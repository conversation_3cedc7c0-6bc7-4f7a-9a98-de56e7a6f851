"""Test configuration and fixtures."""

import pytest
from unittest.mock import MagicMock, patch
import sys
import tempfile
import shutil
import asyncio
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.append(str(src_path))

# Mock dependencies
class MockSymbolicReasoner:
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu
    
    def process_query(self, query, context=None):
        return "Test response"
    
    def get_system_info(self):
        return {
            "engine": "test",
            "model": "test",
            "gpu_enabled": False,
            "gpu_available": False,
            "gpu_name": None
        }

class MockRetriever:
    def __init__(self, use_gpu=True):
        self.use_gpu = use_gpu
        self.documents = {}
    
    def add_documents(self, documents):
        for doc in documents:
            self.documents[doc['id']] = doc['text']
    
    def search(self, query, k=5):
        return [{"text": "Test document", "score": 0.95}]
    
    def get_system_info(self):
        return {
            "vector_db": "test",
            "gpu_enabled": False,
            "gpu_available": False,
            "gpu_name": None,
            "index_size": len(self.documents)
        }

@pytest.fixture
def mock_reasoner():
    return MockSymbolicReasoner()

@pytest.fixture
def mock_retriever():
    return MockRetriever()

# Mock the imports in main.py
sys.modules['symbolic_reasoning'] = MagicMock()
sys.modules['symbolic_reasoning'].SymbolicReasoner = MockSymbolicReasoner
sys.modules['retrieval'] = MagicMock()
sys.modules['retrieval'].Retriever = MockRetriever

# Additional comprehensive fixtures

@pytest.fixture
def sample_documents():
    """Sample documents for testing retrieval."""
    return [
        {
            "id": "doc1",
            "text": "Neural networks are computational models inspired by biological neural networks.",
            "metadata": {"source": "textbook", "chapter": 1}
        },
        {
            "id": "doc2",
            "text": "Symbolic AI uses symbols and rules to represent knowledge and reasoning.",
            "metadata": {"source": "paper", "year": 2023}
        },
        {
            "id": "doc3",
            "text": "Machine learning algorithms can learn patterns from data automatically.",
            "metadata": {"source": "article", "topic": "ML"}
        }
    ]

@pytest.fixture
def sample_chat_messages():
    """Sample chat messages for testing."""
    return [
        {"role": "system", "content": "You are a helpful AI assistant."},
        {"role": "user", "content": "What is artificial intelligence?"},
        {"role": "assistant", "content": "AI is the simulation of human intelligence in machines."},
        {"role": "user", "content": "How does machine learning work?"}
    ]

@pytest.fixture
def mock_api_key():
    """Mock API key for testing authentication."""
    return "test-api-key-12345"

@pytest.fixture
def temp_directory():
    """Temporary directory for testing file operations."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)

@pytest.fixture
def mock_gpu_available():
    """Mock GPU availability."""
    with patch('torch.cuda.is_available', return_value=True):
        with patch('torch.cuda.get_device_name', return_value="Mock GPU"):
            yield

@pytest.fixture
def mock_gpu_unavailable():
    """Mock GPU unavailability."""
    with patch('torch.cuda.is_available', return_value=False):
        yield

@pytest.fixture
def mock_cache():
    """Mock cache for testing."""
    cache_mock = MagicMock()
    cache_mock.get.return_value = None
    cache_mock.set.return_value = None
    cache_mock.stats.return_value = {
        "size": 0,
        "hits": 0,
        "misses": 0,
        "hit_rate": 0.0
    }
    return cache_mock

@pytest.fixture
def mock_settings():
    """Mock application settings."""
    settings_mock = MagicMock()
    settings_mock.model.reasoning_engine = "local"
    settings_mock.model.reasoning_model = "test"
    settings_mock.model.use_gpu = False
    settings_mock.model.vector_db_backend = "faiss"
    settings_mock.security.api_keys = {"test": "test-api-key-12345"}
    settings_mock.security.rate_limit_requests = 100
    settings_mock.cache.max_size = 1000
    settings_mock.cache.ttl_seconds = 3600
    return settings_mock

@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def mock_fastapi_request():
    """Mock FastAPI request object."""
    request_mock = MagicMock()
    request_mock.client.host = "127.0.0.1"
    request_mock.headers = {"content-length": "100"}
    return request_mock

# Performance testing fixtures
@pytest.fixture
def performance_test_data():
    """Large dataset for performance testing."""
    return {
        "queries": [f"Test query {i}" for i in range(100)],
        "documents": [f"Test document {i} with content" for i in range(1000)]
    }

# Security testing fixtures
@pytest.fixture
def malicious_inputs():
    """Malicious inputs for security testing."""
    return [
        "<script>alert('xss')</script>",
        "'; DROP TABLE users; --",
        "../../../etc/passwd",
        "A" * 100000,  # Very long input
        "\x00\x01\x02",  # Binary data
        "{{7*7}}",  # Template injection
    ]

# Error simulation fixtures
@pytest.fixture
def mock_network_error():
    """Mock network errors."""
    def side_effect(*args, **kwargs):
        raise ConnectionError("Network error")
    return side_effect

@pytest.fixture
def mock_timeout_error():
    """Mock timeout errors."""
    def side_effect(*args, **kwargs):
        raise TimeoutError("Operation timed out")
    return side_effect

# Pytest markers for test categorization
pytest_plugins = ["pytest_asyncio"]
