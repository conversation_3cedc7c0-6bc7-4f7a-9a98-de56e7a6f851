Search.setIndex({"alltitles": {"API Key Authentication": [[3, "api-key-authentication"]], "API Key Issues": [[9, "api-key-issues"]], "API Key Management": [[3, "api-key-management"]], "API Reference": [[5, null], [10, null], [11, null]], "API Routes": [[11, "api-routes"]], "API Security": [[8, "api-security"]], "Advanced Configuration": [[4, "advanced-configuration"]], "Advanced Search": [[2, "advanced-search"]], "Anthropic Engine": [[4, "anthropic-engine"]], "Architecture": [[10, "architecture"]], "Architecture Overview": [[6, null]], "Authentication": [[3, "authentication"], [5, "authentication"]], "Auto-scaling": [[8, "auto-scaling"]], "Backup and Recovery": [[8, "backup-and-recovery"]], "Base URL": [[5, "base-url"]], "Basic Document Retrieval": [[2, "basic-document-retrieval"]], "Basic Usage": [[4, "basic-usage"]], "Batch Processing": [[2, "batch-processing"], [2, "id19"], [4, "batch-processing"], [4, "id12"]], "Best Practices": [[1, "best-practices"], [2, "best-practices"], [3, "best-practices"], [7, "best-practices"]], "Blue-Green Deployment": [[6, "blue-green-deployment"]], "Built-in Validation": [[7, "built-in-validation"]], "CORS Configuration": [[3, "cors-configuration"], [3, "id22"]], "CORS Support": [[5, "cors-support"]], "Cache Configuration": [[7, "cache-configuration"]], "Caching": [[2, "caching"], [4, "caching"]], "Caching Strategy": [[6, "caching-strategy"]], "Canary Deployment": [[6, "canary-deployment"]], "Chat Completions": [[0, "chat-completions"]], "Chat Completions API": [[5, "chat-completions-api"]], "Chat Endpoints": [[11, "chat-endpoints"]], "ChatCompletionChunk": [[1, "chatcompletionchunk"]], "ChatCompletionRequest": [[1, "chatcompletionrequest"]], "ChatCompletionResponse": [[1, "chatcompletionresponse"]], "Choice": [[1, "choice"]], "ChoiceDelta": [[1, "choicedelta"]], "ChromaDB Backend": [[2, "chromadb-backend"]], "Client IP Detection": [[3, "client-ip-detection"]], "Common Issues": [[7, "common-issues"], [8, "common-issues"], [9, "common-issues"]], "Common Parameters": [[5, "common-parameters"]], "Complete Request/Response Cycle": [[1, "complete-request-response-cycle"]], "Complete Security Setup": [[3, "complete-security-setup"]], "Configuration": [[0, "configuration"], [2, "configuration"], [4, "configuration"]], "Configuration Files": [[7, "configuration-files"]], "Configuration Guide": [[7, null]], "Configuration Management": [[6, "configuration-management"], [11, "module-core.cache"]], "Configuration Templates": [[7, "configuration-templates"]], "Configuration Validation": [[7, "configuration-validation"]], "Core Application Settings": [[7, "core-application-settings"]], "Core Components": [[6, "core-components"], [10, "core-components"], [11, "module-main"]], "Core Methods": [[2, "core-methods"], [3, "core-methods"], [4, "core-methods"]], "Core Modules": [[11, "core-modules"]], "Custom Validation": [[7, "custom-validation"]], "Custom Validators": [[1, "custom-validators"]], "Data Backup": [[8, "data-backup"]], "Data Flow": [[6, "data-flow"]], "Data Models Module": [[1, null]], "Database Configuration": [[8, "database-configuration"]], "Default Limits": [[5, "default-limits"]], "Defense in Depth": [[6, "defense-in-depth"]], "Dependencies": [[9, "dependencies"]], "Deployment Guide": [[8, null]], "Deployment Options": [[8, "deployment-options"]], "Deployment Patterns": [[6, "deployment-patterns"]], "Developer Guide": [[10, null]], "Development Environment": [[7, "development-environment"]], "Dictionary Conversion": [[1, "dictionary-conversion"]], "Disaster Recovery": [[8, "disaster-recovery"]], "Distributed Tracing": [[6, "distributed-tracing"]], "Docker Compose": [[8, "docker-compose"]], "Docker Configuration": [[7, "docker-configuration"]], "Docker Deployment": [[8, "docker-deployment"]], "Docker Swarm": [[8, "docker-swarm"]], "Document Preparation": [[2, "document-preparation"]], "Embedding Models": [[2, "embedding-models"]], "Endpoints": [[0, "endpoints"]], "Environment Management": [[7, "environment-management"]], "Environment Setup": [[7, "environment-setup"]], "Environment Variables": [[7, "environment-variables"], [8, "environment-variables"]], "Environment-Specific YAML Files": [[7, "environment-specific-yaml-files"]], "Error Codes": [[5, "error-codes"]], "Error Format": [[5, "error-format"]], "Error Handling": [[0, "error-handling"], [2, "error-handling"], [3, "error-handling"], [4, "error-handling"]], "Error Models": [[1, "error-models"]], "Error Responses": [[5, "error-responses"]], "ErrorDetail": [[1, "errordetail"]], "ErrorResponse": [[1, "errorresponse"]], "Example Usage": [[0, "example-usage"], [1, "example-usage"], [2, "example-usage"], [3, "example-usage"], [4, "example-usage"]], "Exception Handling": [[11, "module-exceptions"]], "FAISS Backend": [[2, "faiss-backend"]], "Failed Attempt Tracking": [[3, "failed-attempt-tracking"]], "FastAPI Application": [[0, "fastapi-application"]], "FastAPI Application Layer": [[6, "fastapi-application-layer"]], "Features": [[10, "features"]], "Field Validation": [[1, "field-validation"]], "First API Call": [[9, "first-api-call"]], "Future Enhancements": [[6, "future-enhancements"]], "GPU Acceleration": [[2, "gpu-acceleration"]], "GPU Not Available": [[9, "gpu-not-available"]], "GPU Usage": [[4, "gpu-usage"]], "Generate Environment Template": [[7, "generate-environment-template"]], "Getting Help": [[9, "getting-help"]], "Getting Started": [[9, null]], "Grafana Dashboard": [[8, "grafana-dashboard"]], "HAProxy Configuration": [[8, "haproxy-configuration"]], "Health Check": [[5, "health-check"], [9, "health-check"]], "Health Checks": [[0, "health-checks"]], "High-Level Architecture": [[6, "high-level-architecture"]], "Horizontal Scaling": [[6, "horizontal-scaling"]], "IP Blocking": [[3, "ip-blocking"]], "Index Optimization": [[2, "index-optimization"]], "Indices and Tables": [[10, "indices-and-tables"]], "Initialization": [[2, "initialization"], [3, "initialization"], [4, "initialization"]], "Input Sanitization": [[3, "input-sanitization"]], "Input Validation": [[3, "input-validation"], [3, "id21"]], "JSON Serialization": [[1, "json-serialization"]], "Key Features": [[9, "key-features"]], "Kubernetes Deployment": [[8, "kubernetes-deployment"]], "Kubernetes HPA": [[8, "kubernetes-hpa"]], "License": [[10, "license"]], "Load Balancing": [[8, "load-balancing"]], "Loading Configuration Files": [[7, "loading-configuration-files"]], "Local Development": [[8, "local-development"]], "Local Engine": [[4, "local-engine"]], "Logging": [[8, "logging"]], "Logging Configuration": [[7, "logging-configuration"]], "Logging Strategy": [[6, "logging-strategy"]], "Main Application Module": [[0, null]], "Message": [[1, "message"]], "Message Format": [[5, "message-format"]], "Metrics Collection": [[6, "metrics-collection"]], "Missing Dependencies": [[9, "missing-dependencies"]], "Model Configuration": [[7, "model-configuration"]], "Model Design": [[1, "model-design"]], "Monitoring": [[7, "monitoring"]], "Monitoring & Observability": [[10, "monitoring-observability"]], "Monitoring Endpoints": [[11, "monitoring-endpoints"]], "Monitoring and Logging": [[11, "module-monitoring"]], "Monitoring and Observability": [[6, "monitoring-and-observability"], [8, "monitoring-and-observability"]], "Neural Symbolic Language Model Documentation": [[10, null]], "Next Steps": [[9, "next-steps"]], "Nginx Configuration": [[8, "nginx-configuration"]], "OpenAI Engine": [[4, "openai-engine"]], "OpenAI-Compatible Endpoint": [[5, "openai-compatible-endpoint"]], "OpenAPI Documentation": [[5, "openapi-documentation"]], "Origin Validation": [[3, "origin-validation"]], "Overview": [[1, "overview"], [2, "overview"], [3, "overview"], [4, "overview"], [7, "overview"], [8, "overview"], [9, "overview"], [10, "overview"]], "Performance": [[1, "performance"], [7, "performance"]], "Performance Considerations": [[4, "performance-considerations"]], "Performance Features": [[10, "performance-features"]], "Performance Metrics": [[5, "performance-metrics"], [9, "performance-metrics"]], "Performance Monitoring": [[0, "performance-monitoring"]], "Performance Monitoring API": [[5, "performance-monitoring-api"]], "Performance Optimization": [[2, "performance-optimization"], [6, "performance-optimization"]], "Performance Tuning": [[8, "performance-tuning"]], "PerformanceMetrics": [[1, "performancemetrics"]], "Planned Improvements": [[6, "planned-improvements"]], "Port Already in Use": [[9, "port-already-in-use"]], "Prerequisites": [[9, "prerequisites"]], "Production Configuration": [[8, "production-configuration"]], "Production Deployment": [[10, "production-deployment"]], "Production Environment": [[7, "production-environment"]], "Prometheus Metrics": [[8, "prometheus-metrics"]], "Query Optimization": [[2, "query-optimization"]], "Quick Installation": [[9, "quick-installation"]], "Quick Start": [[10, "quick-start"]], "REST API Endpoints": [[5, "rest-api-endpoints"]], "Rate Limit Headers": [[5, "rate-limit-headers"]], "Rate Limiting": [[3, "rate-limiting"], [3, "id20"], [5, "rate-limiting"]], "RateLimiter Class": [[3, "ratelimiter-class"]], "Readiness Check": [[5, "readiness-check"]], "Request Models": [[1, "request-models"]], "Request Parameters": [[5, "request-parameters"]], "Request Processing Flow": [[6, "request-processing-flow"]], "Request Size Limits": [[3, "request-size-limits"]], "Response Models": [[1, "response-models"]], "Retriever Class": [[2, "retriever-class"]], "SSL/TLS Configuration": [[8, "ssl-tls-configuration"]], "Scalability Design": [[6, "scalability-design"]], "Scaling Considerations": [[2, "scaling-considerations"]], "Security": [[0, "security"], [7, "security"]], "Security Architecture": [[6, "security-architecture"]], "Security Configuration": [[7, "security-configuration"]], "Security Features": [[10, "security-features"]], "Security Hardening": [[8, "security-hardening"]], "Security Headers": [[3, "security-headers"]], "Security Layer": [[6, "security-layer"]], "Security Module": [[3, null]], "SecurityManager Class": [[3, "securitymanager-class"]], "Serialization": [[1, "serialization"]], "Single Server Deployment": [[8, "single-server-deployment"]], "Streaming Models": [[1, "streaming-models"]], "Streaming Responses": [[5, "streaming-responses"]], "Support": [[10, "support"]], "Supported Backends": [[2, "supported-backends"]], "Supported Engines": [[4, "supported-engines"]], "Symbolic Reasoning Engine": [[6, "symbolic-reasoning-engine"]], "Symbolic Reasoning Module": [[4, null]], "SymbolicReasoner Class": [[4, "symbolicreasoner-class"]], "System Architecture": [[6, "system-architecture"]], "System Endpoints": [[11, "system-endpoints"]], "System Info": [[5, "system-info"]], "System Information": [[0, "system-information"], [2, "system-information"], [9, "system-information"]], "System Information API": [[5, "system-information-api"]], "System Models": [[1, "system-models"]], "System Requirements": [[9, "system-requirements"]], "SystemInfo": [[1, "systeminfo"]], "Table of Contents": [[10, "table-of-contents"]], "Threat Model": [[6, "threat-model"]], "Troubleshooting": [[7, "troubleshooting"], [8, "troubleshooting"]], "Usage": [[1, "usage"]], "User Guide": [[10, null]], "Utility Modules": [[11, "utility-modules"]], "Validation": [[1, "validation"]], "Validation Features": [[1, "validation-features"]], "Vector Retrieval Module": [[2, null]], "Vector Retrieval System": [[6, "vector-retrieval-system"]], "Vector Storage": [[11, "module-vector_store"]], "Verification": [[9, "verification"]]}, "docnames": ["api/main", "api/models", "api/retrieval", "api/security", "api/symbolic_reasoning", "api_reference", "architecture", "configuration", "deployment", "getting_started", "index", "modules"], "envversion": {"sphinx": 64, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["api\\main.rst", "api\\models.rst", "api\\retrieval.rst", "api\\security.rst", "api\\symbolic_reasoning.rst", "api_reference.rst", "architecture.rst", "configuration.rst", "deployment.rst", "getting_started.rst", "index.rst", "modules.rst"], "indexentries": {"__format__() (models.modelrole method)": [[1, "models.ModelRole.__format__", false], [11, "models.ModelRole.__format__", false]], "__init__() (core.cache.cachemanager method)": [[11, "core.cache.CacheManager.__init__", false]], "__init__() (core.cache.lrucache method)": [[11, "core.cache.LRUCache.__init__", false]], "__init__() (exceptions.authenticationerror method)": [[11, "exceptions.AuthenticationError.__init__", false]], "__init__() (exceptions.authorizationerror method)": [[11, "exceptions.AuthorizationError.__init__", false]], "__init__() (exceptions.configurationerror method)": [[11, "exceptions.ConfigurationError.__init__", false]], "__init__() (exceptions.ratelimiterror method)": [[11, "exceptions.RateLimitError.__init__", false]], "__init__() (exceptions.reasoningerror method)": [[11, "exceptions.ReasoningError.__init__", false]], "__init__() (exceptions.resourcenotfounderror method)": [[11, "exceptions.ResourceNotFoundError.__init__", false]], "__init__() (exceptions.retrievalerror method)": [[11, "exceptions.RetrievalError.__init__", false]], "__init__() (exceptions.serviceunavailableerror method)": [[11, "exceptions.ServiceUnavailableError.__init__", false]], "__init__() (exceptions.symbolicaiexception method)": [[11, "exceptions.SymbolicAIException.__init__", false]], "__init__() (exceptions.validationerror method)": [[11, "exceptions.ValidationError.__init__", false]], "__init__() (exceptions.vectorstoreerror method)": [[11, "exceptions.VectorStoreError.__init__", false]], "__init__() (logging_config.contextfilter method)": [[11, "logging_config.ContextFilter.__init__", false]], "__init__() (logging_config.performancefilter method)": [[11, "logging_config.PerformanceFilter.__init__", false]], "__init__() (logging_config.structuredformatter method)": [[11, "logging_config.StructuredFormatter.__init__", false]], "__init__() (main.responsecache method)": [[0, "main.ResponseCache.__init__", false], [11, "main.ResponseCache.__init__", false]], "__init__() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.__init__", false]], "__init__() (monitoring.requestmetrics method)": [[11, "monitoring.RequestMetrics.__init__", false]], "__init__() (monitoring.systemmetrics method)": [[11, "monitoring.SystemMetrics.__init__", false]], "__init__() (retrieval.retriever method)": [[11, "retrieval.Retriever.__init__", false]], "__init__() (security.ratelimiter method)": [[11, "security.RateLimiter.__init__", false]], "__init__() (security.securitymanager method)": [[11, "security.SecurityManager.__init__", false]], "__init__() (symbolic_reasoning.symbolicreasoner method)": [[11, "symbolic_reasoning.SymbolicReasoner.__init__", false]], "__init__() (vector_store.torchvectorstore method)": [[11, "vector_store.TorchVectorStore.__init__", false]], "active_requests (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.active_requests", false]], "add() (vector_store.torchvectorstore method)": [[11, "vector_store.TorchVectorStore.add", false]], "add_document() (in module main)": [[0, "main.add_document", false], [11, "main.add_document", false]], "add_documents() (retrieval.retriever method)": [[11, "retrieval.Retriever.add_documents", false]], "add_security_headers() (in module main)": [[0, "main.add_security_headers", false], [11, "main.add_security_headers", false]], "api_info() (in module main)": [[0, "main.api_info", false], [11, "main.api_info", false]], "assistant (models.modelrole attribute)": [[1, "models.ModelRole.ASSISTANT", false], [11, "models.ModelRole.ASSISTANT", false]], "authentication_exception_handler() (in module main)": [[0, "main.authentication_exception_handler", false], [11, "main.authentication_exception_handler", false]], "authenticationerror": [[11, "exceptions.AuthenticationError", false]], "authorizationerror": [[11, "exceptions.AuthorizationError", false]], "batch_add_documents() (retrieval.retriever method)": [[11, "retrieval.Retriever.batch_add_documents", false]], "batch_process_queries() (symbolic_reasoning.symbolicreasoner method)": [[11, "symbolic_reasoning.SymbolicReasoner.batch_process_queries", false]], "cache (models.performancemetrics attribute)": [[1, "id33", false], [1, "id49", false], [1, "id53", false], [1, "models.PerformanceMetrics.cache", false], [11, "id33", false], [11, "models.PerformanceMetrics.cache", false]], "cache_hits (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.cache_hits", false]], "cache_misses (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.cache_misses", false]], "cache_size (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.cache_size", false]], "cached (main.legacychatresponse attribute)": [[0, "main.LegacyChatResponse.cached", false], [11, "main.LegacyChatResponse.cached", false]], "cached (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.cached", false]], "cachemanager (class in core.cache)": [[11, "core.cache.CacheManager", false]], "chat() (in module main)": [[0, "main.chat", false], [11, "main.chat", false]], "chat_stream() (in module main)": [[0, "main.chat_stream", false], [11, "main.chat_stream", false]], "chatchoice (class in models)": [[1, "models.ChatChoice", false], [11, "models.ChatChoice", false]], "chatmessage (class in models)": [[1, "models.ChatMessage", false], [11, "models.ChatMessage", false]], "chatrequest (class in models)": [[1, "models.ChatRequest", false], [11, "models.ChatRequest", false]], "chatresponse (class in models)": [[1, "models.ChatResponse", false], [11, "models.ChatResponse", false]], "check_request_size() (in module security)": [[11, "security.check_request_size", false]], "check_request_size_middleware() (in module main)": [[0, "main.check_request_size_middleware", false], [11, "main.check_request_size_middleware", false]], "choices (models.chatresponse attribute)": [[1, "id19", false], [1, "models.ChatResponse.choices", false], [11, "id19", false], [11, "models.ChatResponse.choices", false]], "choices (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.choices", false], [11, "models.OpenAIChatResponse.choices", false]], "clean() (main.responsecache method)": [[0, "main.ResponseCache.clean", false], [11, "main.ResponseCache.clean", false]], "clean_cache() (in module main)": [[0, "main.clean_cache", false], [11, "main.clean_cache", false]], "cleanup_all() (core.cache.cachemanager method)": [[11, "core.cache.CacheManager.cleanup_all", false]], "cleanup_expired() (core.cache.lrucache method)": [[11, "core.cache.LRUCache.cleanup_expired", false]], "clear() (core.cache.lrucache method)": [[11, "core.cache.LRUCache.clear", false]], "clear_context() (logging_config.contextfilter method)": [[11, "logging_config.ContextFilter.clear_context", false]], "completion_tokens (models.tokenusage attribute)": [[1, "id13", false], [1, "models.TokenUsage.completion_tokens", false], [11, "id13", false], [11, "models.TokenUsage.completion_tokens", false]], "configurationerror": [[11, "exceptions.ConfigurationError", false]], "content (models.chatmessage attribute)": [[1, "id1", false], [1, "models.ChatMessage.content", false], [11, "id1", false], [11, "models.ChatMessage.content", false]], "content (models.openaimessage attribute)": [[1, "models.OpenAIMessage.content", false], [11, "models.OpenAIMessage.content", false]], "contextfilter (class in logging_config)": [[11, "logging_config.ContextFilter", false]], "core.cache": [[11, "module-core.cache", false]], "cpu_percent (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.cpu_percent", false]], "create_cache() (core.cache.cachemanager method)": [[11, "core.cache.CacheManager.create_cache", false]], "created (models.chatresponse attribute)": [[1, "id17", false], [1, "models.ChatResponse.created", false], [11, "id17", false], [11, "models.ChatResponse.created", false]], "created (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.created", false], [11, "models.OpenAIChatResponse.created", false]], "delete() (core.cache.lrucache method)": [[11, "core.cache.LRUCache.delete", false]], "details (exceptions.symbolicaiexception attribute)": [[11, "exceptions.SymbolicAIException.details", false]], "dimension (retrieval.retriever attribute)": [[11, "retrieval.Retriever.dimension", false]], "document_id (models.documentaddrequest attribute)": [[1, "id23", false], [1, "models.DocumentAddRequest.document_id", false], [11, "id23", false], [11, "models.DocumentAddRequest.document_id", false]], "document_id (models.documentaddresponse attribute)": [[1, "id26", false], [1, "models.DocumentAddResponse.document_id", false], [11, "id26", false], [11, "models.DocumentAddResponse.document_id", false]], "documentaddrequest (class in models)": [[1, "models.DocumentAddRequest", false], [11, "models.DocumentAddRequest", false]], "documentaddresponse (class in models)": [[1, "models.DocumentAddResponse", false], [11, "models.DocumentAddResponse", false]], "documents (retrieval.retriever attribute)": [[11, "retrieval.Retriever.documents", false]], "duration() (monitoring.requestmetrics method)": [[11, "monitoring.RequestMetrics.duration", false]], "end_request() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.end_request", false]], "end_time (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.end_time", false]], "endpoint (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.endpoint", false]], "engine (symbolic_reasoning.symbolicreasoner attribute)": [[11, "symbolic_reasoning.SymbolicReasoner.engine", false]], "error (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.error", false]], "error_code (exceptions.symbolicaiexception attribute)": [[11, "exceptions.SymbolicAIException.error_code", false]], "exceptions": [[11, "module-exceptions", false]], "filter() (logging_config.contextfilter method)": [[11, "logging_config.ContextFilter.filter", false]], "filter() (logging_config.performancefilter method)": [[11, "logging_config.PerformanceFilter.filter", false]], "finish_reason (models.chatchoice attribute)": [[1, "id11", false], [1, "models.ChatChoice.finish_reason", false], [11, "id11", false], [11, "models.ChatChoice.finish_reason", false]], "finish_reason (models.openaichatchoice attribute)": [[1, "models.OpenAIChatChoice.finish_reason", false], [11, "models.OpenAIChatChoice.finish_reason", false]], "format() (logging_config.structuredformatter method)": [[11, "logging_config.StructuredFormatter.format", false]], "general_exception_handler() (in module main)": [[0, "main.general_exception_handler", false], [11, "main.general_exception_handler", false]], "get() (core.cache.lrucache method)": [[11, "core.cache.LRUCache.get", false]], "get() (main.responsecache method)": [[0, "main.ResponseCache.get", false], [11, "main.ResponseCache.get", false]], "get_all_stats() (core.cache.cachemanager method)": [[11, "core.cache.CacheManager.get_all_stats", false]], "get_cache() (core.cache.cachemanager method)": [[11, "core.cache.CacheManager.get_cache", false]], "get_cache() (in module core.cache)": [[11, "core.cache.get_cache", false]], "get_client_ip() (in module security)": [[11, "security.get_client_ip", false]], "get_cors_config() (in module security)": [[11, "security.get_cors_config", false]], "get_document_count() (in module main)": [[0, "main.get_document_count", false], [11, "main.get_document_count", false]], "get_logger() (in module logging_config)": [[11, "logging_config.get_logger", false]], "get_recent_metrics() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.get_recent_metrics", false]], "get_security_headers() (in module security)": [[11, "security.get_security_headers", false]], "get_system_info() (retrieval.retriever method)": [[11, "retrieval.Retriever.get_system_info", false]], "get_system_info() (symbolic_reasoning.symbolicreasoner method)": [[11, "symbolic_reasoning.SymbolicReasoner.get_system_info", false]], "get_system_info() (vector_store.torchvectorstore method)": [[11, "vector_store.TorchVectorStore.get_system_info", false]], "gpu_available (models.systeminfo attribute)": [[1, "id28", false], [1, "id37", false], [1, "id43", false], [1, "models.SystemInfo.gpu_available", false], [11, "id28", false], [11, "models.SystemInfo.gpu_available", false]], "gpu_memory_used (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.gpu_memory_used", false]], "gpu_name (models.systeminfo attribute)": [[1, "id29", false], [1, "id38", false], [1, "id44", false], [1, "models.SystemInfo.gpu_name", false], [11, "id29", false], [11, "models.SystemInfo.gpu_name", false]], "gpu_optimized (models.systeminfo attribute)": [[1, "id30", false], [1, "id39", false], [1, "id45", false], [1, "models.SystemInfo.gpu_optimized", false], [11, "id30", false], [11, "models.SystemInfo.gpu_optimized", false]], "gpu_utilization (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.gpu_utilization", false]], "handle_exception() (in module exceptions)": [[11, "exceptions.handle_exception", false]], "id (models.chatresponse attribute)": [[1, "id15", false], [1, "models.ChatResponse.id", false], [11, "id15", false], [11, "models.ChatResponse.id", false]], "id (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.id", false], [11, "models.OpenAIChatResponse.id", false]], "index (models.chatchoice attribute)": [[1, "id9", false], [1, "models.ChatChoice.index", false], [11, "id9", false], [11, "models.ChatChoice.index", false]], "index (models.openaichatchoice attribute)": [[1, "models.OpenAIChatChoice.index", false], [11, "models.OpenAIChatChoice.index", false]], "index (retrieval.retriever attribute)": [[11, "retrieval.Retriever.index", false]], "is_allowed() (security.ratelimiter method)": [[11, "security.RateLimiter.is_allowed", false]], "is_ip_blocked() (security.securitymanager method)": [[11, "security.SecurityManager.is_ip_blocked", false]], "legacychatrequest (class in main)": [[0, "main.LegacyChatRequest", false], [11, "main.LegacyChatRequest", false]], "legacychatresponse (class in main)": [[0, "main.LegacyChatResponse", false], [11, "main.LegacyChatResponse", false]], "logging_config": [[11, "module-logging_config", false]], "lrucache (class in core.cache)": [[11, "core.cache.LRUCache", false]], "main": [[0, "module-main", false], [11, "module-main", false]], "max_tokens (models.chatrequest attribute)": [[1, "id6", false], [1, "models.ChatRequest.max_tokens", false], [11, "id6", false], [11, "models.ChatRequest.max_tokens", false]], "max_tokens (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.max_tokens", false], [11, "models.OpenAIChatRequest.max_tokens", false]], "memory_percent (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.memory_percent", false]], "message (exceptions.symbolicaiexception attribute)": [[11, "exceptions.SymbolicAIException.message", false]], "message (models.chatchoice attribute)": [[1, "id10", false], [1, "models.ChatChoice.message", false], [11, "id10", false], [11, "models.ChatChoice.message", false]], "message (models.documentaddresponse attribute)": [[1, "id25", false], [1, "models.DocumentAddResponse.message", false], [11, "id25", false], [11, "models.DocumentAddResponse.message", false]], "message (models.openaichatchoice attribute)": [[1, "models.OpenAIChatChoice.message", false], [11, "models.OpenAIChatChoice.message", false]], "messages (models.chatrequest attribute)": [[1, "id3", false], [1, "models.ChatRequest.messages", false], [11, "id3", false], [11, "models.ChatRequest.messages", false]], "messages (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.messages", false], [11, "models.OpenAIChatRequest.messages", false]], "metadata (models.documentaddrequest attribute)": [[1, "id22", false], [1, "models.DocumentAddRequest.metadata", false], [11, "id22", false], [11, "models.DocumentAddRequest.metadata", false]], "metadata (models.documentaddresponse attribute)": [[1, "id27", false], [1, "models.DocumentAddResponse.metadata", false], [11, "id27", false], [11, "models.DocumentAddResponse.metadata", false]], "model (models.chatrequest attribute)": [[1, "id4", false], [1, "models.ChatRequest.model", false], [11, "id4", false], [11, "models.ChatRequest.model", false]], "model (models.chatresponse attribute)": [[1, "id18", false], [1, "models.ChatResponse.model", false], [11, "id18", false], [11, "models.ChatResponse.model", false]], "model (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.model", false], [11, "models.OpenAIChatRequest.model", false]], "model (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.model", false], [11, "models.OpenAIChatResponse.model", false]], "model (symbolic_reasoning.symbolicreasoner attribute)": [[11, "symbolic_reasoning.SymbolicReasoner.model", false]], "model_config (main.legacychatrequest attribute)": [[0, "main.LegacyChatRequest.model_config", false], [11, "main.LegacyChatRequest.model_config", false]], "model_config (main.legacychatresponse attribute)": [[0, "main.LegacyChatResponse.model_config", false], [11, "main.LegacyChatResponse.model_config", false]], "model_config (models.chatchoice attribute)": [[1, "models.ChatChoice.model_config", false], [11, "models.ChatChoice.model_config", false]], "model_config (models.chatmessage attribute)": [[1, "models.ChatMessage.model_config", false], [11, "models.ChatMessage.model_config", false]], "model_config (models.chatrequest attribute)": [[1, "models.ChatRequest.model_config", false], [11, "models.ChatRequest.model_config", false]], "model_config (models.chatresponse attribute)": [[1, "models.ChatResponse.model_config", false], [11, "models.ChatResponse.model_config", false]], "model_config (models.documentaddrequest attribute)": [[1, "models.DocumentAddRequest.model_config", false], [11, "models.DocumentAddRequest.model_config", false]], "model_config (models.documentaddresponse attribute)": [[1, "models.DocumentAddResponse.model_config", false], [11, "models.DocumentAddResponse.model_config", false]], "model_config (models.openaichatchoice attribute)": [[1, "models.OpenAIChatChoice.model_config", false], [11, "models.OpenAIChatChoice.model_config", false]], "model_config (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.model_config", false], [11, "models.OpenAIChatRequest.model_config", false]], "model_config (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.model_config", false], [11, "models.OpenAIChatResponse.model_config", false]], "model_config (models.openaimessage attribute)": [[1, "models.OpenAIMessage.model_config", false], [11, "models.OpenAIMessage.model_config", false]], "model_config (models.performancemetrics attribute)": [[1, "id52", false], [1, "models.PerformanceMetrics.model_config", false], [11, "models.PerformanceMetrics.model_config", false]], "model_config (models.systeminfo attribute)": [[1, "id42", false], [1, "models.SystemInfo.model_config", false], [11, "models.SystemInfo.model_config", false]], "model_config (models.tokenusage attribute)": [[1, "models.TokenUsage.model_config", false], [11, "models.TokenUsage.model_config", false]], "modelrole (class in models)": [[1, "models.ModelRole", false], [11, "models.ModelRole", false]], "models": [[1, "module-models", false], [11, "module-models", false]], "module": [[0, "module-main", false], [1, "module-models", false], [11, "module-core.cache", false], [11, "module-exceptions", false], [11, "module-logging_config", false], [11, "module-main", false], [11, "module-models", false], [11, "module-monitoring", false], [11, "module-retrieval", false], [11, "module-security", false], [11, "module-symbolic_reasoning", false], [11, "module-vector_store", false]], "monitoring": [[11, "module-monitoring", false]], "object (models.chatresponse attribute)": [[1, "id16", false], [1, "models.ChatResponse.object", false], [11, "id16", false], [11, "models.ChatResponse.object", false]], "object (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.object", false], [11, "models.OpenAIChatResponse.object", false]], "openai_chat() (in module main)": [[0, "main.openai_chat", false], [11, "main.openai_chat", false]], "openai_chat_stream() (in module main)": [[0, "main.openai_chat_stream", false], [11, "main.openai_chat_stream", false]], "openaichatchoice (class in models)": [[1, "models.OpenAIChatChoice", false], [11, "models.OpenAIChatChoice", false]], "openaichatrequest (class in models)": [[1, "models.OpenAIChatRequest", false], [11, "models.OpenAIChatRequest", false]], "openaichatresponse (class in models)": [[1, "models.OpenAIChatResponse", false], [11, "models.OpenAIChatResponse", false]], "openaimessage (class in models)": [[1, "models.OpenAIMessage", false], [11, "models.OpenAIMessage", false]], "optimize_gpu_memory() (in module main)": [[0, "main.optimize_gpu_memory", false], [11, "main.optimize_gpu_memory", false]], "optimize_index() (retrieval.retriever method)": [[11, "retrieval.Retriever.optimize_index", false]], "performance_stats() (in module main)": [[0, "main.performance_stats", false], [11, "main.performance_stats", false]], "performancefilter (class in logging_config)": [[11, "logging_config.PerformanceFilter", false]], "performancemetrics (class in models)": [[1, "id48", false], [1, "models.PerformanceMetrics", false], [11, "models.PerformanceMetrics", false]], "performancemonitor (class in monitoring)": [[11, "monitoring.PerformanceMonitor", false]], "process_query() (in module main)": [[0, "main.process_query", false], [11, "main.process_query", false]], "process_query() (symbolic_reasoning.symbolicreasoner method)": [[11, "symbolic_reasoning.SymbolicReasoner.process_query", false]], "prompt_tokens (models.tokenusage attribute)": [[1, "id12", false], [1, "models.TokenUsage.prompt_tokens", false], [11, "id12", false], [11, "models.TokenUsage.prompt_tokens", false]], "rate_limit_middleware() (in module main)": [[0, "main.rate_limit_middleware", false], [11, "main.rate_limit_middleware", false]], "ratelimiter (class in security)": [[11, "security.RateLimiter", false]], "ratelimiterror": [[11, "exceptions.RateLimitError", false]], "read_root() (in module main)": [[0, "main.read_root", false], [11, "main.read_root", false]], "reasoner_info (models.systeminfo attribute)": [[1, "id31", false], [1, "id40", false], [1, "id46", false], [1, "models.SystemInfo.reasoner_info", false], [11, "id31", false], [11, "models.SystemInfo.reasoner_info", false]], "reasoning_time (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.reasoning_time", false]], "reasoningerror": [[11, "exceptions.ReasoningError", false]], "record_cache_hit() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.record_cache_hit", false]], "record_cache_miss() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.record_cache_miss", false]], "record_failed_attempt() (security.securitymanager method)": [[11, "security.SecurityManager.record_failed_attempt", false]], "record_reasoning_time() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.record_reasoning_time", false]], "record_retrieval_time() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.record_retrieval_time", false]], "record_token_count() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.record_token_count", false]], "request_id (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.request_id", false]], "requestmetrics (class in monitoring)": [[11, "monitoring.RequestMetrics", false]], "requests (models.performancemetrics attribute)": [[1, "id35", false], [1, "id51", false], [1, "id55", false], [1, "models.PerformanceMetrics.requests", false], [11, "id35", false], [11, "models.PerformanceMetrics.requests", false]], "resourcenotfounderror": [[11, "exceptions.ResourceNotFoundError", false]], "response (main.legacychatresponse attribute)": [[0, "main.LegacyChatResponse.response", false], [11, "main.LegacyChatResponse.response", false]], "responsecache (class in main)": [[0, "main.ResponseCache", false], [11, "main.ResponseCache", false]], "retrieval": [[11, "module-retrieval", false]], "retrieval_time (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.retrieval_time", false]], "retrievalerror": [[11, "exceptions.RetrievalError", false]], "retriever (class in retrieval)": [[11, "retrieval.Retriever", false]], "retriever_info (models.systeminfo attribute)": [[1, "id32", false], [1, "id41", false], [1, "id47", false], [1, "models.SystemInfo.retriever_info", false], [11, "id32", false], [11, "models.SystemInfo.retriever_info", false]], "role (models.chatmessage attribute)": [[1, "id0", false], [1, "models.ChatMessage.role", false], [11, "id0", false], [11, "models.ChatMessage.role", false]], "role (models.openaimessage attribute)": [[1, "models.OpenAIMessage.role", false], [11, "models.OpenAIMessage.role", false]], "sanitize_input() (security.securitymanager method)": [[11, "security.SecurityManager.sanitize_input", false]], "search() (retrieval.retriever method)": [[11, "retrieval.Retriever.search", false]], "search() (vector_store.torchvectorstore method)": [[11, "vector_store.TorchVectorStore.search", false]], "security": [[11, "module-security", false]], "securitymanager (class in security)": [[11, "security.SecurityManager", false]], "serviceunavailableerror": [[11, "exceptions.ServiceUnavailableError", false]], "set() (core.cache.lrucache method)": [[11, "core.cache.LRUCache.set", false]], "set() (main.responsecache method)": [[0, "main.ResponseCache.set", false], [11, "main.ResponseCache.set", false]], "set_context() (logging_config.contextfilter method)": [[11, "logging_config.ContextFilter.set_context", false]], "setup_logging() (in module logging_config)": [[11, "logging_config.setup_logging", false]], "setup_structured_logging() (in module logging_config)": [[11, "logging_config.setup_structured_logging", false]], "setup_vector_db() (retrieval.retriever method)": [[11, "retrieval.Retriever.setup_vector_db", false]], "shutdown() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.shutdown", false]], "shutdown_event() (in module main)": [[0, "main.shutdown_event", false], [11, "main.shutdown_event", false]], "size() (core.cache.lrucache method)": [[11, "core.cache.LRUCache.size", false]], "size() (main.responsecache method)": [[0, "main.ResponseCache.size", false], [11, "main.ResponseCache.size", false]], "start_request() (monitoring.performancemonitor method)": [[11, "monitoring.PerformanceMonitor.start_request", false]], "start_time (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.start_time", false]], "stats() (core.cache.lrucache method)": [[11, "core.cache.LRUCache.stats", false]], "status_code (exceptions.symbolicaiexception attribute)": [[11, "exceptions.SymbolicAIException.status_code", false]], "stream (models.chatrequest attribute)": [[1, "id7", false], [1, "models.ChatRequest.stream", false], [11, "id7", false], [11, "models.ChatRequest.stream", false]], "stream (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.stream", false], [11, "models.OpenAIChatRequest.stream", false]], "structuredformatter (class in logging_config)": [[11, "logging_config.StructuredFormatter", false]], "success (models.documentaddresponse attribute)": [[1, "id24", false], [1, "models.DocumentAddResponse.success", false], [11, "id24", false], [11, "models.DocumentAddResponse.success", false]], "symbolic_ai_exception_handler() (in module main)": [[0, "main.symbolic_ai_exception_handler", false], [11, "main.symbolic_ai_exception_handler", false]], "symbolic_reasoning": [[11, "module-symbolic_reasoning", false]], "symbolicaiexception": [[11, "exceptions.SymbolicAIException", false]], "symbolicreasoner (class in symbolic_reasoning)": [[11, "symbolic_reasoning.SymbolicReasoner", false]], "system (models.modelrole attribute)": [[1, "models.ModelRole.SYSTEM", false], [11, "models.ModelRole.SYSTEM", false]], "system (models.performancemetrics attribute)": [[1, "id34", false], [1, "id50", false], [1, "id54", false], [1, "models.PerformanceMetrics.system", false], [11, "id34", false], [11, "models.PerformanceMetrics.system", false]], "system_info() (in module main)": [[0, "main.system_info", false], [11, "main.system_info", false]], "systeminfo (class in models)": [[1, "id36", false], [1, "models.SystemInfo", false], [11, "models.SystemInfo", false]], "systemmetrics (class in monitoring)": [[11, "monitoring.SystemMetrics", false]], "temperature (models.chatrequest attribute)": [[1, "id5", false], [1, "models.ChatRequest.temperature", false], [11, "id5", false], [11, "models.ChatRequest.temperature", false]], "temperature (models.openaichatrequest attribute)": [[1, "models.OpenAIChatRequest.temperature", false], [11, "models.OpenAIChatRequest.temperature", false]], "text (main.legacychatrequest attribute)": [[0, "main.LegacyChatRequest.text", false], [11, "main.LegacyChatRequest.text", false]], "text (models.documentaddrequest attribute)": [[1, "id21", false], [1, "models.DocumentAddRequest.text", false], [11, "id21", false], [11, "models.DocumentAddRequest.text", false]], "timestamp (models.chatmessage attribute)": [[1, "id2", false], [1, "models.ChatMessage.timestamp", false], [11, "id2", false], [11, "models.ChatMessage.timestamp", false]], "timestamp (monitoring.systemmetrics attribute)": [[11, "monitoring.SystemMetrics.timestamp", false]], "to_dict() (exceptions.symbolicaiexception method)": [[11, "exceptions.SymbolicAIException.to_dict", false]], "tokenusage (class in models)": [[1, "models.TokenUsage", false], [11, "models.TokenUsage", false]], "top_p (models.chatrequest attribute)": [[1, "id8", false], [1, "models.ChatRequest.top_p", false], [11, "id8", false], [11, "models.ChatRequest.top_p", false]], "torchvectorstore (class in vector_store)": [[11, "vector_store.TorchVectorStore", false]], "total_tokens (models.tokenusage attribute)": [[1, "id14", false], [1, "models.TokenUsage.total_tokens", false], [11, "id14", false], [11, "models.TokenUsage.total_tokens", false]], "total_tokens (monitoring.requestmetrics attribute)": [[11, "monitoring.RequestMetrics.total_tokens", false]], "usage (models.chatresponse attribute)": [[1, "id20", false], [1, "models.ChatResponse.usage", false], [11, "id20", false], [11, "models.ChatResponse.usage", false]], "usage (models.openaichatresponse attribute)": [[1, "models.OpenAIChatResponse.usage", false], [11, "models.OpenAIChatResponse.usage", false]], "use_gpu (retrieval.retriever attribute)": [[11, "retrieval.Retriever.use_gpu", false]], "use_gpu (symbolic_reasoning.symbolicreasoner attribute)": [[11, "symbolic_reasoning.SymbolicReasoner.use_gpu", false]], "user (models.modelrole attribute)": [[1, "models.ModelRole.USER", false], [11, "models.ModelRole.USER", false]], "using_torch_fallback (retrieval.retriever attribute)": [[11, "retrieval.Retriever.using_torch_fallback", false]], "validate_api_key() (security.securitymanager method)": [[11, "security.SecurityManager.validate_api_key", false]], "validate_content() (models.chatmessage class method)": [[1, "models.ChatMessage.validate_content", false], [11, "models.ChatMessage.validate_content", false]], "validate_cors_origin() (in module security)": [[11, "security.validate_cors_origin", false]], "validate_messages() (models.chatrequest class method)": [[1, "models.ChatRequest.validate_messages", false], [11, "models.ChatRequest.validate_messages", false]], "validate_text() (models.documentaddrequest class method)": [[1, "models.DocumentAddRequest.validate_text", false], [11, "models.DocumentAddRequest.validate_text", false]], "validate_total_tokens() (models.tokenusage class method)": [[1, "models.TokenUsage.validate_total_tokens", false], [11, "models.TokenUsage.validate_total_tokens", false]], "validation_exception_handler() (in module main)": [[0, "main.validation_exception_handler", false], [11, "main.validation_exception_handler", false]], "validationerror": [[11, "exceptions.ValidationError", false]], "vector_db (retrieval.retriever attribute)": [[11, "retrieval.Retriever.vector_db", false]], "vector_store": [[11, "module-vector_store", false]], "vectorstoreerror": [[11, "exceptions.VectorStoreError", false]], "verify_api_key() (in module security)": [[11, "security.verify_api_key", false]]}, "objects": {"": [[11, 0, 0, "-", "exceptions"], [11, 0, 0, "-", "logging_config"], [11, 0, 0, "-", "main"], [11, 0, 0, "-", "models"], [11, 0, 0, "-", "monitoring"], [11, 0, 0, "-", "retrieval"], [11, 0, 0, "-", "security"], [11, 0, 0, "-", "symbolic_reasoning"], [11, 0, 0, "-", "vector_store"]], "core": [[11, 0, 0, "-", "cache"]], "core.cache": [[11, 1, 1, "", "CacheManager"], [11, 1, 1, "", "LRUCache"], [11, 3, 1, "", "get_cache"]], "core.cache.CacheManager": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "cleanup_all"], [11, 2, 1, "", "create_cache"], [11, 2, 1, "", "get_all_stats"], [11, 2, 1, "", "get_cache"]], "core.cache.LRUCache": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "cleanup_expired"], [11, 2, 1, "", "clear"], [11, 2, 1, "", "delete"], [11, 2, 1, "", "get"], [11, 2, 1, "", "set"], [11, 2, 1, "", "size"], [11, 2, 1, "", "stats"]], "exceptions": [[11, 4, 1, "", "AuthenticationError"], [11, 4, 1, "", "AuthorizationError"], [11, 4, 1, "", "ConfigurationError"], [11, 4, 1, "", "RateLimitError"], [11, 4, 1, "", "ReasoningError"], [11, 4, 1, "", "ResourceNotFoundError"], [11, 4, 1, "", "RetrievalError"], [11, 4, 1, "", "ServiceUnavailableError"], [11, 4, 1, "", "SymbolicAIException"], [11, 4, 1, "", "ValidationError"], [11, 4, 1, "", "VectorStoreError"], [11, 3, 1, "", "handle_exception"]], "exceptions.AuthenticationError": [[11, 2, 1, "", "__init__"]], "exceptions.AuthorizationError": [[11, 2, 1, "", "__init__"]], "exceptions.ConfigurationError": [[11, 2, 1, "", "__init__"]], "exceptions.RateLimitError": [[11, 2, 1, "", "__init__"]], "exceptions.ReasoningError": [[11, 2, 1, "", "__init__"]], "exceptions.ResourceNotFoundError": [[11, 2, 1, "", "__init__"]], "exceptions.RetrievalError": [[11, 2, 1, "", "__init__"]], "exceptions.ServiceUnavailableError": [[11, 2, 1, "", "__init__"]], "exceptions.SymbolicAIException": [[11, 2, 1, "", "__init__"], [11, 5, 1, "", "details"], [11, 5, 1, "", "error_code"], [11, 5, 1, "", "message"], [11, 5, 1, "", "status_code"], [11, 2, 1, "", "to_dict"]], "exceptions.ValidationError": [[11, 2, 1, "", "__init__"]], "exceptions.VectorStoreError": [[11, 2, 1, "", "__init__"]], "logging_config": [[11, 1, 1, "", "ContextFilter"], [11, 1, 1, "", "PerformanceFilter"], [11, 1, 1, "", "StructuredFormatter"], [11, 3, 1, "", "get_logger"], [11, 3, 1, "", "setup_logging"], [11, 3, 1, "", "setup_structured_logging"]], "logging_config.ContextFilter": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "clear_context"], [11, 2, 1, "", "filter"], [11, 2, 1, "", "set_context"]], "logging_config.PerformanceFilter": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "filter"]], "logging_config.StructuredFormatter": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "format"]], "main": [[11, 1, 1, "", "LegacyChatRequest"], [11, 1, 1, "", "LegacyChatResponse"], [11, 1, 1, "", "ResponseCache"], [11, 3, 1, "", "add_document"], [11, 3, 1, "", "add_security_headers"], [11, 3, 1, "", "api_info"], [11, 3, 1, "", "authentication_exception_handler"], [11, 3, 1, "", "chat"], [11, 3, 1, "", "chat_stream"], [11, 3, 1, "", "check_request_size_middleware"], [11, 3, 1, "", "clean_cache"], [11, 3, 1, "", "general_exception_handler"], [11, 3, 1, "", "get_document_count"], [11, 3, 1, "", "openai_chat"], [11, 3, 1, "", "openai_chat_stream"], [11, 3, 1, "", "optimize_gpu_memory"], [11, 3, 1, "", "performance_stats"], [11, 3, 1, "", "process_query"], [11, 3, 1, "", "rate_limit_middleware"], [11, 3, 1, "", "read_root"], [11, 3, 1, "", "shutdown_event"], [11, 3, 1, "", "symbolic_ai_exception_handler"], [11, 3, 1, "", "system_info"], [11, 3, 1, "", "validation_exception_handler"]], "main.LegacyChatRequest": [[11, 5, 1, "", "model_config"], [11, 5, 1, "", "text"]], "main.LegacyChatResponse": [[11, 5, 1, "", "cached"], [11, 5, 1, "", "model_config"], [11, 5, 1, "", "response"]], "main.ResponseCache": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "clean"], [11, 2, 1, "", "get"], [11, 2, 1, "", "set"], [11, 2, 1, "", "size"]], "models": [[11, 1, 1, "", "ChatChoice"], [11, 1, 1, "", "ChatMessage"], [11, 1, 1, "", "ChatRequest"], [11, 1, 1, "", "ChatResponse"], [11, 1, 1, "", "DocumentAddRequest"], [11, 1, 1, "", "DocumentAddResponse"], [11, 1, 1, "", "ModelRole"], [11, 1, 1, "", "OpenAIChatChoice"], [11, 1, 1, "", "OpenAIChatRequest"], [11, 1, 1, "", "OpenAIChatResponse"], [11, 1, 1, "", "OpenAIMessage"], [11, 1, 1, "", "PerformanceMetrics"], [11, 1, 1, "", "SystemInfo"], [11, 1, 1, "", "TokenUsage"]], "models.ChatChoice": [[11, 5, 1, "id11", "finish_reason"], [11, 5, 1, "id9", "index"], [11, 5, 1, "id10", "message"], [11, 5, 1, "", "model_config"]], "models.ChatMessage": [[11, 5, 1, "id1", "content"], [11, 5, 1, "", "model_config"], [11, 5, 1, "id0", "role"], [11, 5, 1, "id2", "timestamp"], [11, 2, 1, "", "validate_content"]], "models.ChatRequest": [[11, 5, 1, "id6", "max_tokens"], [11, 5, 1, "id3", "messages"], [11, 5, 1, "id4", "model"], [11, 5, 1, "", "model_config"], [11, 5, 1, "id7", "stream"], [11, 5, 1, "id5", "temperature"], [11, 5, 1, "id8", "top_p"], [11, 2, 1, "", "validate_messages"]], "models.ChatResponse": [[11, 5, 1, "id19", "choices"], [11, 5, 1, "id17", "created"], [11, 5, 1, "id15", "id"], [11, 5, 1, "id18", "model"], [11, 5, 1, "", "model_config"], [11, 5, 1, "id16", "object"], [11, 5, 1, "id20", "usage"]], "models.DocumentAddRequest": [[11, 5, 1, "id23", "document_id"], [11, 5, 1, "id22", "metadata"], [11, 5, 1, "", "model_config"], [11, 5, 1, "id21", "text"], [11, 2, 1, "", "validate_text"]], "models.DocumentAddResponse": [[11, 5, 1, "id26", "document_id"], [11, 5, 1, "id25", "message"], [11, 5, 1, "id27", "metadata"], [11, 5, 1, "", "model_config"], [11, 5, 1, "id24", "success"]], "models.ModelRole": [[11, 5, 1, "", "ASSISTANT"], [11, 5, 1, "", "SYSTEM"], [11, 5, 1, "", "USER"], [11, 2, 1, "", "__format__"]], "models.OpenAIChatChoice": [[11, 5, 1, "", "finish_reason"], [11, 5, 1, "", "index"], [11, 5, 1, "", "message"], [11, 5, 1, "", "model_config"]], "models.OpenAIChatRequest": [[11, 5, 1, "", "max_tokens"], [11, 5, 1, "", "messages"], [11, 5, 1, "", "model"], [11, 5, 1, "", "model_config"], [11, 5, 1, "", "stream"], [11, 5, 1, "", "temperature"]], "models.OpenAIChatResponse": [[11, 5, 1, "", "choices"], [11, 5, 1, "", "created"], [11, 5, 1, "", "id"], [11, 5, 1, "", "model"], [11, 5, 1, "", "model_config"], [11, 5, 1, "", "object"], [11, 5, 1, "", "usage"]], "models.OpenAIMessage": [[11, 5, 1, "", "content"], [11, 5, 1, "", "model_config"], [11, 5, 1, "", "role"]], "models.PerformanceMetrics": [[11, 5, 1, "id33", "cache"], [11, 5, 1, "", "model_config"], [11, 5, 1, "id35", "requests"], [11, 5, 1, "id34", "system"]], "models.SystemInfo": [[11, 5, 1, "id28", "gpu_available"], [11, 5, 1, "id29", "gpu_name"], [11, 5, 1, "id30", "gpu_optimized"], [11, 5, 1, "", "model_config"], [11, 5, 1, "id31", "reasoner_info"], [11, 5, 1, "id32", "retriever_info"]], "models.TokenUsage": [[11, 5, 1, "id13", "completion_tokens"], [11, 5, 1, "", "model_config"], [11, 5, 1, "id12", "prompt_tokens"], [11, 5, 1, "id14", "total_tokens"], [11, 2, 1, "", "validate_total_tokens"]], "monitoring": [[11, 1, 1, "", "PerformanceMonitor"], [11, 1, 1, "", "RequestMetrics"], [11, 1, 1, "", "SystemMetrics"]], "monitoring.PerformanceMonitor": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "end_request"], [11, 2, 1, "", "get_recent_metrics"], [11, 2, 1, "", "record_cache_hit"], [11, 2, 1, "", "record_cache_miss"], [11, 2, 1, "", "record_reasoning_time"], [11, 2, 1, "", "record_retrieval_time"], [11, 2, 1, "", "record_token_count"], [11, 2, 1, "", "shutdown"], [11, 2, 1, "", "start_request"]], "monitoring.RequestMetrics": [[11, 2, 1, "", "__init__"], [11, 5, 1, "", "cached"], [11, 2, 1, "", "duration"], [11, 5, 1, "", "end_time"], [11, 5, 1, "", "endpoint"], [11, 5, 1, "", "error"], [11, 5, 1, "", "reasoning_time"], [11, 5, 1, "", "request_id"], [11, 5, 1, "", "retrieval_time"], [11, 5, 1, "", "start_time"], [11, 5, 1, "", "total_tokens"]], "monitoring.SystemMetrics": [[11, 2, 1, "", "__init__"], [11, 5, 1, "", "active_requests"], [11, 5, 1, "", "cache_hits"], [11, 5, 1, "", "cache_misses"], [11, 5, 1, "", "cache_size"], [11, 5, 1, "", "cpu_percent"], [11, 5, 1, "", "gpu_memory_used"], [11, 5, 1, "", "gpu_utilization"], [11, 5, 1, "", "memory_percent"], [11, 5, 1, "", "timestamp"]], "retrieval": [[11, 1, 1, "", "Retriever"]], "retrieval.Retriever": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "add_documents"], [11, 2, 1, "", "batch_add_documents"], [11, 5, 1, "", "dimension"], [11, 5, 1, "", "documents"], [11, 2, 1, "", "get_system_info"], [11, 5, 1, "", "index"], [11, 2, 1, "", "optimize_index"], [11, 2, 1, "", "search"], [11, 2, 1, "", "setup_vector_db"], [11, 5, 1, "", "use_gpu"], [11, 5, 1, "", "using_torch_fallback"], [11, 5, 1, "", "vector_db"]], "security": [[11, 1, 1, "", "RateLimiter"], [11, 1, 1, "", "SecurityManager"], [11, 3, 1, "", "check_request_size"], [11, 3, 1, "", "get_client_ip"], [11, 3, 1, "", "get_cors_config"], [11, 3, 1, "", "get_security_headers"], [11, 3, 1, "", "validate_cors_origin"], [11, 3, 1, "", "verify_api_key"]], "security.RateLimiter": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "is_allowed"]], "security.SecurityManager": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "is_ip_blocked"], [11, 2, 1, "", "record_failed_attempt"], [11, 2, 1, "", "sanitize_input"], [11, 2, 1, "", "validate_api_key"]], "symbolic_reasoning": [[11, 1, 1, "", "SymbolicReasoner"]], "symbolic_reasoning.SymbolicReasoner": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "batch_process_queries"], [11, 5, 1, "", "engine"], [11, 2, 1, "", "get_system_info"], [11, 5, 1, "", "model"], [11, 2, 1, "", "process_query"], [11, 5, 1, "", "use_gpu"]], "vector_store": [[11, 1, 1, "", "TorchVectorStore"]], "vector_store.TorchVectorStore": [[11, 2, 1, "", "__init__"], [11, 2, 1, "", "add"], [11, 2, 1, "", "get_system_info"], [11, 2, 1, "", "search"]]}, "objnames": {"0": ["py", "module", "Python module"], "1": ["py", "class", "Python class"], "2": ["py", "method", "Python method"], "3": ["py", "function", "Python function"], "4": ["py", "exception", "Python exception"], "5": ["py", "attribute", "Python attribute"]}, "objtypes": {"0": "py:module", "1": "py:class", "2": "py:method", "3": "py:function", "4": "py:exception", "5": "py:attribute"}, "terms": {"": [0, 2, 4, 7, 8, 9, 10, 11], "0": [0, 1, 2, 4, 5, 7, 8, 9, 11], "00": [1, 6, 9, 11], "000": [2, 4, 11], "00z": [1, 6, 9, 11], "04": 9, "06": [1, 3, 4, 6, 9, 11], "1": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11], "10": [1, 2, 4, 5, 6, 8, 9, 11], "100": [1, 2, 3, 5, 6, 7, 8, 11], "1000": [0, 1, 7, 8, 11], "10000": [1, 2, 3, 7, 8, 11], "100000": [1, 11], "1000m": 8, "10485760": [7, 11], "10gb": 9, "10m": [6, 8], "10mb": [5, 7], "10r": 8, "11": 9, "12": [1, 6, 11], "123": [1, 5, 6, 11], "12345": 7, "127": 7, "15": [3, 8, 9], "150": [0, 1, 5, 9], "150m": 6, "1677652288": [1, 11], "1677652348": 5, "168": 3, "192": 3, "1m": 6, "2": [1, 2, 6, 8, 9, 11], "20": [1, 8, 9, 11], "200": 6, "2000m": 8, "2025": [1, 3, 4, 6, 9, 11], "20m": 6, "21": [1, 11], "234": [2, 11], "25": 1, "250": 6, "29": [1, 3, 4, 11], "29t12": [1, 6, 9, 11], "2g": 8, "2gi": 8, "2m": 6, "2x": 8, "3": [2, 4, 6, 7, 8, 9, 11], "30": [1, 8], "300": 7, "3000": [3, 7, 11], "301": 8, "30m": 6, "31536000": 8, "32": [2, 8, 11], "3600": [7, 11], "365": 8, "3f": [2, 11], "3m": 6, "4": [4, 6, 7, 8, 9], "400": [0, 5], "401": [0, 5], "403": [3, 5], "4090": [4, 11], "4096": [1, 8, 11], "40m": 6, "429": [0, 3, 5], "443": 8, "4g": 8, "4gb": 9, "4gi": 8, "5": [1, 2, 3, 4, 6, 7, 8, 11], "50": [2, 11], "500": [0, 4, 5, 11], "50000": 8, "50000m": 8, "5000m": 8, "5044": 8, "50gb": 9, "50m": 6, "5432": 8, "5601": 8, "5m": 6, "6": 6, "60": [1, 3, 7, 8, 11], "600": 8, "60m": 6, "6379": [7, 8], "67890": 7, "7": [0, 1, 2, 5, 6, 8, 9, 11], "70": [6, 8], "768": [2, 7, 11], "8": [1, 7, 8, 9], "80": [1, 6, 8], "8000": [0, 5, 7, 8, 9, 10], "8001": 9, "85": 10, "856": [2, 11], "8gb": 9, "9": [1, 8, 11], "9200": 8, "95": [5, 6], "A": [4, 11], "At": 1, "For": [3, 4, 8, 11], "If": [1, 2, 3, 4, 9, 11], "In": [2, 4, 6, 11], "It": [2, 4, 11], "No": 6, "On": 9, "Or": 9, "The": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "These": 5, "To": 1, "With": 8, "_": 3, "__format__": [1, 11], "__init__": [0, 2, 3, 4, 6, 11], "__str__": [1, 11], "abc": 6, "about": [1, 2, 4, 11], "abus": 6, "acceler": [4, 6, 7, 8, 10, 11], "access": [3, 4, 5, 6, 11], "access_log": 8, "accord": [4, 11], "accordingli": [4, 11], "accur": 3, "accuraci": [2, 4, 11], "across": 6, "activ": 9, "active_request": [1, 11], "actual": [1, 11], "ad": [1, 2, 4, 11], "adapt": 6, "add": [0, 1, 2, 3, 7, 11], "add_docu": [0, 2, 10, 11], "add_head": 8, "add_middlewar": 3, "add_security_head": [0, 10, 11], "addit": [1, 4, 6, 11], "additional_context": 5, "address": [0, 3, 11], "adjust": [3, 7], "advanc": [6, 9, 10, 11], "after": [2, 3, 11], "ag": 8, "ai": [0, 1, 2, 3, 4, 6, 8, 9, 10, 11], "alb": 6, "alert": 10, "algorithm": [2, 6], "aliv": 6, "all": [0, 1, 2, 3, 4, 5, 7, 11], "allow": [3, 5, 11], "allow_origin": [3, 11], "allowed_origin": [3, 11], "alpin": [7, 8], "alwai": [1, 7, 8, 11], "an": [3, 4, 10, 11], "ani": [1, 3, 4, 11], "annot": [1, 11], "annotated_typ": [1, 11], "anthrop": [6, 7, 11], "anthropic_api_kei": 4, "api": [0, 1, 4, 6, 7], "api1": 8, "api2": 8, "api3": 8, "api_info": [0, 10, 11], "api_kei": [3, 7, 11], "apivers": 8, "app": [3, 6, 7, 8, 11], "app_": 7, "app_debug": [0, 7, 8], "app_environ": [7, 8], "app_host": [0, 7, 8], "app_port": [0, 7, 8, 9], "app_reload": 7, "app_titl": 7, "app_vers": 7, "app_work": [0, 7, 8], "append": 7, "appli": [0, 2, 4, 6, 8, 11], "applic": [1, 2, 3, 5, 8, 9, 10, 11], "approach": 9, "appropri": [1, 2, 3, 7], "approxim": 2, "appset": 7, "appus": 8, "apt": 8, "ar": [0, 1, 2, 3, 4, 11], "architectur": 9, "argument": 11, "around": [2, 11], "arrai": [0, 5, 7, 11], "asctim": 7, "asgi": 9, "assist": [1, 3, 4, 5, 9, 11], "assistant_msg": 1, "associ": 11, "async": [0, 3, 6, 10, 11], "attack": 3, "attempt": [10, 11], "attent": 6, "audit": 6, "augment": [2, 9, 11], "auth": 6, "authent": [0, 6, 7, 9, 10, 11], "authentication_error": [0, 5], "authentication_exception_handl": [0, 10, 11], "authenticationerror": [0, 3, 11], "author": [0, 1, 3, 4, 5, 6, 7, 9, 10, 11], "authorization_error": 5, "authorizationerror": 11, "auto": [6, 10], "autom": 8, "automat": [0, 1, 2, 4, 6, 9, 10, 11], "autonom": 6, "autosc": 8, "avail": [0, 1, 2, 4, 5, 7, 11], "averageutil": 8, "avoid": [2, 3], "aw": 6, "await": [3, 9, 10], "awar": 6, "b": [4, 11], "baai": [2, 7], "back": 11, "backend": [4, 6, 8, 10, 11], "background": [4, 11], "background_task": [0, 11], "backgroundtask": [0, 11], "backup": [10, 11], "backup_": 8, "backup_count": 11, "balanc": [2, 3, 6, 10], "bandwidth": 6, "base": [0, 1, 2, 3, 4, 6, 7, 9, 10, 11], "basemodel": [0, 1], "baseset": 7, "bash": 8, "basic": [7, 11], "batch": [6, 11], "batch_add_docu": [2, 11], "batch_process_queri": [4, 11], "batch_siz": [2, 11], "bearer": [0, 3, 5, 6, 7, 9, 10], "been": [1, 11], "befor": [2, 3, 7, 11], "being": [2, 4, 11], "benefit": [4, 6, 9], "bert": [2, 11], "best": [10, 11], "better": [2, 4, 11], "between": [1, 2, 11], "bge": [2, 7], "bin": [8, 9], "binary_remote_addr": 8, "bind": 8, "block": [5, 6, 7, 9, 10, 11], "block_dur": 7, "bodi": 9, "bool": [0, 1, 2, 3, 4, 11], "boolean": 5, "both": [0, 2, 11], "boundari": 1, "brows": 9, "bug": 9, "build": [7, 8, 10], "built": [2, 4, 9], "burst": [3, 8], "busi": [1, 6, 7], "byte": [3, 11], "c": [2, 4, 7, 8, 11], "cach": [0, 1, 3, 8, 10, 11], "cache_cleanup_interv": 7, "cache_hit": 11, "cache_max_s": [7, 8], "cache_miss": 11, "cache_redis_url": [7, 8], "cache_s": 11, "cache_ttl_second": 7, "cachemanag": 11, "cachemetr": 1, "call": [2, 5, 10, 11], "call_next": [0, 3, 11], "can": [1, 2, 3, 4, 5, 7, 9, 11], "cannot": [0, 1, 4, 11], "capabl": [2, 4, 6, 9, 10, 11], "case": [2, 7, 10], "categori": 2, "caus": 11, "cd": 9, "cdn": 6, "cento": 9, "central": 8, "cert": 8, "certain": 1, "certbot": 8, "certif": 8, "certonli": 8, "cfg": 8, "chang": [7, 9], "charact": [2, 3, 4, 8, 11], "chat": [1, 3, 6, 9, 10], "chat_endpoint": 3, "chat_stream": [0, 10, 11], "chatchoic": [1, 10, 11], "chatcmpl": [1, 5, 11], "chatmessag": [1, 10, 11], "chatrequest": [0, 1, 10, 11], "chatrespons": [0, 1, 10, 11], "check": [2, 3, 4, 6, 7, 8, 10, 11], "check_request_s": [3, 10, 11], "check_request_size_middlewar": [0, 10, 11], "checklist": 8, "chmod": 8, "choic": [0, 9, 11], "choos": 2, "chosen": [4, 11], "chown": 8, "chromadb": [6, 7, 10, 11], "chunk": [1, 5, 6], "cl": 1, "class": [0, 1, 6, 10, 11], "classic": 6, "classmethod": [1, 11], "classvar": [0, 1, 11], "claud": [4, 7], "clean": [0, 2, 11], "clean_cach": [0, 10, 11], "clean_text": 3, "cleanup": [0, 3, 11], "cleanup_al": 11, "cleanup_expir": 11, "clear": [1, 10, 11], "clear_context": 11, "cli": [6, 7, 8], "clickjack": 3, "client": [5, 6, 8, 10, 11], "client_ip": 3, "clone": 9, "cloud": [4, 6, 11], "cluster": 6, "cmd": 8, "co": 8, "code": [0, 1, 4, 6, 8, 9, 11], "collect": [2, 10, 11], "collection_interv": 11, "com": [3, 7, 8, 9, 11], "combin": [2, 4, 9, 10, 11], "commit": 7, "common": [0, 10], "commun": [4, 9, 10, 11], "compar": [4, 11], "comparison": [2, 3, 11], "compat": [0, 1, 4, 6, 8, 9, 10, 11], "complement": [4, 11], "complet": [6, 9, 10, 11], "completion_token": [1, 11], "complex": [1, 4, 6], "compon": [0, 9], "compos": 7, "comprehens": [1, 2, 3, 4, 5, 6, 7, 9, 10, 11], "compress": 6, "comput": [2, 10, 11], "concern": 10, "conclud": [4, 11], "conclus": [4, 11], "concurr": [5, 10], "conda": 9, "condit": [6, 8], "conf": 8, "config": [0, 1, 6, 7, 8, 10, 11], "config_backup": 8, "config_data": 7, "config_kei": 11, "configdict": [0, 1, 11], "configur": [1, 5, 9, 10], "configurationerror": [2, 4, 11], "conform": [0, 1, 11], "connect": [3, 4, 6, 7, 8, 10, 11], "consid": [1, 2, 3], "consider": [1, 10, 11], "consist": 2, "consol": [9, 11], "const": 9, "constant": 3, "constraint": [1, 11], "contact": 9, "contain": [0, 1, 2, 3, 4, 6, 8, 11], "container": [8, 10], "containerport": 8, "content": [0, 1, 2, 3, 4, 5, 8, 9, 11], "content_filt": 1, "context": [1, 2, 4, 6, 10, 11], "contextfilt": 11, "contextu": 11, "contribut": 9, "control": [3, 6, 7, 11], "convers": [4, 5, 11], "convert": [1, 11], "copi": [7, 8, 9], "cor": [0, 6, 7, 8, 10, 11], "core": [0, 8, 9], "correctli": 9, "correl": 10, "correlation_id": 6, "cors_config": [3, 11], "cors_origin": 7, "corsmiddlewar": 3, "cost": 4, "count": [0, 7, 11], "cover": [7, 8], "coverag": 10, "cp": [7, 8, 9, 10], "cpu": [2, 4, 6, 7, 8, 9], "cpu_perc": [1, 11], "creat": [1, 7, 8, 9, 11], "create_cach": 11, "create_env_templ": 7, "creation": [1, 11], "credenti": [3, 5, 11], "critic": [6, 11], "cross": [3, 5, 10, 11], "crt": 8, "csp": 6, "cuda": [4, 7, 11], "curl": [7, 8, 9], "current": [0, 4, 6, 7, 11], "custom": [0, 2, 6, 11], "czf": 8, "d": [8, 9], "d_": 8, "daemon": 8, "dai": 8, "dashboard": 6, "data": [2, 5, 7, 9, 10, 11], "databas": [2, 6, 7, 10, 11], "database_max_overflow": 8, "database_pool_s": 8, "database_url": 8, "dataset": 2, "date": [1, 3, 4, 8, 11], "datetim": [1, 11], "db": [2, 8], "ddo": 6, "debug": [0, 6, 7, 11], "decod": 0, "def": [1, 3, 6, 7], "default": [0, 1, 2, 3, 4, 7, 8, 11], "default_backend": 8, "default_max_s": 11, "default_ttl": 11, "defin": [1, 11], "delai": 8, "delet": [2, 5, 11], "delta": 1, "deni": [5, 8, 11], "denial": 6, "dens": [2, 11], "depend": [2, 3, 4, 8, 10, 11], "depends_on": [7, 8], "deploi": [7, 8], "deploy": 7, "descend": [2, 11], "descript": [1, 5], "deseri": 1, "design": [8, 9, 10], "detail": [0, 1, 2, 3, 4, 5, 6, 9, 10, 11], "detect": [4, 9, 11], "dev": [7, 10], "develop": [6, 9], "dict": [0, 1, 2, 3, 4, 11], "dictionari": [0, 2, 3, 4, 11], "differ": [2, 3, 4, 7, 9, 11], "dimens": [2, 11], "dir": 8, "direct": [3, 9], "directli": 5, "directori": [8, 11], "disabl": [7, 9], "disclosur": 6, "discoveri": 8, "discuss": [4, 9, 10, 11], "disk": [6, 9], "distribut": [2, 11], "doc": 5, "doc1": [2, 11], "doc2": [2, 11], "doc_": 2, "doc_123": [1, 11], "doc_id": 11, "docker": 10, "dockerfil": [7, 8], "docstr": 1, "document": [0, 1, 6, 7, 9, 11], "document_id": [1, 11], "documentaddrequest": [0, 1, 10, 11], "documentaddrespons": [0, 1, 10, 11], "doe": [4, 9, 11], "doesn": [1, 11], "domain": [2, 11], "don": 9, "done": [4, 5], "download": 9, "downtim": 6, "dr": [1, 11], "drop": [9, 10], "due": [2, 3, 4, 11], "durat": [3, 11], "duration_m": 6, "e": [1, 2, 3, 4, 8, 11], "each": [2, 4, 5, 7, 11], "easi": 6, "echo": 7, "edg": 6, "edit": [7, 9, 10], "effici": [2, 6, 9, 10, 11], "elast": 8, "elasticsearch": 8, "elk": [8, 10], "els": [3, 6, 7], "embed": [6, 10, 11], "embedding_model": 2, "empti": [0, 1, 2, 3, 4, 11], "en": [2, 7], "enabl": [1, 2, 3, 4, 5, 7, 8, 11], "encod": [4, 11], "encount": 9, "encrypt": [6, 8], "end": 11, "end_request": 11, "end_tim": 11, "endpoint": [1, 3, 6, 10], "enforc": 3, "engag": 6, "engin": [7, 9, 10, 11], "enhanc": [2, 3, 4, 9, 10, 11], "ensembl": 6, "ensur": 9, "enterpris": [9, 10], "entri": [0, 6, 11], "enum": 1, "enumer": [1, 11], "env": [7, 8, 9, 10], "environ": [0, 3, 4, 6, 9, 10], "equal": [1, 11], "error": [6, 8, 9, 10, 11], "error_cod": [4, 5, 11], "escal": 6, "essenti": 8, "etc": [4, 6, 8, 11], "event": [0, 11], "evict": 2, "exact": [2, 3], "exampl": [7, 9, 10, 11], "exc": [0, 11], "exceed": [0, 2, 3, 4, 5, 11], "except": [0, 1, 2, 3, 4], "exclud": 1, "exec": 8, "execut": 6, "exit": 8, "expans": 2, "expect": [3, 9], "experi": 2, "expir": [9, 11], "explain": [0, 4, 6, 9, 11], "explan": [4, 6, 11], "explor": 9, "export": 7, "expos": [3, 8], "extend": [2, 11], "extern": 6, "extra": [1, 11], "f": [2, 4, 7, 8, 11], "facebook": 2, "fact": [4, 11], "factor": 6, "factori": [1, 11], "factual": 9, "fail": [2, 4, 5, 7, 9, 10, 11], "failur": [2, 3, 4, 6, 8, 11], "faiss": [6, 7, 9, 10, 11], "fallback": [2, 4, 7, 11], "fals": [0, 1, 3, 4, 5, 7, 8, 9, 11], "fast": [2, 6, 10, 11], "fastapi": [3, 9, 10, 11], "faster": [2, 4, 11], "featur": [0, 2, 3, 4, 6, 11], "feder": 6, "fetch": 9, "field": [0, 5, 11], "field_valid": 1, "file": [8, 9, 10, 11], "file_path": 7, "filter": [2, 3, 6, 11], "filtered_result": 2, "finish": 1, "finish_reason": [1, 11], "firewal": 6, "first": [2, 7, 10, 11], "float": [1, 2, 11], "flow": 10, "follow": [0, 2, 4, 6, 10], "forbid": [1, 11], "forc": 9, "form": 2, "formal": 2, "format": [0, 1, 2, 6, 7, 9, 11], "format_spec": [1, 11], "formatt": 11, "forward": [3, 8], "found": [0, 2, 11], "foundat": [2, 4, 11], "frame": [3, 8], "framework": [4, 9, 10, 11], "from": [0, 1, 2, 3, 4, 7, 8, 9, 11], "frontend": 8, "full": [2, 4, 9, 10, 11], "function": [3, 11], "futur": 10, "g": [2, 3, 11], "gatewai": 6, "ge": [1, 11], "geforc": [4, 11], "gener": [0, 1, 2, 3, 4, 6, 8, 9, 11], "general_exception_handl": [0, 10, 11], "get": [0, 2, 3, 4, 5, 7, 8, 10, 11], "get_all_stat": 11, "get_cach": 11, "get_client_ip": [3, 10, 11], "get_cors_config": [3, 10, 11], "get_document_count": [0, 10, 11], "get_logg": 11, "get_recent_metr": 11, "get_security_head": [3, 10, 11], "get_set": 7, "get_system_info": [2, 4, 11], "git": 9, "github": [9, 10], "global": 8, "good": 2, "gpt": [4, 7, 11], "gpu": [0, 1, 6, 7, 8, 10, 11], "gpu_avail": [1, 4, 11], "gpu_context": 6, "gpu_en": [2, 4, 11], "gpu_memory_us": 11, "gpu_nam": [1, 4, 11], "gpu_optim": [1, 9, 11], "gpu_util": 11, "gpucontextmanag": 6, "gracefulli": 1, "grade": [9, 10], "gradual": 6, "grafana": 10, "grant": 3, "graph": 2, "graphql": 6, "grep": 7, "ground": 9, "gt": [1, 11], "guid": 9, "guidelin": 9, "gz": 8, "h": [7, 8, 9], "ha": [1, 5, 11], "handl": [1, 6, 10], "handle_except": 11, "handler": 6, "haproxi": 6, "harden": [6, 10], "hardwar": [4, 11], "have": [4, 9, 11], "header": [0, 6, 8, 9, 10, 11], "health": [6, 8, 10], "healthcheck": 8, "healthi": 9, "hello": [1, 3, 11], "help": [1, 10, 11], "here": [5, 8], "hierarch": [6, 7], "high": [1, 2, 4, 8, 10], "higher": [2, 7, 9, 11], "highli": 2, "hint": 1, "histori": [4, 11], "hit": [1, 6, 8, 11], "hit_rat": 1, "horizont": 10, "horizontalpodautoscal": 8, "host": [0, 6, 7, 8], "hour": [3, 7], "how": [1, 4, 7, 9, 11], "hst": 6, "http": [0, 3, 5, 6, 7, 8, 9, 10, 11], "http2": 8, "httpauthorizationcredenti": [3, 11], "httpbearer": [3, 11], "httpchk": 8, "httpexcept": [3, 11], "httpget": 8, "human": [1, 5, 11], "hybrid": [2, 4, 6, 9, 11], "i": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "id": [1, 2, 5, 6, 10, 11], "identifi": [1, 2, 3, 5, 6, 11], "imag": [7, 8], "implement": [0, 2, 3, 4, 6, 11], "impli": [4, 11], "import": [0, 1, 2, 3, 4, 7, 8, 9, 10], "improv": [2, 4, 11], "incid": 6, "includ": [1, 2, 3, 4, 5, 7, 11], "include_extra": 11, "include_metadata": 2, "includesubdomain": 8, "incom": 1, "incorpor": [4, 11], "increas": 8, "increment": 1, "index": [1, 6, 9, 10, 11], "index_s": [1, 2, 9], "index_typ": 2, "indexflatip": 2, "indexhnsw": 2, "indexivfflat": 2, "indic": [0, 2, 8, 11], "individu": 1, "industri": 6, "infer": 6, "info": [1, 2, 4, 6, 7, 8, 9, 11], "inform": [1, 3, 4, 6, 10, 11], "infrastructur": 6, "initi": 11, "initialdelaysecond": 8, "inject": 6, "input": [0, 1, 2, 4, 6, 9, 10, 11], "inspir": 6, "instal": [4, 8, 10, 11], "instanc": [6, 11], "instead": 8, "int": [0, 1, 2, 3, 11], "integ": [2, 5, 11], "integr": [2, 4, 6, 8, 10, 11], "intellig": 10, "intens": 8, "interact": [5, 6], "interfac": 11, "internal_error": [0, 5], "internal_field": 1, "internet": [4, 11], "interv": [3, 8, 11], "invalid": [0, 1, 2, 3, 4, 5, 11], "involv": [4, 11], "ip": [5, 6, 8, 10, 11], "ip_address": [3, 11], "is_allow": [3, 11], "is_avail": 7, "is_ip_block": [3, 11], "issu": [4, 6, 10, 11], "item": [3, 11], "iter_lin": 0, "javascript": 9, "job_nam": 8, "join": 9, "json": [0, 5, 7, 9, 10, 11], "json_data": 1, "json_schema_extra": [1, 11], "jsonrespons": 3, "k": [2, 11], "keep": [0, 6, 11], "kei": [0, 1, 2, 4, 5, 6, 7, 8, 10, 11], "key1": 7, "key2": 7, "keyout": 8, "keyword": 6, "kibana": 8, "kind": 8, "knowledg": [4, 6, 11], "kubectl": 8, "kubernet": 10, "kwarg": 11, "l1": 6, "l2": 6, "l3": 6, "l6": 2, "label": 8, "languag": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11], "larg": [2, 3, 4, 11], "large_document_set": 2, "larger": 7, "latenc": [4, 6, 8], "latest": 8, "layer": 10, "le": [1, 11], "leak": 2, "learn": [2, 6, 9, 11], "least": [1, 6], "legaci": 11, "legacychatrequest": [0, 10, 11], "legacychatrespons": [0, 10, 11], "legitim": 3, "len": [1, 2, 11], "length": [1, 2, 3, 4, 11], "less": [2, 11], "let": 8, "level": [7, 11], "levelnam": 7, "lib": 8, "lightrag": [2, 11], "lightweight": 2, "like": [2, 11], "limit": [0, 4, 6, 7, 8, 9, 10, 11], "limit_req": 8, "limit_req_zon": 8, "line": 0, "link": 9, "linux": 9, "list": [1, 2, 3, 4, 5, 8, 11], "listen": 8, "liter": 8, "live": 11, "livenessprob": 8, "llama": [1, 4, 6, 7, 9, 11], "load": [3, 6, 10], "load_config_from_fil": 7, "loadbalanc": 8, "local": [0, 1, 5, 6, 7, 9, 10, 11], "localhost": [0, 3, 5, 7, 8, 9, 10, 11], "locat": 8, "log": [3, 4, 9, 10], "log_backup_count": 7, "log_dir": 11, "log_fil": 11, "log_file_en": 7, "log_file_path": [7, 8], "log_format": 7, "log_level": [7, 8, 11], "log_max_file_s": 7, "log_structured_log": [7, 8], "logger": 11, "logging_config": 11, "logic": [1, 2, 4, 6, 7, 9, 10, 11], "login": 3, "logrecord": 11, "logstash": 8, "long": [2, 4, 6], "look": 11, "low": 4, "lru": [2, 6, 10, 11], "lrucach": 11, "m": [7, 8, 9], "machin": [1, 2, 11], "maco": 9, "mai": [2, 4, 11], "main": [1, 2, 3, 4, 6, 7, 8, 9, 10, 11], "maintain": [3, 6, 11], "mainten": 2, "make": 9, "malici": [3, 11], "manag": [0, 1, 2, 4, 8, 10], "manipul": [4, 11], "map": [3, 11], "match": [1, 11], "matchlabel": 8, "max": 8, "max_file_s": 11, "max_length": [1, 3, 11], "max_siz": [0, 1, 7, 11], "max_token": [0, 1, 4, 5, 9, 11], "maxconn": 8, "maximum": [1, 2, 3, 4, 5, 11], "maxlen": [1, 11], "maxreplica": 8, "mechan": 2, "medium": [2, 6], "memori": [0, 2, 3, 4, 6, 7, 8, 10, 11], "memory_perc": [1, 11], "messag": [0, 2, 3, 4, 6, 7, 9, 10, 11], "metadata": [1, 2, 6, 8, 11], "method": [5, 9, 11], "methodologi": [4, 11], "metric": [1, 2, 10, 11], "metrics_path": 8, "microservic": 6, "middlewar": [3, 6], "million": 2, "mime": 3, "min_length": [1, 11], "minilm": 2, "minimum": 9, "minlen": [1, 11], "minreplica": 8, "minut": [3, 5, 11], "miss": [0, 1, 4, 5, 6, 11], "mit": 10, "mitig": 6, "ml": 2, "mobil": 6, "mode": [0, 7, 8], "model": [0, 3, 4, 5, 8, 9, 11], "model_config": [0, 1, 11], "model_dump": 1, "model_dump_json": 1, "model_embedding_dimens": 7, "model_embedding_model": 7, "model_gpu_memory_fract": [7, 8], "model_reasoning_engin": 7, "model_reasoning_model": 7, "model_use_gpu": [7, 8, 9], "model_valid": 1, "model_validate_json": 1, "model_vector_db_backend": 7, "modelrol": [1, 10, 11], "modern": [2, 6, 9, 10], "modul": 10, "modular": [6, 10], "monitor": [1, 2, 3, 4, 9], "month": 6, "more": [2, 4, 11], "most": [2, 11], "msg": 1, "multi": [6, 10], "multipl": [2, 4, 6, 7, 8, 10, 11], "must": [1, 2, 4, 11], "n": [2, 4, 9, 11], "name": [1, 2, 3, 4, 5, 7, 8, 11], "nano": 9, "nativ": 6, "natur": [4, 6, 11], "ndarrai": [2, 11], "need": 9, "network": [1, 2, 4, 6, 8, 9, 11], "neural": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11], "neural_symbol": 8, "neural_symbolic_backend": 8, "neural_symbolic_frontend": 8, "never": [3, 7], "new": [6, 11], "newkei": 8, "next": 10, "nginx": 6, "node": 8, "nodelai": 8, "non": [2, 3, 4, 8, 11], "none": [0, 1, 2, 3, 4, 6, 11], "normal": 6, "nosniff": 8, "now": 9, "nucleu": [1, 5, 11], "null": [3, 7], "number": [0, 2, 3, 5, 11], "numpi": 11, "nvidia": [4, 8, 9, 11], "o": 6, "object": [0, 1, 2, 3, 4, 5, 11], "observ": 9, "off": 8, "old": [0, 11], "oldest": [0, 11], "onc": 9, "one": 1, "onli": [4, 8, 11], "open": 7, "openai": [0, 1, 6, 7, 9, 10, 11], "openai_api_kei": 4, "openai_chat": [0, 10, 11], "openai_chat_stream": [0, 10, 11], "openaichatchoic": [1, 10, 11], "openaichatrequest": [0, 1, 10, 11], "openaichatrespons": [0, 1, 10, 11], "openaimessag": [1, 10, 11], "openapi": 10, "openssl": 8, "oper": [0, 1, 2, 3, 4, 5, 6, 9, 10, 11], "optim": [0, 1, 4, 8, 10, 11], "optimize_gpu_memori": [0, 10, 11], "optimize_index": [2, 11], "option": [0, 1, 2, 3, 4, 5, 6, 7, 10, 11], "opu": 4, "orchestr": 8, "order": [2, 11], "org": 9, "organ": 2, "origin": [5, 6, 7, 10, 11], "other": [0, 1, 3, 11], "otherwis": [3, 11], "our": 9, "out": [2, 8], "output": 6, "overrid": [6, 7], "overridden": [1, 11], "overview": 11, "p": 8, "packag": 9, "page": 10, "paper": 2, "parallel": 8, "param": 6, "paramet": [0, 1, 2, 3, 4, 10, 11], "pars": [1, 4, 6, 11], "parti": 6, "pass": [3, 6], "password": 8, "path": [8, 11], "pattern": [2, 3, 10, 11], "pem": 8, "penetr": 6, "per": [3, 4, 5, 10], "perfect": [2, 11], "perform": 11, "performance_stat": [0, 10, 11], "performancefilt": 11, "performancemetr": [10, 11], "performancemonitor": 11, "period": [8, 11], "periodsecond": 8, "permiss": [4, 8, 11], "persist": [2, 6], "pinecon": [6, 7], "ping": [7, 8], "pip": [8, 9, 10], "pipelin": 8, "point": 6, "polici": [2, 3, 10], "pool": [6, 10], "popul": 7, "port": [0, 7, 8], "posit": [2, 11], "possibl": 3, "post": [0, 3, 5, 9, 10], "postgresql": [6, 8], "potenti": 6, "power": [2, 11], "practic": [10, 11], "pre": [2, 11], "predict": [4, 11], "preflight": 3, "preprocess": 2, "prerequisit": 10, "present": [4, 11], "prevent": [2, 3], "previou": [4, 11], "price": 4, "principl": 4, "print": [0, 1, 2, 3, 4, 7, 9, 10, 11], "privaci": 4, "privileg": 6, "probe": 10, "procedur": 6, "process": [0, 1, 3, 9, 10, 11], "process_chat_request": 1, "process_queri": [0, 4, 6, 10, 11], "prod": [7, 8], "product": [2, 3, 4, 6, 9, 11], "profil": 1, "progress": [2, 4], "project": 10, "prometheu": 10, "prompt": [1, 11], "prompt_token": [1, 11], "proper": [1, 2, 4, 11], "properli": [4, 8, 11], "properti": [4, 11], "protect": [0, 3, 6, 10], "protected_endpoint": 3, "proto": 8, "protocol": 8, "provid": [0, 1, 2, 3, 4, 5, 6, 8, 10, 11], "proxi": 3, "proxy_add_x_forwarded_for": 8, "proxy_connect_timeout": 8, "proxy_pass": 8, "proxy_read_timeout": 8, "proxy_send_timeout": 8, "proxy_set_head": 8, "public_data": 1, "purpos": 6, "put": 5, "py": [6, 7, 8, 9, 10], "pydant": [0, 1, 6, 7, 9, 11], "python": [1, 7, 8, 9, 10], "pytorch": [0, 2, 4, 9, 11], "q": 4, "qualiti": [2, 4, 6, 9, 11], "quantiz": 6, "quantum": 6, "queri": [0, 4, 6, 11], "query_hash": 6, "query_vector": 11, "question": [2, 4, 9, 11], "queue": 6, "quickli": 9, "r": [2, 8, 9, 10], "rais": [1, 2, 3, 4, 11], "ram": 9, "random": [1, 2, 4, 5, 11], "randomli": 3, "rang": [2, 11], "rank": 6, "rate": [0, 4, 6, 7, 8, 9, 10, 11], "rate_limit": 3, "rate_limit_error": [0, 5], "rate_limit_middlewar": [0, 10, 11], "rate_limit_request": 7, "ratelimit": [5, 10, 11], "ratelimiterror": [3, 11], "ratio": 6, "read": 6, "read_root": [0, 10, 11], "readabl": [1, 5, 11], "readi": [8, 9, 10], "readinessprob": 8, "real": [3, 6, 8, 10], "reason": [0, 1, 2, 5, 7, 9, 10, 11], "reasoner_info": [1, 11], "reasoning_engin": 7, "reasoning_error": 5, "reasoning_tim": 11, "reasoning_typ": 11, "reasoningerror": [4, 11], "recal": 2, "recent": [6, 11], "recommend": [7, 9], "recomput": 2, "record": [3, 11], "record_cache_hit": 11, "record_cache_miss": 11, "record_failed_attempt": [3, 11], "record_reasoning_tim": 11, "record_retrieval_tim": 11, "record_token_count": 11, "recoveri": 10, "redi": [6, 7, 8, 11], "redirect": 8, "redis_url": 7, "redoc": [5, 6], "reduc": 8, "reduct": 6, "refer": 9, "referr": 3, "regular": [2, 6, 8], "regularli": [3, 7], "reinstal": 9, "reject": 3, "relev": [2, 4, 6, 11], "reload": [7, 8], "remain": 5, "remot": [4, 11], "remote_addr": 8, "remov": [0, 3, 11], "repeat": 3, "replac": [2, 9, 10, 11], "replica": [6, 8], "report": 9, "repositori": 9, "repres": [1, 11], "represent": 11, "req": 8, "request": [0, 7, 8, 9, 10, 11], "request_copi": 1, "request_data": 1, "request_id": 11, "request_uri": 8, "requestmetr": 11, "requir": [1, 4, 5, 8, 10, 11], "rerank": [2, 11], "research_pap": [1, 11], "reserv": 8, "reset": 5, "resourc": [3, 4, 5, 6, 8, 10, 11], "resource_id": 11, "resource_typ": 11, "resourcenotfounderror": 11, "respons": [0, 3, 4, 6, 8, 9, 10, 11], "response_cont": 1, "responsecach": [0, 10, 11], "rest": [6, 10], "restart": 8, "restart_polici": 8, "restor": 8, "result": [0, 2, 6, 9, 11], "retent": 7, "retri": [8, 11], "retriev": [0, 1, 4, 5, 9, 10, 11], "retrieval_error": 5, "retrieval_tim": 11, "retrievalerror": [2, 11], "retriever_info": [1, 11], "retry_aft": [3, 11], "return": [0, 1, 2, 3, 4, 5, 7, 8, 11], "revers": 3, "review": 3, "rf": 8, "rich": 2, "risk": 6, "rm": 8, "role": [0, 1, 5, 9, 10, 11], "rollback": 6, "rollout": 6, "root": [3, 8], "rotat": [3, 7], "roundrobin": 8, "rout": [6, 10], "rsa": 8, "rtx": [4, 11], "rule": [1, 2, 4, 6, 7, 11], "run": [2, 4, 8, 9, 10], "runtim": 6, "safe": [3, 11], "safe_load": 7, "safeti": [1, 4, 7], "sampl": [1, 5, 11], "sanit": [6, 10, 11], "sanitize_input": [3, 11], "scalabl": [2, 9, 10], "scale": [4, 10, 11], "scaletargetref": 8, "scenario": 1, "scheme": 8, "score": [2, 6, 11], "score_threshold": 2, "scrape": 8, "scrape_config": 8, "scrape_interv": 8, "script": [8, 9], "search": [6, 9, 10, 11], "second": [3, 6, 11], "secret": [3, 6, 8], "section": [5, 11], "secur": [1, 9, 11], "security_api_kei": [7, 8], "security_block_dur": 7, "security_cors_origin": [7, 8], "security_manag": 3, "security_max_failed_attempt": 7, "security_max_request_s": 7, "security_middlewar": 3, "security_rate_limit_request": [7, 8], "security_rate_limit_window": 7, "securitymanag": [10, 11], "see": [9, 10], "select": [2, 11], "selector": 8, "self": [3, 6, 8], "semant": [2, 6, 9, 10], "sender": [1, 11], "sensibl": 1, "sensit": 7, "sent": [0, 11], "sentenc": [2, 11], "separ": [7, 10], "sequenc": [1, 5], "serial": [6, 10, 11], "server": [0, 5, 6, 7, 9, 10, 11], "server_nam": 8, "servic": [6, 7, 8, 11], "service_nam": 11, "serviceunavailableerror": 11, "session": 6, "set": [0, 2, 3, 4, 5, 6, 8, 9, 10, 11], "set_context": 11, "setup": [2, 10, 11], "setup_log": 11, "setup_structured_log": 11, "setup_vector_db": [2, 11], "sever": 9, "shape": 11, "share": [3, 5, 6, 8, 11], "short": 6, "should": [0, 1, 2, 11], "show_progress": 2, "shutdown": [0, 11], "shutdown_ev": [0, 10, 11], "side": 6, "sign": 8, "signific": [2, 11], "significantli": [2, 4, 11], "similar": [2, 6, 9, 11], "simpl": [3, 4, 11], "singl": [1, 11], "size": [0, 1, 2, 4, 5, 6, 7, 8, 10, 11], "slim": 8, "slow": 8, "small": [2, 7, 8], "smaller": 7, "smi": 8, "smith": [1, 11], "snif": 3, "sonnet": [4, 7], "sophist": [2, 4, 11], "sort": [2, 11], "sourc": [0, 1, 2, 3, 4, 9, 11], "space": 9, "spars": [2, 11], "spec": 8, "specif": [2, 4, 6, 9, 11], "specifi": [3, 4, 9, 11], "speed": [2, 11], "split": 1, "sqlite": 8, "src": [6, 7, 8, 9, 10], "ssl": 6, "ssl_certif": 8, "ssl_certificate_kei": 8, "ssl_fc": 8, "stabl": 6, "stack": [6, 8, 10], "stage": [2, 6, 7, 10, 11], "standalon": 8, "start": [7, 8, 11], "start_request": 11, "start_tim": 11, "stat": [8, 11], "state": 6, "stateless": 6, "statement": [4, 11], "static_config": 8, "statist": [0, 1, 6, 11], "statu": [1, 4, 5, 6, 8, 9, 11], "status_cod": [3, 6, 11], "step": [4, 10, 11], "stop": [1, 5, 8, 11], "storag": [2, 6], "store": [2, 3, 11], "str": [0, 1, 2, 3, 4, 6, 11], "str_strip_whitespac": [1, 11], "stream": [0, 6, 9, 10, 11], "streamingrespons": [0, 11], "strict": [3, 8], "string": [2, 4, 5, 11], "stringifi": 9, "strip": 3, "strong": [3, 8], "structur": [0, 1, 3, 4, 5, 6, 7, 10, 11], "structured_log": 7, "structuredformatt": 11, "success": [0, 1, 2, 11], "successfulli": 6, "suit": 10, "sum": [1, 11], "support": [0, 1, 3, 6, 7, 8, 9, 11], "swagger": [5, 6], "switch": 6, "symbol": [0, 1, 2, 3, 5, 7, 8, 9, 11], "symbolic_ai_exception_handl": [0, 10, 11], "symbolic_reason": [4, 11], "symbolicai": [0, 11], "symbolicaiexcept": [0, 11], "symbolicreason": [6, 10, 11], "syntax": 7, "system": [3, 4, 7, 8, 10], "system_info": [0, 10, 11], "system_msg": 1, "systeminfo": [10, 11], "systemmetr": [1, 11], "t": [1, 8, 9, 11], "taken": [4, 11], "tar": 8, "target": 8, "targetport": 8, "task": [4, 11], "tcp": 8, "team": 9, "temperatur": [0, 1, 4, 5, 9, 11], "templat": [8, 9, 10], "temporarili": 11, "term": 6, "test": [2, 3, 5, 6, 7, 8, 9, 10, 11], "text": [0, 1, 2, 3, 5, 6, 11], "text_hash": 6, "textbook": 2, "thi": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "third": 6, "thoroughli": 3, "thread": 11, "threshold": 2, "through": [7, 9], "throughout": 1, "throughput": [1, 4], "time": [1, 3, 6, 8, 10, 11], "timeout": [7, 8, 10], "timestamp": [1, 6, 9, 11], "titl": 7, "tl": 6, "tmp": 8, "to_dict": [3, 11], "todai": [1, 11], "token": [1, 3, 4, 5, 6, 10, 11], "tokenusag": [1, 10, 11], "too": [3, 11], "tool": [6, 7, 9], "top": [2, 11], "top_p": [1, 5, 11], "topic": [2, 9, 11], "torch": [7, 9], "torchaudio": 9, "torchvectorstor": 11, "torchvis": 9, "total": [1, 11], "total_token": [1, 11], "trace": 11, "track": [2, 4, 7, 10, 11], "tracker": 9, "traffic": [3, 6], "transform": [2, 9], "transit": [4, 6, 11], "transport": [3, 8], "trigger": [3, 6], "troubleshoot": 10, "true": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11], "trust": 1, "try": [1, 2, 3, 4], "ttl": [2, 4, 6, 10, 11], "ttl_second": 11, "turbo": [4, 7], "two": [2, 11], "txt": [8, 9, 10], "type": [0, 1, 2, 3, 4, 5, 7, 8, 9, 11], "typic": [2, 11], "u": [7, 8], "ubuntu": 9, "ui": 5, "unavail": 11, "under": 10, "underli": [4, 11], "understand": [6, 9], "unexpect": 5, "unifi": 11, "union": [2, 11], "uniqu": [1, 2, 3, 11], "unix": [1, 11], "unless": [1, 8, 11], "up": [2, 4, 7, 8, 9, 11], "updat": [2, 3, 6, 8], "update_config": 8, "upload": 3, "upload_endpoint": 3, "upstream": 8, "url": 9, "us": [0, 1, 2, 3, 4, 5, 7, 8, 10, 11], "usag": [6, 8, 9, 10, 11], "use_gpu": [2, 4, 6, 7, 11], "user": [0, 1, 3, 5, 6, 8, 9, 11], "user1": 7, "user2": 7, "user_456": 6, "user_id": 6, "user_input": 3, "user_msg": 1, "useradd": 8, "using_torch_fallback": [2, 11], "usr": 8, "usual": 3, "util": [4, 6, 8, 10], "uvicorn": [7, 8, 9], "v": [1, 8, 11], "v1": [0, 2, 6, 7, 8, 9, 10], "v2": [1, 2, 6, 8, 11], "valid": [0, 2, 4, 6, 9, 10, 11], "validate_api_kei": [3, 11], "validate_assign": [1, 11], "validate_configur": 7, "validate_cont": [1, 11], "validate_cors_origin": [3, 10, 11], "validate_custom_set": 7, "validate_messag": [1, 11], "validate_temperatur": 1, "validate_text": [1, 11], "validate_total_token": [1, 11], "validation_error": [0, 1, 5], "validation_exception_handl": [0, 10, 11], "validationerror": [0, 1, 2, 4, 11], "valu": [0, 1, 2, 3, 7, 8, 11], "valueerror": [1, 3, 11], "var": [7, 8], "variabl": [3, 4, 6, 10, 11], "variou": 4, "vector": [5, 8, 9, 10], "vector_db": [2, 9, 11], "vector_db_backend": 7, "vector_indices_backup": 8, "vector_stor": 11, "vectorstoreerror": [2, 11], "venv": 9, "veri": [2, 8], "verif": 10, "verifi": [3, 9, 11], "verify_api_kei": [3, 10, 11], "version": [1, 4, 7, 8, 9, 11], "via": [3, 4], "virtual": 9, "volum": 8, "vpn": 6, "vram": 9, "wa": [1, 11], "wait": 11, "warn": [6, 7, 11], "we": [4, 11], "web": [6, 9, 10], "websocket": 6, "welcom": [9, 10], "well": 2, "what": [0, 1, 2, 4, 9, 10, 11], "when": [1, 3, 4, 7, 9, 11], "where": [2, 5, 11], "whether": [1, 2, 4, 11], "which": 9, "while": [3, 11], "whitelist": 3, "whitespac": 3, "whl": 9, "why": [1, 11], "wildcard": 3, "window": [3, 9, 11], "within": [3, 4, 11], "work": [4, 8, 9, 10], "workdir": 8, "worker": [0, 6, 7, 8, 10], "workload": 8, "world": [3, 6], "would": [2, 4, 11], "wrapper": [2, 11], "x": [3, 5, 8, 9], "x509": 8, "xpack": 8, "xss": 3, "xzf": 8, "y": 8, "yaml": [6, 8], "yml": 8, "you": [1, 5, 7, 9, 11], "your": [0, 2, 5, 7, 8, 9, 10], "yourdomain": [3, 7, 8, 11], "zero": 6, "zip": 4, "zone": 8}, "titles": ["Main Application Module", "Data Models Module", "Vector Retrieval Module", "Security Module", "Symbolic Reasoning Module", "API Reference", "Architecture Overview", "Configuration Guide", "Deployment Guide", "Getting Started", "Neural Symbolic Language Model Documentation", "API Reference"], "titleterms": {"Not": 9, "acceler": 2, "advanc": [2, 4], "alreadi": 9, "anthrop": 4, "api": [3, 5, 8, 9, 10, 11], "applic": [0, 6, 7], "architectur": [6, 10], "attempt": 3, "authent": [3, 5], "auto": 8, "avail": 9, "backend": 2, "backup": 8, "balanc": 8, "base": 5, "basic": [2, 4], "batch": [2, 4], "best": [1, 2, 3, 7], "block": 3, "blue": 6, "built": 7, "cach": [2, 4, 6, 7], "call": 9, "canari": 6, "chat": [0, 5, 11], "chatcompletionchunk": 1, "chatcompletionrequest": 1, "chatcompletionrespons": 1, "check": [0, 5, 9], "choic": 1, "choicedelta": 1, "chromadb": 2, "class": [2, 3, 4], "client": 3, "code": 5, "collect": 6, "common": [5, 7, 8, 9], "compat": 5, "complet": [0, 1, 3, 5], "compon": [6, 10, 11], "compos": 8, "configur": [0, 2, 3, 4, 6, 7, 8, 11], "consider": [2, 4], "content": 10, "convers": 1, "cor": [3, 5], "core": [2, 3, 4, 6, 7, 10, 11], "custom": [1, 7], "cycl": 1, "dashboard": 8, "data": [1, 6, 8], "databas": 8, "default": 5, "defens": 6, "depend": 9, "deploy": [6, 8, 10], "depth": 6, "design": [1, 6], "detect": 3, "develop": [7, 8, 10], "dictionari": 1, "disast": 8, "distribut": 6, "docker": [7, 8], "document": [2, 5, 10], "embed": 2, "endpoint": [0, 5, 11], "engin": [4, 6], "enhanc": 6, "environ": [7, 8], "error": [0, 1, 2, 3, 4, 5], "errordetail": 1, "errorrespons": 1, "exampl": [0, 1, 2, 3, 4], "except": 11, "fail": 3, "faiss": 2, "fastapi": [0, 6], "featur": [1, 9, 10], "field": 1, "file": 7, "first": 9, "flow": 6, "format": 5, "futur": 6, "gener": 7, "get": 9, "gpu": [2, 4, 9], "grafana": 8, "green": 6, "guid": [7, 8, 10], "handl": [0, 2, 3, 4, 11], "haproxi": 8, "harden": 8, "header": [3, 5], "health": [0, 5, 9], "help": 9, "high": 6, "horizont": 6, "hpa": 8, "improv": 6, "index": 2, "indic": 10, "info": 5, "inform": [0, 2, 5, 9], "initi": [2, 3, 4], "input": 3, "instal": 9, "ip": 3, "issu": [7, 8, 9], "json": 1, "kei": [3, 9], "kubernet": 8, "languag": 10, "layer": 6, "level": 6, "licens": 10, "limit": [3, 5], "load": [7, 8], "local": [4, 8], "log": [6, 7, 8, 11], "main": 0, "manag": [3, 6, 7, 11], "messag": [1, 5], "method": [2, 3, 4], "metric": [5, 6, 8, 9], "miss": 9, "model": [1, 2, 6, 7, 10], "modul": [0, 1, 2, 3, 4, 11], "monitor": [0, 5, 6, 7, 8, 10, 11], "neural": 10, "next": 9, "nginx": 8, "observ": [6, 8, 10], "openai": [4, 5], "openapi": 5, "optim": [2, 6], "option": 8, "origin": 3, "overview": [1, 2, 3, 4, 6, 7, 8, 9, 10], "paramet": 5, "pattern": 6, "perform": [0, 1, 2, 4, 5, 6, 7, 8, 9, 10], "performancemetr": 1, "plan": 6, "port": 9, "practic": [1, 2, 3, 7], "prepar": 2, "prerequisit": 9, "process": [2, 4, 6], "product": [7, 8, 10], "prometheu": 8, "queri": 2, "quick": [9, 10], "rate": [3, 5], "ratelimit": 3, "readi": 5, "reason": [4, 6], "recoveri": 8, "refer": [5, 10, 11], "request": [1, 3, 5, 6], "requir": 9, "respons": [1, 5], "rest": 5, "retriev": [2, 6], "rout": 11, "sanit": 3, "scalabl": 6, "scale": [2, 6, 8], "search": 2, "secur": [0, 3, 6, 7, 8, 10], "securitymanag": 3, "serial": 1, "server": 8, "set": 7, "setup": [3, 7], "singl": 8, "size": 3, "specif": 7, "ssl": 8, "start": [9, 10], "step": 9, "storag": 11, "strategi": 6, "stream": [1, 5], "support": [2, 4, 5, 10], "swarm": 8, "symbol": [4, 6, 10], "symbolicreason": 4, "system": [0, 1, 2, 5, 6, 9, 11], "systeminfo": 1, "tabl": 10, "templat": 7, "threat": 6, "tl": 8, "trace": 6, "track": 3, "troubleshoot": [7, 8], "tune": 8, "url": 5, "us": 9, "usag": [0, 1, 2, 3, 4], "user": 10, "util": 11, "valid": [1, 3, 7], "variabl": [7, 8], "vector": [2, 6, 11], "verif": 9, "yaml": 7}})