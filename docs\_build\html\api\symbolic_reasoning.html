

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Symbolic Reasoning Module &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Vector Retrieval Module" href="retrieval.html" />
    <link rel="prev" title="Main Application Module" href="main.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../modules.html">API Reference</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="../modules.html#core-modules">Core Modules</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="main.html">Main Application Module</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">Symbolic Reasoning Module</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="#symbolicreasoner-class">SymbolicReasoner Class</a></li>
<li class="toctree-l4"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#supported-engines">Supported Engines</a></li>
<li class="toctree-l4"><a class="reference internal" href="#example-usage">Example Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l4"><a class="reference internal" href="#performance-considerations">Performance Considerations</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="security.html">Security Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="models.html">Data Models Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="../modules.html#module-main">Core Components</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#utility-modules">Utility Modules</a></li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#api-routes">API Routes</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="main.html">Main Application Module</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Symbolic Reasoning Module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner.use_gpu"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.use_gpu</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner.engine"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.engine</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner.model"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner.__init__"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.__init__()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner.process_query"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.process_query()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner.get_system_info"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.get_system_info()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#symbolic_reasoning.SymbolicReasoner.batch_process_queries"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.batch_process_queries()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#symbolicreasoner-class">SymbolicReasoner Class</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id0"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id1"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.use_gpu</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.engine</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.model</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.process_query()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id6"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.get_system_info()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.batch_process_queries()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#initialization">Initialization</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id8"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.__init__()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#core-methods">Core Methods</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id9"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.process_query()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id10"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.batch_process_queries()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id11"><code class="docutils literal notranslate"><span class="pre">SymbolicReasoner.get_system_info()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="#supported-engines">Supported Engines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#local-engine">Local Engine</a></li>
<li class="toctree-l3"><a class="reference internal" href="#openai-engine">OpenAI Engine</a></li>
<li class="toctree-l3"><a class="reference internal" href="#anthropic-engine">Anthropic Engine</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#basic-usage">Basic Usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="#batch-processing">Batch Processing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#performance-considerations">Performance Considerations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#gpu-usage">GPU Usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="#caching">Caching</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id12">Batch Processing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../modules.html">API Reference</a></li>
      <li class="breadcrumb-item active">Symbolic Reasoning Module</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api/symbolic_reasoning.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-symbolic_reasoning">
<span id="symbolic-reasoning-module"></span><h1>Symbolic Reasoning Module<a class="headerlink" href="#module-symbolic_reasoning" title="Link to this heading"></a></h1>
<p>Symbolic reasoning module for the Neural Symbolic Language Model.</p>
<p>This module provides symbolic reasoning capabilities with proper error handling,
logging, and performance monitoring.</p>
<p>Author: AI Assistant
Date: 2025-06-29</p>
<dl class="py class">
<dt class="sig sig-object py" id="symbolic_reasoning.SymbolicReasoner">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">symbolic_reasoning.</span></span><span class="sig-name descname"><span class="pre">SymbolicReasoner</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">engine</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'local'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'llama'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#symbolic_reasoning.SymbolicReasoner" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Symbolic reasoning engine for neural-symbolic AI processing.</p>
<p>This class provides symbolic reasoning capabilities that can be combined
with neural networks for enhanced AI reasoning. It supports multiple
reasoning engines and GPU acceleration for optimal performance.</p>
<p>The symbolic reasoner processes queries using logical rules and symbolic
manipulation, providing explainable AI capabilities that complement
neural network predictions.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="symbolic_reasoning.SymbolicReasoner.use_gpu">
<span class="sig-name descname"><span class="pre">use_gpu</span></span><a class="headerlink" href="#symbolic_reasoning.SymbolicReasoner.use_gpu" title="Link to this definition"></a></dt>
<dd><p>Whether GPU acceleration is enabled</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="symbolic_reasoning.SymbolicReasoner.engine">
<span class="sig-name descname"><span class="pre">engine</span></span><a class="headerlink" href="#symbolic_reasoning.SymbolicReasoner.engine" title="Link to this definition"></a></dt>
<dd><p>The reasoning engine being used</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="symbolic_reasoning.SymbolicReasoner.model">
<span class="sig-name descname"><span class="pre">model</span></span><a class="headerlink" href="#symbolic_reasoning.SymbolicReasoner.model" title="Link to this definition"></a></dt>
<dd><p>The specific model within the engine</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<p class="rubric">Example</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span><span class="n">engine</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span> <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="s2">&quot;What is symbolic reasoning?&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;Symbolic reasoning involves manipulating symbols according to logical rules...&quot;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This implementation provides a foundation for symbolic reasoning.
In production, this would integrate with more sophisticated
symbolic AI frameworks and knowledge bases.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="symbolic_reasoning.SymbolicReasoner.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">engine</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'local'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'llama'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#symbolic_reasoning.SymbolicReasoner.__init__" title="Link to this definition"></a></dt>
<dd><p>Initialize the symbolic reasoning engine.</p>
<p>Sets up the symbolic reasoning engine with the specified configuration.
Automatically detects GPU availability and configures the engine accordingly.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>engine</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The reasoning engine to use. Supported engines include:
- “local”: Local symbolic reasoning engine
- “openai”: OpenAI-based reasoning (requires API key)
- “anthropic”: Anthropic-based reasoning (requires API key)</p></li>
<li><p><strong>model</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The specific model to use within the chosen engine.
For local engine, supports “llama”, “gpt”, etc.</p></li>
<li><p><strong>use_gpu</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><em>bool</em></a>) – Whether to enable GPU acceleration if available.
GPU acceleration significantly improves performance
for large-scale reasoning tasks.</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="../modules.html#exceptions.ConfigurationError" title="exceptions.ConfigurationError"><strong>ConfigurationError</strong></a> – If the engine configuration is invalid
or required dependencies are missing</p>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="c1"># Initialize with default local engine</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Initialize with specific configuration</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span>
<span class="gp">... </span>    <span class="n">engine</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;llama&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Check if GPU is being used</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU enabled: </span><span class="si">{</span><span class="n">reasoner</span><span class="o">.</span><span class="n">use_gpu</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>GPU acceleration requires CUDA-compatible hardware and
proper PyTorch installation with CUDA support.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Remote engines (OpenAI, Anthropic) require valid API keys
and internet connectivity.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symbolic_reasoning.SymbolicReasoner.process_query">
<span class="sig-name descname"><span class="pre">process_query</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.process_query"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#symbolic_reasoning.SymbolicReasoner.process_query" title="Link to this definition"></a></dt>
<dd><p>Process a query using symbolic reasoning with optional context.</p>
<p>This method performs symbolic reasoning on the input query, optionally
incorporating additional context to improve reasoning accuracy. The
reasoning process involves parsing the query, applying logical rules,
and generating an explainable response.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>query</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The natural language query to process. Must be a non-empty
string containing the question or statement to reason about.
Maximum length is 10,000 characters.</p></li>
<li><p><strong>context</strong> (<em>Optional</em><em>[</em><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a><em>]</em>) – Optional additional context to inform the reasoning process.
This can include relevant facts, background information,
or previous conversation history. If provided, it will be
incorporated into the reasoning process.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The reasoning engine’s response as a natural language string.
The response includes the reasoning conclusion and may contain
explanations of the logical steps taken.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><a class="reference internal" href="../modules.html#exceptions.ReasoningError" title="exceptions.ReasoningError"><strong>ReasoningError</strong></a> – If the reasoning process fails due to:
- Invalid logical structure in the query
- Engine-specific processing errors
- Resource limitations (memory, GPU)</p></li>
<li><p><a class="reference internal" href="../modules.html#exceptions.ValidationError" title="exceptions.ValidationError"><strong>ValidationError</strong></a> – If the input validation fails due to:
- Empty or None query
- Query exceeding maximum length
- Invalid character encoding</p></li>
</ul>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Simple reasoning query</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="s2">&quot;What is symbolic reasoning?&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;Symbolic reasoning involves manipulating symbols according to logical rules...&quot;</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Query with context</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">context</span> <span class="o">=</span> <span class="s2">&quot;We are discussing AI methodologies.&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span>
<span class="gp">... </span>    <span class="s2">&quot;How does it differ from neural networks?&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">context</span><span class="o">=</span><span class="n">context</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;In the context of AI methodologies, symbolic reasoning differs from...&quot;</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Logical reasoning</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span>
<span class="gp">... </span>    <span class="s2">&quot;If A implies B and B implies C, what can we conclude about A and C?&quot;</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;If A implies B and B implies C, then A implies C (transitive property)...&quot;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The reasoning quality depends on the underlying engine and model.
Local engines provide faster responses but may have limited
reasoning capabilities compared to cloud-based engines.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="../modules.html#symbolic_reasoning.SymbolicReasoner.get_system_info" title="symbolic_reasoning.SymbolicReasoner.get_system_info"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_system_info()</span></code></a> for checking engine capabilities</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symbolic_reasoning.SymbolicReasoner.get_system_info">
<span class="sig-name descname"><span class="pre">get_system_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><span class="pre">Any</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.get_system_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#symbolic_reasoning.SymbolicReasoner.get_system_info" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive information about the reasoning system configuration.</p>
<p>Retrieves detailed information about the current reasoning engine
configuration, including hardware capabilities, engine status,
and operational parameters.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A dictionary containing system configuration information with keys:</p>
<ul class="simple">
<li><p><strong>engine</strong> (str): The reasoning engine name</p></li>
<li><p><strong>model</strong> (str): The model being used</p></li>
<li><p><strong>gpu_enabled</strong> (bool): Whether GPU acceleration is enabled</p></li>
<li><p><strong>gpu_available</strong> (bool): Whether GPU hardware is available</p></li>
<li><p><strong>gpu_name</strong> (str|None): Name of the GPU if available</p></li>
<li><p><strong>status</strong> (str): Current operational status (“operational”, “error”)</p></li>
</ul>
</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Dict[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, Any]</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="../modules.html#exceptions.ReasoningError" title="exceptions.ReasoningError"><strong>ReasoningError</strong></a> – If system information cannot be retrieved due to:
- Hardware detection failures
- Engine communication errors
- Permission or access issues</p>
</dd>
<dt class="field-even">Example<span class="colon">:</span></dt>
<dd class="field-even"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span><span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">info</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Engine: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;engine&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">Engine: local</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU Available: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_available&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">GPU Available: True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Status: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;status&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">Status: operational</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Check GPU details if available</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">if</span> <span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_available&#39;</span><span class="p">]:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_name&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">GPU: NVIDIA GeForce RTX 4090</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>GPU information is only available when CUDA is properly installed
and compatible hardware is present.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 0.1.0.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="symbolic_reasoning.SymbolicReasoner.batch_process_queries">
<span class="sig-name descname"><span class="pre">batch_process_queries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">queries</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">contexts</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.batch_process_queries"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#symbolic_reasoning.SymbolicReasoner.batch_process_queries" title="Link to this definition"></a></dt>
<dd><p>Process multiple queries in batch.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>queries</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a>) – List of queries to process</p></li>
<li><p><strong>contexts</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a><em>, </em><em>optional</em>) – List of contexts for each query</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>List of responses for each query</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)">list</a></p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The symbolic reasoning module provides advanced logical reasoning capabilities
using various backend engines including local models, OpenAI, and Anthropic.</p>
<p>Key Features:
* Multiple reasoning engine support
* GPU acceleration
* Batch processing
* Caching for improved performance
* Comprehensive error handling</p>
</section>
<section id="symbolicreasoner-class">
<h2>SymbolicReasoner Class<a class="headerlink" href="#symbolicreasoner-class" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="id0">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">symbolic_reasoning.</span></span><span class="sig-name descname"><span class="pre">SymbolicReasoner</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">engine</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'local'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'llama'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id0" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Symbolic reasoning engine for neural-symbolic AI processing.</p>
<p>This class provides symbolic reasoning capabilities that can be combined
with neural networks for enhanced AI reasoning. It supports multiple
reasoning engines and GPU acceleration for optimal performance.</p>
<p>The symbolic reasoner processes queries using logical rules and symbolic
manipulation, providing explainable AI capabilities that complement
neural network predictions.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="id1">
<span class="sig-name descname"><span class="pre">use_gpu</span></span><a class="headerlink" href="#id1" title="Link to this definition"></a></dt>
<dd><p>Whether GPU acceleration is enabled</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id2">
<span class="sig-name descname"><span class="pre">engine</span></span><a class="headerlink" href="#id2" title="Link to this definition"></a></dt>
<dd><p>The reasoning engine being used</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id3">
<span class="sig-name descname"><span class="pre">model</span></span><a class="headerlink" href="#id3" title="Link to this definition"></a></dt>
<dd><p>The specific model within the engine</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<p class="rubric">Example</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span><span class="n">engine</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span> <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="s2">&quot;What is symbolic reasoning?&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;Symbolic reasoning involves manipulating symbols according to logical rules...&quot;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This implementation provides a foundation for symbolic reasoning.
In production, this would integrate with more sophisticated
symbolic AI frameworks and knowledge bases.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="id4">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">engine</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'local'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'llama'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id4" title="Link to this definition"></a></dt>
<dd><p>Initialize the symbolic reasoning engine.</p>
<p>Sets up the symbolic reasoning engine with the specified configuration.
Automatically detects GPU availability and configures the engine accordingly.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>engine</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The reasoning engine to use. Supported engines include:
- “local”: Local symbolic reasoning engine
- “openai”: OpenAI-based reasoning (requires API key)
- “anthropic”: Anthropic-based reasoning (requires API key)</p></li>
<li><p><strong>model</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The specific model to use within the chosen engine.
For local engine, supports “llama”, “gpt”, etc.</p></li>
<li><p><strong>use_gpu</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><em>bool</em></a>) – Whether to enable GPU acceleration if available.
GPU acceleration significantly improves performance
for large-scale reasoning tasks.</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="../modules.html#exceptions.ConfigurationError" title="exceptions.ConfigurationError"><strong>ConfigurationError</strong></a> – If the engine configuration is invalid
or required dependencies are missing</p>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="c1"># Initialize with default local engine</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Initialize with specific configuration</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span>
<span class="gp">... </span>    <span class="n">engine</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;llama&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Check if GPU is being used</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU enabled: </span><span class="si">{</span><span class="n">reasoner</span><span class="o">.</span><span class="n">use_gpu</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>GPU acceleration requires CUDA-compatible hardware and
proper PyTorch installation with CUDA support.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Remote engines (OpenAI, Anthropic) require valid API keys
and internet connectivity.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id5">
<span class="sig-name descname"><span class="pre">process_query</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.process_query"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id5" title="Link to this definition"></a></dt>
<dd><p>Process a query using symbolic reasoning with optional context.</p>
<p>This method performs symbolic reasoning on the input query, optionally
incorporating additional context to improve reasoning accuracy. The
reasoning process involves parsing the query, applying logical rules,
and generating an explainable response.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>query</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The natural language query to process. Must be a non-empty
string containing the question or statement to reason about.
Maximum length is 10,000 characters.</p></li>
<li><p><strong>context</strong> (<em>Optional</em><em>[</em><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a><em>]</em>) – Optional additional context to inform the reasoning process.
This can include relevant facts, background information,
or previous conversation history. If provided, it will be
incorporated into the reasoning process.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The reasoning engine’s response as a natural language string.
The response includes the reasoning conclusion and may contain
explanations of the logical steps taken.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><a class="reference internal" href="../modules.html#exceptions.ReasoningError" title="exceptions.ReasoningError"><strong>ReasoningError</strong></a> – If the reasoning process fails due to:
- Invalid logical structure in the query
- Engine-specific processing errors
- Resource limitations (memory, GPU)</p></li>
<li><p><a class="reference internal" href="../modules.html#exceptions.ValidationError" title="exceptions.ValidationError"><strong>ValidationError</strong></a> – If the input validation fails due to:
- Empty or None query
- Query exceeding maximum length
- Invalid character encoding</p></li>
</ul>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Simple reasoning query</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="s2">&quot;What is symbolic reasoning?&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;Symbolic reasoning involves manipulating symbols according to logical rules...&quot;</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Query with context</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">context</span> <span class="o">=</span> <span class="s2">&quot;We are discussing AI methodologies.&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span>
<span class="gp">... </span>    <span class="s2">&quot;How does it differ from neural networks?&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">context</span><span class="o">=</span><span class="n">context</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;In the context of AI methodologies, symbolic reasoning differs from...&quot;</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Logical reasoning</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span>
<span class="gp">... </span>    <span class="s2">&quot;If A implies B and B implies C, what can we conclude about A and C?&quot;</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;If A implies B and B implies C, then A implies C (transitive property)...&quot;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The reasoning quality depends on the underlying engine and model.
Local engines provide faster responses but may have limited
reasoning capabilities compared to cloud-based engines.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="../modules.html#symbolic_reasoning.SymbolicReasoner.get_system_info" title="symbolic_reasoning.SymbolicReasoner.get_system_info"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_system_info()</span></code></a> for checking engine capabilities</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id6">
<span class="sig-name descname"><span class="pre">get_system_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><span class="pre">Any</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.get_system_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id6" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive information about the reasoning system configuration.</p>
<p>Retrieves detailed information about the current reasoning engine
configuration, including hardware capabilities, engine status,
and operational parameters.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A dictionary containing system configuration information with keys:</p>
<ul class="simple">
<li><p><strong>engine</strong> (str): The reasoning engine name</p></li>
<li><p><strong>model</strong> (str): The model being used</p></li>
<li><p><strong>gpu_enabled</strong> (bool): Whether GPU acceleration is enabled</p></li>
<li><p><strong>gpu_available</strong> (bool): Whether GPU hardware is available</p></li>
<li><p><strong>gpu_name</strong> (str|None): Name of the GPU if available</p></li>
<li><p><strong>status</strong> (str): Current operational status (“operational”, “error”)</p></li>
</ul>
</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Dict[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, Any]</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="../modules.html#exceptions.ReasoningError" title="exceptions.ReasoningError"><strong>ReasoningError</strong></a> – If system information cannot be retrieved due to:
- Hardware detection failures
- Engine communication errors
- Permission or access issues</p>
</dd>
<dt class="field-even">Example<span class="colon">:</span></dt>
<dd class="field-even"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span><span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">info</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Engine: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;engine&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">Engine: local</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU Available: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_available&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">GPU Available: True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Status: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;status&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">Status: operational</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Check GPU details if available</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">if</span> <span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_available&#39;</span><span class="p">]:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_name&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">GPU: NVIDIA GeForce RTX 4090</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>GPU information is only available when CUDA is properly installed
and compatible hardware is present.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 0.1.0.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id7">
<span class="sig-name descname"><span class="pre">batch_process_queries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">queries</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">contexts</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.batch_process_queries"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id7" title="Link to this definition"></a></dt>
<dd><p>Process multiple queries in batch.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>queries</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a>) – List of queries to process</p></li>
<li><p><strong>contexts</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a><em>, </em><em>optional</em>) – List of contexts for each query</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>List of responses for each query</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)">list</a></p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<p>The main class for symbolic reasoning operations.</p>
<section id="initialization">
<h3>Initialization<a class="headerlink" href="#initialization" title="Link to this heading"></a></h3>
<dl class="py method">
<dt class="sig sig-object py" id="id8">
<span class="sig-prename descclassname"><span class="pre">SymbolicReasoner.</span></span><span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">engine</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'local'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'llama'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_gpu</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id8" title="Link to this definition"></a></dt>
<dd><p>Initialize the symbolic reasoning engine.</p>
<p>Sets up the symbolic reasoning engine with the specified configuration.
Automatically detects GPU availability and configures the engine accordingly.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>engine</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The reasoning engine to use. Supported engines include:
- “local”: Local symbolic reasoning engine
- “openai”: OpenAI-based reasoning (requires API key)
- “anthropic”: Anthropic-based reasoning (requires API key)</p></li>
<li><p><strong>model</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The specific model to use within the chosen engine.
For local engine, supports “llama”, “gpt”, etc.</p></li>
<li><p><strong>use_gpu</strong> (<a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><em>bool</em></a>) – Whether to enable GPU acceleration if available.
GPU acceleration significantly improves performance
for large-scale reasoning tasks.</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="../modules.html#exceptions.ConfigurationError" title="exceptions.ConfigurationError"><strong>ConfigurationError</strong></a> – If the engine configuration is invalid
or required dependencies are missing</p>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="c1"># Initialize with default local engine</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Initialize with specific configuration</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span>
<span class="gp">... </span>    <span class="n">engine</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;llama&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Check if GPU is being used</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU enabled: </span><span class="si">{</span><span class="n">reasoner</span><span class="o">.</span><span class="n">use_gpu</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>GPU acceleration requires CUDA-compatible hardware and
proper PyTorch installation with CUDA support.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Remote engines (OpenAI, Anthropic) require valid API keys
and internet connectivity.</p>
</div>
</dd></dl>

</section>
<section id="core-methods">
<h3>Core Methods<a class="headerlink" href="#core-methods" title="Link to this heading"></a></h3>
<dl class="py method">
<dt class="sig sig-object py" id="id9">
<span class="sig-prename descclassname"><span class="pre">SymbolicReasoner.</span></span><span class="sig-name descname"><span class="pre">process_query</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">query</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.process_query"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id9" title="Link to this definition"></a></dt>
<dd><p>Process a query using symbolic reasoning with optional context.</p>
<p>This method performs symbolic reasoning on the input query, optionally
incorporating additional context to improve reasoning accuracy. The
reasoning process involves parsing the query, applying logical rules,
and generating an explainable response.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>query</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a>) – The natural language query to process. Must be a non-empty
string containing the question or statement to reason about.
Maximum length is 10,000 characters.</p></li>
<li><p><strong>context</strong> (<em>Optional</em><em>[</em><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><em>str</em></a><em>]</em>) – Optional additional context to inform the reasoning process.
This can include relevant facts, background information,
or previous conversation history. If provided, it will be
incorporated into the reasoning process.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The reasoning engine’s response as a natural language string.
The response includes the reasoning conclusion and may contain
explanations of the logical steps taken.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><a class="reference internal" href="../modules.html#exceptions.ReasoningError" title="exceptions.ReasoningError"><strong>ReasoningError</strong></a> – If the reasoning process fails due to:
- Invalid logical structure in the query
- Engine-specific processing errors
- Resource limitations (memory, GPU)</p></li>
<li><p><a class="reference internal" href="../modules.html#exceptions.ValidationError" title="exceptions.ValidationError"><strong>ValidationError</strong></a> – If the input validation fails due to:
- Empty or None query
- Query exceeding maximum length
- Invalid character encoding</p></li>
</ul>
</dd>
<dt class="field-odd">Example<span class="colon">:</span></dt>
<dd class="field-odd"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Simple reasoning query</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="s2">&quot;What is symbolic reasoning?&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;Symbolic reasoning involves manipulating symbols according to logical rules...&quot;</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Query with context</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">context</span> <span class="o">=</span> <span class="s2">&quot;We are discussing AI methodologies.&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span>
<span class="gp">... </span>    <span class="s2">&quot;How does it differ from neural networks?&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">context</span><span class="o">=</span><span class="n">context</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;In the context of AI methodologies, symbolic reasoning differs from...&quot;</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Logical reasoning</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span>
<span class="gp">... </span>    <span class="s2">&quot;If A implies B and B implies C, what can we conclude about A and C?&quot;</span>
<span class="gp">... </span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="go">&quot;If A implies B and B implies C, then A implies C (transitive property)...&quot;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The reasoning quality depends on the underlying engine and model.
Local engines provide faster responses but may have limited
reasoning capabilities compared to cloud-based engines.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="../modules.html#symbolic_reasoning.SymbolicReasoner.get_system_info" title="symbolic_reasoning.SymbolicReasoner.get_system_info"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_system_info()</span></code></a> for checking engine capabilities</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id10">
<span class="sig-prename descclassname"><span class="pre">SymbolicReasoner.</span></span><span class="sig-name descname"><span class="pre">batch_process_queries</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">queries</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">contexts</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.batch_process_queries"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id10" title="Link to this definition"></a></dt>
<dd><p>Process multiple queries in batch.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>queries</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a>) – List of queries to process</p></li>
<li><p><strong>contexts</strong> (<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)"><em>list</em></a><em>, </em><em>optional</em>) – List of contexts for each query</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>List of responses for each query</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#list" title="(in Python v3.13)">list</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id11">
<span class="sig-prename descclassname"><span class="pre">SymbolicReasoner.</span></span><span class="sig-name descname"><span class="pre">get_system_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><span class="pre">Any</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/symbolic_reasoning.html#SymbolicReasoner.get_system_info"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id11" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive information about the reasoning system configuration.</p>
<p>Retrieves detailed information about the current reasoning engine
configuration, including hardware capabilities, engine status,
and operational parameters.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p><p>A dictionary containing system configuration information with keys:</p>
<ul class="simple">
<li><p><strong>engine</strong> (str): The reasoning engine name</p></li>
<li><p><strong>model</strong> (str): The model being used</p></li>
<li><p><strong>gpu_enabled</strong> (bool): Whether GPU acceleration is enabled</p></li>
<li><p><strong>gpu_available</strong> (bool): Whether GPU hardware is available</p></li>
<li><p><strong>gpu_name</strong> (str|None): Name of the GPU if available</p></li>
<li><p><strong>status</strong> (str): Current operational status (“operational”, “error”)</p></li>
</ul>
</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Dict[<a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a>, Any]</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="../modules.html#exceptions.ReasoningError" title="exceptions.ReasoningError"><strong>ReasoningError</strong></a> – If system information cannot be retrieved due to:
- Hardware detection failures
- Engine communication errors
- Permission or access issues</p>
</dd>
<dt class="field-even">Example<span class="colon">:</span></dt>
<dd class="field-even"><p></p></dd>
</dl>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span><span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">info</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Engine: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;engine&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">Engine: local</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU Available: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_available&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">GPU Available: True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Status: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;status&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">Status: operational</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Check GPU details if available</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">if</span> <span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_available&#39;</span><span class="p">]:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_name&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="go">GPU: NVIDIA GeForce RTX 4090</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>GPU information is only available when CUDA is properly installed
and compatible hardware is present.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">Added in version 0.1.0.</span></p>
</div>
</dd></dl>

</section>
</section>
<section id="configuration">
<h2>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<p>The symbolic reasoner can be configured with the following parameters:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">engine</span></code>: Reasoning engine (“local”, “openai”, “anthropic”)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">model</span></code>: Model name (depends on engine)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">use_gpu</span></code>: Enable GPU acceleration (default: True)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">max_tokens</span></code>: Maximum tokens per response</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">temperature</span></code>: Response randomness (0.0-1.0)</p></li>
</ul>
</section>
<section id="supported-engines">
<h2>Supported Engines<a class="headerlink" href="#supported-engines" title="Link to this heading"></a></h2>
<section id="local-engine">
<h3>Local Engine<a class="headerlink" href="#local-engine" title="Link to this heading"></a></h3>
<p>Uses local language models for reasoning:</p>
<ul class="simple">
<li><p><strong>Model</strong>: Configurable local model (default: “llama”)</p></li>
<li><p><strong>GPU Support</strong>: Full GPU acceleration</p></li>
<li><p><strong>Privacy</strong>: All processing done locally</p></li>
<li><p><strong>Performance</strong>: Optimized for low latency</p></li>
</ul>
</section>
<section id="openai-engine">
<h3>OpenAI Engine<a class="headerlink" href="#openai-engine" title="Link to this heading"></a></h3>
<p>Uses OpenAI’s API for reasoning:</p>
<ul class="simple">
<li><p><strong>Models</strong>: GPT-3.5-turbo, GPT-4, etc.</p></li>
<li><p><strong>API Key</strong>: Required via <code class="docutils literal notranslate"><span class="pre">OPENAI_API_KEY</span></code> environment variable</p></li>
<li><p><strong>Rate Limits</strong>: Managed automatically</p></li>
<li><p><strong>Cost</strong>: Per-token pricing</p></li>
</ul>
</section>
<section id="anthropic-engine">
<h3>Anthropic Engine<a class="headerlink" href="#anthropic-engine" title="Link to this heading"></a></h3>
<p>Uses Anthropic’s Claude models:</p>
<ul class="simple">
<li><p><strong>Models</strong>: Claude-3-sonnet, Claude-3-opus, etc.</p></li>
<li><p><strong>API Key</strong>: Required via <code class="docutils literal notranslate"><span class="pre">ANTHROPIC_API_KEY</span></code> environment variable</p></li>
<li><p><strong>Safety</strong>: Built-in safety features</p></li>
<li><p><strong>Performance</strong>: High-quality reasoning</p></li>
</ul>
</section>
</section>
<section id="example-usage">
<h2>Example Usage<a class="headerlink" href="#example-usage" title="Link to this heading"></a></h2>
<section id="basic-usage">
<h3>Basic Usage<a class="headerlink" href="#basic-usage" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">symbolic_reasoning</span><span class="w"> </span><span class="kn">import</span> <span class="n">SymbolicReasoner</span>

<span class="c1"># Initialize reasoner</span>
<span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span>
    <span class="n">engine</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;llama&quot;</span><span class="p">,</span>
    <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>

<span class="c1"># Process a query</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span>
    <span class="s2">&quot;What are the key principles of symbolic reasoning?&quot;</span>
<span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="batch-processing">
<h3>Batch Processing<a class="headerlink" href="#batch-processing" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Process multiple queries</span>
<span class="n">queries</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s2">&quot;What is symbolic AI?&quot;</span><span class="p">,</span>
    <span class="s2">&quot;How does neural-symbolic integration work?&quot;</span><span class="p">,</span>
    <span class="s2">&quot;What are the benefits of hybrid AI systems?&quot;</span>
<span class="p">]</span>

<span class="n">responses</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">batch_process_queries</span><span class="p">(</span><span class="n">queries</span><span class="p">)</span>
<span class="k">for</span> <span class="n">query</span><span class="p">,</span> <span class="n">response</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="n">queries</span><span class="p">,</span> <span class="n">responses</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Q: </span><span class="si">{</span><span class="n">query</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;A: </span><span class="si">{</span><span class="n">response</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="advanced-configuration">
<h3>Advanced Configuration<a class="headerlink" href="#advanced-configuration" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Advanced configuration</span>
<span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span>
    <span class="n">engine</span><span class="o">=</span><span class="s2">&quot;openai&quot;</span><span class="p">,</span>
    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;gpt-4&quot;</span><span class="p">,</span>
    <span class="n">use_gpu</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
    <span class="n">max_tokens</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span>
    <span class="n">temperature</span><span class="o">=</span><span class="mf">0.3</span>
<span class="p">)</span>

<span class="c1"># Get system information</span>
<span class="n">info</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Engine: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;engine&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Model: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;model&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU Available: </span><span class="si">{</span><span class="n">info</span><span class="p">[</span><span class="s1">&#39;gpu_available&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>The module provides comprehensive error handling:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">symbolic_reasoning</span><span class="w"> </span><span class="kn">import</span> <span class="n">SymbolicReasoner</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">exceptions</span><span class="w"> </span><span class="kn">import</span> <span class="n">ReasoningError</span>

<span class="k">try</span><span class="p">:</span>
    <span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">()</span>
    <span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="s2">&quot;Complex reasoning query&quot;</span><span class="p">)</span>
<span class="k">except</span> <span class="n">ReasoningError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Reasoning failed: </span><span class="si">{</span><span class="n">e</span><span class="o">.</span><span class="n">message</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error code: </span><span class="si">{</span><span class="n">e</span><span class="o">.</span><span class="n">error_code</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Details: </span><span class="si">{</span><span class="n">e</span><span class="o">.</span><span class="n">details</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="performance-considerations">
<h2>Performance Considerations<a class="headerlink" href="#performance-considerations" title="Link to this heading"></a></h2>
<section id="gpu-usage">
<h3>GPU Usage<a class="headerlink" href="#gpu-usage" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Enable GPU acceleration for better performance with local models</p></li>
<li><p>GPU memory is managed automatically with context managers</p></li>
<li><p>Fallback to CPU if GPU is not available</p></li>
</ul>
</section>
<section id="caching">
<h3>Caching<a class="headerlink" href="#caching" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Responses are cached to improve performance</p></li>
<li><p>Cache key includes query content and model parameters</p></li>
<li><p>Configurable TTL and cache size limits</p></li>
</ul>
</section>
<section id="id12">
<h3>Batch Processing<a class="headerlink" href="#id12" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Use batch processing for multiple queries to improve throughput</p></li>
<li><p>Automatic batching optimizes GPU utilization</p></li>
<li><p>Progress tracking for long-running batch operations</p></li>
</ul>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="main.html" class="btn btn-neutral float-left" title="Main Application Module" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="retrieval.html" class="btn btn-neutral float-right" title="Vector Retrieval Module" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>