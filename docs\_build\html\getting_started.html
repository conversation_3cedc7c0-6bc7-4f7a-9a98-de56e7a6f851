

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Getting Started &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=39bd3b11" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=01f34227"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Configuration Guide" href="configuration.html" />
    <link rel="prev" title="Neural Symbolic Language Model Documentation" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Getting Started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#key-features">Key Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dependencies">Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#quick-installation">Quick Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="#first-api-call">First API Call</a></li>
<li class="toctree-l2"><a class="reference internal" href="#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#health-check">Health Check</a></li>
<li class="toctree-l3"><a class="reference internal" href="#system-information">System Information</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-metrics">Performance Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
<li class="toctree-l2"><a class="reference internal" href="#common-issues">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#port-already-in-use">Port Already in Use</a></li>
<li class="toctree-l3"><a class="reference internal" href="#gpu-not-available">GPU Not Available</a></li>
<li class="toctree-l3"><a class="reference internal" href="#missing-dependencies">Missing Dependencies</a></li>
<li class="toctree-l3"><a class="reference internal" href="#api-key-issues">API Key Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="api_reference.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="deployment.html">Deployment Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Getting Started</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/getting_started.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="getting-started">
<h1>Getting Started<a class="headerlink" href="#getting-started" title="Link to this heading"></a></h1>
<p>Welcome to the Neural Symbolic Language Model! This guide will help you get up and running quickly.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Neural Symbolic Language Model is a production-ready AI system that combines:</p>
<ul class="simple">
<li><p><strong>Symbolic Reasoning</strong>: Advanced logical reasoning capabilities</p></li>
<li><p><strong>Neural Language Processing</strong>: Modern transformer-based language understanding</p></li>
<li><p><strong>Vector Retrieval</strong>: Efficient semantic search and document retrieval</p></li>
<li><p><strong>OpenAI Compatibility</strong>: Drop-in replacement for OpenAI’s chat completions API</p></li>
</ul>
</section>
<section id="key-features">
<h2>Key Features<a class="headerlink" href="#key-features" title="Link to this heading"></a></h2>
<dl class="simple">
<dt>🧠 <strong>Hybrid AI Architecture</strong></dt><dd><p>Combines symbolic reasoning with neural language processing for enhanced explainability</p>
</dd>
<dt>🔍 <strong>Retrieval-Augmented Generation</strong></dt><dd><p>Grounds responses in factual information through vector-based document retrieval</p>
</dd>
<dt>🚀 <strong>Production Ready</strong></dt><dd><p>Enterprise-grade security, monitoring, and scalability features</p>
</dd>
<dt>🔌 <strong>OpenAI Compatible</strong></dt><dd><p>Drop-in replacement for OpenAI’s chat completions API</p>
</dd>
<dt>🛡️ <strong>Secure by Design</strong></dt><dd><p>Comprehensive authentication, rate limiting, and input validation</p>
</dd>
<dt>📊 <strong>Full Observability</strong></dt><dd><p>Built-in monitoring, logging, and performance metrics</p>
</dd>
</dl>
</section>
<section id="prerequisites">
<h2>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h2>
<section id="system-requirements">
<h3>System Requirements<a class="headerlink" href="#system-requirements" title="Link to this heading"></a></h3>
<p><strong>Minimum Requirements:</strong>
* Python 3.8 or higher
* 4GB RAM
* 2 CPU cores
* 10GB disk space</p>
<p><strong>Recommended for Production:</strong>
* Python 3.10+
* 8GB+ RAM
* 4+ CPU cores
* NVIDIA GPU with 8GB+ VRAM
* 50GB+ disk space</p>
<p><strong>Operating Systems:</strong>
* Linux (Ubuntu 20.04+, CentOS 8+)
* macOS (10.15+)
* Windows 10/11</p>
</section>
<section id="dependencies">
<h3>Dependencies<a class="headerlink" href="#dependencies" title="Link to this heading"></a></h3>
<p>The system requires several Python packages which will be installed automatically:</p>
<ul class="simple">
<li><p><strong>FastAPI</strong> - Modern web framework</p></li>
<li><p><strong>PyTorch</strong> - Neural network framework</p></li>
<li><p><strong>FAISS</strong> - Vector similarity search</p></li>
<li><p><strong>Pydantic</strong> - Data validation</p></li>
<li><p><strong>Uvicorn</strong> - ASGI server</p></li>
</ul>
</section>
</section>
<section id="quick-installation">
<h2>Quick Installation<a class="headerlink" href="#quick-installation" title="Link to this heading"></a></h2>
<ol class="arabic">
<li><p><strong>Clone the Repository</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/your-org/neural-symbolic-language-model.git
<span class="nb">cd</span><span class="w"> </span>neural-symbolic-language-model
</pre></div>
</div>
</li>
<li><p><strong>Create Virtual Environment</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Using venv</span>
python<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>env
<span class="nb">source</span><span class="w"> </span>env/bin/activate<span class="w">  </span><span class="c1"># On Windows: env\Scripts\activate</span>

<span class="c1"># Or using conda</span>
conda<span class="w"> </span>create<span class="w"> </span>-n<span class="w"> </span>neural-symbolic<span class="w"> </span><span class="nv">python</span><span class="o">=</span><span class="m">3</span>.10
conda<span class="w"> </span>activate<span class="w"> </span>neural-symbolic
</pre></div>
</div>
</li>
<li><p><strong>Install Dependencies</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements.txt
</pre></div>
</div>
</li>
<li><p><strong>Configure Environment</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Copy environment template</span>
cp<span class="w"> </span>.env.example<span class="w"> </span>.env

<span class="c1"># Edit configuration (see Configuration Guide for details)</span>
nano<span class="w"> </span>.env
</pre></div>
</div>
</li>
<li><p><strong>Start the Server</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python<span class="w"> </span>src/main.py
</pre></div>
</div>
<p>The server will start on <code class="docutils literal notranslate"><span class="pre">http://localhost:8000</span></code></p>
</li>
</ol>
</section>
<section id="first-api-call">
<h2>First API Call<a class="headerlink" href="#first-api-call" title="Link to this heading"></a></h2>
<p>Once the server is running, you can make your first API call:</p>
<p><strong>Using curl:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span><span class="s2">&quot;http://localhost:8000/v1/chat/completions&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer your-api-key&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>-d<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">       &quot;model&quot;: &quot;local&quot;,</span>
<span class="s1">       &quot;messages&quot;: [</span>
<span class="s1">         {</span>
<span class="s1">           &quot;role&quot;: &quot;user&quot;,</span>
<span class="s1">           &quot;content&quot;: &quot;What is symbolic reasoning and how does it differ from neural approaches?&quot;</span>
<span class="s1">         }</span>
<span class="s1">       ],</span>
<span class="s1">       &quot;max_tokens&quot;: 150,</span>
<span class="s1">       &quot;temperature&quot;: 0.7</span>
<span class="s1">     }&#39;</span>
</pre></div>
</div>
<p><strong>Using Python:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span><span class="w"> </span><span class="nn">requests</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/v1/chat/completions&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer your-api-key&quot;</span><span class="p">,</span>
        <span class="s2">&quot;Content-Type&quot;</span><span class="p">:</span> <span class="s2">&quot;application/json&quot;</span>
    <span class="p">},</span>
    <span class="n">json</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;local&quot;</span><span class="p">,</span>
        <span class="s2">&quot;messages&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="p">{</span>
                <span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;user&quot;</span><span class="p">,</span>
                <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;Explain the benefits of hybrid AI systems.&quot;</span>
            <span class="p">}</span>
        <span class="p">],</span>
        <span class="s2">&quot;max_tokens&quot;</span><span class="p">:</span> <span class="mi">150</span><span class="p">,</span>
        <span class="s2">&quot;temperature&quot;</span><span class="p">:</span> <span class="mf">0.7</span>
    <span class="p">}</span>
<span class="p">)</span>

<span class="n">result</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="n">result</span><span class="p">[</span><span class="s2">&quot;choices&quot;</span><span class="p">][</span><span class="mi">0</span><span class="p">][</span><span class="s2">&quot;message&quot;</span><span class="p">][</span><span class="s2">&quot;content&quot;</span><span class="p">])</span>
</pre></div>
</div>
<p><strong>Using JavaScript:</strong></p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">fetch</span><span class="p">(</span><span class="s1">&#39;http://localhost:8000/v1/chat/completions&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">headers</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s1">&#39;Authorization&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Bearer your-api-key&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="s1">&#39;Content-Type&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;application/json&#39;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nx">body</span><span class="o">:</span><span class="w"> </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">stringify</span><span class="p">({</span>
<span class="w">    </span><span class="nx">model</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;local&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">messages</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span>
<span class="w">        </span><span class="nx">role</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;user&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">content</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;How does retrieval-augmented generation work?&#39;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">],</span>
<span class="w">    </span><span class="nx">max_tokens</span><span class="o">:</span><span class="w"> </span><span class="mf">150</span><span class="p">,</span>
<span class="w">    </span><span class="nx">temperature</span><span class="o">:</span><span class="w"> </span><span class="mf">0.7</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">});</span>

<span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">response</span><span class="p">.</span><span class="nx">json</span><span class="p">();</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">choices</span><span class="p">[</span><span class="mf">0</span><span class="p">].</span><span class="nx">message</span><span class="p">.</span><span class="nx">content</span><span class="p">);</span>
</pre></div>
</div>
</section>
<section id="verification">
<h2>Verification<a class="headerlink" href="#verification" title="Link to this heading"></a></h2>
<section id="health-check">
<h3>Health Check<a class="headerlink" href="#health-check" title="Link to this heading"></a></h3>
<p>Verify the system is running correctly:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8000/health
</pre></div>
</div>
<p>Expected response:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2025-06-29T12:00:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="system-information">
<h3>System Information<a class="headerlink" href="#system-information" title="Link to this heading"></a></h3>
<p>Get detailed system information:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer your-api-key&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>http://localhost:8000/system/info
</pre></div>
</div>
<p>Expected response:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;reasoner&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;operational&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;engine&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;model&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;llama&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;retriever&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;operational&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;vector_db&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;faiss&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;index_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;gpu_optimized&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;0.1.0&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="performance-metrics">
<h3>Performance Metrics<a class="headerlink" href="#performance-metrics" title="Link to this heading"></a></h3>
<p>Monitor system performance:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer your-api-key&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">     </span>http://localhost:8000/performance
</pre></div>
</div>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>Now that you have the system running, explore these topics:</p>
<dl class="simple">
<dt>📖 <strong>Configuration</strong></dt><dd><p>Learn how to configure the system for different environments</p>
<ul class="simple">
<li><p><a class="reference internal" href="configuration.html"><span class="doc">Configuration Guide</span></a> - Detailed configuration guide</p></li>
<li><p>Environment-specific settings</p></li>
<li><p>Security configuration</p></li>
</ul>
</dd>
<dt>🔌 <strong>API Usage</strong></dt><dd><p>Explore the full API capabilities</p>
<ul class="simple">
<li><p><a class="reference internal" href="api_reference.html"><span class="doc">API Reference</span></a> - Complete API documentation</p></li>
<li><p>OpenAI compatibility features</p></li>
<li><p>Streaming responses</p></li>
</ul>
</dd>
<dt>🏗️ <strong>Architecture</strong></dt><dd><p>Understand the system architecture</p>
<ul class="simple">
<li><p><a class="reference internal" href="architecture.html"><span class="doc">Architecture Overview</span></a> - System design and components</p></li>
<li><p>Symbolic reasoning engine</p></li>
<li><p>Vector retrieval system</p></li>
</ul>
</dd>
<dt>🧪 <strong>Development</strong></dt><dd><p>Set up development environment</p>
<ul class="simple">
<li><p><span class="xref std std-doc">contributing</span> - Development guidelines</p></li>
<li><p><span class="xref std std-doc">testing</span> - Running tests</p></li>
<li><p>Code quality tools</p></li>
</ul>
</dd>
</dl>
</section>
<section id="common-issues">
<h2>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h2>
<section id="port-already-in-use">
<h3>Port Already in Use<a class="headerlink" href="#port-already-in-use" title="Link to this heading"></a></h3>
<p>If port 8000 is already in use:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Change port in .env file</span>
<span class="nv">APP_PORT</span><span class="o">=</span><span class="m">8001</span>

<span class="c1"># Or specify when running</span>
python<span class="w"> </span>src/main.py<span class="w"> </span>--port<span class="w"> </span><span class="m">8001</span>
</pre></div>
</div>
</section>
<section id="gpu-not-available">
<h3>GPU Not Available<a class="headerlink" href="#gpu-not-available" title="Link to this heading"></a></h3>
<p>If you don’t have a GPU or it’s not detected:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Disable GPU in .env file</span>
<span class="nv">MODEL_USE_GPU</span><span class="o">=</span><span class="nb">false</span>
</pre></div>
</div>
</section>
<section id="missing-dependencies">
<h3>Missing Dependencies<a class="headerlink" href="#missing-dependencies" title="Link to this heading"></a></h3>
<p>If you encounter import errors:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Reinstall dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements.txt<span class="w"> </span>--force-reinstall

<span class="c1"># Or install specific packages</span>
pip<span class="w"> </span>install<span class="w"> </span>torch<span class="w"> </span>torchvision<span class="w"> </span>torchaudio<span class="w"> </span>--index-url<span class="w"> </span>https://download.pytorch.org/whl/cpu
</pre></div>
</div>
</section>
<section id="api-key-issues">
<h3>API Key Issues<a class="headerlink" href="#api-key-issues" title="Link to this heading"></a></h3>
<p>If authentication fails:</p>
<ol class="arabic simple">
<li><p>Check your API key in the <code class="docutils literal notranslate"><span class="pre">.env</span></code> file</p></li>
<li><p>Ensure the <code class="docutils literal notranslate"><span class="pre">Authorization</span></code> header is correctly formatted</p></li>
<li><p>Verify the API key is not expired or blocked</p></li>
</ol>
</section>
</section>
<section id="getting-help">
<h2>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h2>
<p>If you need assistance:</p>
<ul class="simple">
<li><p><strong>Documentation</strong>: Browse the complete documentation</p></li>
<li><p><strong>GitHub Issues</strong>: Report bugs or request features</p></li>
<li><p><strong>Community</strong>: Join our community discussions</p></li>
<li><p><strong>Support</strong>: Contact our support team</p></li>
</ul>
<p><strong>Useful Links:</strong></p>
<ul class="simple">
<li><p>📚 <strong>Full Documentation</strong>: Complete user and developer guides</p></li>
<li><p>🐛 <strong>Issue Tracker</strong>: Report bugs and request features</p></li>
<li><p>💬 <strong>Discussions</strong>: Community support and questions</p></li>
<li><p>📧 <strong>Contact</strong>: Direct support contact</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Neural Symbolic Language Model Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="configuration.html" class="btn btn-neutral float-right" title="Configuration Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>