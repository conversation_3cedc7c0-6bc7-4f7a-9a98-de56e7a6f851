"""Document loader module for handling various file formats."""

import os
import json
import csv
from typing import List, Dict, Union, Optional
from pathlib import Path

class DocumentLoader:
    """Utility for loading documents from various file formats."""
    
    @staticmethod
    def load_text(file_path: str) -> str:
        """Load a text file.
        
        Args:
            file_path (str): Path to the text file
            
        Returns:
            str: Content of the text file
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            IOError: If there's an error reading the file
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    @staticmethod
    def load_json(file_path: str) -> Union[Dict, List]:
        """Load a JSON file.
        
        Args:
            file_path (str): Path to the JSON file
            
        Returns:
            Union[Dict, List]: Parsed JSON content
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            json.JSONDecodeError: If the JSON is invalid
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @staticmethod
    def load_csv(file_path: str, delimiter: str = ',', as_dict: bool = True) -> List[Union[Dict, List]]:
        """Load a CSV file.
        
        Args:
            file_path (str): Path to the CSV file
            delimiter (str, optional): CSV delimiter. Defaults to ','.
            as_dict (bool, optional): Whether to return rows as dicts. Defaults to True.
            
        Returns:
            List[Union[Dict, List]]: List of rows from the CSV
            
        Raises:
            FileNotFoundError: If the file doesn't exist
            csv.Error: If there's an error parsing the CSV
        """
        with open(file_path, 'r', encoding='utf-8', newline='') as f:
            if as_dict:
                reader = csv.DictReader(f, delimiter=delimiter)
                return list(reader)
            else:
                reader = csv.reader(f, delimiter=delimiter)
                return list(reader)
    
    @staticmethod
    def load_directory(directory_path: str, 
                      extensions: Optional[List[str]] = None, 
                      recursive: bool = True) -> Dict[str, str]:
        """Load all files in a directory.
        
        Args:
            directory_path (str): Path to the directory
            extensions (List[str], optional): List of file extensions to include. 
                                           Defaults to None (all files).
            recursive (bool, optional): Whether to search recursively. Defaults to True.
            
        Returns:
            Dict[str, str]: Dictionary mapping file paths to their contents
            
        Raises:
            NotADirectoryError: If the path is not a directory
        """
        contents = {}
        directory = Path(directory_path)
        
        if not directory.is_dir():
            raise NotADirectoryError(f"{directory_path} is not a directory")
        
        # Prepare extensions list
        if extensions:
            extensions = [ext.lower() for ext in extensions]
            if any(not ext.startswith('.') for ext in extensions):
                extensions = [f".{ext}" if not ext.startswith('.') else ext 
                            for ext in extensions]
        
        # Walk through directory
        for path in directory.rglob('*') if recursive else directory.glob('*'):
            if path.is_file():
                if extensions and path.suffix.lower() not in extensions:
                    continue
                    
                try:
                    if path.suffix.lower() == '.json':
                        contents[str(path)] = DocumentLoader.load_json(str(path))
                    elif path.suffix.lower() == '.csv':
                        contents[str(path)] = DocumentLoader.load_csv(str(path))
                    else:
                        contents[str(path)] = DocumentLoader.load_text(str(path))
                except Exception as e:
                    print(f"Error loading {path}: {str(e)}")
                    continue
        
        return contents
    
    @staticmethod
    def get_supported_extensions() -> List[str]:
        """Get list of supported file extensions.
        
        Returns:
            List[str]: List of supported extensions
        """
        return ['.txt', '.json', '.csv']
