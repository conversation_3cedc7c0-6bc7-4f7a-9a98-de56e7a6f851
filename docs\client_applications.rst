Client Applications Integration
===============================

The Neural Symbolic Language Model integrates seamlessly with popular LLM client applications,
providing enhanced symbolic reasoning capabilities through a fully OpenAI-compatible API.

Supported Applications
----------------------

Web Interfaces
~~~~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 20 20 20 40

   * - Application
     - Status
     - Access URL
     - Key Features
   * - **Open WebUI**
     - ✅ Full Support
     - http://localhost:3000
     - ChatGPT-like interface, RAG, file uploads
   * - **LibreChat**
     - ✅ Full Support
     - http://localhost:3001
     - Enhanced chat, search, plugins, multi-model
   * - **Chatbot UI**
     - ✅ Supported
     - Custom deployment
     - Simple, customizable interface
   * - **Text Generation WebUI**
     - ✅ Compatible
     - Custom deployment
     - Advanced model parameters

Development Tools
~~~~~~~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 25 20 55

   * - Tool
     - Status
     - Features
   * - **Continue.dev**
     - ✅ Full Support
     - VS Code extension, code completion, chat
   * - **Cursor IDE**
     - ✅ Supported
     - AI-powered code editor
   * - **GitHub Copilot Chat**
     - ✅ Compatible
     - Code assistance and explanation
   * - **Codeium**
     - ✅ Compatible
     - Code completion and chat

Quick Setup Guide
-----------------

Docker Compose Deployment
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Option 1: Full Stack (Recommended)**

.. code-block:: bash

   # Clone repository
   git clone https://github.com/your-org/neural-symbolic-language-model.git
   cd neural-symbolic-language-model
   
   # Start all services
   docker-compose -f docker-compose.client-integration.yml up -d
   
   # Pull the model
   docker exec ollama-neural-symbolic ollama pull gemma3n:e2b
   
   # Access applications
   # Open WebUI: http://localhost:3000
   # LibreChat: http://localhost:3001
   # API: http://localhost:8080

**Option 2: Quick Setup Script**

.. code-block:: bash

   # Run interactive setup
   bash scripts/setup_client_integration.sh
   
   # Choose option 1 for quick setup (Neural Symbolic LM + Open WebUI)
   # Choose option 2 for full setup (all applications)

Manual Configuration
~~~~~~~~~~~~~~~~~~~~

**Step 1: Start Neural Symbolic Language Model**

.. code-block:: bash

   # Start Ollama
   ollama serve
   ollama pull gemma3n:e2b
   
   # Start Neural Symbolic API
   python src/main.py

**Step 2: Configure Client Application**

See individual application sections below for specific configuration.

Open WebUI Integration
----------------------

Installation and Setup
~~~~~~~~~~~~~~~~~~~~~~~

**Docker Installation:**

.. code-block:: bash

   docker run -d -p 3000:8080 \
     -e OPENAI_API_BASE_URL=http://host.docker.internal:8080/v1 \
     -e OPENAI_API_KEY=your-api-key \
     -e DEFAULT_MODELS=gemma3n:e2b \
     --name open-webui \
     ghcr.io/open-webui/open-webui:main

**Native Installation:**

.. code-block:: bash

   pip install open-webui
   open-webui serve --port 3000

Configuration
~~~~~~~~~~~~~

1. **Access Open WebUI**: http://localhost:3000
2. **Go to Settings** → **Connections**
3. **Add Connection**:
   
   * **Name**: Neural Symbolic Language Model
   * **API Base URL**: ``http://localhost:8080/v1``
   * **API Key**: ``your-api-key``

4. **Select Model**: Choose ``gemma3n:e2b`` from the dropdown

Advanced Features
~~~~~~~~~~~~~~~~~

**RAG Integration:**

.. code-block:: yaml

   # Enable RAG features in Open WebUI
   RAG_EMBEDDING_ENGINE: ollama
   RAG_EMBEDDING_MODEL: mxbai-embed-large
   ENABLE_RAG_HYBRID_SEARCH: true
   ENABLE_RAG_WEB_LOADER: true

**File Upload Support:**

.. code-block:: yaml

   # Enable file processing
   ENABLE_RAG_LOCAL_WEB_FETCH: true
   RAG_WEB_SEARCH_ENGINE: searxng
   ENABLE_RAG_WEB_SEARCH: true

LibreChat Integration
---------------------

Configuration File
~~~~~~~~~~~~~~~~~~

Create ``librechat.yaml``:

.. code-block:: yaml

   version: 1.0.5
   cache: true
   
   endpoints:
     custom:
       - name: "Neural Symbolic LM"
         apiKey: "your-api-key"
         baseURL: "http://localhost:8080/v1"
         models:
           default: ["gemma3n:e2b"]
         titleConvo: true
         titleModel: "gemma3n:e2b"
         modelDisplayLabel: "Neural Symbolic Language Model"
         
         # Custom system prompts
         systemPrompts:
           - name: "Symbolic Reasoning Expert"
             prompt: "You are an expert in symbolic reasoning and logic."
           - name: "Code Analysis Expert"
             prompt: "You are an expert programmer and code analyst."

Docker Deployment
~~~~~~~~~~~~~~~~~

.. code-block:: yaml

   # docker-compose.yml for LibreChat
   version: '3.8'
   services:
     librechat:
       image: ghcr.io/danny-avila/librechat:latest
       ports:
         - "3001:3080"
       environment:
         - CUSTOM_CONFIG_PATH=/app/librechat.yaml
       volumes:
         - ./librechat.yaml:/app/librechat.yaml:ro

Continue.dev Integration
------------------------

VS Code Extension Setup
~~~~~~~~~~~~~~~~~~~~~~~

1. **Install Extension**: Search for "Continue" in VS Code extensions
2. **Configure Settings**: Create or edit ``~/.continue/config.json``

Configuration
~~~~~~~~~~~~~

.. code-block:: json

   {
     "models": [
       {
         "title": "Neural Symbolic Language Model",
         "provider": "openai",
         "model": "gemma3n:e2b",
         "apiKey": "your-api-key",
         "apiBase": "http://localhost:8080/v1",
         "contextLength": 4096,
         "completionOptions": {
           "temperature": 0.7,
           "topP": 1.0,
           "presencePenalty": 0.0,
           "frequencyPenalty": 0.0,
           "maxTokens": 1000
         },
         "systemMessage": "You are an expert programmer with advanced symbolic reasoning capabilities."
       }
     ],
     "tabAutocompleteModel": {
       "title": "Neural Symbolic Language Model",
       "provider": "openai", 
       "model": "gemma3n:e2b",
       "apiKey": "your-api-key",
       "apiBase": "http://localhost:8080/v1"
     }
   }

Usage Examples
~~~~~~~~~~~~~~

**Code Explanation:**

1. Select code in VS Code
2. Press ``Ctrl+I`` (or ``Cmd+I`` on Mac)
3. Ask: "Explain this algorithm and its time complexity"

**Code Generation:**

1. Press ``Ctrl+I``
2. Ask: "Generate a Python function to sort a list using quicksort"

**Debugging Help:**

1. Select problematic code
2. Press ``Ctrl+I``
3. Ask: "Find the bug in this code and suggest a fix"

Cursor IDE Integration
----------------------

Configuration
~~~~~~~~~~~~~

1. **Open Cursor Settings**: ``Ctrl+,`` (or ``Cmd+,`` on Mac)
2. **Search for "AI"** in settings
3. **Configure API Settings**:

.. code-block:: json

   {
     "cursor.general.enableCodeActions": true,
     "cursor.chat.openaiApiKey": "your-api-key",
     "cursor.chat.openaiBaseUrl": "http://localhost:8080/v1",
     "cursor.chat.defaultModel": "gemma3n:e2b",
     "cursor.chat.systemPrompt": "You are an AI programming assistant with advanced symbolic reasoning."
   }

Features
~~~~~~~~

* **Chat Interface**: Built-in chat for code discussions
* **Code Generation**: Generate code from natural language
* **Code Explanation**: Understand complex algorithms
* **Refactoring**: Improve code structure and performance

Custom Applications
-------------------

Python Integration
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import openai
   
   class NeuralSymbolicClient:
       def __init__(self, api_key):
           openai.api_base = "http://localhost:8080/v1"
           openai.api_key = api_key
           self.model = "gemma3n:e2b"
       
       def chat(self, message, system_prompt=None):
           messages = []
           if system_prompt:
               messages.append({"role": "system", "content": system_prompt})
           messages.append({"role": "user", "content": message})
           
           response = openai.ChatCompletion.create(
               model=self.model,
               messages=messages,
               temperature=0.7
           )
           return response.choices[0].message.content
       
       def analyze_code(self, code, language="python"):
           system_prompt = f"You are an expert {language} programmer. Analyze the following code and provide insights."
           return self.chat(f"```{language}\n{code}\n```", system_prompt)
   
   # Usage
   client = NeuralSymbolicClient("your-api-key")
   result = client.analyze_code("def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)")
   print(result)

JavaScript Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   class NeuralSymbolicClient {
       constructor(apiKey) {
           this.apiKey = apiKey;
           this.baseURL = "http://localhost:8080/v1";
           this.model = "gemma3n:e2b";
       }
       
       async chat(message, systemPrompt = null) {
           const messages = [];
           if (systemPrompt) {
               messages.push({ role: "system", content: systemPrompt });
           }
           messages.push({ role: "user", content: message });
           
           const response = await fetch(`${this.baseURL}/chat/completions`, {
               method: "POST",
               headers: {
                   "Authorization": `Bearer ${this.apiKey}`,
                   "Content-Type": "application/json"
               },
               body: JSON.stringify({
                   model: this.model,
                   messages: messages,
                   temperature: 0.7
               })
           });
           
           const data = await response.json();
           return data.choices[0].message.content;
       }
       
       async reasoningTask(problem) {
           const systemPrompt = "You are an expert in logical reasoning. Solve problems step by step.";
           return await this.chat(problem, systemPrompt);
       }
   }
   
   // Usage
   const client = new NeuralSymbolicClient("your-api-key");
   client.reasoningTask("If all birds can fly and penguins are birds, what can we conclude?")
       .then(result => console.log(result));

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**1. Connection Refused**

.. code-block:: bash

   # Check if API is running
   curl http://localhost:8080/health
   
   # Check port availability
   netstat -an | grep 8080

**2. Authentication Errors**

.. code-block:: bash

   # Verify API key configuration
   curl -H "Authorization: Bearer your-api-key" http://localhost:8080/v1/models

**3. Model Not Found**

.. code-block:: bash

   # Check available models
   ollama list
   
   # Pull model if missing
   ollama pull gemma3n:e2b

**4. Performance Issues**

.. code-block:: bash

   # Check system resources
   htop
   nvidia-smi  # For GPU usage
   
   # Monitor API performance
   curl http://localhost:8080/performance

Best Practices
--------------

Configuration
~~~~~~~~~~~~~

1. **Use Environment Variables**: Store API keys securely
2. **Set Appropriate Timeouts**: Configure timeouts for your use case
3. **Monitor Usage**: Track API usage and performance
4. **Implement Retry Logic**: Handle temporary failures gracefully
5. **Cache Responses**: Cache frequently used responses

Performance
~~~~~~~~~~~

1. **Optimize Temperature**: Use lower values for reasoning tasks
2. **Batch Requests**: Group related requests when possible
3. **Use Streaming**: For long responses, enable streaming
4. **Monitor Resources**: Keep track of CPU/GPU usage
5. **Scale Horizontally**: Deploy multiple instances for high load

Security
~~~~~~~~

1. **Secure API Keys**: Never expose API keys in client-side code
2. **Use HTTPS**: Enable SSL/TLS for production deployments
3. **Implement Rate Limiting**: Protect against abuse
4. **Validate Inputs**: Sanitize user inputs before processing
5. **Monitor Access**: Log and monitor API access patterns

For more detailed information, see the :doc:`client_integration_guide` and :doc:`api_reference` sections.
