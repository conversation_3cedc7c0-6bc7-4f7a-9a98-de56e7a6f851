"""
LightRAG-based retrieval module for the Neural Symbolic Language Model.
This module provides a wrapper around LightRAG for efficient retrieval-augmented generation.
"""

# Standard library imports
import logging
from typing import Dict, List, Optional, Union

# Third-party imports
import numpy as np
import torch

# Optional third-party imports with fallback
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    faiss = None

# Local application imports
from exceptions import ConfigurationError, RetrievalError, VectorStoreError
from vector_store import TorchVectorStore

# Configure logger
logger = logging.getLogger(__name__)

# Check FAISS availability and GPU support
if FAISS_AVAILABLE:
    logger.info("Imported FAISS successfully")
    # Check if GPU FAISS is available
    GPU_FAISS_AVAILABLE = hasattr(faiss, 'StandardGpuResources')
    if GPU_FAISS_AVAILABLE:
        logger.info("GPU FAISS is available")
    else:
        logger.info("Using CPU version of FAISS")
else:
    logger.warning("FAISS not available. Using fallback vector storage.")
    GPU_FAISS_AVAILABLE = False

class Retriever:
    """Hybrid retrieval system for neural-symbolic language models.

    This class implements a sophisticated retrieval system that combines
    vector similarity search with symbolic reasoning for enhanced document
    retrieval and context generation. It supports multiple vector database
    backends and GPU acceleration for optimal performance.

    The retriever uses dense vector embeddings for fast similarity search
    and can be extended with sparse retrieval methods and reranking for
    improved accuracy in domain-specific applications.

    Attributes:
        use_gpu (bool): Whether GPU acceleration is enabled
        vector_db (str): The vector database backend being used
        index: The vector index for similarity search
        documents (dict): Storage for document content and metadata
        dimension (int): Vector embedding dimension
        using_torch_fallback (bool): Whether using PyTorch fallback implementation

    Example:
        >>> retriever = Retriever(vector_db="faiss", use_gpu=True)
        >>> retriever.add_document("Neural networks are powerful.", "doc1")
        >>> results = retriever.search("What are neural networks?", k=3)
        >>> for result in results:
        ...     print(f"Score: {result['score']:.3f}, Text: {result['text'][:50]}...")

    Note:
        This implementation provides a foundation for hybrid retrieval.
        In production, this would integrate with more sophisticated
        embedding models and reranking systems.
    """
    def __init__(self, vector_db: str = "faiss", use_gpu: bool = True, embedding_backend: str = "random"):
        """Initialize the retrieval system.

        Args:
            vector_db: The vector database to use (e.g., "faiss", "chromadb")
            use_gpu: Whether to use GPU acceleration if available
            embedding_backend: Embedding generation backend ("random", "ollama")

        Raises:
            ConfigurationError: If initialization fails
        """
        try:
            # Check GPU availability
            gpu_available = torch.cuda.is_available()
            if use_gpu and not gpu_available:
                logger.warning("GPU requested but not available. Falling back to CPU.")
            self.use_gpu = use_gpu and gpu_available
            self.vector_db = vector_db
            self.embedding_backend = embedding_backend
            self.index = None
            self.documents = {}

            # Initialize embedding client if using Ollama
            if embedding_backend == "ollama":
                try:
                    import ollama
                    self.ollama_client = ollama.AsyncClient()
                    logger.info("Retriever using Ollama for embeddings")
                except ImportError:
                    logger.warning("Ollama not available, falling back to random embeddings")
                    self.embedding_backend = "random"

            self.setup_vector_db()

            logger.info(f"Retriever initialized with {vector_db} backend, GPU: {self.use_gpu}")

        except Exception as e:
            logger.error(f"Failed to initialize retriever: {str(e)}", exc_info=True)
            raise ConfigurationError(f"Retriever initialization failed: {str(e)}")
    
    def setup_vector_db(self) -> None:
        """Set up the vector database based on the selected backend.

        This method initializes the vector database backend for similarity search.
        It supports FAISS (CPU/GPU) and PyTorch fallback implementations with
        automatic detection and configuration based on available hardware.

        The method performs the following operations:
        1. Sets default embedding dimension (768 for BERT-like models)
        2. Checks FAISS availability and initializes appropriate backend
        3. Configures GPU acceleration if available and requested
        4. Sets up index parameters for optimal performance

        Raises:
            ConfigurationError: If vector database setup fails
            RuntimeError: If GPU is requested but not available

        Note:
            If FAISS is not available, the method automatically falls back
            to a PyTorch-based vector store implementation. GPU acceleration
            is automatically detected and configured when available.

        Example:
            The method is called automatically during initialization::

                retriever = Retriever(vector_db="faiss", use_gpu=True)
                # setup_vector_db() is called internally
        """
        self.dimension = 768  # Default BERT embedding dimension
        self.using_torch_fallback = False
        
        # Check if FAISS is available
        if not FAISS_AVAILABLE:
            logger.warning("FAISS not available. Using PyTorch vector store fallback.")
            self.index = TorchVectorStore(dimension=self.dimension, use_gpu=self.use_gpu)
            self.using_torch_fallback = True
            if self.use_gpu and torch.cuda.is_available():
                logger.info(f"Using PyTorch GPU vector store on: {torch.cuda.get_device_name(0)}")
            return
            
        # FAISS is available
        if self.vector_db == "faiss":
            # Create the base index
            self.index = faiss.IndexFlatL2(self.dimension)
            
            # Try to use GPU FAISS if available and requested
            if self.use_gpu and torch.cuda.is_available():
                if GPU_FAISS_AVAILABLE:
                    try:
                        logger.info("Setting up GPU-optimized FAISS index")
                        # Get GPU resources
                        res = faiss.StandardGpuResources()
                        
                        # Move the index to GPU
                        self.index = faiss.index_cpu_to_gpu(res, 0, self.index)
                        logger.info(f"FAISS index using GPU: {torch.cuda.get_device_name(0)}")
                    except Exception as e:
                        logger.warning(f"Error setting up GPU FAISS: {str(e)}")
                        logger.info("Falling back to PyTorch GPU vector store")
                        self.index = TorchVectorStore(dimension=self.dimension, use_gpu=True)
                        self.using_torch_fallback = True
                else:
                    # No GPU FAISS but GPU is available, use PyTorch fallback
                    logger.info("GPU FAISS not available but GPU detected. Using PyTorch GPU vector store.")
                    self.index = TorchVectorStore(dimension=self.dimension, use_gpu=True)
                    self.using_torch_fallback = True
            else:
                # CPU mode
                logger.info("Using CPU FAISS")
                self.use_gpu = False
        else:
            raise ValueError(f"Unsupported vector database: {self.vector_db}")

    async def _generate_embeddings(self, texts: List[str]) -> np.ndarray:
        """Generate embeddings for a list of texts."""
        if self.embedding_backend == "ollama":
            try:
                embeddings = []
                for text in texts:
                    response = await self.ollama_client.embed(
                        model="mxbai-embed-large",  # Default embedding model
                        input=text
                    )
                    embeddings.append(response['embeddings'][0])
                return np.array(embeddings, dtype='float32')
            except Exception as e:
                logger.error(f"Ollama embedding generation failed: {e}")
                logger.info("Falling back to random embeddings")
                return np.random.randn(len(texts), self.dimension).astype('float32')
        else:
            # Fallback to random embeddings for testing
            return np.random.randn(len(texts), self.dimension).astype('float32')

    def add_documents(self, documents: List[Dict[str, str]], embeddings: Optional[np.ndarray] = None):
        """Add documents to the retrieval system.
        
        Args:
            documents: List of document dictionaries with 'id' and 'text' keys
            embeddings: Optional pre-computed embeddings for the documents
        """
        if not documents:
            return
            
        if embeddings is None:
            # Generate embeddings using the configured backend
            texts = [doc['text'] for doc in documents]
            if self.embedding_backend == "ollama":
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # If we're already in an async context, use sync fallback
                        embeddings = np.random.randn(len(texts), self.dimension).astype('float32')
                    else:
                        embeddings = loop.run_until_complete(self._generate_embeddings(texts))
                except RuntimeError:
                    # No event loop, create one
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        embeddings = loop.run_until_complete(self._generate_embeddings(texts))
                    finally:
                        loop.close()
                except Exception as e:
                    logger.warning(f"Ollama embedding failed: {e}, using random embeddings")
                    embeddings = np.random.randn(len(texts), self.dimension).astype('float32')
            else:
                # Use random embeddings for other backends
                embeddings = np.random.randn(len(texts), self.dimension).astype('float32')
        
        # Add documents to the index based on the backend type
        if self.using_torch_fallback:
            # Extract document IDs and texts
            doc_ids = [doc['id'] for doc in documents]
            texts = [doc['text'] for doc in documents]
            
            # Use the TorchVectorStore add method
            self.index.add(embeddings, doc_ids, texts)
            
            # Store document metadata in our local dictionary as well
            for doc in documents:
                self.documents[doc['id']] = doc['text']
        else:
            # Using FAISS
            try:
                # Add embeddings to FAISS index
                self.index.add(embeddings)
                
                # Store document metadata
                for doc in documents:
                    self.documents[doc['id']] = doc['text']
            except Exception as e:
                logger.error(f"Error adding documents to FAISS index: {str(e)}")
                return
    
    async def search(self, query: str, k: int = 5) -> List[Dict[str, Union[str, float]]]:
        """Search for relevant documents using hybrid retrieval methods.

        This method performs a two-stage search process: first using dense vector
        similarity search for fast retrieval, then optionally applying reranking
        for improved accuracy. The search supports both FAISS and PyTorch backends
        with automatic fallback handling.

        :param query: The search query text. Must be a non-empty string containing
                     the question or topic to search for. Maximum length is 10,000
                     characters. The query will be embedded into a dense vector
                     for similarity comparison.
        :type query: str
        :param k: Number of top results to return. Must be a positive integer.
                 Higher values return more results but may include less relevant
                 documents. Typical values range from 3-10 for most applications.
        :type k: int

        :returns: List of dictionaries containing search results, sorted by
                 relevance score in descending order. Each dictionary contains:

                 - **id** (str): Unique document identifier
                 - **text** (str): Full document content
                 - **score** (float): Relevance score between 0.0 and 1.0,
                   where 1.0 indicates perfect relevance
        :rtype: List[Dict[str, Union[str, float]]]

        :raises RetrievalError: If the search operation fails due to:
                               - Empty or invalid query
                               - Vector database errors
                               - Embedding generation failures
        :raises ValidationError: If input validation fails due to:
                                - Non-positive k value
                                - Query exceeding maximum length
                                - Invalid query format

        :example:

        >>> retriever = Retriever()
        >>> retriever.add_document("Neural networks learn patterns.", "doc1")
        >>> retriever.add_document("Symbolic AI uses logical rules.", "doc2")
        >>>
        >>> # Basic search
        >>> results = retriever.search("machine learning", k=2)
        >>> for result in results:
        ...     print(f"Score: {result['score']:.3f}")
        ...     print(f"Text: {result['text'][:50]}...")
        Score: 0.856
        Text: Neural networks learn patterns...
        Score: 0.234
        Text: Symbolic AI uses logical rules...
        >>>
        >>> # Search with specific number of results
        >>> results = retriever.search("neural networks", k=1)
        >>> print(f"Found {len(results)} result(s)")
        Found 1 result(s)

        .. note::
           Search performance depends on the vector database backend and
           the quality of the embedding model. GPU acceleration significantly
           improves search speed for large document collections.

        .. warning::
           This implementation uses random vectors for testing. In production,
           replace with proper embedding models like BERT, Sentence-BERT,
           or domain-specific embeddings.

        .. seealso::
           :meth:`add_document` for adding documents to search
           :meth:`get_system_info` for checking backend capabilities
        """
        try:
            if not query or not query.strip():
                raise RetrievalError("Query cannot be empty", operation="search")

            if k <= 0:
                raise RetrievalError("k must be positive", operation="search")

            logger.debug(f"Searching for: {query[:100]}... (k={k})")

            # Generate query embedding using the configured backend
            if self.embedding_backend == "ollama":
                try:
                    query_embeddings = await self._generate_embeddings([query])
                    query_vector = query_embeddings.reshape(1, -1)
                except Exception as e:
                    logger.warning(f"Ollama embedding generation failed: {e}. Using random vector.")
                    query_vector = np.random.randn(1, self.dimension).astype('float32')
                # Use random vector for other backends
                query_vector = np.random.randn(1, self.dimension).astype('float32')

            # Handle search differently based on the backend
            if self.using_torch_fallback:
                # For TorchVectorStore
                try:
                    results = self.index.search(query_vector, k)
                    logger.debug(f"TorchVectorStore returned {len(results)} results")
                    return results
                except Exception as e:
                    logger.error(f"Error searching with TorchVectorStore: {str(e)}", exc_info=True)
                    raise VectorStoreError(f"TorchVectorStore search failed: {str(e)}", operation="search")
            else:
                # For FAISS
                try:
                    # If the index is not initialized or empty
                    if self.index is None or len(self.documents) == 0:
                        logger.warning("No documents available for search")
                        return []

                    # Perform the search
                    distances, indices = self.index.search(query_vector, k)

                    # Format results
                    results = []
                    for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
                        if idx < 0 or idx >= len(self.documents):
                            continue
                        doc_id = list(self.documents.keys())[idx]
                        results.append({
                            'id': doc_id,
                            'text': self.documents[doc_id],
                            'score': float(1.0 / (1.0 + dist))  # Convert distance to similarity score
                        })

                    logger.debug(f"FAISS returned {len(results)} results")
                    return results
                except Exception as e:
                    logger.error(f"Error searching with FAISS: {str(e)}", exc_info=True)
                    raise VectorStoreError(f"FAISS search failed: {str(e)}", operation="search")

        except (RetrievalError, VectorStoreError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error in search: {str(e)}", exc_info=True)
            raise RetrievalError(f"Search operation failed: {str(e)}", operation="search")
    
    def get_system_info(self) -> Dict[str, Union[str, bool]]:
        """Get information about the system configuration.
        
        Returns:
            dict: System configuration information
        """
        return {
            'vector_db': self.vector_db,
            'gpu_enabled': self.use_gpu,
            'gpu_available': torch.cuda.is_available(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
            "index_size": len(self.documents)
        }

    def optimize_index(self):
        """Optimize the FAISS index for better performance.
        
        This should be called after adding a significant number of documents.
        """
        if not self.use_gpu:
            return True
            
        try:
            # Clear any unused memory
            torch.cuda.empty_cache()
            
            # If index is not trained (for IVF indexes), train it
            if hasattr(self.index, 'is_trained') and not self.index.is_trained:
                # Generate training data
                train_size = min(len(self.documents), 100000)
                train_data = np.random.randn(train_size, self.dimension).astype('float32')
                self.index.train(train_data)
                print("Trained IVF index")
            
            # If using GPU, ensure we're using float16 for better performance
            if hasattr(self.index, 'getNumProbes'):
                # For IVF indexes, set number of probes for better recall
                self.index.nprobe = min(32, self.index.nlist)
            
            return True
            
        except Exception as e:
            print(f"Error optimizing index: {str(e)}")
            return False

    def batch_add_documents(self, documents, batch_size=32):
        """Add multiple documents in batches for better performance.
        
        Args:
            documents (list): List of documents to add
            batch_size (int): Size of each batch
            
        Returns:
            bool: True if successful
        """
        try:
            # Pre-allocate GPU memory for better performance
            if self.use_gpu:
                torch.cuda.empty_cache()
                # Reserve some GPU memory
                torch.cuda.set_per_process_memory_fraction(0.8)
            
            # Process in batches
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                
                # Generate embeddings for the batch
                texts = [doc['text'] for doc in batch]
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                embeddings = loop.run_until_complete(self._generate_embeddings(texts))
                
                # Extract document IDs and texts
                doc_ids = [doc['id'] for doc in batch]
                texts = [doc['text'] for doc in batch]
                
                if self.using_torch_fallback:
                    # Use TorchVectorStore's add method
                    self.index.add(embeddings, doc_ids, texts)
                else:
                    # Using FAISS
                    if self.use_gpu:
                        # Move embeddings to GPU if needed
                        gpu_embeddings = torch.from_numpy(embeddings).cuda()
                        embeddings = gpu_embeddings.cpu().numpy()
                    
                    # Add to FAISS index
                    self.index.add(embeddings)
                
                # Store document metadata in our local dictionary
                for doc in batch:
                    self.documents[doc['id']] = doc['text']
                
                # Optional: Print progress
                progress = (i + len(batch)) / len(documents) * 100
                print(f"Progress: {progress:.1f}% ({i + len(batch)}/{len(documents)} documents)")
            
            # Final optimization
            if self.use_gpu:
                self.optimize_index()
                torch.cuda.empty_cache()
            
            return True
            
        except Exception as e:
            print(f"Error in batch_add_documents: {str(e)}")
            return False
