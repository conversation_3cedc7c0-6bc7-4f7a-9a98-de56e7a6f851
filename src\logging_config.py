"""
Enhanced logging configuration for the Neural Symbolic Language Model.

This module provides comprehensive logging setup with structured logging,
performance monitoring, and distributed tracing capabilities.

Author: AI Assistant
Date: 2025-06-29
"""

import logging
import logging.handlers
import os
import sys
import json
import time
import threading
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime, timezone
import uuid

# Try to import optional dependencies
try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False

try:
    from opentelemetry import trace
    from opentelemetry.exporter.jaeger.thrift import JaegerExporter
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    OPENTELEMETRY_AVAILABLE = True
except ImportError:
    OPENTELEMETRY_AVAILABLE = False


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""

    def __init__(self, include_extra: bool = True):
        """Initialize the structured formatter.

        :param include_extra: Whether to include extra fields in log records
        :type include_extra: bool
        """
        super().__init__()
        self.include_extra = include_extra

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON.

        :param record: Log record to format
        :type record: logging.LogRecord

        :returns: Formatted JSON string
        :rtype: str
        """
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "thread_name": record.threadName,
        }

        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        # Add extra fields if enabled
        if self.include_extra:
            for key, value in record.__dict__.items():
                if key not in log_entry and not key.startswith('_'):
                    try:
                        # Ensure value is JSON serializable
                        json.dumps(value)
                        log_entry[key] = value
                    except (TypeError, ValueError):
                        log_entry[key] = str(value)

        return json.dumps(log_entry, ensure_ascii=False)


class PerformanceFilter(logging.Filter):
    """Filter to add performance metrics to log records."""

    def __init__(self):
        """Initialize the performance filter."""
        super().__init__()
        self._start_time = time.time()
        self._request_count = 0
        self._lock = threading.Lock()

    def filter(self, record: logging.LogRecord) -> bool:
        """Add performance metrics to log record.

        :param record: Log record to filter
        :type record: logging.LogRecord

        :returns: True to include the record
        :rtype: bool
        """
        with self._lock:
            self._request_count += 1
            record.uptime = time.time() - self._start_time
            record.request_count = self._request_count

        return True


class ContextFilter(logging.Filter):
    """Filter to add contextual information to log records."""

    def __init__(self):
        """Initialize the context filter."""
        super().__init__()
        self._context = threading.local()

    def set_context(self, **kwargs):
        """Set context variables for the current thread.

        :param kwargs: Context variables to set
        """
        for key, value in kwargs.items():
            setattr(self._context, key, value)

    def clear_context(self):
        """Clear context variables for the current thread."""
        self._context = threading.local()

    def filter(self, record: logging.LogRecord) -> bool:
        """Add context information to log record.

        :param record: Log record to filter
        :type record: logging.LogRecord

        :returns: True to include the record
        :rtype: bool
        """
        # Add context variables
        for key, value in self._context.__dict__.items():
            if not hasattr(record, key):
                setattr(record, key, value)

        # Add correlation ID if not present
        if not hasattr(record, 'correlation_id'):
            record.correlation_id = getattr(self._context, 'correlation_id', str(uuid.uuid4()))

        return True


def setup_structured_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    structured: bool = False,
    max_file_size: int = 10 * 1024 * 1024,
    backup_count: int = 5
) -> logging.Logger:
    """Setup comprehensive logging configuration.

    :param log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    :type log_level: str
    :param log_file: Path to log file. If None, logs to console only
    :type log_file: Optional[str]
    :param structured: Whether to use structured JSON logging
    :type structured: bool
    :param max_file_size: Maximum log file size in bytes
    :type max_file_size: int
    :param backup_count: Number of backup log files to keep
    :type backup_count: int

    :returns: Configured logger instance
    :rtype: logging.Logger
    """
    # Create logs directory if needed
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

    # Clear existing handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Create formatters
    if structured:
        formatter = StructuredFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(correlation_id)s - %(message)s'
        )

    # Create filters
    performance_filter = PerformanceFilter()
    context_filter = ContextFilter()

    # Setup handlers
    handlers = []

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.addFilter(performance_filter)
    console_handler.addFilter(context_filter)
    handlers.append(console_handler)

    # File handler
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.addFilter(performance_filter)
        file_handler.addFilter(context_filter)
        handlers.append(file_handler)

    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        handlers=handlers,
        force=True
    )

    # Configure specific loggers
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)
    logging.getLogger('uvicorn.error').setLevel(logging.INFO)
    logging.getLogger('fastapi').setLevel(logging.INFO)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

    logger = logging.getLogger(__name__)
    logger.info(
        "Logging configured",
        extra={
            "level": log_level,
            "structured": structured,
            "file": log_file,
            "handlers": len(handlers)
        }
    )

    return logger


# Legacy function for backward compatibility
def setup_logging(log_dir="logs", log_level=logging.INFO):
    """Legacy logging setup function.

    :param log_dir: Log directory
    :param log_level: Logging level

    :returns: Logger instance
    """
    log_file = os.path.join(log_dir, "app.log")
    return setup_structured_logging(
        log_level=logging.getLevelName(log_level),
        log_file=log_file,
        structured=False
    )


def get_logger(name):
    """Get a logger with the specified name.

    Args:
        name (str): Name for the logger

    Returns:
        logging.Logger: Configured logger instance
    """
    return logging.getLogger(name)
