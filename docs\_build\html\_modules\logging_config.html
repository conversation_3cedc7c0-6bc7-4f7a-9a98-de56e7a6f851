

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>logging_config &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Module code</a></li>
      <li class="breadcrumb-item active">logging_config</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for logging_config</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Enhanced logging configuration for the Neural Symbolic Language Model.</span>

<span class="sd">This module provides comprehensive logging setup with structured logging,</span>
<span class="sd">performance monitoring, and distributed tracing capabilities.</span>

<span class="sd">Author: AI Assistant</span>
<span class="sd">Date: 2025-06-29</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging.handlers</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">os</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">sys</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">json</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">threading</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pathlib</span><span class="w"> </span><span class="kn">import</span> <span class="n">Path</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Any</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timezone</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">uuid</span>

<span class="c1"># Try to import optional dependencies</span>
<span class="k">try</span><span class="p">:</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">structlog</span>
    <span class="n">STRUCTLOG_AVAILABLE</span> <span class="o">=</span> <span class="kc">True</span>
<span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
    <span class="n">STRUCTLOG_AVAILABLE</span> <span class="o">=</span> <span class="kc">False</span>

<span class="k">try</span><span class="p">:</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">opentelemetry</span><span class="w"> </span><span class="kn">import</span> <span class="n">trace</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">opentelemetry.exporter.jaeger.thrift</span><span class="w"> </span><span class="kn">import</span> <span class="n">JaegerExporter</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">opentelemetry.sdk.trace</span><span class="w"> </span><span class="kn">import</span> <span class="n">TracerProvider</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">opentelemetry.sdk.trace.export</span><span class="w"> </span><span class="kn">import</span> <span class="n">BatchSpanProcessor</span>
    <span class="n">OPENTELEMETRY_AVAILABLE</span> <span class="o">=</span> <span class="kc">True</span>
<span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
    <span class="n">OPENTELEMETRY_AVAILABLE</span> <span class="o">=</span> <span class="kc">False</span>


<div class="viewcode-block" id="StructuredFormatter">
<a class="viewcode-back" href="../modules.html#logging_config.StructuredFormatter">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">StructuredFormatter</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">Formatter</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Custom formatter for structured JSON logging.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="StructuredFormatter.__init__">
<a class="viewcode-back" href="../modules.html#logging_config.StructuredFormatter.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">include_extra</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the structured formatter.</span>

<span class="sd">        :param include_extra: Whether to include extra fields in log records</span>
<span class="sd">        :type include_extra: bool</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">include_extra</span> <span class="o">=</span> <span class="n">include_extra</span></div>


<div class="viewcode-block" id="StructuredFormatter.format">
<a class="viewcode-back" href="../modules.html#logging_config.StructuredFormatter.format">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">format</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">record</span><span class="p">:</span> <span class="n">logging</span><span class="o">.</span><span class="n">LogRecord</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Format log record as structured JSON.</span>

<span class="sd">        :param record: Log record to format</span>
<span class="sd">        :type record: logging.LogRecord</span>

<span class="sd">        :returns: Formatted JSON string</span>
<span class="sd">        :rtype: str</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">log_entry</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;timestamp&quot;</span><span class="p">:</span> <span class="n">datetime</span><span class="o">.</span><span class="n">fromtimestamp</span><span class="p">(</span><span class="n">record</span><span class="o">.</span><span class="n">created</span><span class="p">,</span> <span class="n">tz</span><span class="o">=</span><span class="n">timezone</span><span class="o">.</span><span class="n">utc</span><span class="p">)</span><span class="o">.</span><span class="n">isoformat</span><span class="p">(),</span>
            <span class="s2">&quot;level&quot;</span><span class="p">:</span> <span class="n">record</span><span class="o">.</span><span class="n">levelname</span><span class="p">,</span>
            <span class="s2">&quot;logger&quot;</span><span class="p">:</span> <span class="n">record</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
            <span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="n">record</span><span class="o">.</span><span class="n">getMessage</span><span class="p">(),</span>
            <span class="s2">&quot;module&quot;</span><span class="p">:</span> <span class="n">record</span><span class="o">.</span><span class="n">module</span><span class="p">,</span>
            <span class="s2">&quot;function&quot;</span><span class="p">:</span> <span class="n">record</span><span class="o">.</span><span class="n">funcName</span><span class="p">,</span>
            <span class="s2">&quot;line&quot;</span><span class="p">:</span> <span class="n">record</span><span class="o">.</span><span class="n">lineno</span><span class="p">,</span>
            <span class="s2">&quot;thread&quot;</span><span class="p">:</span> <span class="n">record</span><span class="o">.</span><span class="n">thread</span><span class="p">,</span>
            <span class="s2">&quot;thread_name&quot;</span><span class="p">:</span> <span class="n">record</span><span class="o">.</span><span class="n">threadName</span><span class="p">,</span>
        <span class="p">}</span>

        <span class="c1"># Add exception info if present</span>
        <span class="k">if</span> <span class="n">record</span><span class="o">.</span><span class="n">exc_info</span><span class="p">:</span>
            <span class="n">log_entry</span><span class="p">[</span><span class="s2">&quot;exception&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">formatException</span><span class="p">(</span><span class="n">record</span><span class="o">.</span><span class="n">exc_info</span><span class="p">)</span>

        <span class="c1"># Add extra fields if enabled</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">include_extra</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">record</span><span class="o">.</span><span class="vm">__dict__</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
                <span class="k">if</span> <span class="n">key</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">log_entry</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">key</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;_&#39;</span><span class="p">):</span>
                    <span class="k">try</span><span class="p">:</span>
                        <span class="c1"># Ensure value is JSON serializable</span>
                        <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
                        <span class="n">log_entry</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
                    <span class="k">except</span> <span class="p">(</span><span class="ne">TypeError</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">):</span>
                        <span class="n">log_entry</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="nb">str</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">log_entry</span><span class="p">,</span> <span class="n">ensure_ascii</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span></div>
</div>



<div class="viewcode-block" id="PerformanceFilter">
<a class="viewcode-back" href="../modules.html#logging_config.PerformanceFilter">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">PerformanceFilter</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">Filter</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Filter to add performance metrics to log records.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="PerformanceFilter.__init__">
<a class="viewcode-back" href="../modules.html#logging_config.PerformanceFilter.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the performance filter.&quot;&quot;&quot;</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_start_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_request_count</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">Lock</span><span class="p">()</span></div>


<div class="viewcode-block" id="PerformanceFilter.filter">
<a class="viewcode-back" href="../modules.html#logging_config.PerformanceFilter.filter">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">filter</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">record</span><span class="p">:</span> <span class="n">logging</span><span class="o">.</span><span class="n">LogRecord</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Add performance metrics to log record.</span>

<span class="sd">        :param record: Log record to filter</span>
<span class="sd">        :type record: logging.LogRecord</span>

<span class="sd">        :returns: True to include the record</span>
<span class="sd">        :rtype: bool</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">_lock</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_request_count</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="n">record</span><span class="o">.</span><span class="n">uptime</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_start_time</span>
            <span class="n">record</span><span class="o">.</span><span class="n">request_count</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_request_count</span>

        <span class="k">return</span> <span class="kc">True</span></div>
</div>



<div class="viewcode-block" id="ContextFilter">
<a class="viewcode-back" href="../modules.html#logging_config.ContextFilter">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ContextFilter</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">Filter</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Filter to add contextual information to log records.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="ContextFilter.__init__">
<a class="viewcode-back" href="../modules.html#logging_config.ContextFilter.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the context filter.&quot;&quot;&quot;</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_context</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">local</span><span class="p">()</span></div>


<div class="viewcode-block" id="ContextFilter.set_context">
<a class="viewcode-back" href="../modules.html#logging_config.ContextFilter.set_context">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">set_context</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Set context variables for the current thread.</span>

<span class="sd">        :param kwargs: Context variables to set</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="nb">setattr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_context</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span></div>


<div class="viewcode-block" id="ContextFilter.clear_context">
<a class="viewcode-back" href="../modules.html#logging_config.ContextFilter.clear_context">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">clear_context</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Clear context variables for the current thread.&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_context</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">local</span><span class="p">()</span></div>


<div class="viewcode-block" id="ContextFilter.filter">
<a class="viewcode-back" href="../modules.html#logging_config.ContextFilter.filter">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">filter</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">record</span><span class="p">:</span> <span class="n">logging</span><span class="o">.</span><span class="n">LogRecord</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Add context information to log record.</span>

<span class="sd">        :param record: Log record to filter</span>
<span class="sd">        :type record: logging.LogRecord</span>

<span class="sd">        :returns: True to include the record</span>
<span class="sd">        :rtype: bool</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Add context variables</span>
        <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_context</span><span class="o">.</span><span class="vm">__dict__</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">record</span><span class="p">,</span> <span class="n">key</span><span class="p">):</span>
                <span class="nb">setattr</span><span class="p">(</span><span class="n">record</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>

        <span class="c1"># Add correlation ID if not present</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">record</span><span class="p">,</span> <span class="s1">&#39;correlation_id&#39;</span><span class="p">):</span>
            <span class="n">record</span><span class="o">.</span><span class="n">correlation_id</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_context</span><span class="p">,</span> <span class="s1">&#39;correlation_id&#39;</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()))</span>

        <span class="k">return</span> <span class="kc">True</span></div>
</div>



<div class="viewcode-block" id="setup_structured_logging">
<a class="viewcode-back" href="../modules.html#logging_config.setup_structured_logging">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">setup_structured_logging</span><span class="p">(</span>
    <span class="n">log_level</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;INFO&quot;</span><span class="p">,</span>
    <span class="n">log_file</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="n">structured</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span><span class="p">,</span>
    <span class="n">max_file_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">10</span> <span class="o">*</span> <span class="mi">1024</span> <span class="o">*</span> <span class="mi">1024</span><span class="p">,</span>
    <span class="n">backup_count</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">5</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">logging</span><span class="o">.</span><span class="n">Logger</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Setup comprehensive logging configuration.</span>

<span class="sd">    :param log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)</span>
<span class="sd">    :type log_level: str</span>
<span class="sd">    :param log_file: Path to log file. If None, logs to console only</span>
<span class="sd">    :type log_file: Optional[str]</span>
<span class="sd">    :param structured: Whether to use structured JSON logging</span>
<span class="sd">    :type structured: bool</span>
<span class="sd">    :param max_file_size: Maximum log file size in bytes</span>
<span class="sd">    :type max_file_size: int</span>
<span class="sd">    :param backup_count: Number of backup log files to keep</span>
<span class="sd">    :type backup_count: int</span>

<span class="sd">    :returns: Configured logger instance</span>
<span class="sd">    :rtype: logging.Logger</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Create logs directory if needed</span>
    <span class="k">if</span> <span class="n">log_file</span><span class="p">:</span>
        <span class="n">log_path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">log_file</span><span class="p">)</span>
        <span class="n">log_path</span><span class="o">.</span><span class="n">parent</span><span class="o">.</span><span class="n">mkdir</span><span class="p">(</span><span class="n">parents</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exist_ok</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="c1"># Clear existing handlers</span>
    <span class="n">root_logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">()</span>
    <span class="k">for</span> <span class="n">handler</span> <span class="ow">in</span> <span class="n">root_logger</span><span class="o">.</span><span class="n">handlers</span><span class="p">[:]:</span>
        <span class="n">root_logger</span><span class="o">.</span><span class="n">removeHandler</span><span class="p">(</span><span class="n">handler</span><span class="p">)</span>

    <span class="c1"># Create formatters</span>
    <span class="k">if</span> <span class="n">structured</span><span class="p">:</span>
        <span class="n">formatter</span> <span class="o">=</span> <span class="n">StructuredFormatter</span><span class="p">()</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">formatter</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">Formatter</span><span class="p">(</span>
            <span class="s1">&#39;</span><span class="si">%(asctime)s</span><span class="s1"> - </span><span class="si">%(name)s</span><span class="s1"> - </span><span class="si">%(levelname)s</span><span class="s1"> - </span><span class="si">%(correlation_id)s</span><span class="s1"> - </span><span class="si">%(message)s</span><span class="s1">&#39;</span>
        <span class="p">)</span>

    <span class="c1"># Create filters</span>
    <span class="n">performance_filter</span> <span class="o">=</span> <span class="n">PerformanceFilter</span><span class="p">()</span>
    <span class="n">context_filter</span> <span class="o">=</span> <span class="n">ContextFilter</span><span class="p">()</span>

    <span class="c1"># Setup handlers</span>
    <span class="n">handlers</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="c1"># Console handler</span>
    <span class="n">console_handler</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">StreamHandler</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="p">)</span>
    <span class="n">console_handler</span><span class="o">.</span><span class="n">setFormatter</span><span class="p">(</span><span class="n">formatter</span><span class="p">)</span>
    <span class="n">console_handler</span><span class="o">.</span><span class="n">addFilter</span><span class="p">(</span><span class="n">performance_filter</span><span class="p">)</span>
    <span class="n">console_handler</span><span class="o">.</span><span class="n">addFilter</span><span class="p">(</span><span class="n">context_filter</span><span class="p">)</span>
    <span class="n">handlers</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">console_handler</span><span class="p">)</span>

    <span class="c1"># File handler</span>
    <span class="k">if</span> <span class="n">log_file</span><span class="p">:</span>
        <span class="n">file_handler</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">handlers</span><span class="o">.</span><span class="n">RotatingFileHandler</span><span class="p">(</span>
            <span class="n">log_file</span><span class="p">,</span>
            <span class="n">maxBytes</span><span class="o">=</span><span class="n">max_file_size</span><span class="p">,</span>
            <span class="n">backupCount</span><span class="o">=</span><span class="n">backup_count</span><span class="p">,</span>
            <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span>
        <span class="p">)</span>
        <span class="n">file_handler</span><span class="o">.</span><span class="n">setFormatter</span><span class="p">(</span><span class="n">formatter</span><span class="p">)</span>
        <span class="n">file_handler</span><span class="o">.</span><span class="n">addFilter</span><span class="p">(</span><span class="n">performance_filter</span><span class="p">)</span>
        <span class="n">file_handler</span><span class="o">.</span><span class="n">addFilter</span><span class="p">(</span><span class="n">context_filter</span><span class="p">)</span>
        <span class="n">handlers</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">file_handler</span><span class="p">)</span>

    <span class="c1"># Configure root logger</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">basicConfig</span><span class="p">(</span>
        <span class="n">level</span><span class="o">=</span><span class="nb">getattr</span><span class="p">(</span><span class="n">logging</span><span class="p">,</span> <span class="n">log_level</span><span class="o">.</span><span class="n">upper</span><span class="p">()),</span>
        <span class="n">handlers</span><span class="o">=</span><span class="n">handlers</span><span class="p">,</span>
        <span class="n">force</span><span class="o">=</span><span class="kc">True</span>
    <span class="p">)</span>

    <span class="c1"># Configure specific loggers</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="s1">&#39;uvicorn.access&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">setLevel</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">WARNING</span><span class="p">)</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="s1">&#39;uvicorn.error&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">setLevel</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">INFO</span><span class="p">)</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="s1">&#39;fastapi&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">setLevel</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">INFO</span><span class="p">)</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="s1">&#39;httpx&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">setLevel</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">WARNING</span><span class="p">)</span>
    <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="s1">&#39;urllib3&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">setLevel</span><span class="p">(</span><span class="n">logging</span><span class="o">.</span><span class="n">WARNING</span><span class="p">)</span>

    <span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
        <span class="s2">&quot;Logging configured&quot;</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="p">{</span>
            <span class="s2">&quot;level&quot;</span><span class="p">:</span> <span class="n">log_level</span><span class="p">,</span>
            <span class="s2">&quot;structured&quot;</span><span class="p">:</span> <span class="n">structured</span><span class="p">,</span>
            <span class="s2">&quot;file&quot;</span><span class="p">:</span> <span class="n">log_file</span><span class="p">,</span>
            <span class="s2">&quot;handlers&quot;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">handlers</span><span class="p">)</span>
        <span class="p">}</span>
    <span class="p">)</span>

    <span class="k">return</span> <span class="n">logger</span></div>



<span class="c1"># Legacy function for backward compatibility</span>
<div class="viewcode-block" id="setup_logging">
<a class="viewcode-back" href="../modules.html#logging_config.setup_logging">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">setup_logging</span><span class="p">(</span><span class="n">log_dir</span><span class="o">=</span><span class="s2">&quot;logs&quot;</span><span class="p">,</span> <span class="n">log_level</span><span class="o">=</span><span class="n">logging</span><span class="o">.</span><span class="n">INFO</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Legacy logging setup function.</span>

<span class="sd">    :param log_dir: Log directory</span>
<span class="sd">    :param log_level: Logging level</span>

<span class="sd">    :returns: Logger instance</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">log_file</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">log_dir</span><span class="p">,</span> <span class="s2">&quot;app.log&quot;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">setup_structured_logging</span><span class="p">(</span>
        <span class="n">log_level</span><span class="o">=</span><span class="n">logging</span><span class="o">.</span><span class="n">getLevelName</span><span class="p">(</span><span class="n">log_level</span><span class="p">),</span>
        <span class="n">log_file</span><span class="o">=</span><span class="n">log_file</span><span class="p">,</span>
        <span class="n">structured</span><span class="o">=</span><span class="kc">False</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="get_logger">
<a class="viewcode-back" href="../modules.html#logging_config.get_logger">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_logger</span><span class="p">(</span><span class="n">name</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get a logger with the specified name.</span>

<span class="sd">    Args:</span>
<span class="sd">        name (str): Name for the logger</span>

<span class="sd">    Returns:</span>
<span class="sd">        logging.Logger: Configured logger instance</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="n">name</span><span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>