# Import required modules
import time
import logging
import psutil
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta

# Get logger
logger = logging.getLogger(__name__)

@dataclass
class RequestMetrics:
    """Metrics for a single request."""
    request_id: str
    endpoint: str
    start_time: float
    end_time: Optional[float] = None
    error: Optional[str] = None
    cached: bool = False
    retrieval_time: Optional[float] = None
    reasoning_time: Optional[float] = None
    total_tokens: Optional[int] = None

    def duration(self) -> float:
        """Get request duration in seconds."""
        if self.end_time is None:
            return 0
        return self.end_time - self.start_time

@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    gpu_memory_used: Optional[int] = None
    gpu_utilization: Optional[float] = None
    active_requests: int = 0
    cache_size: int = 0
    cache_hits: int = 0
    cache_misses: int = 0

class PerformanceMonitor:
    """Monitor system and request performance."""
    
    def __init__(self, collection_interval: int = 60):
        """Initialize the performance monitor.
        
        Args:
            collection_interval: Interval in seconds for collecting system metrics
        """
        self.collection_interval = collection_interval
        self.request_metrics: List[RequestMetrics] = []
        self.system_metrics: List[SystemMetrics] = []
        self.current_requests: Dict[str, RequestMetrics] = {}
        
        # Initialize counters
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Start collection thread
        self.running = True
        self.collection_thread = threading.Thread(target=self._collect_metrics)
        self.collection_thread.daemon = True
        self.collection_thread.start()
        
        logger.info("Performance monitoring initialized")
    
    def start_request(self, request_id: str, endpoint: str) -> None:
        """Start tracking a new request.
        
        Args:
            request_id: Unique identifier for the request
            endpoint: The API endpoint being called
        """
        metrics = RequestMetrics(
            request_id=request_id,
            endpoint=endpoint,
            start_time=time.time()
        )
        self.current_requests[request_id] = metrics
        logger.debug(f"Started tracking request {request_id}")
    
    def end_request(self, request_id: str, error: Optional[str] = None) -> None:
        """End tracking a request.
        
        Args:
            request_id: Unique identifier for the request
            error: Optional error message if request failed
        """
        if request_id in self.current_requests:
            metrics = self.current_requests[request_id]
            metrics.end_time = time.time()
            metrics.error = error
            
            # Move to completed requests
            self.request_metrics.append(metrics)
            del self.current_requests[request_id]
            
            # Log completion
            duration = metrics.duration()
            if error:
                logger.warning(f"Request {request_id} failed after {duration:.2f}s: {error}")
            else:
                logger.info(f"Request {request_id} completed in {duration:.2f}s")
    
    def record_cache_hit(self, request_id: str) -> None:
        """Record a cache hit for a request.
        
        Args:
            request_id: Unique identifier for the request
        """
        if request_id in self.current_requests:
            self.current_requests[request_id].cached = True
            self.cache_hits += 1
            logger.debug(f"Cache hit for request {request_id}")
    
    def record_cache_miss(self, request_id: str) -> None:
        """Record a cache miss for a request.
        
        Args:
            request_id: Unique identifier for the request
        """
        self.cache_misses += 1
        logger.debug(f"Cache miss for request {request_id}")
    
    def record_retrieval_time(self, request_id: str, duration: float) -> None:
        """Record retrieval operation time for a request.
        
        Args:
            request_id: Unique identifier for the request
            duration: Time taken for retrieval in seconds
        """
        if request_id in self.current_requests:
            self.current_requests[request_id].retrieval_time = duration
            logger.debug(f"Retrieval for request {request_id} took {duration:.2f}s")
    
    def record_reasoning_time(self, request_id: str, duration: float) -> None:
        """Record reasoning operation time for a request.
        
        Args:
            request_id: Unique identifier for the request
            duration: Time taken for reasoning in seconds
        """
        if request_id in self.current_requests:
            self.current_requests[request_id].reasoning_time = duration
            logger.debug(f"Reasoning for request {request_id} took {duration:.2f}s")
    
    def record_token_count(self, request_id: str, count: int) -> None:
        """Record total tokens processed for a request.
        
        Args:
            request_id: Unique identifier for the request
            count: Total number of tokens
        """
        if request_id in self.current_requests:
            self.current_requests[request_id].total_tokens = count
            logger.debug(f"Request {request_id} processed {count} tokens")
    
    def get_recent_metrics(self, minutes: int = 5) -> Dict:
        """Get performance metrics for the recent time period.
        
        Args:
            minutes: Number of minutes to look back
            
        Returns:
            dict: Performance metrics
        """
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        # Filter recent metrics
        recent_system = [m for m in self.system_metrics 
                        if m.timestamp > cutoff_time]
        recent_requests = [m for m in self.request_metrics 
                         if m.end_time and m.end_time > time.mktime(cutoff_time.timetuple())]
        
        # Calculate averages
        if recent_system:
            avg_cpu = sum(m.cpu_percent for m in recent_system) / len(recent_system)
            avg_memory = sum(m.memory_percent for m in recent_system) / len(recent_system)
            avg_active = sum(m.active_requests for m in recent_system) / len(recent_system)
        else:
            avg_cpu = 0
            avg_memory = 0
            avg_active = 0
        
        if recent_requests:
            avg_duration = sum(m.duration() for m in recent_requests) / len(recent_requests)
            error_rate = len([m for m in recent_requests if m.error]) / len(recent_requests)
            cache_hit_rate = len([m for m in recent_requests if m.cached]) / len(recent_requests)
        else:
            avg_duration = 0
            error_rate = 0
            cache_hit_rate = 0
        
        return {
            "system": {
                "cpu_percent": avg_cpu,
                "memory_percent": avg_memory,
                "active_requests": avg_active
            },
            "requests": {
                "total": len(recent_requests),
                "avg_duration": avg_duration,
                "error_rate": error_rate,
                "cache_hit_rate": cache_hit_rate
            },
            "cache": {
                "hits": self.cache_hits,
                "misses": self.cache_misses,
                "hit_rate": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
            }
        }
    
    def _collect_metrics(self) -> None:
        """Collect system metrics periodically."""
        while self.running:
            try:
                # Collect CPU and memory metrics
                cpu_percent = psutil.cpu_percent()
                memory = psutil.virtual_memory()
                
                # Create metrics object
                metrics = SystemMetrics(
                    timestamp=datetime.now(),
                    cpu_percent=cpu_percent,
                    memory_percent=memory.percent,
                    active_requests=len(self.current_requests),
                    cache_hits=self.cache_hits,
                    cache_misses=self.cache_misses
                )
                
                # Try to collect GPU metrics if available
                try:
                    import torch
                    if torch.cuda.is_available():
                        metrics.gpu_memory_used = torch.cuda.memory_allocated()
                        metrics.gpu_utilization = torch.cuda.utilization()
                except ImportError:
                    pass
                
                # Store metrics
                self.system_metrics.append(metrics)
                
                # Keep only last hour of metrics
                cutoff_time = datetime.now() - timedelta(hours=1)
                self.system_metrics = [m for m in self.system_metrics 
                                     if m.timestamp > cutoff_time]
                
                # Log current status
                logger.debug(
                    f"System metrics - CPU: {cpu_percent}%, "
                    f"Memory: {memory.percent}%, "
                    f"Active requests: {len(self.current_requests)}"
                )
                
            except Exception as e:
                logger.error(f"Error collecting system metrics: {str(e)}", exc_info=True)
            
            # Wait for next collection
            time.sleep(self.collection_interval)
    
    def shutdown(self) -> None:
        """Shutdown the monitoring system."""
        self.running = False
        self.collection_thread.join()
        logger.info("Performance monitoring shutdown")
