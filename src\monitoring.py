"""Performance monitoring and metrics collection for the Neural Symbolic Language Model.

This module provides comprehensive performance monitoring capabilities including
system resource tracking, request metrics collection, and performance analytics
for the Neural Symbolic Language Model application.

The monitoring system tracks:
    - System resources (CPU, memory, GPU utilization)
    - Request performance (duration, errors, cache hits)
    - Component-specific metrics (retrieval time, reasoning time)
    - Cache performance and efficiency metrics
    - Real-time performance statistics

Key Components:
    - RequestMetrics: Individual request performance tracking
    - SystemMetrics: System-wide resource utilization
    - PerformanceMonitor: Main monitoring orchestrator
    - Automatic metric collection and aggregation
    - Thread-safe metric storage and retrieval

Features:
    - Real-time system monitoring with configurable intervals
    - Request-level performance tracking with unique IDs
    - GPU utilization monitoring when CUDA is available
    - Cache performance analysis and optimization insights
    - Historical data retention with automatic cleanup
    - Thread-safe operations for concurrent environments

Example:
    Basic usage of the performance monitor::

        from monitoring import PerformanceMonitor

        # Initialize monitor with 30-second collection interval
        monitor = PerformanceMonitor(collection_interval=30)

        # Track a request
        request_id = "req_123"
        monitor.start_request(request_id, "/v1/chat/completions")

        # ... process request ...

        # Complete request tracking
        monitor.complete_request(request_id, error=None, cached=False)

        # Get performance statistics
        stats = monitor.get_performance_stats()
        print(f"Average response time: {stats['avg_response_time']:.3f}s")

        # Shutdown monitor
        monitor.shutdown()

Note:
    The monitor automatically starts a background thread for system metrics
    collection. Always call shutdown() to properly clean up resources when
    the application terminates.

Author:
    AI Assistant

Date:
    2025-06-29

Version:
    0.1.0
"""

# Standard library imports
import logging
import threading
import time
import uuid
from collections import deque
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any

# Third-party imports
import psutil

# Get logger
logger = logging.getLogger(__name__)

@dataclass
class RequestMetrics:
    """Comprehensive metrics tracking for individual API requests.

    This dataclass captures detailed performance metrics for each API request,
    enabling fine-grained analysis of request processing times, error rates,
    and component-specific performance characteristics.

    Attributes:
        request_id (str): Unique identifier for the request
        endpoint (str): API endpoint that handled the request
        start_time (float): Request start timestamp (Unix time)
        end_time (Optional[float]): Request completion timestamp
        error (Optional[str]): Error message if request failed
        cached (bool): Whether the response was served from cache
        retrieval_time (Optional[float]): Time spent on document retrieval
        reasoning_time (Optional[float]): Time spent on symbolic reasoning
        total_tokens (Optional[int]): Total tokens processed in the request

    Example:
        Create and use request metrics::

            metrics = RequestMetrics(
                request_id="req_123",
                endpoint="/v1/chat/completions",
                start_time=time.time()
            )

            # ... process request ...

            metrics.end_time = time.time()
            metrics.cached = True

            print(f"Request duration: {metrics.duration():.3f}s")
    """
    request_id: str
    endpoint: str
    start_time: float
    end_time: Optional[float] = None
    error: Optional[str] = None
    cached: bool = False
    retrieval_time: Optional[float] = None
    reasoning_time: Optional[float] = None
    total_tokens: Optional[int] = None

    def duration(self) -> float:
        """Get request duration in seconds."""
        if self.end_time is None:
            return 0
        return self.end_time - self.start_time

@dataclass
class SystemMetrics:
    """Comprehensive system-wide performance and resource utilization metrics.

    This dataclass captures system-level performance metrics including CPU,
    memory, and GPU utilization, along with application-specific metrics
    like cache performance and active request counts.

    Attributes:
        timestamp (datetime): When the metrics were collected
        cpu_percent (float): CPU utilization percentage (0-100)
        memory_percent (float): Memory utilization percentage (0-100)
        gpu_memory_used (Optional[int]): GPU memory usage in bytes
        gpu_utilization (Optional[float]): GPU utilization percentage (0-100)
        active_requests (int): Number of currently active requests
        cache_size (int): Current number of items in cache
        cache_hits (int): Total cache hits since startup
        cache_misses (int): Total cache misses since startup

    Example:
        Create system metrics snapshot::

            import psutil
            from datetime import datetime

            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=psutil.cpu_percent(),
                memory_percent=psutil.virtual_memory().percent,
                active_requests=5,
                cache_hits=100,
                cache_misses=20
            )

            cache_hit_rate = metrics.cache_hits / (metrics.cache_hits + metrics.cache_misses)
            print(f"Cache hit rate: {cache_hit_rate:.2%}")
    """
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    gpu_memory_used: Optional[int] = None
    gpu_utilization: Optional[float] = None
    active_requests: int = 0
    cache_size: int = 0
    cache_hits: int = 0
    cache_misses: int = 0

class PerformanceMonitor:
    """Monitor system and request performance."""
    
    def __init__(self, collection_interval: int = 60):
        """Initialize the performance monitor.
        
        Args:
            collection_interval: Interval in seconds for collecting system metrics
        """
        self.collection_interval = collection_interval
        self.request_metrics: List[RequestMetrics] = []
        self.system_metrics: List[SystemMetrics] = []
        self.current_requests: Dict[str, RequestMetrics] = {}
        
        # Initialize counters
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Start collection thread
        self.running = True
        self.collection_thread = threading.Thread(target=self._collect_metrics)
        self.collection_thread.daemon = True
        self.collection_thread.start()
        
        logger.info("Performance monitoring initialized")
    
    def start_request(self, request_id: str, endpoint: str) -> None:
        """Start tracking a new request.
        
        Args:
            request_id: Unique identifier for the request
            endpoint: The API endpoint being called
        """
        metrics = RequestMetrics(
            request_id=request_id,
            endpoint=endpoint,
            start_time=time.time()
        )
        self.current_requests[request_id] = metrics
        logger.debug(f"Started tracking request {request_id}")
    
    def end_request(self, request_id: str, error: Optional[str] = None) -> None:
        """End tracking a request.
        
        Args:
            request_id: Unique identifier for the request
            error: Optional error message if request failed
        """
        if request_id in self.current_requests:
            metrics = self.current_requests[request_id]
            metrics.end_time = time.time()
            metrics.error = error
            
            # Move to completed requests
            self.request_metrics.append(metrics)
            del self.current_requests[request_id]
            
            # Log completion
            duration = metrics.duration()
            if error:
                logger.warning(f"Request {request_id} failed after {duration:.2f}s: {error}")
            else:
                logger.info(f"Request {request_id} completed in {duration:.2f}s")
    
    def record_cache_hit(self, request_id: str) -> None:
        """Record a cache hit for a request.
        
        Args:
            request_id: Unique identifier for the request
        """
        if request_id in self.current_requests:
            self.current_requests[request_id].cached = True
            self.cache_hits += 1
            logger.debug(f"Cache hit for request {request_id}")
    
    def record_cache_miss(self, request_id: str) -> None:
        """Record a cache miss for a request.
        
        Args:
            request_id: Unique identifier for the request
        """
        self.cache_misses += 1
        logger.debug(f"Cache miss for request {request_id}")
    
    def record_retrieval_time(self, request_id: str, duration: float) -> None:
        """Record retrieval operation time for a request.
        
        Args:
            request_id: Unique identifier for the request
            duration: Time taken for retrieval in seconds
        """
        if request_id in self.current_requests:
            self.current_requests[request_id].retrieval_time = duration
            logger.debug(f"Retrieval for request {request_id} took {duration:.2f}s")
    
    def record_reasoning_time(self, request_id: str, duration: float) -> None:
        """Record reasoning operation time for a request.
        
        Args:
            request_id: Unique identifier for the request
            duration: Time taken for reasoning in seconds
        """
        if request_id in self.current_requests:
            self.current_requests[request_id].reasoning_time = duration
            logger.debug(f"Reasoning for request {request_id} took {duration:.2f}s")
    
    def record_token_count(self, request_id: str, count: int) -> None:
        """Record total tokens processed for a request.
        
        Args:
            request_id: Unique identifier for the request
            count: Total number of tokens
        """
        if request_id in self.current_requests:
            self.current_requests[request_id].total_tokens = count
            logger.debug(f"Request {request_id} processed {count} tokens")
    
    def get_recent_metrics(self, minutes: int = 5) -> Dict:
        """Get performance metrics for the recent time period.
        
        Args:
            minutes: Number of minutes to look back
            
        Returns:
            dict: Performance metrics
        """
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        # Filter recent metrics
        recent_system = [m for m in self.system_metrics 
                        if m.timestamp > cutoff_time]
        recent_requests = [m for m in self.request_metrics 
                         if m.end_time and m.end_time > time.mktime(cutoff_time.timetuple())]
        
        # Calculate averages
        if recent_system:
            avg_cpu = sum(m.cpu_percent for m in recent_system) / len(recent_system)
            avg_memory = sum(m.memory_percent for m in recent_system) / len(recent_system)
            avg_active = sum(m.active_requests for m in recent_system) / len(recent_system)
        else:
            avg_cpu = 0
            avg_memory = 0
            avg_active = 0
        
        if recent_requests:
            avg_duration = sum(m.duration() for m in recent_requests) / len(recent_requests)
            error_rate = len([m for m in recent_requests if m.error]) / len(recent_requests)
            cache_hit_rate = len([m for m in recent_requests if m.cached]) / len(recent_requests)
        else:
            avg_duration = 0
            error_rate = 0
            cache_hit_rate = 0
        
        return {
            "system": {
                "cpu_percent": avg_cpu,
                "memory_percent": avg_memory,
                "active_requests": avg_active
            },
            "requests": {
                "total": len(recent_requests),
                "avg_duration": avg_duration,
                "error_rate": error_rate,
                "cache_hit_rate": cache_hit_rate
            },
            "cache": {
                "hits": self.cache_hits,
                "misses": self.cache_misses,
                "hit_rate": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
            }
        }
    
    def _collect_metrics(self) -> None:
        """Collect system metrics periodically."""
        while self.running:
            try:
                # Collect CPU and memory metrics
                cpu_percent = psutil.cpu_percent()
                memory = psutil.virtual_memory()
                
                # Create metrics object
                metrics = SystemMetrics(
                    timestamp=datetime.now(),
                    cpu_percent=cpu_percent,
                    memory_percent=memory.percent,
                    active_requests=len(self.current_requests),
                    cache_hits=self.cache_hits,
                    cache_misses=self.cache_misses
                )
                
                # Try to collect GPU metrics if available
                try:
                    import torch
                    if torch.cuda.is_available():
                        metrics.gpu_memory_used = torch.cuda.memory_allocated()
                        metrics.gpu_utilization = torch.cuda.utilization()
                except ImportError:
                    pass
                
                # Store metrics
                self.system_metrics.append(metrics)
                
                # Keep only last hour of metrics
                cutoff_time = datetime.now() - timedelta(hours=1)
                self.system_metrics = [m for m in self.system_metrics 
                                     if m.timestamp > cutoff_time]
                
                # Log current status
                logger.debug(
                    f"System metrics - CPU: {cpu_percent}%, "
                    f"Memory: {memory.percent}%, "
                    f"Active requests: {len(self.current_requests)}"
                )
                
            except Exception as e:
                logger.error(f"Error collecting system metrics: {str(e)}", exc_info=True)
            
            # Wait for next collection
            time.sleep(self.collection_interval)
    
    def shutdown(self) -> None:
        """Shutdown the monitoring system."""
        self.running = False
        self.collection_thread.join()
        logger.info("Performance monitoring shutdown")


class AlertManager:
    """Manages alerting for critical system conditions."""

    def __init__(self):
        """Initialize the alert manager."""
        self.alert_thresholds = {
            'cpu_usage': 90.0,
            'memory_usage': 90.0,
            'gpu_usage': 95.0,
            'error_rate': 10.0,  # errors per minute
            'response_time': 30.0,  # seconds
        }
        self.alert_callbacks: List[Callable] = []
        self.active_alerts: Dict[str, datetime] = {}
        self.alert_cooldown = timedelta(minutes=5)
        self._lock = threading.Lock()

    def add_alert_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """Add a callback function for alert notifications.

        Args:
            callback: Function to call when an alert is triggered
        """
        self.alert_callbacks.append(callback)

    def check_system_alerts(self, system_metrics: SystemMetrics):
        """Check system metrics against alert thresholds.

        Args:
            system_metrics: Current system metrics to check
        """
        current_time = datetime.now()

        # Check CPU usage
        if system_metrics.cpu_usage > self.alert_thresholds['cpu_usage']:
            self._trigger_alert(
                'high_cpu_usage',
                {
                    'current_usage': system_metrics.cpu_usage,
                    'threshold': self.alert_thresholds['cpu_usage'],
                    'timestamp': current_time.isoformat()
                }
            )

        # Check memory usage
        if system_metrics.memory_usage > self.alert_thresholds['memory_usage']:
            self._trigger_alert(
                'high_memory_usage',
                {
                    'current_usage': system_metrics.memory_usage,
                    'threshold': self.alert_thresholds['memory_usage'],
                    'timestamp': current_time.isoformat()
                }
            )

        # Check GPU usage if available
        if system_metrics.gpu_usage and system_metrics.gpu_usage > self.alert_thresholds['gpu_usage']:
            self._trigger_alert(
                'high_gpu_usage',
                {
                    'current_usage': system_metrics.gpu_usage,
                    'threshold': self.alert_thresholds['gpu_usage'],
                    'timestamp': current_time.isoformat()
                }
            )

    def check_performance_alerts(self, performance_stats: Dict[str, Any]):
        """Check performance metrics against alert thresholds.

        Args:
            performance_stats: Current performance statistics
        """
        current_time = datetime.now()

        # Check error rate
        error_rate = performance_stats.get('error_rate', 0)
        if error_rate > self.alert_thresholds['error_rate']:
            self._trigger_alert(
                'high_error_rate',
                {
                    'current_rate': error_rate,
                    'threshold': self.alert_thresholds['error_rate'],
                    'timestamp': current_time.isoformat()
                }
            )

        # Check response time
        avg_response_time = performance_stats.get('avg_response_time', 0)
        if avg_response_time > self.alert_thresholds['response_time']:
            self._trigger_alert(
                'slow_response_time',
                {
                    'current_time': avg_response_time,
                    'threshold': self.alert_thresholds['response_time'],
                    'timestamp': current_time.isoformat()
                }
            )

    def _trigger_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Trigger an alert if not in cooldown period.

        Args:
            alert_type: Type of alert to trigger
            alert_data: Additional data about the alert
        """
        with self._lock:
            current_time = datetime.now()

            # Check if alert is in cooldown
            if alert_type in self.active_alerts:
                last_alert_time = self.active_alerts[alert_type]
                if current_time - last_alert_time < self.alert_cooldown:
                    return  # Skip alert due to cooldown

            # Update active alerts
            self.active_alerts[alert_type] = current_time

            # Log the alert
            logger.warning(
                f"Alert triggered: {alert_type}",
                extra={
                    'event': 'alert_triggered',
                    'alert_type': alert_type,
                    'alert_data': alert_data
                }
            )

            # Call alert callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert_type, alert_data)
                except Exception as e:
                    logger.error(f"Alert callback failed: {e}", exc_info=True)


class DistributedTracer:
    """Provides distributed tracing capabilities for request tracking."""

    def __init__(self):
        """Initialize the distributed tracer."""
        self.active_traces: Dict[str, Dict[str, Any]] = {}
        self.completed_traces: deque = deque(maxlen=1000)
        self._lock = threading.Lock()

    def start_trace(self, trace_id: str, operation: str, parent_span_id: Optional[str] = None) -> str:
        """Start a new trace span.

        Args:
            trace_id: Unique trace identifier
            operation: Name of the operation being traced
            parent_span_id: ID of parent span if this is a child span

        Returns:
            Span ID for the new span
        """
        span_id = str(uuid.uuid4())

        with self._lock:
            if trace_id not in self.active_traces:
                self.active_traces[trace_id] = {
                    'trace_id': trace_id,
                    'spans': {},
                    'start_time': time.time()
                }

            self.active_traces[trace_id]['spans'][span_id] = {
                'span_id': span_id,
                'operation': operation,
                'parent_span_id': parent_span_id,
                'start_time': time.time(),
                'end_time': None,
                'tags': {},
                'logs': []
            }

        return span_id

    def finish_span(self, trace_id: str, span_id: str, tags: Optional[Dict[str, Any]] = None):
        """Finish a trace span.

        Args:
            trace_id: Trace identifier
            span_id: Span identifier
            tags: Additional tags to add to the span
        """
        with self._lock:
            if trace_id in self.active_traces and span_id in self.active_traces[trace_id]['spans']:
                span = self.active_traces[trace_id]['spans'][span_id]
                span['end_time'] = time.time()
                span['duration'] = span['end_time'] - span['start_time']

                if tags:
                    span['tags'].update(tags)

    def add_span_log(self, trace_id: str, span_id: str, message: str, level: str = 'info'):
        """Add a log entry to a span.

        Args:
            trace_id: Trace identifier
            span_id: Span identifier
            message: Log message
            level: Log level
        """
        with self._lock:
            if trace_id in self.active_traces and span_id in self.active_traces[trace_id]['spans']:
                span = self.active_traces[trace_id]['spans'][span_id]
                span['logs'].append({
                    'timestamp': time.time(),
                    'message': message,
                    'level': level
                })

    def complete_trace(self, trace_id: str):
        """Complete a trace and move it to completed traces.

        Args:
            trace_id: Trace identifier to complete
        """
        with self._lock:
            if trace_id in self.active_traces:
                trace = self.active_traces.pop(trace_id)
                trace['end_time'] = time.time()
                trace['total_duration'] = trace['end_time'] - trace['start_time']
                self.completed_traces.append(trace)

    def get_trace(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """Get trace information.

        Args:
            trace_id: Trace identifier

        Returns:
            Trace information or None if not found
        """
        with self._lock:
            # Check active traces
            if trace_id in self.active_traces:
                return self.active_traces[trace_id].copy()

            # Check completed traces
            for trace in self.completed_traces:
                if trace['trace_id'] == trace_id:
                    return trace.copy()

        return None

    def get_trace_statistics(self) -> Dict[str, Any]:
        """Get statistics about traces.

        Returns:
            Dictionary containing trace statistics
        """
        with self._lock:
            active_count = len(self.active_traces)
            completed_count = len(self.completed_traces)

            # Calculate average trace duration from completed traces
            if completed_count > 0:
                total_duration = sum(trace.get('total_duration', 0) for trace in self.completed_traces)
                avg_duration = total_duration / completed_count
            else:
                avg_duration = 0

            return {
                'active_traces': active_count,
                'completed_traces': completed_count,
                'average_trace_duration': avg_duration
            }


# Global instances
alert_manager = AlertManager()
distributed_tracer = DistributedTracer()
