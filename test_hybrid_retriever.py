"""
Test script for the HybridRetriever class.
Run this from the project root directory.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = str(Path(__file__).parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Now import the HybridRetriever
try:
    from src.hybrid_retriever import HybridRetriever
    print("Successfully imported HybridRetriever!")
    
    # Test initialization
    print("\nTesting HybridRetriever initialization...")
    retriever = HybridRetriever(
        vector_db="torch",  # Use torch for this test to avoid FAISS dependency issues
        use_gpu=False,      # Disable GPU for this test
        first_stage_k=10,
        rerank_k=5
    )
    
    # Add test documents
    print("\nAdding test documents...")
    documents = [
        {"id": "doc1", "text": "The quick brown fox jumps over the lazy dog.", "category": "animals"},
        {"id": "doc2", "text": "Machine learning is a subset of artificial intelligence.", "category": "ai"},
        {"id": "doc3", "text": "The capital of France is Paris.", "category": "geography"},
        {"id": "doc4", "text": "Python is a popular programming language for data science.", "category": "programming"},
    ]
    retriever.add_documents(documents)
    
    # Test search
    print("\nTesting search...")
    query = "What is machine learning?"
    print(f"\nQuery: {query}")
    results = retriever.search(query)
    
    print("\nSearch Results:")
    for i, result in enumerate(results, 1):
        print(f"{i}. [{result.metadata.get('category', 'N/A')}] {result.text} (Score: {result.score:.4f})")
    
    print("\nTest completed successfully!")
    
except ImportError as e:
    print(f"Error importing HybridRetriever: {e}")
    print("\nCurrent Python path:")
    for p in sys.path:
        print(f"  - {p}")
    
    # Check if src directory exists
    src_path = os.path.join(project_root, 'src')
    print(f"\nChecking if {src_path} exists: {os.path.exists(src_path)}")
    if os.path.exists(src_path):
        print("\nContents of src directory:")
        for f in os.listdir(src_path):
            print(f"  - {f}")
