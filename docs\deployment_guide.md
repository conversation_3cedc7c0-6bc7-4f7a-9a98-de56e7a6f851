# Production Deployment Guide

This guide provides comprehensive instructions for deploying the Neural Symbolic Language Model with Ollama gemma3n:e2b in production environments.

## Overview

The Neural Symbolic Language Model is designed for production deployment with:
- **High Availability**: Load balancing and failover support
- **Scalability**: Horizontal and vertical scaling options
- **Security**: API authentication, rate limiting, and secure configurations
- **Monitoring**: Health checks, performance metrics, and logging
- **Performance**: Optimized for concurrent requests and caching

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │────│  API Gateway    │────│   Application   │
│   (nginx/HAProxy)│    │  (Rate Limiting)│    │   Instances     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │  Ollama Service │
                                               │  (gemma3n:e2b)  │
                                               └─────────────────┘
```

## Prerequisites

### System Requirements

**Minimum Requirements:**
- **CPU**: 8 cores (16 threads recommended)
- **RAM**: 32GB (64GB recommended for high load)
- **GPU**: 16GB VRAM (for optimal performance)
- **Storage**: 100GB SSD (for models and logs)
- **Network**: 1Gbps connection

**Recommended Production Setup:**
- **CPU**: 16+ cores with high clock speed
- **RAM**: 64GB+ DDR4/DDR5
- **GPU**: NVIDIA A100, H100, or RTX 4090
- **Storage**: NVMe SSD with 500GB+ space
- **Network**: 10Gbps with low latency

### Software Requirements

- **OS**: Ubuntu 20.04+ LTS, CentOS 8+, or RHEL 8+
- **Docker**: 20.10+ (for containerized deployment)
- **Python**: 3.10+ (if running natively)
- **NVIDIA Drivers**: 525+ (for GPU support)
- **CUDA**: 11.8+ (for GPU acceleration)

## Installation Methods

### Method 1: Docker Deployment (Recommended)

#### 1. Create Production Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-prod
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  neural-symbolic-api:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: neural-symbolic-api-prod
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - APP_ENVIRONMENT=production
      - MODEL_REASONING_ENGINE=ollama
      - MODEL_REASONING_MODEL=gemma3n:e2b
      - MODEL_OLLAMA_HOST=http://ollama:11434
      - SECURITY_API_KEYS_JSON=${SECURITY_API_KEYS_JSON}
      - LOG_LEVEL=INFO
      - CACHE_ENABLED=true
      - CACHE_MAX_SIZE=10000
    volumes:
      - app_logs:/app/logs
      - app_data:/app/data
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - neural-symbolic-api

volumes:
  ollama_data:
  app_logs:
  app_data:
```

#### 2. Create Production Dockerfile

```dockerfile
# Dockerfile.prod
FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY config/ ./config/

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start application
CMD ["python", "src/main.py"]
```

#### 3. Deploy with Docker Compose

```bash
# Set environment variables
export SECURITY_API_KEYS_JSON='{"prod_key": {"key": "your-secure-api-key", "permissions": ["read", "write"]}}'

# Deploy the stack
docker-compose -f docker-compose.prod.yml up -d

# Pull and start the model
docker exec ollama-prod ollama pull gemma3n:e2b

# Verify deployment
curl http://localhost:8080/health
```

### Method 2: Native Installation

#### 1. Install Ollama

```bash
# Install Ollama
curl -fsSL https://ollama.com/install.sh | sh

# Start Ollama service
sudo systemctl enable ollama
sudo systemctl start ollama

# Pull the model
ollama pull gemma3n:e2b
```

#### 2. Install Application

```bash
# Clone repository
git clone https://github.com/your-org/neural-symbolic-language-model.git
cd neural-symbolic-language-model

# Create virtual environment
python3.10 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Copy production configuration
cp config/production.env .env

# Edit configuration
nano .env
```

#### 3. Create Systemd Service

```ini
# /etc/systemd/system/neural-symbolic-api.service
[Unit]
Description=Neural Symbolic Language Model API
After=network.target ollama.service
Requires=ollama.service

[Service]
Type=simple
User=neural-symbolic
Group=neural-symbolic
WorkingDirectory=/opt/neural-symbolic-language-model
Environment=PATH=/opt/neural-symbolic-language-model/venv/bin
ExecStart=/opt/neural-symbolic-language-model/venv/bin/python src/main.py
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/neural-symbolic-language-model/logs

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable neural-symbolic-api
sudo systemctl start neural-symbolic-api
sudo systemctl status neural-symbolic-api
```

## Configuration

### Environment Variables

Create a production environment file:

```bash
# Production Configuration
APP_ENVIRONMENT=production
APP_DEBUG=false
APP_HOST=0.0.0.0
APP_PORT=8080
APP_WORKERS=4

# Model Configuration
MODEL_REASONING_ENGINE=ollama
MODEL_REASONING_MODEL=gemma3n:e2b
MODEL_OLLAMA_HOST=http://localhost:11434
MODEL_OLLAMA_TIMEOUT=60
MODEL_USE_GPU=true

# Security Configuration
SECURITY_API_KEYS_JSON='{"prod_key": {"key": "your-secure-api-key-here", "permissions": ["read", "write"], "rate_limit": 1000}}'
SECURITY_RATE_LIMIT_REQUESTS=100
SECURITY_RATE_LIMIT_WINDOW=60
SECURITY_MAX_REQUEST_SIZE=1048576
SECURITY_CORS_ORIGINS='["https://yourdomain.com"]'

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=7200
CACHE_MAX_SIZE=10000

# Logging Configuration
LOG_LEVEL=INFO
LOG_STRUCTURED=true
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/var/log/neural-symbolic-api
LOG_MAX_FILE_SIZE=104857600
LOG_BACKUP_COUNT=10

# Performance Configuration
PERFORMANCE_MAX_CONCURRENT_REQUESTS=50
PERFORMANCE_REQUEST_TIMEOUT=300
```

### Nginx Configuration

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream neural_symbolic_api {
        server neural-symbolic-api-prod:8080;
        # Add more instances for load balancing
        # server neural-symbolic-api-2:8080;
        # server neural-symbolic-api-3:8080;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    server {
        listen 80;
        server_name yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API endpoints
        location / {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://neural_symbolic_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }

        # Health check endpoint (no rate limiting)
        location /health {
            proxy_pass http://neural_symbolic_api;
            access_log off;
        }
    }
}
```

## Security

### API Key Management

```bash
# Generate secure API keys
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Set in environment
export SECURITY_API_KEYS_JSON='{
  "admin": {
    "key": "your-admin-key-here",
    "permissions": ["read", "write", "admin"],
    "rate_limit": 1000
  },
  "user": {
    "key": "your-user-key-here", 
    "permissions": ["read"],
    "rate_limit": 100
  }
}'
```

### Firewall Configuration

```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 8080/tcp   # Block direct API access
sudo ufw deny 11434/tcp  # Block direct Ollama access
sudo ufw enable

# iptables (CentOS/RHEL)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
iptables -A INPUT -p tcp --dport 8080 -j DROP
iptables -A INPUT -p tcp --dport 11434 -j DROP
```

### SSL/TLS Setup

```bash
# Using Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com

# Or generate self-signed certificate for testing
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/key.pem \
    -out /etc/nginx/ssl/cert.pem
```

## Monitoring

### Health Checks

```bash
# Basic health check
curl -f https://yourdomain.com/health

# Detailed health check script
#!/bin/bash
HEALTH_URL="https://yourdomain.com/health"
RESPONSE=$(curl -s -w "%{http_code}" "$HEALTH_URL")
HTTP_CODE="${RESPONSE: -3}"

if [ "$HTTP_CODE" -eq 200 ]; then
    echo "✅ Service is healthy"
    exit 0
else
    echo "❌ Service is unhealthy (HTTP $HTTP_CODE)"
    exit 1
fi
```

### Logging

```bash
# View application logs
docker logs neural-symbolic-api-prod -f

# Or for native installation
journalctl -u neural-symbolic-api -f

# Log rotation configuration
# /etc/logrotate.d/neural-symbolic-api
/var/log/neural-symbolic-api/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 neural-symbolic neural-symbolic
    postrotate
        systemctl reload neural-symbolic-api
    endscript
}
```

### Performance Monitoring

```bash
# Monitor system resources
htop
nvidia-smi -l 1

# Monitor API performance
curl -H "Authorization: Bearer your-api-key" \
     https://yourdomain.com/performance

# Set up monitoring alerts (example with Prometheus)
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'neural-symbolic-api'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    bearer_token: 'your-api-key'
```

## Scaling

### Horizontal Scaling

```yaml
# docker-compose.scale.yml
version: '3.8'

services:
  neural-symbolic-api:
    # ... existing configuration ...
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 8G
        reservations:
          cpus: '1'
          memory: 4G

  nginx:
    # ... existing configuration ...
    # Update upstream to include all replicas
```

### Load Balancing

```nginx
# Advanced load balancing
upstream neural_symbolic_api {
    least_conn;
    server neural-symbolic-api-1:8080 weight=3;
    server neural-symbolic-api-2:8080 weight=2;
    server neural-symbolic-api-3:8080 weight=1;
    
    # Health checks
    keepalive 32;
}
```

## Backup and Recovery

### Data Backup

```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/neural-symbolic-$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# Backup application data
docker run --rm -v neural-symbolic-language-model_app_data:/data \
    -v "$BACKUP_DIR":/backup alpine \
    tar czf /backup/app_data.tar.gz -C /data .

# Backup Ollama models
docker run --rm -v neural-symbolic-language-model_ollama_data:/data \
    -v "$BACKUP_DIR":/backup alpine \
    tar czf /backup/ollama_data.tar.gz -C /data .

# Backup configuration
cp -r config/ "$BACKUP_DIR/"
cp .env "$BACKUP_DIR/"

echo "Backup completed: $BACKUP_DIR"
```

### Disaster Recovery

```bash
#!/bin/bash
# restore.sh
BACKUP_DIR="$1"

if [ -z "$BACKUP_DIR" ]; then
    echo "Usage: $0 <backup_directory>"
    exit 1
fi

# Stop services
docker-compose -f docker-compose.prod.yml down

# Restore data
docker run --rm -v neural-symbolic-language-model_app_data:/data \
    -v "$BACKUP_DIR":/backup alpine \
    tar xzf /backup/app_data.tar.gz -C /data

docker run --rm -v neural-symbolic-language-model_ollama_data:/data \
    -v "$BACKUP_DIR":/backup alpine \
    tar xzf /backup/ollama_data.tar.gz -C /data

# Restore configuration
cp "$BACKUP_DIR/.env" .
cp -r "$BACKUP_DIR/config/" .

# Start services
docker-compose -f docker-compose.prod.yml up -d

echo "Restore completed from: $BACKUP_DIR"
```

## Troubleshooting

### Common Issues

**1. High Memory Usage**
```bash
# Check memory usage
free -h
docker stats

# Solution: Adjust cache settings
CACHE_MAX_SIZE=5000
MODEL_GPU_MEMORY_FRACTION=0.6
```

**2. Slow Response Times**
```bash
# Check GPU utilization
nvidia-smi

# Solution: Enable GPU acceleration
MODEL_USE_GPU=true
MODEL_GPU_MEMORY_FRACTION=0.8
```

**3. Connection Errors**
```bash
# Check Ollama status
curl http://localhost:11434/api/tags

# Restart Ollama if needed
docker restart ollama-prod
```

### Performance Tuning

```bash
# Optimize for high concurrency
PERFORMANCE_MAX_CONCURRENT_REQUESTS=100
CACHE_MAX_SIZE=20000
APP_WORKERS=8

# Optimize for low latency
CACHE_TTL=300
MODEL_OLLAMA_TIMEOUT=30
```

## Maintenance

### Regular Tasks

```bash
# Weekly maintenance script
#!/bin/bash

# Update models
docker exec ollama-prod ollama pull gemma3n:e2b

# Clean up old logs
find /var/log/neural-symbolic-api -name "*.log.*" -mtime +30 -delete

# Clean up Docker
docker system prune -f

# Restart services for fresh state
docker-compose -f docker-compose.prod.yml restart

echo "Maintenance completed: $(date)"
```

### Updates

```bash
# Update application
git pull origin main
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Update Ollama
docker pull ollama/ollama:latest
docker-compose -f docker-compose.prod.yml up -d ollama
```

This deployment guide provides a comprehensive foundation for running the Neural Symbolic Language Model in production. Adjust configurations based on your specific requirements and infrastructure.
