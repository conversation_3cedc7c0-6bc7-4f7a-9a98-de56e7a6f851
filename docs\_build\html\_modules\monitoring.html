

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>monitoring &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Module code</a></li>
      <li class="breadcrumb-item active">monitoring</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for monitoring</h1><div class="highlight"><pre>
<span></span><span class="c1"># Import required modules</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">psutil</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">threading</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">dataclasses</span><span class="w"> </span><span class="kn">import</span> <span class="n">dataclass</span><span class="p">,</span> <span class="n">field</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timedelta</span>

<span class="c1"># Get logger</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<div class="viewcode-block" id="RequestMetrics">
<a class="viewcode-back" href="../modules.html#monitoring.RequestMetrics">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span><span class="w"> </span><span class="nc">RequestMetrics</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Metrics for a single request.&quot;&quot;&quot;</span>
    <span class="n">request_id</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">endpoint</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">start_time</span><span class="p">:</span> <span class="nb">float</span>
    <span class="n">end_time</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">error</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">cached</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span>
    <span class="n">retrieval_time</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">reasoning_time</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">total_tokens</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

<div class="viewcode-block" id="RequestMetrics.duration">
<a class="viewcode-back" href="../modules.html#monitoring.RequestMetrics.duration">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">duration</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">float</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get request duration in seconds.&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">end_time</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="mi">0</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">end_time</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">start_time</span></div>
</div>


<div class="viewcode-block" id="SystemMetrics">
<a class="viewcode-back" href="../modules.html#monitoring.SystemMetrics">[docs]</a>
<span class="nd">@dataclass</span>
<span class="k">class</span><span class="w"> </span><span class="nc">SystemMetrics</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;System performance metrics.&quot;&quot;&quot;</span>
    <span class="n">timestamp</span><span class="p">:</span> <span class="n">datetime</span>
    <span class="n">cpu_percent</span><span class="p">:</span> <span class="nb">float</span>
    <span class="n">memory_percent</span><span class="p">:</span> <span class="nb">float</span>
    <span class="n">gpu_memory_used</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">gpu_utilization</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">active_requests</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="n">cache_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="n">cache_hits</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="n">cache_misses</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">0</span></div>


<div class="viewcode-block" id="PerformanceMonitor">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">PerformanceMonitor</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Monitor system and request performance.&quot;&quot;&quot;</span>
    
<div class="viewcode-block" id="PerformanceMonitor.__init__">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">collection_interval</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">60</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the performance monitor.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            collection_interval: Interval in seconds for collecting system metrics</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">collection_interval</span> <span class="o">=</span> <span class="n">collection_interval</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">request_metrics</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">RequestMetrics</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">system_metrics</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">SystemMetrics</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">RequestMetrics</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
        
        <span class="c1"># Initialize counters</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">cache_hits</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">cache_misses</span> <span class="o">=</span> <span class="mi">0</span>
        
        <span class="c1"># Start collection thread</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">running</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">collection_thread</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">Thread</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_collect_metrics</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">collection_thread</span><span class="o">.</span><span class="n">daemon</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">collection_thread</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Performance monitoring initialized&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="PerformanceMonitor.start_request">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.start_request">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">start_request</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">endpoint</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Start tracking a new request.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            request_id: Unique identifier for the request</span>
<span class="sd">            endpoint: The API endpoint being called</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">metrics</span> <span class="o">=</span> <span class="n">RequestMetrics</span><span class="p">(</span>
            <span class="n">request_id</span><span class="o">=</span><span class="n">request_id</span><span class="p">,</span>
            <span class="n">endpoint</span><span class="o">=</span><span class="n">endpoint</span><span class="p">,</span>
            <span class="n">start_time</span><span class="o">=</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
        <span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">[</span><span class="n">request_id</span><span class="p">]</span> <span class="o">=</span> <span class="n">metrics</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Started tracking request </span><span class="si">{</span><span class="n">request_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="PerformanceMonitor.end_request">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.end_request">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">end_request</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">error</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;End tracking a request.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            request_id: Unique identifier for the request</span>
<span class="sd">            error: Optional error message if request failed</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">request_id</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">:</span>
            <span class="n">metrics</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">[</span><span class="n">request_id</span><span class="p">]</span>
            <span class="n">metrics</span><span class="o">.</span><span class="n">end_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
            <span class="n">metrics</span><span class="o">.</span><span class="n">error</span> <span class="o">=</span> <span class="n">error</span>
            
            <span class="c1"># Move to completed requests</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">request_metrics</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">metrics</span><span class="p">)</span>
            <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">[</span><span class="n">request_id</span><span class="p">]</span>
            
            <span class="c1"># Log completion</span>
            <span class="n">duration</span> <span class="o">=</span> <span class="n">metrics</span><span class="o">.</span><span class="n">duration</span><span class="p">()</span>
            <span class="k">if</span> <span class="n">error</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Request </span><span class="si">{</span><span class="n">request_id</span><span class="si">}</span><span class="s2"> failed after </span><span class="si">{</span><span class="n">duration</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2">s: </span><span class="si">{</span><span class="n">error</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Request </span><span class="si">{</span><span class="n">request_id</span><span class="si">}</span><span class="s2"> completed in </span><span class="si">{</span><span class="n">duration</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2">s&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="PerformanceMonitor.record_cache_hit">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.record_cache_hit">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">record_cache_hit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Record a cache hit for a request.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            request_id: Unique identifier for the request</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">request_id</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">[</span><span class="n">request_id</span><span class="p">]</span><span class="o">.</span><span class="n">cached</span> <span class="o">=</span> <span class="kc">True</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">cache_hits</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache hit for request </span><span class="si">{</span><span class="n">request_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="PerformanceMonitor.record_cache_miss">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.record_cache_miss">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">record_cache_miss</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Record a cache miss for a request.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            request_id: Unique identifier for the request</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">cache_misses</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cache miss for request </span><span class="si">{</span><span class="n">request_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="PerformanceMonitor.record_retrieval_time">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.record_retrieval_time">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">record_retrieval_time</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">duration</span><span class="p">:</span> <span class="nb">float</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Record retrieval operation time for a request.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            request_id: Unique identifier for the request</span>
<span class="sd">            duration: Time taken for retrieval in seconds</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">request_id</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">[</span><span class="n">request_id</span><span class="p">]</span><span class="o">.</span><span class="n">retrieval_time</span> <span class="o">=</span> <span class="n">duration</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Retrieval for request </span><span class="si">{</span><span class="n">request_id</span><span class="si">}</span><span class="s2"> took </span><span class="si">{</span><span class="n">duration</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2">s&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="PerformanceMonitor.record_reasoning_time">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.record_reasoning_time">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">record_reasoning_time</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">duration</span><span class="p">:</span> <span class="nb">float</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Record reasoning operation time for a request.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            request_id: Unique identifier for the request</span>
<span class="sd">            duration: Time taken for reasoning in seconds</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">request_id</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">[</span><span class="n">request_id</span><span class="p">]</span><span class="o">.</span><span class="n">reasoning_time</span> <span class="o">=</span> <span class="n">duration</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Reasoning for request </span><span class="si">{</span><span class="n">request_id</span><span class="si">}</span><span class="s2"> took </span><span class="si">{</span><span class="n">duration</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2">s&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="PerformanceMonitor.record_token_count">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.record_token_count">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">record_token_count</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">request_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">count</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Record total tokens processed for a request.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            request_id: Unique identifier for the request</span>
<span class="sd">            count: Total number of tokens</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">request_id</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">[</span><span class="n">request_id</span><span class="p">]</span><span class="o">.</span><span class="n">total_tokens</span> <span class="o">=</span> <span class="n">count</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Request </span><span class="si">{</span><span class="n">request_id</span><span class="si">}</span><span class="s2"> processed </span><span class="si">{</span><span class="n">count</span><span class="si">}</span><span class="s2"> tokens&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="PerformanceMonitor.get_recent_metrics">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.get_recent_metrics">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_recent_metrics</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">minutes</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">5</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get performance metrics for the recent time period.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            minutes: Number of minutes to look back</span>
<span class="sd">            </span>
<span class="sd">        Returns:</span>
<span class="sd">            dict: Performance metrics</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">cutoff_time</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span> <span class="o">-</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">minutes</span><span class="o">=</span><span class="n">minutes</span><span class="p">)</span>
        
        <span class="c1"># Filter recent metrics</span>
        <span class="n">recent_system</span> <span class="o">=</span> <span class="p">[</span><span class="n">m</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_metrics</span> 
                        <span class="k">if</span> <span class="n">m</span><span class="o">.</span><span class="n">timestamp</span> <span class="o">&gt;</span> <span class="n">cutoff_time</span><span class="p">]</span>
        <span class="n">recent_requests</span> <span class="o">=</span> <span class="p">[</span><span class="n">m</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">request_metrics</span> 
                         <span class="k">if</span> <span class="n">m</span><span class="o">.</span><span class="n">end_time</span> <span class="ow">and</span> <span class="n">m</span><span class="o">.</span><span class="n">end_time</span> <span class="o">&gt;</span> <span class="n">time</span><span class="o">.</span><span class="n">mktime</span><span class="p">(</span><span class="n">cutoff_time</span><span class="o">.</span><span class="n">timetuple</span><span class="p">())]</span>
        
        <span class="c1"># Calculate averages</span>
        <span class="k">if</span> <span class="n">recent_system</span><span class="p">:</span>
            <span class="n">avg_cpu</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">cpu_percent</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="n">recent_system</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">recent_system</span><span class="p">)</span>
            <span class="n">avg_memory</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">memory_percent</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="n">recent_system</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">recent_system</span><span class="p">)</span>
            <span class="n">avg_active</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">active_requests</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="n">recent_system</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">recent_system</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">avg_cpu</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="n">avg_memory</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="n">avg_active</span> <span class="o">=</span> <span class="mi">0</span>
        
        <span class="k">if</span> <span class="n">recent_requests</span><span class="p">:</span>
            <span class="n">avg_duration</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">m</span><span class="o">.</span><span class="n">duration</span><span class="p">()</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="n">recent_requests</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">recent_requests</span><span class="p">)</span>
            <span class="n">error_rate</span> <span class="o">=</span> <span class="nb">len</span><span class="p">([</span><span class="n">m</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="n">recent_requests</span> <span class="k">if</span> <span class="n">m</span><span class="o">.</span><span class="n">error</span><span class="p">])</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">recent_requests</span><span class="p">)</span>
            <span class="n">cache_hit_rate</span> <span class="o">=</span> <span class="nb">len</span><span class="p">([</span><span class="n">m</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="n">recent_requests</span> <span class="k">if</span> <span class="n">m</span><span class="o">.</span><span class="n">cached</span><span class="p">])</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">recent_requests</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">avg_duration</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="n">error_rate</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="n">cache_hit_rate</span> <span class="o">=</span> <span class="mi">0</span>
        
        <span class="k">return</span> <span class="p">{</span>
            <span class="s2">&quot;system&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;cpu_percent&quot;</span><span class="p">:</span> <span class="n">avg_cpu</span><span class="p">,</span>
                <span class="s2">&quot;memory_percent&quot;</span><span class="p">:</span> <span class="n">avg_memory</span><span class="p">,</span>
                <span class="s2">&quot;active_requests&quot;</span><span class="p">:</span> <span class="n">avg_active</span>
            <span class="p">},</span>
            <span class="s2">&quot;requests&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;total&quot;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">recent_requests</span><span class="p">),</span>
                <span class="s2">&quot;avg_duration&quot;</span><span class="p">:</span> <span class="n">avg_duration</span><span class="p">,</span>
                <span class="s2">&quot;error_rate&quot;</span><span class="p">:</span> <span class="n">error_rate</span><span class="p">,</span>
                <span class="s2">&quot;cache_hit_rate&quot;</span><span class="p">:</span> <span class="n">cache_hit_rate</span>
            <span class="p">},</span>
            <span class="s2">&quot;cache&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;hits&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache_hits</span><span class="p">,</span>
                <span class="s2">&quot;misses&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache_misses</span><span class="p">,</span>
                <span class="s2">&quot;hit_rate&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache_hits</span> <span class="o">/</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">cache_hits</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache_misses</span><span class="p">)</span> <span class="k">if</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">cache_hits</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache_misses</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="mi">0</span>
            <span class="p">}</span>
        <span class="p">}</span></div>

    
    <span class="k">def</span><span class="w"> </span><span class="nf">_collect_metrics</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Collect system metrics periodically.&quot;&quot;&quot;</span>
        <span class="k">while</span> <span class="bp">self</span><span class="o">.</span><span class="n">running</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="c1"># Collect CPU and memory metrics</span>
                <span class="n">cpu_percent</span> <span class="o">=</span> <span class="n">psutil</span><span class="o">.</span><span class="n">cpu_percent</span><span class="p">()</span>
                <span class="n">memory</span> <span class="o">=</span> <span class="n">psutil</span><span class="o">.</span><span class="n">virtual_memory</span><span class="p">()</span>
                
                <span class="c1"># Create metrics object</span>
                <span class="n">metrics</span> <span class="o">=</span> <span class="n">SystemMetrics</span><span class="p">(</span>
                    <span class="n">timestamp</span><span class="o">=</span><span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">(),</span>
                    <span class="n">cpu_percent</span><span class="o">=</span><span class="n">cpu_percent</span><span class="p">,</span>
                    <span class="n">memory_percent</span><span class="o">=</span><span class="n">memory</span><span class="o">.</span><span class="n">percent</span><span class="p">,</span>
                    <span class="n">active_requests</span><span class="o">=</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">),</span>
                    <span class="n">cache_hits</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">cache_hits</span><span class="p">,</span>
                    <span class="n">cache_misses</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">cache_misses</span>
                <span class="p">)</span>
                
                <span class="c1"># Try to collect GPU metrics if available</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="kn">import</span><span class="w"> </span><span class="nn">torch</span>
                    <span class="k">if</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">():</span>
                        <span class="n">metrics</span><span class="o">.</span><span class="n">gpu_memory_used</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">memory_allocated</span><span class="p">()</span>
                        <span class="n">metrics</span><span class="o">.</span><span class="n">gpu_utilization</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">utilization</span><span class="p">()</span>
                <span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
                    <span class="k">pass</span>
                
                <span class="c1"># Store metrics</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">system_metrics</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">metrics</span><span class="p">)</span>
                
                <span class="c1"># Keep only last hour of metrics</span>
                <span class="n">cutoff_time</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span> <span class="o">-</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">hours</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">system_metrics</span> <span class="o">=</span> <span class="p">[</span><span class="n">m</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_metrics</span> 
                                     <span class="k">if</span> <span class="n">m</span><span class="o">.</span><span class="n">timestamp</span> <span class="o">&gt;</span> <span class="n">cutoff_time</span><span class="p">]</span>
                
                <span class="c1"># Log current status</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
                    <span class="sa">f</span><span class="s2">&quot;System metrics - CPU: </span><span class="si">{</span><span class="n">cpu_percent</span><span class="si">}</span><span class="s2">%, &quot;</span>
                    <span class="sa">f</span><span class="s2">&quot;Memory: </span><span class="si">{</span><span class="n">memory</span><span class="o">.</span><span class="n">percent</span><span class="si">}</span><span class="s2">%, &quot;</span>
                    <span class="sa">f</span><span class="s2">&quot;Active requests: </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">current_requests</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span>
                <span class="p">)</span>
                
            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error collecting system metrics: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            
            <span class="c1"># Wait for next collection</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">collection_interval</span><span class="p">)</span>
    
<div class="viewcode-block" id="PerformanceMonitor.shutdown">
<a class="viewcode-back" href="../modules.html#monitoring.PerformanceMonitor.shutdown">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">shutdown</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Shutdown the monitoring system.&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">running</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">collection_thread</span><span class="o">.</span><span class="n">join</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Performance monitoring shutdown&quot;</span><span class="p">)</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>