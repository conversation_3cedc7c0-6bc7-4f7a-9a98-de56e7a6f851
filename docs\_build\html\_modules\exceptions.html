

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>exceptions &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Module code</a></li>
      <li class="breadcrumb-item active">exceptions</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for exceptions</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Custom exception classes for the Neural Symbolic Language Model.</span>

<span class="sd">This module defines domain-specific exceptions with proper error codes</span>
<span class="sd">and structured error responses for better error handling and debugging.</span>

<span class="sd">Author: AI Assistant</span>
<span class="sd">Date: 2025-06-29</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Any</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>


<div class="viewcode-block" id="SymbolicAIException">
<a class="viewcode-back" href="../modules.html#exceptions.SymbolicAIException">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">SymbolicAIException</span><span class="p">(</span><span class="ne">Exception</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Base exception class for all SymbolicAI errors.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        message: Human-readable error message</span>
<span class="sd">        error_code: Machine-readable error code</span>
<span class="sd">        details: Additional error details</span>
<span class="sd">        status_code: HTTP status code for API responses</span>
<span class="sd">    &quot;&quot;&quot;</span>

<div class="viewcode-block" id="SymbolicAIException.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.SymbolicAIException.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
        <span class="n">error_code</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">details</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">status_code</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">500</span>
    <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the exception.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Human-readable error message</span>
<span class="sd">            error_code: Machine-readable error code</span>
<span class="sd">            details: Additional error details</span>
<span class="sd">            status_code: HTTP status code for API responses</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">message</span> <span class="o">=</span> <span class="n">message</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">error_code</span> <span class="o">=</span> <span class="n">error_code</span> <span class="ow">or</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span><span class="o">.</span><span class="vm">__name__</span><span class="o">.</span><span class="n">upper</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">details</span> <span class="o">=</span> <span class="n">details</span> <span class="ow">or</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">status_code</span> <span class="o">=</span> <span class="n">status_code</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">message</span><span class="p">)</span></div>


<div class="viewcode-block" id="SymbolicAIException.to_dict">
<a class="viewcode-back" href="../modules.html#exceptions.SymbolicAIException.to_dict">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">to_dict</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Convert exception to dictionary for API responses.</span>

<span class="sd">        Returns:</span>
<span class="sd">            Dictionary representation of the exception</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="p">{</span>
            <span class="s2">&quot;error&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;code&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">error_code</span><span class="p">,</span>
                <span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">message</span><span class="p">,</span>
                <span class="s2">&quot;details&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">details</span>
            <span class="p">}</span>
        <span class="p">}</span></div>
</div>



<div class="viewcode-block" id="ValidationError">
<a class="viewcode-back" href="../modules.html#exceptions.ValidationError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ValidationError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when input validation fails.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="ValidationError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.ValidationError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">field</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize validation error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            field: Field that failed validation</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">details</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;details&#39;</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">field</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;field&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">field</span>

        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="n">details</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="AuthenticationError">
<a class="viewcode-back" href="../modules.html#exceptions.AuthenticationError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">AuthenticationError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when authentication fails.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="AuthenticationError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.AuthenticationError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;Authentication failed&quot;</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize authentication error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;AUTHENTICATION_ERROR&quot;</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">401</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="AuthorizationError">
<a class="viewcode-back" href="../modules.html#exceptions.AuthorizationError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">AuthorizationError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when authorization fails.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="AuthorizationError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.AuthorizationError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;Access denied&quot;</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize authorization error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;AUTHORIZATION_ERROR&quot;</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">403</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="RateLimitError">
<a class="viewcode-back" href="../modules.html#exceptions.RateLimitError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">RateLimitError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when rate limit is exceeded.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="RateLimitError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.RateLimitError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;Rate limit exceeded&quot;</span><span class="p">,</span> <span class="n">retry_after</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize rate limit error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            retry_after: Seconds to wait before retrying</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">details</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;details&#39;</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">retry_after</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;retry_after&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">retry_after</span>

        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;RATE_LIMIT_ERROR&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="n">details</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">429</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="RetrievalError">
<a class="viewcode-back" href="../modules.html#exceptions.RetrievalError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">RetrievalError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when retrieval operations fail.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="RetrievalError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.RetrievalError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">operation</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize retrieval error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            operation: The retrieval operation that failed</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">details</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;details&#39;</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">operation</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;operation&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">operation</span>

        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;RETRIEVAL_ERROR&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="n">details</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="ReasoningError">
<a class="viewcode-back" href="../modules.html#exceptions.ReasoningError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ReasoningError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when reasoning operations fail.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="ReasoningError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.ReasoningError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">reasoning_type</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize reasoning error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            reasoning_type: The type of reasoning that failed</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">details</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;details&#39;</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">reasoning_type</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;reasoning_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">reasoning_type</span>

        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;REASONING_ERROR&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="n">details</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="VectorStoreError">
<a class="viewcode-back" href="../modules.html#exceptions.VectorStoreError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">VectorStoreError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when vector store operations fail.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="VectorStoreError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.VectorStoreError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">operation</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize vector store error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            operation: The vector store operation that failed</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">details</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;details&#39;</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">operation</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;operation&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">operation</span>

        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;VECTOR_STORE_ERROR&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="n">details</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="ConfigurationError">
<a class="viewcode-back" href="../modules.html#exceptions.ConfigurationError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ConfigurationError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when configuration is invalid or missing.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="ConfigurationError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.ConfigurationError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">config_key</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize configuration error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            config_key: The configuration key that caused the error</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">details</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;details&#39;</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">config_key</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;config_key&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">config_key</span>

        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;CONFIGURATION_ERROR&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="n">details</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="ResourceNotFoundError">
<a class="viewcode-back" href="../modules.html#exceptions.ResourceNotFoundError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ResourceNotFoundError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when a requested resource is not found.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="ResourceNotFoundError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.ResourceNotFoundError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">resource_type</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="n">resource_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize resource not found error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            resource_type: Type of resource that was not found</span>
<span class="sd">            resource_id: ID of the resource that was not found</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">details</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;details&#39;</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">resource_type</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;resource_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">resource_type</span>
        <span class="k">if</span> <span class="n">resource_id</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;resource_id&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">resource_id</span>

        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;RESOURCE_NOT_FOUND&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="n">details</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="ServiceUnavailableError">
<a class="viewcode-back" href="../modules.html#exceptions.ServiceUnavailableError">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ServiceUnavailableError</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when a service is temporarily unavailable.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="ServiceUnavailableError.__init__">
<a class="viewcode-back" href="../modules.html#exceptions.ServiceUnavailableError.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">service_name</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize service unavailable error.</span>

<span class="sd">        Args:</span>
<span class="sd">            message: Error message</span>
<span class="sd">            service_name: Name of the unavailable service</span>
<span class="sd">            **kwargs: Additional arguments for base class</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">details</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;details&#39;</span><span class="p">,</span> <span class="p">{})</span>
        <span class="k">if</span> <span class="n">service_name</span><span class="p">:</span>
            <span class="n">details</span><span class="p">[</span><span class="s1">&#39;service_name&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">service_name</span>

        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="n">message</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;SERVICE_UNAVAILABLE&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="n">details</span><span class="p">,</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">503</span><span class="p">,</span>
            <span class="o">**</span><span class="n">kwargs</span>
        <span class="p">)</span></div>
</div>



<div class="viewcode-block" id="handle_exception">
<a class="viewcode-back" href="../modules.html#exceptions.handle_exception">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">handle_exception</span><span class="p">(</span><span class="n">exc</span><span class="p">:</span> <span class="ne">Exception</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SymbolicAIException</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Convert generic exceptions to SymbolicAI exceptions.</span>

<span class="sd">    Args:</span>
<span class="sd">        exc: The exception to convert</span>

<span class="sd">    Returns:</span>
<span class="sd">        SymbolicAIException instance</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">exc</span><span class="p">,</span> <span class="n">SymbolicAIException</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">exc</span>

    <span class="c1"># Map common exceptions to specific types</span>
    <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">exc</span><span class="p">,</span> <span class="ne">ValueError</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">ValidationError</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">exc</span><span class="p">))</span>
    <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">exc</span><span class="p">,</span> <span class="ne">FileNotFoundError</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">ResourceNotFoundError</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">exc</span><span class="p">),</span> <span class="n">resource_type</span><span class="o">=</span><span class="s2">&quot;file&quot;</span><span class="p">)</span>
    <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">exc</span><span class="p">,</span> <span class="ne">ConnectionError</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">ServiceUnavailableError</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">exc</span><span class="p">))</span>
    <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">exc</span><span class="p">,</span> <span class="ne">TimeoutError</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">ServiceUnavailableError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Operation timed out: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">exc</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="c1"># Generic fallback</span>
        <span class="k">return</span> <span class="n">SymbolicAIException</span><span class="p">(</span>
            <span class="n">message</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Unexpected error: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">exc</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="n">error_code</span><span class="o">=</span><span class="s2">&quot;INTERNAL_ERROR&quot;</span><span class="p">,</span>
            <span class="n">details</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;exception_type&quot;</span><span class="p">:</span> <span class="nb">type</span><span class="p">(</span><span class="n">exc</span><span class="p">)</span><span class="o">.</span><span class="vm">__name__</span><span class="p">}</span>
        <span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>