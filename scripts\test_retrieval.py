"""
Test script for the retrieval module.
"""

import sys
import os

# Prevent OpenMP runtime conflicts
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# Add the project root directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.retrieval import Retriever

def test_retrieval():
    # Initialize the retriever
    retriever = Retriever(vector_db="faiss", use_gpu=True)
    
    # Print system information
    print("System Configuration:")
    print("--------------------")
    for key, value in retriever.get_system_info().items():
        print(f"{key}: {value}")
    print("--------------------\n")
    
    # Add sample documents
    documents = [
        "Neural-Symbolic AI integrates logic-based reasoning with deep learning models.",
        "FAISS is a library for efficient similarity search developed by Facebook AI.",
        "Retrieval-Augmented Generation improves language model outputs by incorporating external knowledge."
    ]
    
    print("Adding sample documents...")
    documents_with_ids = [
        {'id': str(i), 'text': text} for i, text in enumerate(documents)
    ]
    retriever.add_documents(documents_with_ids)
    
    # Test query
    test_query = "How does Neural-Symbolic AI work?"
    print(f"\nQuery: {test_query}")
    
    results = retriever.search(test_query, k=2)
    print("\nRetrieved Information:")
    print("---------------------")
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['text']}")
        print(f"   Relevance: {result['score']:.4f}\n")

if __name__ == '__main__':
    test_retrieval()
