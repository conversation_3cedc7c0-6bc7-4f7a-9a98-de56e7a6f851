"""Comprehensive tests for the symbolic reasoning module.

This test suite provides extensive coverage of the SymbolicReasoner class
including edge cases, error conditions, performance tests, and integration tests.
"""

import asyncio
import pytest
import unittest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
import time

# Import the module under test
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from symbolic_reasoning import SymbolicReasoner
from exceptions import ReasoningError, ConfigurationError


class TestSymbolicReasonerComprehensive(unittest.TestCase):
    """Comprehensive test suite for SymbolicReasoner."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.reasoner = SymbolicReasoner(
            engine="local",
            model="test",
            use_gpu=False
        )
    
    def test_init_default_parameters(self):
        """Test reasoner initialization with default parameters."""
        reasoner = SymbolicReasoner()
        
        self.assertEqual(reasoner.engine, "local")
        self.assertEqual(reasoner.model, "llama")
        self.assertIsInstance(reasoner.use_gpu, bool)
    
    def test_init_custom_parameters(self):
        """Test reasoner initialization with custom parameters."""
        reasoner = SymbolicReasoner(
            engine="ollama",
            model="custom",
            use_gpu=False
        )
        
        self.assertEqual(reasoner.engine, "ollama")
        self.assertEqual(reasoner.model, "custom")
        self.assertEqual(reasoner.use_gpu, False)
    
    def test_init_invalid_engine(self):
        """Test initialization with invalid engine raises error."""
        with self.assertRaises(ConfigurationError):
            SymbolicReasoner(engine="invalid_engine")
    
    @pytest.mark.asyncio
    async def test_process_query_valid_input(self):
        """Test processing valid query input."""
        query = "What is symbolic reasoning?"
        
        result = await self.reasoner.process_query(query)
        
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)
        self.assertIn("symbolic", result.lower())
    
    @pytest.mark.asyncio
    async def test_process_query_empty_input(self):
        """Test processing empty query raises ValidationError."""
        with self.assertRaises(ReasoningError):
            await self.reasoner.process_query("")
    
    @pytest.mark.asyncio
    async def test_process_query_whitespace_only(self):
        """Test processing whitespace-only query raises ValidationError."""
        with self.assertRaises(ReasoningError):
            await self.reasoner.process_query("   \n\t   ")
    
    @pytest.mark.asyncio
    async def test_process_query_with_context(self):
        """Test processing query with context."""
        query = "What is AI?"
        context = "In the field of computer science"
        
        result = await self.reasoner.process_query(query, context)
        
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)
    
    @pytest.mark.asyncio
    async def test_process_query_logical_reasoning(self):
        """Test logical reasoning capabilities."""
        query = "If A implies B and B implies C, what can we conclude?"
        
        result = await self.reasoner.process_query(query)
        
        self.assertIsInstance(result, str)
        self.assertIn("transitive", result.lower())
        self.assertIn("implies", result.lower())
    
    @pytest.mark.asyncio
    async def test_process_query_neural_symbolic(self):
        """Test neural-symbolic reasoning query."""
        query = "How do neural networks and symbolic reasoning differ?"
        
        result = await self.reasoner.process_query(query)
        
        self.assertIsInstance(result, str)
        self.assertIn("neural", result.lower())
        self.assertIn("symbolic", result.lower())
    
    def test_build_messages_no_context(self):
        """Test building messages without context."""
        query = "Test query"
        
        messages = self.reasoner._build_messages(query)
        
        self.assertEqual(len(messages), 2)  # system + user
        self.assertEqual(messages[0]["role"], "system")
        self.assertEqual(messages[1]["role"], "user")
        self.assertEqual(messages[1]["content"], query)
    
    def test_build_messages_with_context(self):
        """Test building messages with context."""
        query = "Test query"
        context = "Test context"
        
        messages = self.reasoner._build_messages(query, context)
        
        self.assertEqual(len(messages), 3)  # system + context + user
        self.assertEqual(messages[0]["role"], "system")
        self.assertEqual(messages[1]["role"], "system")
        self.assertEqual(messages[2]["role"], "user")
        self.assertIn(context, messages[1]["content"])
    
    def test_process_basic_query_sync(self):
        """Test synchronous basic query processing."""
        query = "What is reasoning?"
        
        result = self.reasoner._process_basic_query(query)
        
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)
    
    @pytest.mark.asyncio
    async def test_process_basic_query_async(self):
        """Test asynchronous basic query processing."""
        query = "What is async reasoning?"
        
        result = await self.reasoner._process_basic_query_async(query)
        
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)
        self.assertIn("async", result.lower())
    
    def test_get_system_info(self):
        """Test getting system information."""
        info = self.reasoner.get_system_info()
        
        self.assertIsInstance(info, dict)
        self.assertIn("engine", info)
        self.assertIn("model", info)
        self.assertIn("use_gpu", info)
        self.assertEqual(info["engine"], "local")
        self.assertEqual(info["model"], "test")
    
    @pytest.mark.asyncio
    async def test_ollama_engine_fallback(self):
        """Test Ollama engine with fallback to basic processing."""
        reasoner = SymbolicReasoner(engine="ollama", use_gpu=False)
        
        # Mock Ollama client to raise an exception
        with patch.object(reasoner, '_process_ollama_query', side_effect=Exception("Ollama error")):
            result = await reasoner.process_query("Test query")
            
            self.assertIsInstance(result, str)
            self.assertGreater(len(result), 0)
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_processing_performance(self):
        """Test query processing performance."""
        query = "Performance test query"
        
        start_time = time.time()
        await self.reasoner.process_query(query)
        end_time = time.time()
        
        processing_time = end_time - start_time
        self.assertLess(processing_time, 5.0)  # Should complete within 5 seconds
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_multiple_concurrent_queries(self):
        """Integration test for concurrent query processing."""
        queries = [
            "What is artificial intelligence?",
            "Explain machine learning",
            "How does neural symbolic AI work?",
            "What is logical reasoning?",
            "Define symbolic computation"
        ]
        
        # Process queries concurrently
        tasks = [self.reasoner.process_query(query) for query in queries]
        results = await asyncio.gather(*tasks)
        
        # Verify all results
        self.assertEqual(len(results), len(queries))
        for result in results:
            self.assertIsInstance(result, str)
            self.assertGreater(len(result), 10)
    
    @pytest.mark.asyncio
    async def test_error_handling_with_logging(self):
        """Test error handling includes proper logging."""
        with patch('symbolic_reasoning.logger') as mock_logger:
            with self.assertRaises(ReasoningError):
                await self.reasoner.process_query("")
            
            # Verify error was logged
            mock_logger.debug.assert_called()
    
    def test_configuration_validation(self):
        """Test configuration validation during initialization."""
        # Test valid configurations
        valid_configs = [
            {"engine": "local", "model": "llama"},
            {"engine": "ollama", "model": "mistral"},
            {"engine": "openai", "model": "gpt-4"},
        ]
        
        for config in valid_configs:
            try:
                reasoner = SymbolicReasoner(**config, use_gpu=False)
                self.assertIsNotNone(reasoner)
            except Exception as e:
                self.fail(f"Valid configuration {config} raised {e}")
    
    @pytest.mark.asyncio
    async def test_context_handling_edge_cases(self):
        """Test context handling with edge cases."""
        query = "Test query"
        
        # Test with empty context
        result1 = await self.reasoner.process_query(query, context="")
        self.assertIsInstance(result1, str)
        
        # Test with very long context
        long_context = "Context " * 1000
        result2 = await self.reasoner.process_query(query, context=long_context)
        self.assertIsInstance(result2, str)
        
        # Test with special characters in context
        special_context = "Context with special chars: !@#$%^&*()[]{}|;:,.<>?"
        result3 = await self.reasoner.process_query(query, context=special_context)
        self.assertIsInstance(result3, str)


if __name__ == '__main__':
    # Run tests with pytest for async support
    pytest.main([__file__, "-v", "--tb=short"])
