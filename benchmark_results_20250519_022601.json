{"first_query_times": [2.03151273727417, 2.028944492340088, 2.0416012605031333, 2.0330216884613037, 2.036523183186849], "cached_query_times": [2.032313505808512, 2.037937879562378, 2.0293856461842856, 2.0295862356821694, 2.0244694550832114], "queries": ["What is Neural-Symbolic AI?", "Explain the difference between FAISS and ChromaDB", "How does GPU acceleration improve vector search?", "What are the benefits of symbolic reasoning?", "How to implement RAG in a production system?"], "timestamp": "2025-05-19T02:24:36.386883", "raw_data": [{"query": "What is Neural-Symbolic AI?", "iterations": [{"iteration": 1, "first_query": {"time": 2.044389009475708, "cached": false}, "cached_query": {"time": 2.045318365097046, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.0219814777374268, "cached": true}, "cached_query": {"time": 2.0290699005126953, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.028167724609375, "cached": true}, "cached_query": {"time": 2.022552251815796, "cached": true}}]}, {"query": "Explain the difference between FAISS and ChromaDB", "iterations": [{"iteration": 1, "first_query": {"time": 2.0220532417297363, "cached": false}, "cached_query": {"time": 2.033740282058716, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.0306396484375, "cached": true}, "cached_query": {"time": 2.045424461364746, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.0341405868530273, "cached": true}, "cached_query": {"time": 2.034648895263672, "cached": true}}]}, {"query": "How does GPU acceleration improve vector search?", "iterations": [{"iteration": 1, "first_query": {"time": 2.0352189540863037, "cached": false}, "cached_query": {"time": 2.015791177749634, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.0365450382232666, "cached": true}, "cached_query": {"time": 2.0262913703918457, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.053039789199829, "cached": true}, "cached_query": {"time": 2.046074390411377, "cached": true}}]}, {"query": "What are the benefits of symbolic reasoning?", "iterations": [{"iteration": 1, "first_query": {"time": 2.037278652191162, "cached": false}, "cached_query": {"time": 2.0434396266937256, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.038851499557495, "cached": true}, "cached_query": {"time": 2.026597738265991, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.022934913635254, "cached": true}, "cached_query": {"time": 2.018721342086792, "cached": true}}]}, {"query": "How to implement RAG in a production system?", "iterations": [{"iteration": 1, "first_query": {"time": 2.0257134437561035, "cached": false}, "cached_query": {"time": 2.0366463661193848, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.0514018535614014, "cached": true}, "cached_query": {"time": 2.0120086669921875, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.032454252243042, "cached": true}, "cached_query": {"time": 2.0247533321380615, "cached": true}}]}]}