import sys
import os
import site

print("--- Python Environment Diagnostics ---")
print(f"Python Executable: {sys.executable}")
print("sys.path:")
for p in sys.path:
    print(f"  {p}")
print("Site packages:")
site_packages_paths = site.getsitepackages()
for sp_path in site_packages_paths:
    print(f"  {sp_path}")
user_site = site.getusersitepackages()
print(f"User site packages: {user_site}")
print("-------------------------------------")

try:
    print("Attempting to import faiss...")
    import faiss
    print(f"Successfully imported faiss.")
    
    faiss_module_location = "N/A"
    if hasattr(faiss, '__file__') and faiss.__file__:
        faiss_module_location = faiss.__file__
    elif hasattr(faiss, '__path__') and faiss.__path__:
        faiss_module_location = faiss.__path__[0]
    print(f"faiss module location: {faiss_module_location}")

    if faiss_module_location != "N/A" and os.path.isdir(faiss_module_location):
        faiss_dir_path = faiss_module_location
    elif faiss_module_location != "N/A":
        faiss_dir_path = os.path.dirname(faiss_module_location)
    else:
        faiss_dir_path = None
        print("Could not determine faiss module directory path.")

    if faiss_dir_path and os.path.isdir(faiss_dir_path):
        print(f"Contents of faiss directory ({faiss_dir_path}):")
        try:
            for item in sorted(os.listdir(faiss_dir_path)):
                print(f"  - {item}")
            # Specifically look for swigfaiss_avx2 files
            expected_pyd = os.path.join(faiss_dir_path, 'swigfaiss_avx2.pyd')
            expected_py = os.path.join(faiss_dir_path, 'swigfaiss_avx2.py') # Less likely for compiled part
            print(f"Checking for {expected_pyd}: Exists = {os.path.exists(expected_pyd)}")
            print(f"Checking for {expected_py}: Exists = {os.path.exists(expected_py)}")
        except Exception as e:
            print(f"    Could not list directory contents: {e}")
    else:
        print(f"FAISS directory path not found or is not a directory: {faiss_dir_path}")

    print("Attempting to import faiss.swigfaiss_avx2...")
    import faiss.swigfaiss_avx2 # This is the critical import
    print("Successfully imported faiss.swigfaiss_avx2")
    
    print("Attempting to import faiss.contrib.torch_utils...") # another common submodule
    import faiss.contrib.torch_utils
    print("Successfully imported faiss.contrib.torch_utils")

    if hasattr(faiss, 'StandardGpuResources'):
        print("faiss.StandardGpuResources attribute found.")
        try:
            print("Attempting to initialize faiss.StandardGpuResources()...")
            res = faiss.StandardGpuResources()
            print("FAISS GPU resources initialized successfully (faiss.StandardGpuResources).")
        except Exception as e:
            print(f"Failed to initialize FAISS GPU resources (faiss.StandardGpuResources): {e}")
    else:
        print("faiss.StandardGpuResources attribute NOT found.")

except ImportError as e:
    print(f"ImportError: {e}")
    if "swigfaiss_avx2" in str(e) and sys.platform == "win32":
        print("This error on Windows ('No module named faiss.swigfaiss_avx2') can be due to:")
        print("1. Missing Microsoft Visual C++ Redistributables (ensure latest x64 for VS 2015-2022 is installed).")
        print("2. CUDA Toolkit path issues or version mismatch with the FAISS wheel.")
        print("3. Corrupted FAISS installation.")
        print("4. Conflicting DLLs in PATH.")
except FileNotFoundError as e:
    print(f"FileNotFoundError: {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
    import traceback
    traceback.print_exc()
