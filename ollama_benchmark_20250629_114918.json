{"timestamp": "2025-06-29T11:45:19.944095", "model": "gemma3n:e2b", "backend": "ollama", "tests": {"direct_ollama": {"status": "success", "total_duration": 122.49647378921509, "results": [{"query": "What is 2 + 2?", "duration": 2.9975616931915283, "word_count": 5, "response_length": 10, "status": "success"}, {"query": "Explain AI in one sentence.", "duration": 0.9799392223358154, "word_count": 21, "response_length": 167, "status": "success"}, {"query": "What is symbolic reasoning?", "duration": 33.20411944389343, "word_count": 740, "response_length": 5419, "status": "success"}, {"query": "How does machine learning work?", "duration": 57.77546954154968, "word_count": 1343, "response_length": 9613, "status": "success"}, {"query": "What is the difference between AI and ML?", "duration": 27.53938388824463, "word_count": 678, "response_length": 4689, "status": "success"}], "statistics": {"total_queries": 5, "successful": 5, "success_rate": 100.0, "avg_duration": 24.499294757843018, "min_duration": 0.9799392223358154, "max_duration": 57.77546954154968, "avg_words": 557.4, "total_words": 2787, "words_per_second": 22.751675324105328}}, "reasoning": {"status": "success", "results": [{"query": "If A implies B and B implies C, what can we conclude?", "duration": 9.753708124160767, "reasoning_keywords": 5, "response_length": 1240, "status": "success"}, {"query": "What is the logical relationship between premises and conclusions?", "duration": 25.866450309753418, "reasoning_keywords": 4, "response_length": 4152, "status": "success"}, {"query": "Solve: All cats are animals. <PERSON><PERSON><PERSON> is a cat. What follows?", "duration": 5.780949592590332, "reasoning_keywords": 4, "response_length": 891, "status": "success"}, {"query": "Compare deductive and inductive reasoning.", "duration": 24.28700876235962, "reasoning_keywords": 5, "response_length": 4456, "status": "success"}, {"query": "What is a syllogism? Give an example.", "duration": 11.827048063278198, "reasoning_keywords": 5, "response_length": 1908, "status": "success"}], "statistics": {"avg_duration": 15.503032970428468, "avg_reasoning_keywords": 4.6, "success_rate": 100.0}}, "concurrent": {"status": "success", "statistics": {"total_queries": 5, "successful": 5, "success_rate": 100.0, "total_duration": 38.886314153671265, "avg_time_per_query": 7.777262830734253, "total_words": 2679, "concurrent_throughput": 68.89313266906977}}}}