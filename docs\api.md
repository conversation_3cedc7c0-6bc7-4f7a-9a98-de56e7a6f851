# API Documentation

## Overview

The Neural Symbolic Language Model exposes a RESTful API for interacting with the system. The API is built using FastAPI and provides endpoints for chat interaction and performance monitoring.

## Base URL

By default, the API is available at: `http://localhost:8080`

## Authentication

Currently, the API does not require authentication. For production deployments, it is recommended to implement appropriate authentication mechanisms.

## Endpoints

### POST /chat

Process a chat request and get a response from the system.

#### Request

```http
POST /chat
Content-Type: application/json

{
    "text": "Your query here"
}
```

##### Parameters

| Name | Type | Required | Description |
|------|------|----------|-------------|
| text | string | Yes | The query text to process |

#### Response

```json
{
    "response": "The response from the system",
    "cached": false
}
```

##### Fields

| Name | Type | Description |
|------|------|-------------|
| response | string | The generated response |
| cached | boolean | Whether the response was retrieved from cache |

#### Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 422 | Validation Error (invalid request format) |
| 500 | Internal Server Error |

### GET /performance

Get performance statistics and system information.

#### Request

```http
GET /performance
```

#### Response

```json
{
    "cache": {
        "size": 42,
        "max_size": 1000
    },
    "system_info": {
        "gpu_available": true,
        "gpu_name": "NVIDIA GeForce RTX 3080",
        "reasoner": {
            "engine": "local",
            "model": "llama",
            "gpu_enabled": true,
            "gpu_available": true,
            "gpu_name": "NVIDIA GeForce RTX 3080"
        },
        "retriever": {
            "vector_db": "faiss",
            "gpu_enabled": true,
            "gpu_available": true,
            "gpu_name": "NVIDIA GeForce RTX 3080",
            "index_size": 1000
        }
    }
}
```

##### Fields

| Name | Type | Description |
|------|------|-------------|
| cache.size | integer | Current number of items in the response cache |
| cache.max_size | integer | Maximum cache capacity |
| system_info.gpu_available | boolean | Whether a GPU is available |
| system_info.gpu_name | string | Name of the available GPU |
| system_info.reasoner | object | Information about the symbolic reasoning engine |
| system_info.retriever | object | Information about the retrieval system |

#### Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 500 | Internal Server Error |

## Error Handling

The API uses standard HTTP status codes for error reporting. Error responses include a detail message explaining the error:

```json
{
    "detail": "Error message here"
}
```

## Rate Limiting

Currently, there is no rate limiting implemented. For production deployments, consider adding rate limiting based on your requirements.

## Caching

The API implements an LRU (Least Recently Used) cache for chat responses with the following characteristics:

- Maximum cache size: 1000 items
- Cache eviction: Timestamp-based LRU
- Cache cleaning: Removes 20% of oldest entries when full

## Examples

### Python

```python
import requests

# Chat request
response = requests.post(
    "http://localhost:8080/chat",
    json={"text": "What is Neural-Symbolic AI?"}
)
print(response.json())

# Performance stats
stats = requests.get("http://localhost:8080/performance")
print(stats.json())
```

### cURL

```bash
# Chat request
curl -X POST "http://localhost:8080/chat" \
     -H "Content-Type: application/json" \
     -d '{"text": "What is Neural-Symbolic AI?"}'

# Performance stats
curl -X GET "http://localhost:8080/performance"
```
