# API Documentation

## Overview

The Neural Symbolic Language Model exposes a RESTful API for interacting with the system. The API is built using FastAPI and provides OpenAI-compatible endpoints for chat interaction, document management, and performance monitoring.

## Base URL

By default, the API is available at: `http://localhost:8080`

## Authentication

The API uses Bearer token authentication for security. Include your API key in the Authorization header:

```http
Authorization: Bearer your-api-key-here
```

### API Key Configuration

Set your API keys in the environment:

```bash
SECURITY_API_KEYS_JSON='{"user1": {"key": "your-api-key", "permissions": ["read", "write"], "rate_limit": 1000}}'
```

## Endpoints

### POST /v1/chat/completions

OpenAI-compatible chat completions endpoint for processing queries with the Neural Symbolic Language Model.

#### Request

```http
POST /v1/chat/completions
Content-Type: application/json
Authorization: Bearer your-api-key

{
    "model": "gemma3n:e2b",
    "messages": [
        {"role": "user", "content": "What is symbolic reasoning?"}
    ],
    "temperature": 0.7,
    "max_tokens": 1000
}
```

##### Parameters

| Name | Type | Required | Description |
|------|------|----------|-------------|
| model | string | Yes | Model to use (e.g., "gemma3n:e2b") |
| messages | array | Yes | Array of message objects |
| temperature | number | No | Sampling temperature (0.0-2.0) |
| max_tokens | number | No | Maximum tokens in response |
| stream | boolean | No | Enable streaming responses |

##### Message Object

| Name | Type | Required | Description |
|------|------|----------|-------------|
| role | string | Yes | "system", "user", or "assistant" |
| content | string | Yes | The message content |

#### Response

```json
{
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gemma3n:e2b",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Symbolic reasoning is a method of AI that uses symbols and logical rules..."
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 12,
        "completion_tokens": 150,
        "total_tokens": 162
    }
}
```

### POST /v1/chat/completions (Streaming)

Enable streaming responses by setting `"stream": true`:

#### Request

```http
POST /v1/chat/completions
Content-Type: application/json
Authorization: Bearer your-api-key

{
    "model": "gemma3n:e2b",
    "messages": [{"role": "user", "content": "Explain AI"}],
    "stream": true
}
```

#### Response

```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gemma3n:e2b","choices":[{"index":0,"delta":{"content":"Artificial"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gemma3n:e2b","choices":[{"index":0,"delta":{"content":" intelligence"},"finish_reason":null}]}

data: [DONE]
```

### GET /health

Check the health status of the API and connected services.

#### Response

```json
{
    "status": "healthy",
    "timestamp": "2025-06-29T12:00:00Z",
    "services": {
        "ollama": "connected",
        "database": "healthy",
        "cache": "operational"
    },
    "version": "0.1.0"
}
```
| response | string | The generated response |
| cached | boolean | Whether the response was retrieved from cache |

#### Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 422 | Validation Error (invalid request format) |
| 500 | Internal Server Error |

### GET /performance

Get performance statistics and system information.

#### Request

```http
GET /performance
```

#### Response

```json
{
    "cache": {
        "size": 42,
        "max_size": 1000
    },
    "system_info": {
        "gpu_available": true,
        "gpu_name": "NVIDIA GeForce RTX 3080",
        "reasoner": {
            "engine": "local",
            "model": "llama",
            "gpu_enabled": true,
            "gpu_available": true,
            "gpu_name": "NVIDIA GeForce RTX 3080"
        },
        "retriever": {
            "vector_db": "faiss",
            "gpu_enabled": true,
            "gpu_available": true,
            "gpu_name": "NVIDIA GeForce RTX 3080",
            "index_size": 1000
        }
    }
}
```

### POST /documents/add

Add documents to the retrieval system for enhanced context.

#### Request

```http
POST /documents/add
Content-Type: application/json
Authorization: Bearer your-api-key

{
    "content": "This is a document about artificial intelligence...",
    "metadata": {
        "title": "AI Overview",
        "category": "technology",
        "source": "internal"
    }
}
```

#### Response

```json
{
    "document_id": "doc_123",
    "status": "added",
    "message": "Document successfully added to retrieval system"
}
```

### GET /documents/count

Get the total number of documents in the retrieval system.

#### Response

```json
{
    "total_documents": 1250,
    "indexed_documents": 1248,
    "pending_documents": 2
}
```

## Error Responses

The API uses standard HTTP status codes and returns error information in JSON format:

```json
{
    "error": {
        "type": "invalid_request_error",
        "code": "missing_required_field",
        "message": "Missing required field: messages",
        "param": "messages"
    }
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| invalid_request_error | 400 | The request is malformed |
| authentication_error | 401 | Invalid or missing API key |
| permission_error | 403 | Insufficient permissions |
| not_found_error | 404 | Resource not found |
| rate_limit_error | 429 | Rate limit exceeded |
| server_error | 500 | Internal server error |
| model_error | 502 | Model/Ollama unavailable |

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Default Limit**: 100 requests per minute per API key
- **Configurable**: Set via `SECURITY_RATE_LIMIT_REQUESTS` environment variable
- **Headers**: Rate limit information is included in response headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when the rate limit resets

## SDKs and Examples

### Python Example (OpenAI Compatible)

```python
import openai

# Configure for local API
openai.api_base = "http://localhost:8080/v1"
openai.api_key = "your-api-key"

# Chat completion
response = openai.ChatCompletion.create(
    model="gemma3n:e2b",
    messages=[
        {"role": "user", "content": "Explain symbolic reasoning"}
    ]
)

print(response.choices[0].message.content)
```

### Python Example (Direct API)

```python
import requests

# Chat completion
response = requests.post(
    "http://localhost:8080/v1/chat/completions",
    headers={"Authorization": "Bearer your-api-key"},
    json={
        "model": "gemma3n:e2b",
        "messages": [
            {"role": "user", "content": "What is machine learning?"}
        ]
    }
)

print(response.json())
```

### cURL Examples

```bash
# Chat completion
curl -X POST "http://localhost:8080/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-api-key" \
     -d '{
       "model": "gemma3n:e2b",
       "messages": [{"role": "user", "content": "Hello!"}]
     }'

# Add document
curl -X POST "http://localhost:8080/documents/add" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-api-key" \
     -d '{
       "content": "AI is transforming technology...",
       "metadata": {"title": "AI Overview"}
     }'

# Get performance metrics
curl -H "Authorization: Bearer your-api-key" \
     "http://localhost:8080/performance"

# Health check
curl "http://localhost:8080/health"
```
```

##### Fields

| Name | Type | Description |
|------|------|-------------|
| cache.size | integer | Current number of items in the response cache |
| cache.max_size | integer | Maximum cache capacity |
| system_info.gpu_available | boolean | Whether a GPU is available |
| system_info.gpu_name | string | Name of the available GPU |
| system_info.reasoner | object | Information about the symbolic reasoning engine |
| system_info.retriever | object | Information about the retrieval system |

#### Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 500 | Internal Server Error |

## Error Handling

The API uses standard HTTP status codes for error reporting. Error responses include a detail message explaining the error:

```json
{
    "detail": "Error message here"
}
```

## Rate Limiting

Currently, there is no rate limiting implemented. For production deployments, consider adding rate limiting based on your requirements.

## Caching

The API implements an LRU (Least Recently Used) cache for chat responses with the following characteristics:

- Maximum cache size: 1000 items
- Cache eviction: Timestamp-based LRU
- Cache cleaning: Removes 20% of oldest entries when full

## Examples

### Python

```python
import requests

# Chat request
response = requests.post(
    "http://localhost:8080/chat",
    json={"text": "What is Neural-Symbolic AI?"}
)
print(response.json())

# Performance stats
stats = requests.get("http://localhost:8080/performance")
print(stats.json())
```

### cURL

```bash
# Chat request
curl -X POST "http://localhost:8080/chat" \
     -H "Content-Type: application/json" \
     -d '{"text": "What is Neural-Symbolic AI?"}'

# Performance stats
curl -X GET "http://localhost:8080/performance"
```
