2025-05-21 04:36:33,397 - root - INFO - Logging system initialized
2025-05-21 04:36:33,432 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 04:36:33,502 - monitoring - INFO - Performance monitoring initialized
2025-05-21 04:36:33,568 - __mp_main__ - INFO - Initializing components...
2025-05-21 04:36:33,609 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 04:36:33,646 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 04:36:33,691 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 04:36:33,726 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 04:36:33,767 - __mp_main__ - INFO - Components initialized successfully
2025-05-21 04:36:33,823 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 04:36:33,903 - __mp_main__ - WARNING - No documents found in data directory
2025-05-21 04:36:34,239 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-21 04:36:34,459 - root - INFO - Logging system initialized
2025-05-21 04:36:34,472 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 04:36:34,484 - monitoring - INFO - Performance monitoring initialized
2025-05-21 04:36:34,510 - main - INFO - Initializing components...
2025-05-21 04:36:34,517 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 04:36:34,523 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 04:36:34,530 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 04:36:34,535 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 04:36:34,542 - main - INFO - Components initialized successfully
2025-05-21 04:36:34,547 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 04:36:34,566 - main - WARNING - No documents found in data directory
2025-05-21 04:38:34,522 - main - INFO - Application shutting down
2025-05-21 04:39:34,508 - monitoring - INFO - Performance monitoring shutdown
2025-05-21 04:39:41,039 - root - INFO - Logging system initialized
2025-05-21 04:39:41,081 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 04:39:41,106 - monitoring - INFO - Performance monitoring initialized
2025-05-21 04:39:41,115 - __mp_main__ - INFO - Initializing components...
2025-05-21 04:39:41,154 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 04:39:41,162 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 04:39:41,168 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 04:39:41,177 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 04:39:41,185 - __mp_main__ - INFO - Components initialized successfully
2025-05-21 04:39:41,221 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 04:39:41,511 - __mp_main__ - WARNING - No documents found in data directory
2025-05-21 04:39:41,856 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-21 04:39:42,288 - root - INFO - Logging system initialized
2025-05-21 04:39:42,311 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 04:39:42,323 - monitoring - INFO - Performance monitoring initialized
2025-05-21 04:39:42,354 - main - INFO - Initializing components...
2025-05-21 04:39:42,356 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 04:39:42,367 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 04:39:42,373 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 04:39:42,382 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 04:39:42,382 - main - INFO - Components initialized successfully
2025-05-21 04:39:42,396 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 04:39:42,430 - main - WARNING - No documents found in data directory
2025-05-21 04:50:56,026 - main - INFO - Application shutting down
2025-05-21 04:51:42,483 - monitoring - INFO - Performance monitoring shutdown
2025-05-21 04:51:47,056 - root - INFO - Logging system initialized
2025-05-21 04:51:47,073 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 04:51:47,090 - monitoring - INFO - Performance monitoring initialized
2025-05-21 04:51:47,130 - __mp_main__ - INFO - Initializing components...
2025-05-21 04:51:47,138 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 04:51:47,145 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 04:51:47,150 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 04:51:47,156 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 04:51:47,162 - __mp_main__ - INFO - Components initialized successfully
2025-05-21 04:51:47,169 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 04:51:47,201 - __mp_main__ - WARNING - No documents found in data directory
2025-05-21 04:51:47,479 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-21 04:51:47,703 - root - INFO - Logging system initialized
2025-05-21 04:51:47,711 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 04:51:47,721 - monitoring - INFO - Performance monitoring initialized
2025-05-21 04:51:47,746 - main - INFO - Initializing components...
2025-05-21 04:51:47,753 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 04:51:47,759 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 04:51:47,765 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 04:51:47,773 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 04:51:47,780 - main - INFO - Components initialized successfully
2025-05-21 04:51:47,787 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 04:51:47,812 - main - WARNING - No documents found in data directory
2025-05-21 21:11:59,342 - root - INFO - Logging system initialized
2025-05-21 21:11:59,395 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 21:11:59,454 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:11:59,516 - __main__ - INFO - Initializing components...
2025-05-21 21:11:59,557 - __main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 21:11:59,600 - __main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 21:11:59,645 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 21:11:59,684 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 21:11:59,731 - __main__ - INFO - Components initialized successfully
2025-05-21 21:11:59,769 - __main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 21:11:59,855 - __main__ - WARNING - No documents found in data directory
2025-05-21 21:12:02,521 - root - INFO - Logging system initialized
2025-05-21 21:12:02,531 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 21:12:02,556 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:12:02,565 - __mp_main__ - INFO - Initializing components...
2025-05-21 21:12:02,583 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 21:12:02,590 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 21:12:02,596 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 21:12:02,605 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 21:12:02,612 - __mp_main__ - INFO - Components initialized successfully
2025-05-21 21:12:02,620 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 21:12:02,626 - __mp_main__ - WARNING - No documents found in data directory
2025-05-21 21:12:02,781 - watchfiles.main - INFO - 1 change detected
2025-05-21 21:12:02,963 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-21 21:12:03,141 - root - INFO - Logging system initialized
2025-05-21 21:12:03,170 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-21 21:12:03,184 - monitoring - INFO - Performance monitoring initialized
2025-05-21 21:12:03,226 - main - INFO - Initializing components...
2025-05-21 21:12:03,235 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-21 21:12:03,246 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-21 21:12:03,252 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-21 21:12:03,259 - vector_store - INFO - TorchVectorStore using CPU
2025-05-21 21:12:03,268 - main - INFO - Components initialized successfully
2025-05-21 21:12:03,275 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-21 21:12:03,297 - main - WARNING - No documents found in data directory
2025-05-21 21:12:04,959 - watchfiles.main - INFO - 1 change detected
2025-05-21 21:17:38,582 - main - INFO - Received chat request for model: gpt-4o
2025-05-21 21:17:38,662 - main - INFO - Performing retrieval operation
2025-05-21 21:17:38,715 - main - INFO - Retrieved relevant context
2025-05-21 21:17:38,752 - main - INFO - Processing with symbolic reasoning
2025-05-21 21:17:38,793 - main - INFO - Symbolic reasoning completed
2025-05-21 21:17:38,832 - main - INFO - Successfully generated response
2025-05-21 21:17:38,868 - monitoring - INFO - Request chat-3ed0803c-00f5-4af2-9ec5-e00687391ebb completed in 0.29s
2025-05-21 21:50:49,883 - watchfiles.main - INFO - 1 change detected
2025-05-22 00:54:30,006 - watchfiles.main - INFO - 2 changes detected
2025-05-22 00:54:30,422 - watchfiles.main - INFO - 4 changes detected
2025-05-22 00:54:30,825 - watchfiles.main - INFO - 3 changes detected
2025-05-22 00:54:31,307 - watchfiles.main - INFO - 1 change detected
2025-05-22 00:54:31,713 - watchfiles.main - INFO - 1 change detected
2025-05-22 00:54:32,160 - watchfiles.main - INFO - 4 changes detected
2025-05-22 00:54:32,561 - watchfiles.main - INFO - 2 changes detected
2025-05-22 00:54:32,799 - main - INFO - Application shutting down
2025-05-22 00:55:05,852 - monitoring - INFO - Performance monitoring shutdown
2025-05-22 00:55:06,597 - watchfiles.main - INFO - 67 changes detected
2025-05-22 00:55:10,368 - watchfiles.main - INFO - 2 changes detected
2025-05-22 00:55:11,116 - watchfiles.main - INFO - 1 change detected
2025-05-22 00:55:11,513 - watchfiles.main - INFO - 1 change detected
2025-05-22 00:55:13,445 - watchfiles.main - INFO - 1 change detected
2025-05-22 04:55:25,645 - watchfiles.main - INFO - 2 changes detected
2025-05-22 05:06:56,389 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:07:19,479 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:07:19,884 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:07:21,920 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:09:57,113 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:10:24,282 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:10:24,685 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:10:26,762 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:12:07,642 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:12:29,106 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:12:29,620 - watchfiles.main - INFO - 1 change detected
2025-05-22 05:12:32,083 - watchfiles.main - INFO - 1 change detected
