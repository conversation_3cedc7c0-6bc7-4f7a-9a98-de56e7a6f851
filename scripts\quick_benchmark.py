"""
Quick FAISS CPU vs GPU Benchmark

This script performs a focused benchmark of FAISS on CPU vs GPU,
using realistic workloads to demonstrate the performance difference.
"""

import sys
import time
import numpy as np
import os
import psutil

# Try to import FAISS
try:
    import faiss
    FAISS_AVAILABLE = True
    GPU_AVAILABLE = hasattr(faiss, 'StandardGpuResources')
except ImportError:
    FAISS_AVAILABLE = False
    GPU_AVAILABLE = False
    print("⚠️ FAISS is not available. Please install FAISS to run this benchmark.")
    sys.exit(1)

# Import PyTorch for CUDA info
try:
    import torch
    TORCH_AVAILABLE = True
    CUDA_AVAILABLE = torch.cuda.is_available()
    if CUDA_AVAILABLE:
        CUDA_DEVICE = torch.cuda.get_device_name(0)
        CUDA_VERSION = torch.version.cuda
    else:
        CUDA_DEVICE = "N/A"
        CUDA_VERSION = "N/A"
except ImportError:
    TORCH_AVAILABLE = False
    CUDA_AVAILABLE = False
    CUDA_DEVICE = "N/A"
    CUDA_VERSION = "N/A"

# Print system info
def print_system_info():
    """Print system information."""
    print("\n" + "="*60)
    print("SYSTEM INFORMATION".center(60))
    print("="*60)
    
    print(f"Python version: {sys.version.split()[0]}")
    print(f"Python executable: {sys.executable}")
    print(f"FAISS version: {faiss.__version__}")
    print(f"FAISS-GPU available: {GPU_AVAILABLE}")
    
    if TORCH_AVAILABLE:
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {CUDA_AVAILABLE}")
        print(f"CUDA version: {CUDA_VERSION}")
        print(f"GPU device: {CUDA_DEVICE}")
    
    print(f"CPU count: {psutil.cpu_count(logical=False)} physical, {psutil.cpu_count()} logical")
    
    memory = psutil.virtual_memory()
    print(f"System memory: {memory.total / (1024**3):.2f} GB total, {memory.available / (1024**3):.2f} GB available")
    
    print("="*60)

def run_benchmark(dimensions, dataset_sizes):
    """Run the benchmark for each dimension and dataset size."""
    results = []
    
    # Initialize GPU resources if available
    gpu_res = None
    gpu_working = GPU_AVAILABLE  # Local copy of the flag
    
    if gpu_working:
        try:
            gpu_res = faiss.StandardGpuResources()
            print(f"✅ FAISS-GPU initialized successfully")
        except Exception as e:
            print(f"⚠️ FAISS-GPU initialization failed: {e}")
            gpu_working = False
    
    # Run benchmarks
    for dim in dimensions:
        for size in dataset_sizes:
            actual_size = size * 1000  # Convert to actual number of vectors
            
            print("\n" + "="*60)
            print(f"BENCHMARK: {actual_size} vectors, {dim} dimensions".center(60))
            print("="*60)
            
            # Create dataset
            print(f"Creating dataset...")
            np.random.seed(42)  # For reproducibility
            dataset = np.random.random((actual_size, dim)).astype('float32')
            query = np.random.random((1000, dim)).astype('float32')
            
            # CPU benchmark
            print("\n--- CPU Benchmark ---")
            
            # Measure index creation time
            cpu_index = faiss.IndexFlatL2(dim)
            
            start_time = time.time()
            cpu_index.add(dataset)
            cpu_build_time = time.time() - start_time
            print(f"CPU index build time: {cpu_build_time:.4f} seconds")
            
            # Measure search time
            start_time = time.time()
            cpu_D, cpu_I = cpu_index.search(query, 10)  # Find 10 nearest neighbors
            cpu_search_time = time.time() - start_time
            print(f"CPU search time (1000 queries): {cpu_search_time:.4f} seconds")
            
            # GPU benchmark
            gpu_build_time = None
            gpu_search_time = None
            
            if gpu_working:
                print("\n--- GPU Benchmark ---")
                
                # Create GPU index
                gpu_index = faiss.index_cpu_to_gpu(gpu_res, 0, faiss.IndexFlatL2(dim))
                
                # Measure index creation time
                start_time = time.time()
                gpu_index.add(dataset)
                gpu_build_time = time.time() - start_time
                print(f"GPU index build time: {gpu_build_time:.4f} seconds")
                
                # Measure search time
                start_time = time.time()
                gpu_D, gpu_I = gpu_index.search(query, 10)  # Find 10 nearest neighbors
                gpu_search_time = time.time() - start_time
                print(f"GPU search time (1000 queries): {gpu_search_time:.4f} seconds")
                
                # Calculate speedup
                if gpu_build_time > 0:
                    build_speedup = cpu_build_time / gpu_build_time
                    print(f"Build speedup: {build_speedup:.2f}x faster on GPU")
                
                if gpu_search_time > 0:
                    search_speedup = cpu_search_time / gpu_search_time
                    print(f"Search speedup: {search_speedup:.2f}x faster on GPU")
                
                # Verify results match
                results_match = np.allclose(cpu_D, gpu_D, rtol=1e-4, atol=1e-4)
                print(f"Results match: {results_match}")
            
            # Store results
            result = {
                "dimension": dim,
                "dataset_size": actual_size,
                "cpu_build_time": cpu_build_time,
                "cpu_search_time": cpu_search_time,
                "gpu_build_time": gpu_build_time,
                "gpu_search_time": gpu_search_time
            }
            
            results.append(result)
    
    return results

def print_summary(results):
    """Print a summary of the benchmark results."""
    print("\n" + "="*80)
    print("BENCHMARK SUMMARY".center(80))
    print("="*80)
    
    header = "| Dimension | Dataset Size | CPU Build | GPU Build | Speedup | CPU Search | GPU Search | Speedup |"
    separator = "|-----------|--------------|-----------|-----------|---------|------------|------------|---------|"
    
    print(header)
    print(separator)
    
    for result in results:
        dim = result["dimension"]
        size = result["dataset_size"] / 1000  # Convert to K
        cpu_build = result["cpu_build_time"]
        gpu_build = result["gpu_build_time"] if result["gpu_build_time"] else 0
        cpu_search = result["cpu_search_time"]
        gpu_search = result["gpu_search_time"] if result["gpu_search_time"] else 0
        
        build_speedup = cpu_build / gpu_build if gpu_build > 0 else 0
        search_speedup = cpu_search / gpu_search if gpu_search > 0 else 0
        
        row = f"| {dim:9d} | {size:12.1f}K | {cpu_build:9.4f}s | {gpu_build:9.4f}s | {build_speedup:7.2f}x | {cpu_search:10.4f}s | {gpu_search:10.4f}s | {search_speedup:7.2f}x |"
        print(row)
    
    print("="*80)
    
    # Calculate average speedups
    build_speedups = []
    search_speedups = []
    
    for result in results:
        if result["gpu_build_time"] and result["gpu_build_time"] > 0:
            build_speedups.append(result["cpu_build_time"] / result["gpu_build_time"])
        
        if result["gpu_search_time"] and result["gpu_search_time"] > 0:
            search_speedups.append(result["cpu_search_time"] / result["gpu_search_time"])
    
    if build_speedups:
        avg_build_speedup = sum(build_speedups) / len(build_speedups)
        print(f"Average index build speedup: {avg_build_speedup:.2f}x faster on GPU")
    
    if search_speedups:
        avg_search_speedup = sum(search_speedups) / len(search_speedups)
        print(f"Average search speedup: {avg_search_speedup:.2f}x faster on GPU")
    
    print("="*80)

def main():
    """Main function to run the benchmark."""
    print_system_info()
    
    # Define benchmark parameters
    dimensions = [64, 128, 256]
    dataset_sizes = [10, 50, 100]  # in thousands
    
    # Run benchmarks
    results = run_benchmark(dimensions, dataset_sizes)
    
    # Print summary
    print_summary(results)

if __name__ == "__main__":
    main()
