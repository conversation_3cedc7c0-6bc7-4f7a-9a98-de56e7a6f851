#!/usr/bin/env python3
"""Security scanning script for Neural Symbolic Language Model dependencies.

This script performs comprehensive security scanning including:
- Dependency vulnerability scanning with safety
- Code security analysis with bandit
- License compliance checking
- Outdated package detection
"""

import subprocess
import sys
import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse


class SecurityScanner:
    """Comprehensive security scanner for the project."""
    
    def __init__(self, project_root: Optional[Path] = None):
        """Initialize the security scanner.
        
        Args:
            project_root: Root directory of the project
        """
        self.project_root = project_root or Path(__file__).parent.parent
        self.results = {
            'dependency_vulnerabilities': [],
            'code_security_issues': [],
            'license_issues': [],
            'outdated_packages': [],
            'summary': {}
        }
    
    def run_full_scan(self) -> Dict[str, Any]:
        """Run a comprehensive security scan.
        
        Returns:
            Dictionary containing all scan results
        """
        print("🔍 Starting comprehensive security scan...")
        
        # Run individual scans
        self.scan_dependencies()
        self.scan_code_security()
        self.check_licenses()
        self.check_outdated_packages()
        
        # Generate summary
        self.generate_summary()
        
        return self.results
    
    def scan_dependencies(self):
        """Scan dependencies for known vulnerabilities using safety."""
        print("\n📦 Scanning dependencies for vulnerabilities...")
        
        try:
            # Run safety check
            result = subprocess.run(
                [sys.executable, '-m', 'safety', 'check', '--json'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                print("✅ No known vulnerabilities found in dependencies")
                self.results['dependency_vulnerabilities'] = []
            else:
                # Parse safety output
                try:
                    vulnerabilities = json.loads(result.stdout)
                    self.results['dependency_vulnerabilities'] = vulnerabilities
                    print(f"⚠️  Found {len(vulnerabilities)} dependency vulnerabilities")
                    
                    for vuln in vulnerabilities[:3]:  # Show first 3
                        print(f"   - {vuln.get('package', 'Unknown')}: {vuln.get('vulnerability', 'Unknown issue')}")
                    
                    if len(vulnerabilities) > 3:
                        print(f"   ... and {len(vulnerabilities) - 3} more")
                        
                except json.JSONDecodeError:
                    print("❌ Failed to parse safety output")
                    self.results['dependency_vulnerabilities'] = [{'error': 'Failed to parse safety output'}]
        
        except FileNotFoundError:
            print("⚠️  Safety not installed, skipping dependency vulnerability scan")
            print("   Install with: pip install safety")
            self.results['dependency_vulnerabilities'] = [{'error': 'Safety not installed'}]
        except Exception as e:
            print(f"❌ Error running dependency scan: {e}")
            self.results['dependency_vulnerabilities'] = [{'error': str(e)}]
    
    def scan_code_security(self):
        """Scan code for security issues using bandit."""
        print("\n🔒 Scanning code for security issues...")
        
        try:
            # Run bandit
            result = subprocess.run(
                [sys.executable, '-m', 'bandit', '-r', 'src/', '-f', 'json'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            try:
                bandit_output = json.loads(result.stdout)
                issues = bandit_output.get('results', [])
                
                if not issues:
                    print("✅ No security issues found in code")
                    self.results['code_security_issues'] = []
                else:
                    self.results['code_security_issues'] = issues
                    
                    # Categorize by severity
                    high_issues = [i for i in issues if i.get('issue_severity') == 'HIGH']
                    medium_issues = [i for i in issues if i.get('issue_severity') == 'MEDIUM']
                    low_issues = [i for i in issues if i.get('issue_severity') == 'LOW']
                    
                    print(f"⚠️  Found {len(issues)} security issues:")
                    if high_issues:
                        print(f"   - {len(high_issues)} HIGH severity")
                    if medium_issues:
                        print(f"   - {len(medium_issues)} MEDIUM severity")
                    if low_issues:
                        print(f"   - {len(low_issues)} LOW severity")
                    
                    # Show first few high severity issues
                    for issue in high_issues[:2]:
                        filename = issue.get('filename', 'Unknown file')
                        line_number = issue.get('line_number', 'Unknown line')
                        test_name = issue.get('test_name', 'Unknown test')
                        print(f"   - {filename}:{line_number} - {test_name}")
                        
            except json.JSONDecodeError:
                print("❌ Failed to parse bandit output")
                self.results['code_security_issues'] = [{'error': 'Failed to parse bandit output'}]
        
        except FileNotFoundError:
            print("⚠️  Bandit not installed, skipping code security scan")
            print("   Install with: pip install bandit")
            self.results['code_security_issues'] = [{'error': 'Bandit not installed'}]
        except Exception as e:
            print(f"❌ Error running code security scan: {e}")
            self.results['code_security_issues'] = [{'error': str(e)}]
    
    def check_licenses(self):
        """Check package licenses for compliance."""
        print("\n📄 Checking package licenses...")
        
        try:
            # Get installed packages with licenses
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'list', '--format=json'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                packages = json.loads(result.stdout)
                
                # Define problematic licenses
                problematic_licenses = [
                    'GPL-3.0', 'AGPL-3.0', 'LGPL-3.0',  # Copyleft licenses
                    'UNKNOWN', 'UNLICENSED'  # Unknown licenses
                ]
                
                license_issues = []
                
                # Check each package (simplified - would need pip-licenses for full info)
                for package in packages:
                    # This is a simplified check - in practice you'd use pip-licenses
                    # or similar tool to get actual license information
                    if package['name'].lower() in ['gpl-package', 'unknown-license']:
                        license_issues.append({
                            'package': package['name'],
                            'version': package['version'],
                            'license': 'UNKNOWN',
                            'issue': 'Unknown or problematic license'
                        })
                
                self.results['license_issues'] = license_issues
                
                if not license_issues:
                    print("✅ No license compliance issues found")
                else:
                    print(f"⚠️  Found {len(license_issues)} license issues")
                    for issue in license_issues[:3]:
                        print(f"   - {issue['package']}: {issue['license']}")
            
        except Exception as e:
            print(f"❌ Error checking licenses: {e}")
            self.results['license_issues'] = [{'error': str(e)}]
    
    def check_outdated_packages(self):
        """Check for outdated packages."""
        print("\n📅 Checking for outdated packages...")
        
        try:
            # Check for outdated packages
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'list', '--outdated', '--format=json'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                outdated_packages = json.loads(result.stdout)
                self.results['outdated_packages'] = outdated_packages
                
                if not outdated_packages:
                    print("✅ All packages are up to date")
                else:
                    print(f"📦 Found {len(outdated_packages)} outdated packages:")
                    for package in outdated_packages[:5]:
                        current = package.get('version', 'unknown')
                        latest = package.get('latest_version', 'unknown')
                        print(f"   - {package['name']}: {current} → {latest}")
                    
                    if len(outdated_packages) > 5:
                        print(f"   ... and {len(outdated_packages) - 5} more")
            
        except Exception as e:
            print(f"❌ Error checking outdated packages: {e}")
            self.results['outdated_packages'] = [{'error': str(e)}]
    
    def generate_summary(self):
        """Generate a summary of all scan results."""
        summary = {
            'total_vulnerabilities': len(self.results['dependency_vulnerabilities']),
            'total_security_issues': len(self.results['code_security_issues']),
            'total_license_issues': len(self.results['license_issues']),
            'total_outdated_packages': len(self.results['outdated_packages']),
            'scan_timestamp': subprocess.run(['date'], capture_output=True, text=True).stdout.strip(),
            'overall_status': 'PASS'
        }
        
        # Determine overall status
        critical_issues = (
            summary['total_vulnerabilities'] +
            len([i for i in self.results['code_security_issues'] 
                 if isinstance(i, dict) and i.get('issue_severity') == 'HIGH'])
        )
        
        if critical_issues > 0:
            summary['overall_status'] = 'FAIL'
        elif (summary['total_security_issues'] > 0 or 
              summary['total_license_issues'] > 0):
            summary['overall_status'] = 'WARN'
        
        self.results['summary'] = summary
    
    def save_results(self, output_file: Path):
        """Save scan results to a JSON file.
        
        Args:
            output_file: Path to save results
        """
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
    
    def print_summary(self):
        """Print a summary of scan results."""
        summary = self.results['summary']
        
        print("\n" + "="*60)
        print("🔍 SECURITY SCAN SUMMARY")
        print("="*60)
        
        status_emoji = {
            'PASS': '✅',
            'WARN': '⚠️',
            'FAIL': '❌'
        }
        
        print(f"Overall Status: {status_emoji.get(summary['overall_status'], '❓')} {summary['overall_status']}")
        print(f"Scan Time: {summary['scan_timestamp']}")
        print()
        print(f"📦 Dependency Vulnerabilities: {summary['total_vulnerabilities']}")
        print(f"🔒 Code Security Issues: {summary['total_security_issues']}")
        print(f"📄 License Issues: {summary['total_license_issues']}")
        print(f"📅 Outdated Packages: {summary['total_outdated_packages']}")
        
        if summary['overall_status'] == 'FAIL':
            print("\n❌ CRITICAL ISSUES FOUND - Review and fix before deployment")
        elif summary['overall_status'] == 'WARN':
            print("\n⚠️  WARNINGS FOUND - Review recommended")
        else:
            print("\n✅ NO CRITICAL ISSUES FOUND")


def main():
    """Main entry point for the security scanner."""
    parser = argparse.ArgumentParser(description='Security scanner for Neural Symbolic Language Model')
    parser.add_argument('--output', '-o', type=Path, help='Output file for results')
    parser.add_argument('--project-root', type=Path, help='Project root directory')
    
    args = parser.parse_args()
    
    # Initialize scanner
    scanner = SecurityScanner(args.project_root)
    
    # Run scan
    results = scanner.run_full_scan()
    
    # Print summary
    scanner.print_summary()
    
    # Save results if requested
    if args.output:
        scanner.save_results(args.output)
    
    # Exit with appropriate code
    status = results['summary']['overall_status']
    if status == 'FAIL':
        sys.exit(1)
    elif status == 'WARN':
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == '__main__':
    main()
