Vector Retrieval Module
========================

.. automodule:: retrieval
   :members:
   :undoc-members:
   :show-inheritance:

Overview
--------

The retrieval module provides efficient semantic search and document retrieval
capabilities using vector embeddings and similarity search.

Key Features:
* Multiple vector database backends (FAISS, ChromaDB)
* GPU-accelerated embeddings
* Batch document processing
* Similarity search with configurable parameters
* Automatic fallback mechanisms

Retriever Class
---------------

.. autoclass:: retrieval.Retriever
   :members:
   :undoc-members:
   :show-inheritance:

The main class for vector retrieval operations.

Initialization
~~~~~~~~~~~~~~

.. automethod:: retrieval.Retriever.__init__

Core Methods
~~~~~~~~~~~~

.. automethod:: retrieval.Retriever.add_documents
.. automethod:: retrieval.Retriever.batch_add_documents
.. automethod:: retrieval.Retriever.search
.. automethod:: retrieval.Retriever.get_system_info

Configuration
-------------

The retriever can be configured with the following parameters:

* ``vector_db``: Vector database backend ("faiss", "chromadb")
* ``embedding_model``: Embedding model name
* ``use_gpu``: Enable GPU acceleration (default: True)
* ``dimension``: Vector dimension (default: 768)
* ``index_type``: FAISS index type for optimization

Supported Backends
------------------

FAISS Backend
~~~~~~~~~~~~~

Facebook AI Similarity Search for efficient vector operations:

* **Performance**: Highly optimized C++ implementation
* **GPU Support**: Full GPU acceleration available
* **Scalability**: Handles millions of vectors efficiently
* **Index Types**: Multiple index types for different use cases

ChromaDB Backend
~~~~~~~~~~~~~~~~

Modern vector database with advanced features:

* **Metadata**: Rich metadata support for documents
* **Filtering**: Advanced filtering capabilities
* **Collections**: Organized document collections
* **Persistence**: Built-in data persistence

Embedding Models
----------------

Supported embedding models:

* **BAAI/bge-small-en-v1.5**: Lightweight, fast embeddings
* **BAAI/bge-large-en-v1.5**: High-quality embeddings
* **sentence-transformers/all-MiniLM-L6-v2**: Balanced performance
* **Custom models**: Support for custom embedding models

Example Usage
-------------

Basic Document Retrieval
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from retrieval import Retriever
   
   # Initialize retriever
   retriever = Retriever(
       vector_db="faiss",
       embedding_model="BAAI/bge-small-en-v1.5",
       use_gpu=True
   )
   
   # Add documents
   documents = [
       {
           "id": "doc1",
           "text": "Symbolic reasoning uses formal logic and rules.",
           "metadata": {"category": "AI", "source": "textbook"}
       },
       {
           "id": "doc2", 
           "text": "Neural networks learn patterns from data.",
           "metadata": {"category": "ML", "source": "paper"}
       }
   ]
   
   retriever.add_documents(documents)
   
   # Search for similar documents
   results = retriever.search("What is symbolic AI?", k=5)
   for result in results:
       print(f"Score: {result['score']:.3f}")
       print(f"Text: {result['text']}")
       print(f"Metadata: {result['metadata']}\n")

Batch Processing
~~~~~~~~~~~~~~~~

.. code-block:: python

   # Process large document collections
   large_document_set = [
       {"id": f"doc_{i}", "text": f"Document content {i}"}
       for i in range(10000)
   ]
   
   # Add documents in batches
   retriever.batch_add_documents(
       large_document_set,
       batch_size=100,
       show_progress=True
   )

Advanced Search
~~~~~~~~~~~~~~~

.. code-block:: python

   # Advanced search with parameters
   results = retriever.search(
       query="machine learning algorithms",
       k=10,
       score_threshold=0.7,
       include_metadata=True
   )
   
   # Filter results by metadata
   filtered_results = [
       r for r in results 
       if r.get('metadata', {}).get('category') == 'ML'
   ]

System Information
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Get system information
   info = retriever.get_system_info()
   print(f"Vector DB: {info['vector_db']}")
   print(f"Index Size: {info['index_size']}")
   print(f"GPU Enabled: {info['gpu_enabled']}")
   print(f"Embedding Model: {info['embedding_model']}")

Performance Optimization
------------------------

GPU Acceleration
~~~~~~~~~~~~~~~~

* Enable GPU acceleration for faster embedding computation
* Automatic memory management prevents GPU memory leaks
* Fallback to CPU if GPU is not available or runs out of memory

Batch Processing
~~~~~~~~~~~~~~~~

* Use batch processing for large document collections
* Configurable batch sizes optimize memory usage
* Progress tracking for long-running operations

Index Optimization
~~~~~~~~~~~~~~~~~~

* Choose appropriate FAISS index types for your use case:
  * ``IndexFlatIP``: Exact search, good for small datasets
  * ``IndexIVFFlat``: Approximate search, good for medium datasets
  * ``IndexHNSW``: Graph-based search, good for large datasets

Caching
~~~~~~~

* Embedding results are cached to avoid recomputation
* Configurable cache size and TTL
* LRU eviction policy for memory management

Error Handling
--------------

The module provides comprehensive error handling:

.. code-block:: python

   from retrieval import Retriever
   from exceptions import RetrievalError, VectorStoreError
   
   try:
       retriever = Retriever(vector_db="faiss")
       retriever.add_documents(documents)
       results = retriever.search("query")
   except RetrievalError as e:
       print(f"Retrieval failed: {e.message}")
       print(f"Operation: {e.details.get('operation')}")
   except VectorStoreError as e:
       print(f"Vector store error: {e.message}")

Best Practices
--------------

Document Preparation
~~~~~~~~~~~~~~~~~~~~

* Clean and preprocess text before adding to the index
* Include relevant metadata for filtering and organization
* Use consistent document IDs for updates and deletions

Query Optimization
~~~~~~~~~~~~~~~~~~

* Use specific, well-formed queries for better results
* Experiment with different similarity thresholds
* Consider query expansion for better recall

Scaling Considerations
~~~~~~~~~~~~~~~~~~~~~~

* Monitor index size and performance metrics
* Consider distributed setups for very large datasets
* Regular index optimization and maintenance
