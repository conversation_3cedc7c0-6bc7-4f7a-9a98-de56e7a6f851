# Development Dependencies for Neural Symbolic Language Model
# These dependencies are only needed for development, testing, and code quality

# Testing Framework
pytest==7.4.3
pytest-cov==4.1.0
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-xdist==3.3.1

# Code Quality and Formatting
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.5.0

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
sphinx-autodoc-typehints==1.25.2

# Security Scanning
bandit==1.7.5
safety==2.3.5

# Performance Profiling
py-spy==0.3.14
memory-profiler==0.61.0

# Development Tools
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6

# Type Checking
types-requests==*********
types-PyYAML==*********

# Linting Extensions
flake8-docstrings==1.7.0
flake8-import-order==0.18.2
flake8-bugbear==23.11.28

# Testing Utilities
factory-boy==3.3.0
faker==20.1.0
responses==0.24.1

# Development Server
watchdog==3.0.0
