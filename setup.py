from setuptools import setup, find_packages

setup(
    name="symbolic-language-model",
    version="0.1.0",
    description="A local language model using SymbolicAI and LightRAG with OpenAI-compatible API",
    author="Windsurf Engineering Team",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.10",
    install_requires=[
        "symbolicai",
        "lightrag",
        "fastapi>=0.68.0",
        "uvicorn>=0.15.0",
        "pydantic>=1.8.0",
        "chromadb>=0.3.0",
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "pytest>=7.0.0",
        "matplotlib>=3.5.0",
        "numpy>=1.21.0",
        "requests>=2.26.0",
        "psutil>=5.8.0",
        "python-multipart>=0.0.5",  # For handling file uploads
        "aiofiles>=0.8.0",  # For async file operations
    ],
    extras_require={
        "dev": [
            "black",
            "isort",
            "flake8",
            "mypy",
            "pytest-cov",
            "pytest-asyncio",
        ],
        "gpu": [
            "faiss-gpu>=1.7.0",
        ]
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    entry_points={
        "console_scripts": [
            "symbolic-lm=main:app",
        ],
    },
)
