# Makefile for Neural Symbolic Language Model
# This file provides convenient commands for development, testing, and deployment

.PHONY: help install install-dev test test-cov lint format type-check security docs clean build run docker-build docker-run

# Default target
help:
	@echo "Neural Symbolic Language Model - Development Commands"
	@echo ""
	@echo "Setup Commands:"
	@echo "  install          Install production dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo "  setup-hooks      Setup pre-commit hooks"
	@echo ""
	@echo "Development Commands:"
	@echo "  run              Run the development server"
	@echo "  test             Run tests"
	@echo "  test-cov         Run tests with coverage"
	@echo "  test-watch       Run tests in watch mode"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  lint             Run all linters"
	@echo "  format           Format code with black and isort"
	@echo "  type-check       Run type checking with mypy"
	@echo "  security         Run security checks with bandit"
	@echo "  pre-commit       Run pre-commit hooks on all files"
	@echo ""
	@echo "Documentation Commands:"
	@echo "  docs             Build documentation"
	@echo "  docs-serve       Serve documentation locally"
	@echo ""
	@echo "Build Commands:"
	@echo "  build            Build package"
	@echo "  clean            Clean build artifacts"
	@echo ""
	@echo "Docker Commands:"
	@echo "  docker-build     Build Docker image"
	@echo "  docker-run       Run Docker container"
	@echo "  docker-dev       Run development Docker setup"

# Setup Commands
install:
	pip install -r requirements.txt

install-dev:
	pip install -e ".[dev,docs,monitoring,vector-db]"
	pip install -r requirements.txt

setup-hooks:
	pre-commit install
	pre-commit install --hook-type commit-msg

# Development Commands
run:
	python src/main.py

run-dev:
	uvicorn main:app --reload --host 0.0.0.0 --port 8000 --app-dir src

# Testing Commands
test:
	pytest

test-cov:
	pytest --cov=src --cov-report=html --cov-report=term-missing

test-watch:
	pytest-watch

test-unit:
	pytest -m "unit"

test-integration:
	pytest -m "integration"

test-security:
	pytest -m "security"

test-performance:
	pytest -m "performance"

# Code Quality Commands
lint: lint-flake8 lint-mypy lint-bandit lint-pydocstyle

lint-flake8:
	flake8 src tests

lint-mypy:
	mypy src

lint-bandit:
	bandit -r src/ -f json -o bandit-report.json

lint-pydocstyle:
	pydocstyle src

format:
	black src tests
	isort src tests

type-check:
	mypy src tests

security:
	bandit -r src/
	safety check

pre-commit:
	pre-commit run --all-files

# Documentation Commands
docs:
	cd docs && make html

docs-serve:
	cd docs/_build/html && python -m http.server 8080

docs-clean:
	cd docs && make clean

# Build Commands
build:
	python -m build

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf bandit-report.json
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Docker Commands
docker-build:
	docker build -t neural-symbolic-ai:latest .

docker-run:
	docker run -p 8000:8000 neural-symbolic-ai:latest

docker-dev:
	docker-compose -f docker-compose.dev.yml up --build

docker-stop:
	docker-compose -f docker-compose.dev.yml down

# Dependency Management
update-deps:
	pip-compile requirements.in
	pip-compile requirements-dev.in

check-deps:
	pip-audit

# Database Commands
db-migrate:
	@echo "Database migration commands would go here"

db-reset:
	@echo "Database reset commands would go here"

# Monitoring Commands
start-monitoring:
	docker-compose -f docker-compose.monitoring.yml up -d

stop-monitoring:
	docker-compose -f docker-compose.monitoring.yml down

# Release Commands
release-patch:
	bump2version patch

release-minor:
	bump2version minor

release-major:
	bump2version major

# Environment Commands
create-env:
	python -m venv venv
	@echo "Virtual environment created. Activate with:"
	@echo "  source venv/bin/activate  # Linux/Mac"
	@echo "  venv\\Scripts\\activate     # Windows"

# Performance Commands
benchmark:
	python scripts/benchmark.py

profile:
	python -m cProfile -o profile.stats src/main.py

# Utility Commands
check-all: lint type-check security test
	@echo "All checks passed!"

ci: install-dev lint type-check security test-cov
	@echo "CI pipeline completed successfully!"

# Variables
PYTHON_VERSION := $(shell python --version 2>&1 | cut -d' ' -f2)
PROJECT_NAME := neural-symbolic-language-model

info:
	@echo "Project: $(PROJECT_NAME)"
	@echo "Python Version: $(PYTHON_VERSION)"
	@echo "Current Directory: $(PWD)"