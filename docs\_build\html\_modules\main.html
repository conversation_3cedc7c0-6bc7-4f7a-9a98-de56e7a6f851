

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>main &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Module code</a></li>
      <li class="breadcrumb-item active">main</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for main</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Main application module for the Neural Symbolic Language Model.</span>
<span class="sd">This module provides the FastAPI server implementation and core API endpoints.</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="c1"># Import required modules</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">os</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">json</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">uuid</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">document_loader</span><span class="w"> </span><span class="kn">import</span> <span class="n">DocumentLoader</span>

<span class="c1"># Import logging configuration</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">logging_config</span><span class="w"> </span><span class="kn">import</span> <span class="n">setup_logging</span><span class="p">,</span> <span class="n">get_logger</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">get_logger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="k">try</span><span class="p">:</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">faiss</span>
    <span class="n">FAISS_AVAILABLE</span> <span class="o">=</span> <span class="kc">True</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Imported FAISS successfully&quot;</span><span class="p">)</span>
    
    <span class="c1"># Check if GPU FAISS is available</span>
    <span class="n">GPU_FAISS_AVAILABLE</span> <span class="o">=</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">faiss</span><span class="p">,</span> <span class="s1">&#39;StandardGpuResources&#39;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">GPU_FAISS_AVAILABLE</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU FAISS is available&quot;</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Using CPU version of FAISS&quot;</span><span class="p">)</span>
        
<span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;FAISS not available. Using fallback vector storage.&quot;</span><span class="p">)</span>
    <span class="n">FAISS_AVAILABLE</span> <span class="o">=</span> <span class="kc">False</span>
    <span class="n">GPU_FAISS_AVAILABLE</span> <span class="o">=</span> <span class="kc">False</span>
    <span class="n">faiss</span> <span class="o">=</span> <span class="kc">None</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">FastAPI</span><span class="p">,</span> <span class="n">HTTPException</span><span class="p">,</span> <span class="n">BackgroundTasks</span><span class="p">,</span> <span class="n">Request</span><span class="p">,</span> <span class="n">Depends</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi.responses</span><span class="w"> </span><span class="kn">import</span> <span class="n">StreamingResponse</span><span class="p">,</span> <span class="n">FileResponse</span><span class="p">,</span> <span class="n">HTMLResponse</span><span class="p">,</span> <span class="n">JSONResponse</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi.staticfiles</span><span class="w"> </span><span class="kn">import</span> <span class="n">StaticFiles</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi.templating</span><span class="w"> </span><span class="kn">import</span> <span class="n">Jinja2Templates</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi.middleware.cors</span><span class="w"> </span><span class="kn">import</span> <span class="n">CORSMiddleware</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">asyncio</span>

<span class="c1"># Import our new models and security</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pydantic</span><span class="w"> </span><span class="kn">import</span> <span class="n">BaseModel</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">ChatRequest</span><span class="p">,</span> <span class="n">ChatResponse</span><span class="p">,</span> <span class="n">ChatMessage</span><span class="p">,</span> <span class="n">ChatChoice</span><span class="p">,</span> <span class="n">TokenUsage</span><span class="p">,</span>
    <span class="n">DocumentAddRequest</span><span class="p">,</span> <span class="n">DocumentAddResponse</span><span class="p">,</span> <span class="n">SystemInfo</span><span class="p">,</span> <span class="n">PerformanceMetrics</span><span class="p">,</span>
    <span class="n">ModelRole</span><span class="p">,</span> <span class="n">OpenAIMessage</span><span class="p">,</span> <span class="n">OpenAIChatRequest</span><span class="p">,</span> <span class="n">OpenAIChatChoice</span><span class="p">,</span> <span class="n">OpenAIChatResponse</span>
<span class="p">)</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">verify_api_key</span><span class="p">,</span> <span class="n">check_request_size</span><span class="p">,</span> <span class="n">get_security_headers</span><span class="p">,</span> <span class="n">security_manager</span><span class="p">,</span> <span class="n">get_client_ip</span><span class="p">,</span> <span class="n">rate_limiter</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">exceptions</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">SymbolicAIException</span><span class="p">,</span> <span class="n">ValidationError</span><span class="p">,</span> <span class="n">AuthenticationError</span><span class="p">,</span>
    <span class="n">RetrievalError</span><span class="p">,</span> <span class="n">ReasoningError</span><span class="p">,</span> <span class="n">handle_exception</span>
<span class="p">)</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">torch</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">uvicorn</span>

<span class="c1"># Import local modules</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">symbolic_reasoning</span><span class="w"> </span><span class="kn">import</span> <span class="n">SymbolicReasoner</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">retrieval</span><span class="w"> </span><span class="kn">import</span> <span class="n">Retriever</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">logging_config</span><span class="w"> </span><span class="kn">import</span> <span class="n">setup_logging</span><span class="p">,</span> <span class="n">get_logger</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">monitoring</span><span class="w"> </span><span class="kn">import</span> <span class="n">PerformanceMonitor</span>

<span class="c1"># Set up logging</span>
<span class="n">setup_logging</span><span class="p">()</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">get_logger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="c1"># Initialize performance monitoring</span>
<span class="n">monitor</span> <span class="o">=</span> <span class="n">PerformanceMonitor</span><span class="p">()</span>

<span class="c1"># Create FastAPI app with security configuration</span>
<span class="n">app</span> <span class="o">=</span> <span class="n">FastAPI</span><span class="p">(</span>
    <span class="n">title</span><span class="o">=</span><span class="s2">&quot;Neural Symbolic Language Model API&quot;</span><span class="p">,</span>
    <span class="n">description</span><span class="o">=</span><span class="s2">&quot;&quot;&quot;A production-ready neural-symbolic language model with retrieval-augmented generation.</span>

<span class="s2">    Features:</span>
<span class="s2">    - Neural-symbolic reasoning with explainable AI</span>
<span class="s2">    - Hybrid retrieval with FAISS and LightRAG</span>
<span class="s2">    - GPU acceleration for optimal performance</span>
<span class="s2">    - Comprehensive security and monitoring</span>
<span class="s2">    - OpenAI-compatible API endpoints</span>

<span class="s2">    Authentication:</span>
<span class="s2">    - All endpoints require API key authentication</span>
<span class="s2">    - Use Bearer token in Authorization header</span>

<span class="s2">    Available endpoints:</span>
<span class="s2">    - GET /: Web interface</span>
<span class="s2">    - GET /docs: Interactive API documentation</span>
<span class="s2">    - GET /redoc: ReDoc API documentation</span>
<span class="s2">    - POST /v1/chat/completions: OpenAI-compatible chat endpoint</span>
<span class="s2">    - POST /documents/add: Add documents to retrieval system</span>
<span class="s2">    - GET /system/info: Get system information</span>
<span class="s2">    - GET /performance: Get performance metrics</span>
<span class="s2">    &quot;&quot;&quot;</span><span class="p">,</span>
    <span class="n">version</span><span class="o">=</span><span class="s2">&quot;0.1.0&quot;</span><span class="p">,</span>
    <span class="n">docs_url</span><span class="o">=</span><span class="s2">&quot;/docs&quot;</span><span class="p">,</span>
    <span class="n">redoc_url</span><span class="o">=</span><span class="s2">&quot;/redoc&quot;</span>
<span class="p">)</span>

<span class="c1"># Add CORS middleware</span>
<span class="n">app</span><span class="o">.</span><span class="n">add_middleware</span><span class="p">(</span>
    <span class="n">CORSMiddleware</span><span class="p">,</span>
    <span class="n">allow_origins</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;http://localhost:3000&quot;</span><span class="p">,</span> <span class="s2">&quot;http://127.0.0.1:3000&quot;</span><span class="p">],</span>  <span class="c1"># Add your frontend URLs</span>
    <span class="n">allow_credentials</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">allow_methods</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;POST&quot;</span><span class="p">],</span>
    <span class="n">allow_headers</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;*&quot;</span><span class="p">],</span>
<span class="p">)</span>

<span class="c1"># Add security headers middleware</span>
<div class="viewcode-block" id="add_security_headers">
<a class="viewcode-back" href="../modules.html#main.add_security_headers">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">middleware</span><span class="p">(</span><span class="s2">&quot;http&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">add_security_headers</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">call_next</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Add security headers to all responses.&quot;&quot;&quot;</span>
    <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">call_next</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>

    <span class="c1"># Add security headers</span>
    <span class="n">security_headers</span> <span class="o">=</span> <span class="n">get_security_headers</span><span class="p">()</span>
    <span class="k">for</span> <span class="n">header</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">security_headers</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="p">[</span><span class="n">header</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>

    <span class="k">return</span> <span class="n">response</span></div>


<span class="c1"># Add request size checking middleware</span>
<div class="viewcode-block" id="check_request_size_middleware">
<a class="viewcode-back" href="../modules.html#main.check_request_size_middleware">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">middleware</span><span class="p">(</span><span class="s2">&quot;http&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">check_request_size_middleware</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">call_next</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Check request size limits.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="k">await</span> <span class="n">check_request_size</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
        <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">call_next</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">response</span>
    <span class="k">except</span> <span class="n">HTTPException</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
            <span class="n">status_code</span><span class="o">=</span><span class="n">e</span><span class="o">.</span><span class="n">status_code</span><span class="p">,</span>
            <span class="n">content</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;error&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;code&quot;</span><span class="p">:</span> <span class="s2">&quot;REQUEST_TOO_LARGE&quot;</span><span class="p">,</span> <span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="n">e</span><span class="o">.</span><span class="n">detail</span><span class="p">}}</span>
        <span class="p">)</span></div>


<span class="c1"># Add rate limiting middleware</span>
<div class="viewcode-block" id="rate_limit_middleware">
<a class="viewcode-back" href="../modules.html#main.rate_limit_middleware">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">middleware</span><span class="p">(</span><span class="s2">&quot;http&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">rate_limit_middleware</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">call_next</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Apply rate limiting.&quot;&quot;&quot;</span>
    <span class="n">client_ip</span> <span class="o">=</span> <span class="n">get_client_ip</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>

    <span class="c1"># Check if IP is blocked</span>
    <span class="k">if</span> <span class="n">security_manager</span><span class="o">.</span><span class="n">is_ip_blocked</span><span class="p">(</span><span class="n">client_ip</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">429</span><span class="p">,</span>
            <span class="n">content</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;error&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;code&quot;</span><span class="p">:</span> <span class="s2">&quot;IP_BLOCKED&quot;</span><span class="p">,</span> <span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;IP address is temporarily blocked&quot;</span><span class="p">}}</span>
        <span class="p">)</span>

    <span class="c1"># Check rate limit</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">rate_limiter</span><span class="o">.</span><span class="n">is_allowed</span><span class="p">(</span><span class="n">client_ip</span><span class="p">):</span>
        <span class="n">security_manager</span><span class="o">.</span><span class="n">record_failed_attempt</span><span class="p">(</span><span class="n">client_ip</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
            <span class="n">status_code</span><span class="o">=</span><span class="mi">429</span><span class="p">,</span>
            <span class="n">content</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;error&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;code&quot;</span><span class="p">:</span> <span class="s2">&quot;RATE_LIMIT_EXCEEDED&quot;</span><span class="p">,</span> <span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;Rate limit exceeded&quot;</span><span class="p">}}</span>
        <span class="p">)</span>

    <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">call_next</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">response</span></div>


<span class="c1"># Global exception handlers</span>
<div class="viewcode-block" id="symbolic_ai_exception_handler">
<a class="viewcode-back" href="../modules.html#main.symbolic_ai_exception_handler">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">exception_handler</span><span class="p">(</span><span class="n">SymbolicAIException</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">symbolic_ai_exception_handler</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">exc</span><span class="p">:</span> <span class="n">SymbolicAIException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Handle SymbolicAI custom exceptions.&quot;&quot;&quot;</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SymbolicAI exception: </span><span class="si">{</span><span class="n">exc</span><span class="o">.</span><span class="n">message</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="n">exc</span><span class="o">.</span><span class="n">status_code</span><span class="p">,</span>
        <span class="n">content</span><span class="o">=</span><span class="n">exc</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
    <span class="p">)</span></div>


<div class="viewcode-block" id="validation_exception_handler">
<a class="viewcode-back" href="../modules.html#main.validation_exception_handler">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">exception_handler</span><span class="p">(</span><span class="n">ValidationError</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">validation_exception_handler</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">exc</span><span class="p">:</span> <span class="n">ValidationError</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Handle validation errors.&quot;&quot;&quot;</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Validation error: </span><span class="si">{</span><span class="n">exc</span><span class="o">.</span><span class="n">message</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="n">exc</span><span class="o">.</span><span class="n">status_code</span><span class="p">,</span>
        <span class="n">content</span><span class="o">=</span><span class="n">exc</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
    <span class="p">)</span></div>


<div class="viewcode-block" id="authentication_exception_handler">
<a class="viewcode-back" href="../modules.html#main.authentication_exception_handler">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">exception_handler</span><span class="p">(</span><span class="n">AuthenticationError</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">authentication_exception_handler</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">exc</span><span class="p">:</span> <span class="n">AuthenticationError</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Handle authentication errors.&quot;&quot;&quot;</span>
    <span class="n">client_ip</span> <span class="o">=</span> <span class="n">get_client_ip</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
    <span class="n">security_manager</span><span class="o">.</span><span class="n">record_failed_attempt</span><span class="p">(</span><span class="n">client_ip</span><span class="p">)</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Authentication failed for IP </span><span class="si">{</span><span class="n">client_ip</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">exc</span><span class="o">.</span><span class="n">message</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="n">exc</span><span class="o">.</span><span class="n">status_code</span><span class="p">,</span>
        <span class="n">content</span><span class="o">=</span><span class="n">exc</span><span class="o">.</span><span class="n">to_dict</span><span class="p">(),</span>
        <span class="n">headers</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;WWW-Authenticate&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer&quot;</span><span class="p">}</span>
    <span class="p">)</span></div>


<div class="viewcode-block" id="general_exception_handler">
<a class="viewcode-back" href="../modules.html#main.general_exception_handler">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">exception_handler</span><span class="p">(</span><span class="ne">Exception</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">general_exception_handler</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">exc</span><span class="p">:</span> <span class="ne">Exception</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Handle all other exceptions.&quot;&quot;&quot;</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Unhandled exception: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">exc</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="c1"># Convert to SymbolicAI exception</span>
    <span class="n">symbolic_exc</span> <span class="o">=</span> <span class="n">handle_exception</span><span class="p">(</span><span class="n">exc</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="n">symbolic_exc</span><span class="o">.</span><span class="n">status_code</span><span class="p">,</span>
        <span class="n">content</span><span class="o">=</span><span class="n">symbolic_exc</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
    <span class="p">)</span></div>


<span class="c1"># Set up templates and static files</span>
<span class="n">BASE_DIR</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">abspath</span><span class="p">(</span><span class="vm">__file__</span><span class="p">))</span>
<span class="n">static_dir</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">BASE_DIR</span><span class="p">,</span> <span class="s1">&#39;static&#39;</span><span class="p">)</span>
<span class="n">app</span><span class="o">.</span><span class="n">mount</span><span class="p">(</span><span class="s2">&quot;/static&quot;</span><span class="p">,</span> <span class="n">StaticFiles</span><span class="p">(</span><span class="n">directory</span><span class="o">=</span><span class="n">static_dir</span><span class="p">),</span> <span class="n">name</span><span class="o">=</span><span class="s2">&quot;static&quot;</span><span class="p">)</span>

<span class="n">templates</span> <span class="o">=</span> <span class="n">Jinja2Templates</span><span class="p">(</span><span class="n">directory</span><span class="o">=</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">BASE_DIR</span><span class="p">,</span> <span class="s1">&#39;templates&#39;</span><span class="p">))</span>

<div class="viewcode-block" id="read_root">
<a class="viewcode-back" href="../modules.html#main.read_root">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">,</span> <span class="n">response_class</span><span class="o">=</span><span class="n">HTMLResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">read_root</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">):</span>
    <span class="c1"># Serve the main web interface.</span>
    <span class="k">return</span> <span class="n">FileResponse</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">static_dir</span><span class="p">,</span> <span class="s1">&#39;index.html&#39;</span><span class="p">))</span></div>


<div class="viewcode-block" id="api_info">
<a class="viewcode-back" href="../modules.html#main.api_info">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/api/info&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">api_info</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get API information and available endpoints.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="p">{</span>
        <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Neural Symbolic Language Model API&quot;</span><span class="p">,</span>
        <span class="s2">&quot;version&quot;</span><span class="p">:</span> <span class="s2">&quot;0.1.0&quot;</span><span class="p">,</span>
        <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;A local GPT-4o alternative using SymbolicAI and LightRAG&quot;</span><span class="p">,</span>
        <span class="s2">&quot;endpoints&quot;</span><span class="p">:</span> <span class="p">{</span>
            <span class="s2">&quot;root&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;path&quot;</span><span class="p">:</span> <span class="s2">&quot;/&quot;</span><span class="p">,</span>
                <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;GET&quot;</span><span class="p">,</span>
                <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;Web interface&quot;</span>
            <span class="p">},</span>
            <span class="s2">&quot;api_info&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;path&quot;</span><span class="p">:</span> <span class="s2">&quot;/api/info&quot;</span><span class="p">,</span>
                <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;GET&quot;</span><span class="p">,</span>
                <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;This API documentation&quot;</span>
            <span class="p">},</span>
            <span class="s2">&quot;docs&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;path&quot;</span><span class="p">:</span> <span class="s2">&quot;/docs&quot;</span><span class="p">,</span>
                <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;GET&quot;</span><span class="p">,</span>
                <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;Interactive API documentation (Swagger UI)&quot;</span>
            <span class="p">},</span>
            <span class="s2">&quot;redoc&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;path&quot;</span><span class="p">:</span> <span class="s2">&quot;/redoc&quot;</span><span class="p">,</span>
                <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;GET&quot;</span><span class="p">,</span>
                <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;ReDoc API documentation&quot;</span>
            <span class="p">},</span>
            <span class="s2">&quot;chat&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;path&quot;</span><span class="p">:</span> <span class="s2">&quot;/chat&quot;</span><span class="p">,</span>
                <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;POST&quot;</span><span class="p">,</span>
                <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;Send a chat message&quot;</span>
            <span class="p">},</span>
            <span class="s2">&quot;system_info&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;path&quot;</span><span class="p">:</span> <span class="s2">&quot;/system/info&quot;</span><span class="p">,</span>
                <span class="s2">&quot;method&quot;</span><span class="p">:</span> <span class="s2">&quot;GET&quot;</span><span class="p">,</span>
                <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;Get system information&quot;</span>
            <span class="p">}</span>
        <span class="p">}</span>
    <span class="p">}</span></div>


<span class="c1"># Initialize components</span>
<span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Initializing components...&quot;</span><span class="p">)</span>
<span class="c1"># Check GPU and FAISS availability</span>
<span class="n">gpu_available</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">()</span>
<span class="n">gpu_faiss_available</span> <span class="o">=</span> <span class="n">GPU_FAISS_AVAILABLE</span> <span class="k">if</span> <span class="s1">&#39;GPU_FAISS_AVAILABLE&#39;</span> <span class="ow">in</span> <span class="nb">globals</span><span class="p">()</span> <span class="k">else</span> <span class="kc">False</span>

<span class="k">if</span> <span class="n">gpu_available</span><span class="p">:</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU available: </span><span class="si">{</span><span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">get_device_name</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">gpu_faiss_available</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;GPU FAISS not available. Will use CPU version of FAISS.&quot;</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;GPU not available. Running in CPU mode.&quot;</span><span class="p">)</span>

<span class="c1"># Initialize components with appropriate GPU settings</span>
<span class="n">use_gpu</span> <span class="o">=</span> <span class="n">gpu_available</span> <span class="ow">and</span> <span class="p">(</span><span class="ow">not</span> <span class="s1">&#39;FAISS_AVAILABLE&#39;</span> <span class="ow">in</span> <span class="nb">globals</span><span class="p">()</span> <span class="ow">or</span> <span class="n">FAISS_AVAILABLE</span><span class="p">)</span>
<span class="n">reasoner</span> <span class="o">=</span> <span class="n">SymbolicReasoner</span><span class="p">(</span><span class="n">use_gpu</span><span class="o">=</span><span class="n">use_gpu</span><span class="p">)</span>
<span class="n">retriever</span> <span class="o">=</span> <span class="n">Retriever</span><span class="p">(</span><span class="n">use_gpu</span><span class="o">=</span><span class="n">use_gpu</span> <span class="ow">and</span> <span class="n">gpu_faiss_available</span><span class="p">)</span>
<span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Components initialized successfully&quot;</span><span class="p">)</span>

<span class="c1"># Load documents from data directory (if exists)</span>
<span class="n">data_dir</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="vm">__file__</span><span class="p">),</span> <span class="s1">&#39;..&#39;</span><span class="p">,</span> <span class="s1">&#39;data&#39;</span><span class="p">)</span>
<span class="k">if</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">exists</span><span class="p">(</span><span class="n">data_dir</span><span class="p">):</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Loading documents from </span><span class="si">{</span><span class="n">data_dir</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="c1"># Load documents with supported extensions</span>
    <span class="n">documents_data</span> <span class="o">=</span> <span class="n">DocumentLoader</span><span class="o">.</span><span class="n">load_directory</span><span class="p">(</span>
        <span class="n">data_dir</span><span class="p">,</span> 
        <span class="n">extensions</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;.txt&#39;</span><span class="p">,</span> <span class="s1">&#39;.md&#39;</span><span class="p">]</span>
    <span class="p">)</span>
    
    <span class="c1"># Add documents to retriever</span>
    <span class="n">documents</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">documents_data</span><span class="o">.</span><span class="n">values</span><span class="p">())</span>
    <span class="k">if</span> <span class="n">documents</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Loading </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">documents</span><span class="p">)</span><span class="si">}</span><span class="s2"> documents from data directory&quot;</span><span class="p">)</span>
        <span class="n">retriever</span><span class="o">.</span><span class="n">add_documents</span><span class="p">(</span><span class="n">documents</span><span class="p">)</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Documents loaded successfully&quot;</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;No documents found in data directory&quot;</span><span class="p">)</span>

<span class="c1"># Create advanced response cache</span>
<div class="viewcode-block" id="ResponseCache">
<a class="viewcode-back" href="../modules.html#main.ResponseCache">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ResponseCache</span><span class="p">:</span>
<div class="viewcode-block" id="ResponseCache.__init__">
<a class="viewcode-back" href="../modules.html#main.ResponseCache.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">max_size</span><span class="o">=</span><span class="mi">1000</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">cache</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">timestamps</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">max_size</span> <span class="o">=</span> <span class="n">max_size</span></div>

    
<div class="viewcode-block" id="ResponseCache.get">
<a class="viewcode-back" href="../modules.html#main.ResponseCache.get">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get a value from the cache.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            key (str): The cache key</span>
<span class="sd">            </span>
<span class="sd">        Returns:</span>
<span class="sd">            The cached value, or None if not found</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache</span><span class="p">:</span>
            <span class="c1"># Update access timestamp</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">timestamps</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache</span><span class="p">[</span><span class="n">key</span><span class="p">]</span>
        <span class="k">return</span> <span class="kc">None</span></div>

    
<div class="viewcode-block" id="ResponseCache.set">
<a class="viewcode-back" href="../modules.html#main.ResponseCache.set">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">set</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Set a value in the cache.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            key (str): The cache key</span>
<span class="sd">            value: The value to cache</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Clean cache if needed</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">cache</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">max_size</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">clean</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">max_size</span> <span class="o">*</span> <span class="mf">0.2</span><span class="p">))</span>  <span class="c1"># Remove 20% of entries</span>
        
        <span class="c1"># Store value and timestamp</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">cache</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">timestamps</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span></div>

    
<div class="viewcode-block" id="ResponseCache.clean">
<a class="viewcode-back" href="../modules.html#main.ResponseCache.clean">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">clean</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">count</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Clean old entries from the cache.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            count (int, optional): Number of entries to remove</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache</span><span class="p">:</span>
            <span class="k">return</span>
        
        <span class="c1"># Sort keys by timestamp</span>
        <span class="n">sorted_keys</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">timestamps</span><span class="o">.</span><span class="n">keys</span><span class="p">(),</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">k</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">timestamps</span><span class="p">[</span><span class="n">k</span><span class="p">])</span>
        
        <span class="c1"># Determine how many entries to remove</span>
        <span class="n">remove_count</span> <span class="o">=</span> <span class="n">count</span> <span class="k">if</span> <span class="n">count</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span> <span class="nb">len</span><span class="p">(</span><span class="n">sorted_keys</span><span class="p">)</span> <span class="o">//</span> <span class="mi">2</span>
        
        <span class="c1"># Remove oldest entries</span>
        <span class="k">for</span> <span class="n">key</span> <span class="ow">in</span> <span class="n">sorted_keys</span><span class="p">[:</span><span class="n">remove_count</span><span class="p">]:</span>
            <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">cache</span><span class="p">[</span><span class="n">key</span><span class="p">]</span>
            <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">timestamps</span><span class="p">[</span><span class="n">key</span><span class="p">]</span></div>

    
<div class="viewcode-block" id="ResponseCache.size">
<a class="viewcode-back" href="../modules.html#main.ResponseCache.size">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">size</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get the current cache size.</span>
<span class="sd">        </span>
<span class="sd">        Returns:</span>
<span class="sd">            int: The number of entries in the cache</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">cache</span><span class="p">)</span></div>
</div>


<span class="c1"># Initialize improved cache</span>
<span class="n">response_cache</span> <span class="o">=</span> <span class="n">ResponseCache</span><span class="p">(</span><span class="n">max_size</span><span class="o">=</span><span class="mi">1000</span><span class="p">)</span>

<span class="c1"># Legacy models for backward compatibility (will be removed)</span>
<div class="viewcode-block" id="LegacyChatRequest">
<a class="viewcode-back" href="../modules.html#main.LegacyChatRequest">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">LegacyChatRequest</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span></div>


<div class="viewcode-block" id="LegacyChatResponse">
<a class="viewcode-back" href="../modules.html#main.LegacyChatResponse">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">LegacyChatResponse</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
    <span class="n">response</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">cached</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">False</span></div>


<span class="c1"># Optimize GPU memory usage</span>
<div class="viewcode-block" id="optimize_gpu_memory">
<a class="viewcode-back" href="../modules.html#main.optimize_gpu_memory">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">optimize_gpu_memory</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Configure PyTorch for optimal GPU memory usage&quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">():</span>
        <span class="c1"># Empty cache before processing</span>
        <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">empty_cache</span><span class="p">()</span>
        
        <span class="c1"># Set memory allocation strategy</span>
        <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">set_per_process_memory_fraction</span><span class="p">(</span><span class="mf">0.8</span><span class="p">)</span>  <span class="c1"># Use 80% of available GPU memory</span>
        
        <span class="c1"># Enable memory optimization</span>
        <span class="n">torch</span><span class="o">.</span><span class="n">backends</span><span class="o">.</span><span class="n">cudnn</span><span class="o">.</span><span class="n">benchmark</span> <span class="o">=</span> <span class="kc">True</span>
        
        <span class="k">return</span> <span class="kc">True</span>
    <span class="k">return</span> <span class="kc">False</span></div>


<span class="c1"># Call GPU optimization at startup</span>
<span class="n">optimize_gpu_memory</span><span class="p">()</span>

<span class="c1"># Add sample documents for testing</span>
<span class="n">sample_documents</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s2">&quot;Neural-Symbolic AI integrates logic-based reasoning with deep learning models.&quot;</span><span class="p">,</span>
    <span class="s2">&quot;FAISS is a library for efficient similarity search developed by Facebook AI.&quot;</span><span class="p">,</span>
    <span class="s2">&quot;Retrieval-Augmented Generation improves language model outputs by incorporating external knowledge.&quot;</span><span class="p">,</span>
    <span class="s2">&quot;GPT models are transformer-based language models developed by OpenAI.&quot;</span><span class="p">,</span>
    <span class="s2">&quot;Vector databases store and retrieve high-dimensional vectors for similarity search.&quot;</span>
<span class="p">]</span>

<span class="c1"># Add documents to retriever</span>
<span class="n">documents_with_ids</span> <span class="o">=</span> <span class="p">[{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">),</span> <span class="s1">&#39;text&#39;</span><span class="p">:</span> <span class="n">doc</span><span class="p">}</span> <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">doc</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">sample_documents</span><span class="p">)]</span>
<span class="n">retriever</span><span class="o">.</span><span class="n">add_documents</span><span class="p">(</span><span class="n">documents_with_ids</span><span class="p">)</span>

<span class="c1"># Helper function to process queries</span>
<div class="viewcode-block" id="process_query">
<a class="viewcode-back" href="../modules.html#main.process_query">[docs]</a>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">process_query</span><span class="p">(</span><span class="n">query</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Process a query using both retrieval and reasoning components.</span>
<span class="sd">    </span>
<span class="sd">    Args:</span>
<span class="sd">        query: The user&#39;s query text</span>
<span class="sd">        </span>
<span class="sd">    Returns:</span>
<span class="sd">        str: The generated response</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># First, retrieve relevant information</span>
    <span class="n">retrieved_info</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">query</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
    
    <span class="c1"># Format retrieved information as context</span>
    <span class="n">context</span> <span class="o">=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">([</span><span class="sa">f</span><span class="s2">&quot;Context </span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span> <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">result</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">retrieved_info</span><span class="p">)])</span>
    
    <span class="c1"># Use symbolic reasoner to process query with context</span>
    <span class="n">response</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="n">query</span><span class="p">,</span> <span class="n">context</span><span class="o">=</span><span class="n">context</span><span class="p">)</span>
    
    <span class="k">return</span> <span class="n">response</span></div>


<span class="c1"># API endpoints</span>
<div class="viewcode-block" id="add_document">
<a class="viewcode-back" href="../modules.html#main.add_document">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/documents/add&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">DocumentAddResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">add_document</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">DocumentAddRequest</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Add a document to the retrieval system.</span>
<span class="sd">    </span>
<span class="sd">    Args:</span>
<span class="sd">        request: DocumentAddRequest object containing the document content</span>
<span class="sd">        </span>
<span class="sd">    Returns:</span>
<span class="sd">        DocumentAddResponse object indicating success</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Add document to retriever</span>
        <span class="n">retriever</span><span class="o">.</span><span class="n">add_documents</span><span class="p">([{</span><span class="s1">&#39;text&#39;</span><span class="p">:</span> <span class="n">request</span><span class="o">.</span><span class="n">content</span><span class="p">}])</span>
        
        <span class="k">return</span> <span class="n">DocumentAddResponse</span><span class="p">(</span>
            <span class="n">success</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="n">message</span><span class="o">=</span><span class="s2">&quot;Document added successfully&quot;</span>
        <span class="p">)</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error adding document: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="get_document_count">
<a class="viewcode-back" href="../modules.html#main.get_document_count">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/documents/count&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_document_count</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get the number of documents in the retrieval system.</span>
<span class="sd">    </span>
<span class="sd">    Returns:</span>
<span class="sd">        dict: Number of documents</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># This is a placeholder - actual implementation depends on LightRAG</span>
    <span class="c1"># Assuming retriever has a `document_count` method</span>
    <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">retriever</span><span class="p">,</span> <span class="s1">&#39;document_count&#39;</span><span class="p">):</span>
        <span class="n">count</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">document_count</span><span class="p">()</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">count</span> <span class="o">=</span> <span class="o">-</span><span class="mi">1</span>  <span class="c1"># Unknown if not supported</span>
        
    <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;count&quot;</span><span class="p">:</span> <span class="n">count</span><span class="p">}</span></div>


<div class="viewcode-block" id="chat">
<a class="viewcode-back" href="../modules.html#main.chat">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/chat&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">ChatResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">chat</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">ChatRequest</span><span class="p">,</span> <span class="n">background_tasks</span><span class="p">:</span> <span class="n">BackgroundTasks</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Process a chat request.</span>
<span class="sd">    </span>
<span class="sd">    Args:</span>
<span class="sd">        request: ChatRequest object containing the text query</span>
<span class="sd">        background_tasks: FastAPI BackgroundTasks for async operations</span>
<span class="sd">        </span>
<span class="sd">    Returns:</span>
<span class="sd">        ChatResponse: Response from the system</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="k">await</span> <span class="n">process_chat_request</span><span class="p">(</span><span class="n">request</span><span class="p">,</span> <span class="n">background_tasks</span><span class="p">)</span></div>


<div class="viewcode-block" id="chat_stream">
<a class="viewcode-back" href="../modules.html#main.chat_stream">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/chat/stream&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">chat_stream</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">ChatRequest</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Process a chat request with streaming response.</span>
<span class="sd">    </span>
<span class="sd">    Args:</span>
<span class="sd">        request: ChatRequest object containing the text query</span>
<span class="sd">        </span>
<span class="sd">    Returns:</span>
<span class="sd">        StreamingResponse: Streaming response generator</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Check cache for identical queries</span>
    <span class="n">cached_response</span> <span class="o">=</span> <span class="n">response_cache</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">text</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">cached_response</span><span class="p">:</span>
        <span class="c1"># For cached responses, stream the entire response at once</span>
        <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">cached_generator</span><span class="p">():</span>
            <span class="k">yield</span> <span class="sa">f</span><span class="s2">&quot;data: </span><span class="si">{</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span><span class="s1">&#39;response&#39;</span><span class="p">:</span><span class="w"> </span><span class="n">cached_response</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;cached&#39;</span><span class="p">:</span><span class="w"> </span><span class="kc">True</span><span class="p">})</span><span class="si">}</span><span class="se">\n\n</span><span class="s2">&quot;</span>
            <span class="k">yield</span> <span class="s2">&quot;data: [DONE]</span><span class="se">\n\n</span><span class="s2">&quot;</span>
        
        <span class="k">return</span> <span class="n">StreamingResponse</span><span class="p">(</span>
            <span class="n">cached_generator</span><span class="p">(),</span>
            <span class="n">media_type</span><span class="o">=</span><span class="s2">&quot;text/event-stream&quot;</span>
        <span class="p">)</span>
    
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Perform retrieval</span>
        <span class="n">retrieved_info</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">to_thread</span><span class="p">(</span><span class="n">retriever</span><span class="o">.</span><span class="n">query</span><span class="p">,</span> <span class="n">request</span><span class="o">.</span><span class="n">text</span><span class="p">)</span>
        
        <span class="c1"># Combine input with retrieved context</span>
        <span class="n">enriched_query</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">request</span><span class="o">.</span><span class="n">text</span><span class="si">}</span><span class="s2">&quot;</span>
        <span class="k">if</span> <span class="n">retrieved_info</span><span class="p">:</span>
            <span class="n">enriched_query</span> <span class="o">+=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="se">\n\n</span><span class="s2">Additional context: </span><span class="si">{</span><span class="n">retrieved_info</span><span class="si">}</span><span class="s2">&quot;</span>
        
        <span class="c1"># This is a placeholder for streaming - actual implementation depends on SymbolicAI</span>
        <span class="c1"># Assuming reasoner has a `process_query_stream` method</span>
        <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">generator</span><span class="p">():</span>
            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">reasoner</span><span class="p">,</span> <span class="s1">&#39;process_query_stream&#39;</span><span class="p">):</span>
                <span class="c1"># Use streaming if available</span>
                <span class="k">async</span> <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query_stream</span><span class="p">(</span><span class="n">enriched_query</span><span class="p">):</span>
                    <span class="k">yield</span> <span class="sa">f</span><span class="s2">&quot;data: </span><span class="si">{</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span><span class="s1">&#39;response&#39;</span><span class="p">:</span><span class="w"> </span><span class="n">chunk</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;finished&#39;</span><span class="p">:</span><span class="w"> </span><span class="kc">False</span><span class="p">})</span><span class="si">}</span><span class="se">\n\n</span><span class="s2">&quot;</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># Fall back to non-streaming</span>
                <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">to_thread</span><span class="p">(</span><span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">,</span> <span class="n">enriched_query</span><span class="p">)</span>
                <span class="k">yield</span> <span class="sa">f</span><span class="s2">&quot;data: </span><span class="si">{</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span><span class="s1">&#39;response&#39;</span><span class="p">:</span><span class="w"> </span><span class="n">response</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;finished&#39;</span><span class="p">:</span><span class="w"> </span><span class="kc">True</span><span class="p">})</span><span class="si">}</span><span class="se">\n\n</span><span class="s2">&quot;</span>
            
            <span class="c1"># Signal completion</span>
            <span class="k">yield</span> <span class="s2">&quot;data: [DONE]</span><span class="se">\n\n</span><span class="s2">&quot;</span>
            
            <span class="c1"># Cache the complete response</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">reasoner</span><span class="p">,</span> <span class="s1">&#39;process_query_stream&#39;</span><span class="p">):</span>
                <span class="n">response_cache</span><span class="o">.</span><span class="n">set</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">text</span><span class="p">,</span> <span class="n">response</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">StreamingResponse</span><span class="p">(</span>
            <span class="n">generator</span><span class="p">(),</span>
            <span class="n">media_type</span><span class="o">=</span><span class="s2">&quot;text/event-stream&quot;</span>
        <span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error processing request: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Process a chat request.</span>
<span class="sd">    </span>
<span class="sd">    Args:</span>
<span class="sd">        request: The chat request containing the user&#39;s query</span>
<span class="sd">        background_tasks: FastAPI background tasks handler</span>
<span class="sd">        </span>
<span class="sd">    Returns:</span>
<span class="sd">        ChatResponse: The response to the user&#39;s query</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">query</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">text</span>
    
    <span class="c1"># Check cache first</span>
    <span class="n">cached_response</span> <span class="o">=</span> <span class="n">response_cache</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">query</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">cached_response</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">ChatResponse</span><span class="p">(</span>
            <span class="n">response</span><span class="o">=</span><span class="n">cached_response</span><span class="p">,</span>
            <span class="n">cached</span><span class="o">=</span><span class="kc">True</span>
        <span class="p">)</span>
    
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Process the query</span>
        <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">process_query</span><span class="p">(</span><span class="n">query</span><span class="p">)</span>
        
        <span class="c1"># Cache the response in the background</span>
        <span class="n">background_tasks</span><span class="o">.</span><span class="n">add_task</span><span class="p">(</span><span class="k">lambda</span><span class="p">:</span> <span class="n">response_cache</span><span class="o">.</span><span class="n">set</span><span class="p">(</span><span class="n">query</span><span class="p">,</span> <span class="n">response</span><span class="p">))</span>
        
        <span class="k">return</span> <span class="n">ChatResponse</span><span class="p">(</span>
            <span class="n">response</span><span class="o">=</span><span class="n">response</span><span class="p">,</span>
            <span class="n">cached</span><span class="o">=</span><span class="kc">False</span>
        <span class="p">)</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">))</span></div>



<div class="viewcode-block" id="clean_cache">
<a class="viewcode-back" href="../modules.html#main.clean_cache">[docs]</a>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">clean_cache</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Clean old entries from the response cache.</span>
<span class="sd">    </span>
<span class="sd">    Keeps the cache size manageable by removing oldest entries.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Keep cache size manageable</span>
    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">response_cache</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">100</span><span class="p">:</span>
        <span class="c1"># Remove oldest entries</span>
        <span class="k">for</span> <span class="n">key</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">response_cache</span><span class="o">.</span><span class="n">keys</span><span class="p">())[:</span><span class="mi">50</span><span class="p">]:</span>
            <span class="k">del</span> <span class="n">response_cache</span><span class="p">[</span><span class="n">key</span><span class="p">]</span></div>


<div class="viewcode-block" id="openai_chat_stream">
<a class="viewcode-back" href="../modules.html#main.openai_chat_stream">[docs]</a>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">openai_chat_stream</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">OpenAIChatRequest</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Handle streaming for OpenAI-compatible chat endpoint.</span>
<span class="sd">    </span>
<span class="sd">    Args:</span>
<span class="sd">        request: OpenAIChatRequest object</span>
<span class="sd">        </span>
<span class="sd">    Returns:</span>
<span class="sd">        StreamingResponse: Server-sent events stream</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Received streaming chat request for model: </span><span class="si">{</span><span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    
    <span class="c1"># Extract the last user message</span>
    <span class="n">last_message</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="k">for</span> <span class="n">msg</span> <span class="ow">in</span> <span class="nb">reversed</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">messages</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">msg</span><span class="o">.</span><span class="n">role</span> <span class="o">==</span> <span class="s2">&quot;user&quot;</span><span class="p">:</span>
            <span class="n">last_message</span> <span class="o">=</span> <span class="n">msg</span><span class="o">.</span><span class="n">content</span>
            <span class="k">break</span>
    
    <span class="k">if</span> <span class="ow">not</span> <span class="n">last_message</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;No user message found in streaming request&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;No user message found&quot;</span><span class="p">)</span>
    
    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Processing streaming message: </span><span class="si">{</span><span class="n">last_message</span><span class="p">[:</span><span class="mi">100</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
    
    <span class="c1"># Check cache for identical queries</span>
    <span class="n">cached_response</span> <span class="o">=</span> <span class="n">response_cache</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">last_message</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">cached_response</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Found response in cache for streaming request&quot;</span><span class="p">)</span>
        <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">cached_generator</span><span class="p">():</span>
            <span class="k">yield</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span>
                <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;chatcmpl-</span><span class="si">{</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s2">&quot;object&quot;</span><span class="p">:</span> <span class="s2">&quot;chat.completion.chunk&quot;</span><span class="p">,</span>
                <span class="s2">&quot;created&quot;</span><span class="p">:</span> <span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()),</span>
                <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
                <span class="s2">&quot;choices&quot;</span><span class="p">:</span> <span class="p">[{</span>
                    <span class="s2">&quot;index&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                    <span class="s2">&quot;delta&quot;</span><span class="p">:</span> <span class="p">{</span>
                        <span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;assistant&quot;</span><span class="p">,</span>
                        <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="n">cached_response</span>
                    <span class="p">},</span>
                    <span class="s2">&quot;finish_reason&quot;</span><span class="p">:</span> <span class="kc">None</span>
                <span class="p">}]</span>
            <span class="p">})</span> <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span>
            
            <span class="c1"># Send completion message</span>
            <span class="k">yield</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span>
                <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;chatcmpl-</span><span class="si">{</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s2">&quot;object&quot;</span><span class="p">:</span> <span class="s2">&quot;chat.completion.chunk&quot;</span><span class="p">,</span>
                <span class="s2">&quot;created&quot;</span><span class="p">:</span> <span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()),</span>
                <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
                <span class="s2">&quot;choices&quot;</span><span class="p">:</span> <span class="p">[{</span>
                    <span class="s2">&quot;index&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                    <span class="s2">&quot;delta&quot;</span><span class="p">:</span> <span class="p">{},</span>
                    <span class="s2">&quot;finish_reason&quot;</span><span class="p">:</span> <span class="s2">&quot;stop&quot;</span>
                <span class="p">}]</span>
            <span class="p">})</span> <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span>
        
        <span class="k">return</span> <span class="n">StreamingResponse</span><span class="p">(</span>
            <span class="n">cached_generator</span><span class="p">(),</span>
            <span class="n">media_type</span><span class="o">=</span><span class="s2">&quot;text/event-stream&quot;</span>
        <span class="p">)</span>
    
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Perform retrieval</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Performing retrieval operation for streaming request&quot;</span><span class="p">)</span>
        <span class="n">search_results</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">to_thread</span><span class="p">(</span><span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">,</span> <span class="n">last_message</span><span class="p">)</span>
        
        <span class="c1"># Extract text from search results</span>
        <span class="n">retrieved_info</span> <span class="o">=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">([</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span> <span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">search_results</span><span class="p">])</span> <span class="k">if</span> <span class="n">search_results</span> <span class="k">else</span> <span class="s2">&quot;&quot;</span>
        
        <span class="c1"># Combine input with retrieved context</span>
        <span class="n">enriched_query</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">last_message</span><span class="si">}</span><span class="s2">&quot;</span>
        <span class="k">if</span> <span class="n">retrieved_info</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Retrieved relevant context for streaming request&quot;</span><span class="p">)</span>
            <span class="n">enriched_query</span> <span class="o">+=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="se">\n\n</span><span class="s2">Additional context: </span><span class="si">{</span><span class="n">retrieved_info</span><span class="si">}</span><span class="s2">&quot;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;No relevant context found for streaming request&quot;</span><span class="p">)</span>
        
        <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">generator</span><span class="p">():</span>
            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">reasoner</span><span class="p">,</span> <span class="s1">&#39;process_query_stream&#39;</span><span class="p">):</span>
                <span class="c1"># Use streaming if available</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Using native streaming capability&quot;</span><span class="p">)</span>
                <span class="k">async</span> <span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">process_query_stream</span><span class="p">(</span><span class="n">enriched_query</span><span class="p">):</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Streaming chunk: </span><span class="si">{</span><span class="n">chunk</span><span class="p">[:</span><span class="mi">50</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
                    <span class="k">yield</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span>
                        <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;chatcmpl-</span><span class="si">{</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                        <span class="s2">&quot;object&quot;</span><span class="p">:</span> <span class="s2">&quot;chat.completion.chunk&quot;</span><span class="p">,</span>
                        <span class="s2">&quot;created&quot;</span><span class="p">:</span> <span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()),</span>
                        <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
                        <span class="s2">&quot;choices&quot;</span><span class="p">:</span> <span class="p">[{</span>
                            <span class="s2">&quot;index&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                            <span class="s2">&quot;delta&quot;</span><span class="p">:</span> <span class="p">{</span>
                                <span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;assistant&quot;</span><span class="p">,</span>
                                <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="n">chunk</span>
                            <span class="p">},</span>
                            <span class="s2">&quot;finish_reason&quot;</span><span class="p">:</span> <span class="kc">None</span>
                        <span class="p">}]</span>
                    <span class="p">})</span> <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># Fall back to non-streaming</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Native streaming not available, falling back to single response&quot;</span><span class="p">)</span>
                <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">to_thread</span><span class="p">(</span><span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">,</span> <span class="n">enriched_query</span><span class="p">)</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Generated full response: </span><span class="si">{</span><span class="n">response</span><span class="p">[:</span><span class="mi">100</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
                <span class="k">yield</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span>
                    <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;chatcmpl-</span><span class="si">{</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                    <span class="s2">&quot;object&quot;</span><span class="p">:</span> <span class="s2">&quot;chat.completion.chunk&quot;</span><span class="p">,</span>
                    <span class="s2">&quot;created&quot;</span><span class="p">:</span> <span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()),</span>
                    <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
                    <span class="s2">&quot;choices&quot;</span><span class="p">:</span> <span class="p">[{</span>
                        <span class="s2">&quot;index&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                        <span class="s2">&quot;delta&quot;</span><span class="p">:</span> <span class="p">{</span>
                            <span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;assistant&quot;</span><span class="p">,</span>
                            <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="n">response</span>
                        <span class="p">},</span>
                        <span class="s2">&quot;finish_reason&quot;</span><span class="p">:</span> <span class="kc">None</span>
                    <span class="p">}]</span>
                <span class="p">})</span> <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span>
            
            <span class="c1"># Send completion message</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Sending completion message&quot;</span><span class="p">)</span>
            <span class="k">yield</span> <span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span>
                <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;chatcmpl-</span><span class="si">{</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s2">&quot;object&quot;</span><span class="p">:</span> <span class="s2">&quot;chat.completion.chunk&quot;</span><span class="p">,</span>
                <span class="s2">&quot;created&quot;</span><span class="p">:</span> <span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()),</span>
                <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
                <span class="s2">&quot;choices&quot;</span><span class="p">:</span> <span class="p">[{</span>
                    <span class="s2">&quot;index&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                    <span class="s2">&quot;delta&quot;</span><span class="p">:</span> <span class="p">{},</span>
                    <span class="s2">&quot;finish_reason&quot;</span><span class="p">:</span> <span class="s2">&quot;stop&quot;</span>
                <span class="p">}]</span>
            <span class="p">})</span> <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span>
            
            <span class="c1"># Cache the complete response if not using streaming</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">reasoner</span><span class="p">,</span> <span class="s1">&#39;process_query_stream&#39;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Caching non-streaming response&quot;</span><span class="p">)</span>
                <span class="n">response_cache</span><span class="o">.</span><span class="n">set</span><span class="p">(</span><span class="n">last_message</span><span class="p">,</span> <span class="n">response</span><span class="p">)</span>
            
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Streaming response completed successfully&quot;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">StreamingResponse</span><span class="p">(</span>
            <span class="n">generator</span><span class="p">(),</span>
            <span class="n">media_type</span><span class="o">=</span><span class="s2">&quot;text/event-stream&quot;</span>
        <span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error processing streaming request: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error processing request: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="openai_chat">
<a class="viewcode-back" href="../modules.html#main.openai_chat">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/v1/chat/completions&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">OpenAIChatResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">openai_chat</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">OpenAIChatRequest</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;OpenAI-compatible chat endpoint.</span>
<span class="sd">    </span>
<span class="sd">    Args:</span>
<span class="sd">        request: OpenAIChatRequest object</span>
<span class="sd">        </span>
<span class="sd">    Returns:</span>
<span class="sd">        OpenAIChatResponse object or StreamingResponse if streaming</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">request_id</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;chat-</span><span class="si">{</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span>
    <span class="n">monitor</span><span class="o">.</span><span class="n">start_request</span><span class="p">(</span><span class="n">request_id</span><span class="p">,</span> <span class="s2">&quot;/v1/chat/completions&quot;</span><span class="p">)</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Received chat request for model: </span><span class="si">{</span><span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    
    <span class="c1"># Handle streaming requests</span>
    <span class="k">if</span> <span class="n">request</span><span class="o">.</span><span class="n">stream</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Request requires streaming, forwarding to streaming endpoint&quot;</span><span class="p">)</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">end_request</span><span class="p">(</span><span class="n">request_id</span><span class="p">)</span>
        <span class="k">return</span> <span class="k">await</span> <span class="n">openai_chat_stream</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
    
    <span class="c1"># Extract the last user message</span>
    <span class="n">last_message</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="k">for</span> <span class="n">msg</span> <span class="ow">in</span> <span class="nb">reversed</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">messages</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">msg</span><span class="o">.</span><span class="n">role</span> <span class="o">==</span> <span class="s2">&quot;user&quot;</span><span class="p">:</span>
            <span class="n">last_message</span> <span class="o">=</span> <span class="n">msg</span><span class="o">.</span><span class="n">content</span>
            <span class="k">break</span>
    
    <span class="k">if</span> <span class="ow">not</span> <span class="n">last_message</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;No user message found in request&quot;</span><span class="p">)</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">end_request</span><span class="p">(</span><span class="n">request_id</span><span class="p">,</span> <span class="n">error</span><span class="o">=</span><span class="s2">&quot;No user message found&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;No user message found&quot;</span><span class="p">)</span>
    
    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Processing message: </span><span class="si">{</span><span class="n">last_message</span><span class="p">[:</span><span class="mi">100</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
    
    <span class="c1"># Check cache for identical queries</span>
    <span class="n">cached_response</span> <span class="o">=</span> <span class="n">response_cache</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">last_message</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">cached_response</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Found response in cache&quot;</span><span class="p">)</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">record_cache_hit</span><span class="p">(</span><span class="n">request_id</span><span class="p">)</span>
        <span class="n">response_obj</span> <span class="o">=</span> <span class="n">OpenAIChatResponse</span><span class="p">(</span>
            <span class="nb">id</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;chatcmpl-</span><span class="si">{</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="n">created</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()),</span>
            <span class="n">model</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
            <span class="n">choices</span><span class="o">=</span><span class="p">[</span>
                <span class="n">OpenAIChatChoice</span><span class="p">(</span>
                    <span class="n">index</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                    <span class="n">message</span><span class="o">=</span><span class="n">OpenAIMessage</span><span class="p">(</span>
                        <span class="n">role</span><span class="o">=</span><span class="s2">&quot;assistant&quot;</span><span class="p">,</span>
                        <span class="n">content</span><span class="o">=</span><span class="n">cached_response</span>
                    <span class="p">),</span>
                    <span class="n">finish_reason</span><span class="o">=</span><span class="s2">&quot;stop&quot;</span>
                <span class="p">)</span>
            <span class="p">],</span>
            <span class="n">usage</span><span class="o">=</span><span class="p">{</span>
                <span class="s2">&quot;prompt_tokens&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                <span class="s2">&quot;completion_tokens&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                <span class="s2">&quot;total_tokens&quot;</span><span class="p">:</span> <span class="mi">0</span>
            <span class="p">}</span>
        <span class="p">)</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">end_request</span><span class="p">(</span><span class="n">request_id</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">response_obj</span>
    
    <span class="n">monitor</span><span class="o">.</span><span class="n">record_cache_miss</span><span class="p">(</span><span class="n">request_id</span><span class="p">)</span>
    
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Perform retrieval</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Performing retrieval operation&quot;</span><span class="p">)</span>
        <span class="n">retrieval_start</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
        <span class="n">search_results</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">to_thread</span><span class="p">(</span><span class="n">retriever</span><span class="o">.</span><span class="n">search</span><span class="p">,</span> <span class="n">last_message</span><span class="p">)</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">record_retrieval_time</span><span class="p">(</span><span class="n">request_id</span><span class="p">,</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">retrieval_start</span><span class="p">)</span>
        
        <span class="c1"># Extract text from search results</span>
        <span class="n">retrieved_info</span> <span class="o">=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">([</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span> <span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">search_results</span><span class="p">])</span> <span class="k">if</span> <span class="n">search_results</span> <span class="k">else</span> <span class="s2">&quot;&quot;</span>
        
        <span class="c1"># Combine input with retrieved context</span>
        <span class="n">enriched_query</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">last_message</span><span class="si">}</span><span class="s2">&quot;</span>
        <span class="k">if</span> <span class="n">retrieved_info</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Retrieved relevant context&quot;</span><span class="p">)</span>
            <span class="n">enriched_query</span> <span class="o">+=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="se">\n\n</span><span class="s2">Additional context: </span><span class="si">{</span><span class="n">retrieved_info</span><span class="si">}</span><span class="s2">&quot;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;No relevant context found&quot;</span><span class="p">)</span>
        
        <span class="c1"># Process with symbolic reasoning</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Processing with symbolic reasoning&quot;</span><span class="p">)</span>
        <span class="n">reasoning_start</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
        <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">to_thread</span><span class="p">(</span><span class="n">reasoner</span><span class="o">.</span><span class="n">process_query</span><span class="p">,</span> <span class="n">enriched_query</span><span class="p">)</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">record_reasoning_time</span><span class="p">(</span><span class="n">request_id</span><span class="p">,</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">reasoning_start</span><span class="p">)</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Symbolic reasoning completed&quot;</span><span class="p">)</span>
        
        <span class="c1"># Cache the response for future use</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Caching response&quot;</span><span class="p">)</span>
        <span class="n">response_cache</span><span class="o">.</span><span class="n">set</span><span class="p">(</span><span class="n">last_message</span><span class="p">,</span> <span class="n">response</span><span class="p">)</span>
        
        <span class="c1"># Calculate token counts</span>
        <span class="n">total_tokens</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">enriched_query</span><span class="o">.</span><span class="n">split</span><span class="p">())</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">split</span><span class="p">())</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">record_token_count</span><span class="p">(</span><span class="n">request_id</span><span class="p">,</span> <span class="n">total_tokens</span><span class="p">)</span>
        
        <span class="n">response_obj</span> <span class="o">=</span> <span class="n">OpenAIChatResponse</span><span class="p">(</span>
            <span class="nb">id</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;chatcmpl-</span><span class="si">{</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="n">created</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()),</span>
            <span class="n">model</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
            <span class="n">choices</span><span class="o">=</span><span class="p">[</span>
                <span class="n">OpenAIChatChoice</span><span class="p">(</span>
                    <span class="n">index</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                    <span class="n">message</span><span class="o">=</span><span class="n">OpenAIMessage</span><span class="p">(</span>
                        <span class="n">role</span><span class="o">=</span><span class="s2">&quot;assistant&quot;</span><span class="p">,</span>
                        <span class="n">content</span><span class="o">=</span><span class="n">response</span>
                    <span class="p">),</span>
                    <span class="n">finish_reason</span><span class="o">=</span><span class="s2">&quot;stop&quot;</span>
                <span class="p">)</span>
            <span class="p">],</span>
            <span class="n">usage</span><span class="o">=</span><span class="p">{</span>
                <span class="s2">&quot;prompt_tokens&quot;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">enriched_query</span><span class="o">.</span><span class="n">split</span><span class="p">()),</span>
                <span class="s2">&quot;completion_tokens&quot;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">split</span><span class="p">()),</span>
                <span class="s2">&quot;total_tokens&quot;</span><span class="p">:</span> <span class="n">total_tokens</span>
            <span class="p">}</span>
        <span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Successfully generated response&quot;</span><span class="p">)</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">end_request</span><span class="p">(</span><span class="n">request_id</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">response_obj</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error processing request: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="n">monitor</span><span class="o">.</span><span class="n">end_request</span><span class="p">(</span><span class="n">request_id</span><span class="p">,</span> <span class="n">error</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">))</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error processing request: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="performance_stats">
<a class="viewcode-back" href="../modules.html#main.performance_stats">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/performance&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">performance_stats</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get performance statistics for the system.</span>
<span class="sd">    </span>
<span class="sd">    Returns:</span>
<span class="sd">        dict: Performance statistics</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Retrieving performance statistics&quot;</span><span class="p">)</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Get cache info</span>
        <span class="n">cache_size</span> <span class="o">=</span> <span class="n">response_cache</span><span class="o">.</span><span class="n">size</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Current cache size: </span><span class="si">{</span><span class="n">cache_size</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="c1"># Get GPU info</span>
        <span class="n">gpu_available</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">()</span>
        <span class="n">gpu_name</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">get_device_name</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span> <span class="k">if</span> <span class="n">gpu_available</span> <span class="k">else</span> <span class="kc">None</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU status - Available: </span><span class="si">{</span><span class="n">gpu_available</span><span class="si">}</span><span class="s2">, Name: </span><span class="si">{</span><span class="n">gpu_name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="c1"># Get monitoring metrics</span>
        <span class="n">monitoring_metrics</span> <span class="o">=</span> <span class="n">monitor</span><span class="o">.</span><span class="n">get_recent_metrics</span><span class="p">(</span><span class="n">minutes</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>
        
        <span class="n">stats</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;cache&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;size&quot;</span><span class="p">:</span> <span class="n">cache_size</span><span class="p">,</span>
                <span class="s2">&quot;max_size&quot;</span><span class="p">:</span> <span class="n">response_cache</span><span class="o">.</span><span class="n">max_size</span><span class="p">,</span>
                <span class="s2">&quot;hits&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;cache&quot;</span><span class="p">][</span><span class="s2">&quot;hits&quot;</span><span class="p">],</span>
                <span class="s2">&quot;misses&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;cache&quot;</span><span class="p">][</span><span class="s2">&quot;misses&quot;</span><span class="p">],</span>
                <span class="s2">&quot;hit_rate&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;cache&quot;</span><span class="p">][</span><span class="s2">&quot;hit_rate&quot;</span><span class="p">]</span>
            <span class="p">},</span>
            <span class="s2">&quot;system&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;gpu_available&quot;</span><span class="p">:</span> <span class="n">gpu_available</span><span class="p">,</span>
                <span class="s2">&quot;gpu_name&quot;</span><span class="p">:</span> <span class="n">gpu_name</span><span class="p">,</span>
                <span class="s2">&quot;cpu_percent&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;system&quot;</span><span class="p">][</span><span class="s2">&quot;cpu_percent&quot;</span><span class="p">],</span>
                <span class="s2">&quot;memory_percent&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;system&quot;</span><span class="p">][</span><span class="s2">&quot;memory_percent&quot;</span><span class="p">],</span>
                <span class="s2">&quot;active_requests&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;system&quot;</span><span class="p">][</span><span class="s2">&quot;active_requests&quot;</span><span class="p">],</span>
                <span class="s2">&quot;reasoner&quot;</span><span class="p">:</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">(),</span>
                <span class="s2">&quot;retriever&quot;</span><span class="p">:</span> <span class="n">retriever</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">()</span>
            <span class="p">},</span>
            <span class="s2">&quot;requests&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;total&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;requests&quot;</span><span class="p">][</span><span class="s2">&quot;total&quot;</span><span class="p">],</span>
                <span class="s2">&quot;avg_duration&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;requests&quot;</span><span class="p">][</span><span class="s2">&quot;avg_duration&quot;</span><span class="p">],</span>
                <span class="s2">&quot;error_rate&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;requests&quot;</span><span class="p">][</span><span class="s2">&quot;error_rate&quot;</span><span class="p">],</span>
                <span class="s2">&quot;cache_hit_rate&quot;</span><span class="p">:</span> <span class="n">monitoring_metrics</span><span class="p">[</span><span class="s2">&quot;requests&quot;</span><span class="p">][</span><span class="s2">&quot;cache_hit_rate&quot;</span><span class="p">]</span>
            <span class="p">}</span>
        <span class="p">}</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Successfully retrieved performance statistics&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">stats</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving performance statistics: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">))</span></div>


<div class="viewcode-block" id="system_info">
<a class="viewcode-back" href="../modules.html#main.system_info">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/system/info&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">system_info</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get system configuration information.&quot;&quot;&quot;</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Retrieving system configuration information&quot;</span><span class="p">)</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">gpu_optimized</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">()</span> <span class="ow">and</span> <span class="n">torch</span><span class="o">.</span><span class="n">backends</span><span class="o">.</span><span class="n">cudnn</span><span class="o">.</span><span class="n">benchmark</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU optimization status: </span><span class="si">{</span><span class="n">gpu_optimized</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">reasoner_info</span> <span class="o">=</span> <span class="n">reasoner</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Retrieved reasoner system info&quot;</span><span class="p">)</span>
        
        <span class="n">retriever_info</span> <span class="o">=</span> <span class="n">retriever</span><span class="o">.</span><span class="n">get_system_info</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="s2">&quot;Retrieved retriever system info&quot;</span><span class="p">)</span>
        
        <span class="n">info</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;reasoner&quot;</span><span class="p">:</span> <span class="n">reasoner_info</span><span class="p">,</span>
            <span class="s2">&quot;retriever&quot;</span><span class="p">:</span> <span class="n">retriever_info</span><span class="p">,</span>
            <span class="s2">&quot;gpu_optimized&quot;</span><span class="p">:</span> <span class="n">gpu_optimized</span>
        <span class="p">}</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Successfully retrieved system configuration&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">info</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving system configuration: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">))</span></div>


<div class="viewcode-block" id="shutdown_event">
<a class="viewcode-back" href="../modules.html#main.shutdown_event">[docs]</a>
<span class="nd">@app</span><span class="o">.</span><span class="n">on_event</span><span class="p">(</span><span class="s2">&quot;shutdown&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">shutdown_event</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Perform cleanup on shutdown.&quot;&quot;&quot;</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Application shutting down&quot;</span><span class="p">)</span>
    <span class="n">monitor</span><span class="o">.</span><span class="n">shutdown</span><span class="p">()</span></div>


<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="c1"># Run the API with development settings</span>
    <span class="n">uvicorn</span><span class="o">.</span><span class="n">run</span><span class="p">(</span>
        <span class="s2">&quot;main:app&quot;</span><span class="p">,</span>    <span class="c1"># Import string format</span>
        <span class="n">host</span><span class="o">=</span><span class="s2">&quot;127.0.0.1&quot;</span><span class="p">,</span>  <span class="c1"># Local connections only for development</span>
        <span class="n">port</span><span class="o">=</span><span class="mi">8080</span><span class="p">,</span>        <span class="c1"># Alternative port</span>
        <span class="n">reload</span><span class="o">=</span><span class="kc">True</span>       <span class="c1"># Enable auto-reload during development</span>
    <span class="p">)</span>
</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>