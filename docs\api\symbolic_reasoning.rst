Symbolic Reasoning Module
==========================

.. automodule:: symbolic_reasoning
   :members:
   :undoc-members:
   :show-inheritance:

Overview
--------

The symbolic reasoning module provides advanced logical reasoning capabilities
using various backend engines including local models, OpenAI, and Anthropic.

Key Features:
* Multiple reasoning engine support
* GPU acceleration
* Batch processing
* Caching for improved performance
* Comprehensive error handling

SymbolicReasoner Class
----------------------

.. autoclass:: symbolic_reasoning.SymbolicReasoner
   :members:
   :undoc-members:
   :show-inheritance:

The main class for symbolic reasoning operations.

Initialization
~~~~~~~~~~~~~~

.. automethod:: symbolic_reasoning.SymbolicReasoner.__init__

Core Methods
~~~~~~~~~~~~

.. automethod:: symbolic_reasoning.SymbolicReasoner.process_query
.. automethod:: symbolic_reasoning.SymbolicReasoner.batch_process_queries
.. automethod:: symbolic_reasoning.SymbolicReasoner.get_system_info

Configuration
-------------

The symbolic reasoner can be configured with the following parameters:

* ``engine``: Reasoning engine ("local", "openai", "anthropic")
* ``model``: Model name (depends on engine)
* ``use_gpu``: Enable GPU acceleration (default: True)
* ``max_tokens``: Maximum tokens per response
* ``temperature``: Response randomness (0.0-1.0)

Supported Engines
-----------------

Local Engine
~~~~~~~~~~~~

Uses local language models for reasoning:

* **Model**: Configurable local model (default: "llama")
* **GPU Support**: Full GPU acceleration
* **Privacy**: All processing done locally
* **Performance**: Optimized for low latency

OpenAI Engine
~~~~~~~~~~~~~

Uses OpenAI's API for reasoning:

* **Models**: GPT-3.5-turbo, GPT-4, etc.
* **API Key**: Required via ``OPENAI_API_KEY`` environment variable
* **Rate Limits**: Managed automatically
* **Cost**: Per-token pricing

Anthropic Engine
~~~~~~~~~~~~~~~~

Uses Anthropic's Claude models:

* **Models**: Claude-3-sonnet, Claude-3-opus, etc.
* **API Key**: Required via ``ANTHROPIC_API_KEY`` environment variable
* **Safety**: Built-in safety features
* **Performance**: High-quality reasoning

Example Usage
-------------

Basic Usage
~~~~~~~~~~~

.. code-block:: python

   from symbolic_reasoning import SymbolicReasoner
   
   # Initialize reasoner
   reasoner = SymbolicReasoner(
       engine="local",
       model="llama",
       use_gpu=True
   )
   
   # Process a query
   response = reasoner.process_query(
       "What are the key principles of symbolic reasoning?"
   )
   print(response)

Batch Processing
~~~~~~~~~~~~~~~~

.. code-block:: python

   # Process multiple queries
   queries = [
       "What is symbolic AI?",
       "How does neural-symbolic integration work?",
       "What are the benefits of hybrid AI systems?"
   ]
   
   responses = reasoner.batch_process_queries(queries)
   for query, response in zip(queries, responses):
       print(f"Q: {query}")
       print(f"A: {response}\n")

Advanced Configuration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Advanced configuration
   reasoner = SymbolicReasoner(
       engine="openai",
       model="gpt-4",
       use_gpu=False,
       max_tokens=500,
       temperature=0.3
   )
   
   # Get system information
   info = reasoner.get_system_info()
   print(f"Engine: {info['engine']}")
   print(f"Model: {info['model']}")
   print(f"GPU Available: {info['gpu_available']}")

Error Handling
--------------

The module provides comprehensive error handling:

.. code-block:: python

   from symbolic_reasoning import SymbolicReasoner
   from exceptions import ReasoningError
   
   try:
       reasoner = SymbolicReasoner()
       response = reasoner.process_query("Complex reasoning query")
   except ReasoningError as e:
       print(f"Reasoning failed: {e.message}")
       print(f"Error code: {e.error_code}")
       print(f"Details: {e.details}")

Performance Considerations
--------------------------

GPU Usage
~~~~~~~~~

* Enable GPU acceleration for better performance with local models
* GPU memory is managed automatically with context managers
* Fallback to CPU if GPU is not available

Caching
~~~~~~~

* Responses are cached to improve performance
* Cache key includes query content and model parameters
* Configurable TTL and cache size limits

Batch Processing
~~~~~~~~~~~~~~~~

* Use batch processing for multiple queries to improve throughput
* Automatic batching optimizes GPU utilization
* Progress tracking for long-running batch operations
