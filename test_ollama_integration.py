#!/usr/bin/env python3
"""
Test script to verify Ollama integration with gemma3n:e2b model.
This script tests the basic functionality before running the full test suite.
"""

import os
import sys
import asyncio
import time

# Set test environment
os.environ['APP_ENVIRONMENT'] = 'test'

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    import ollama
    from symbolic_reasoning import SymbolicReasoner
    from exceptions import ReasoningError, ConfigurationError
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


async def test_ollama_connection():
    """Test basic Ollama connection."""
    print("\n🔍 Testing Ollama connection...")
    
    try:
        client = ollama.AsyncClient()
        models = await client.list()
        print(f"✅ Ollama is running with {len(models.get('models', []))} models")
        
        # Check if gemma3n:e2b is available
        model_names = [model['name'] for model in models.get('models', [])]
        if 'gemma3n:e2b' in model_names:
            print("✅ gemma3n:e2b model is available")
            return True
        else:
            print(f"❌ gemma3n:e2b model not found. Available models: {model_names}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False


async def test_symbolic_reasoner():
    """Test SymbolicReasoner with Ollama."""
    print("\n🧠 Testing SymbolicReasoner with Ollama...")
    
    try:
        # Initialize reasoner with Ollama and gemma3n:e2b
        reasoner = SymbolicReasoner(
            engine="ollama",
            model="gemma3n:e2b",
            use_gpu=False
        )
        print("✅ SymbolicReasoner initialized successfully")
        
        # Test basic reasoning
        test_queries = [
            "What is 2 + 2?",
            "Explain symbolic reasoning in one sentence.",
            "If A implies B and B implies C, what can we conclude about A and C?"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Test {i}: {query}")
            start_time = time.time()
            
            try:
                response = await reasoner.process_query(query)
                duration = time.time() - start_time
                
                print(f"✅ Response ({duration:.2f}s): {response[:100]}...")
                
            except Exception as e:
                print(f"❌ Query failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ SymbolicReasoner test failed: {e}")
        return False


async def test_performance():
    """Test performance with multiple concurrent requests."""
    print("\n⚡ Testing performance with concurrent requests...")
    
    try:
        reasoner = SymbolicReasoner(
            engine="ollama",
            model="gemma3n:e2b",
            use_gpu=False
        )
        
        # Test concurrent requests
        queries = [
            "What is artificial intelligence?",
            "Explain machine learning briefly.",
            "What is the difference between AI and ML?"
        ]
        
        start_time = time.time()
        
        # Run queries concurrently
        tasks = [reasoner.process_query(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        duration = time.time() - start_time
        
        # Check results
        successful = sum(1 for result in results if not isinstance(result, Exception))
        print(f"✅ Processed {successful}/{len(queries)} queries in {duration:.2f}s")
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"❌ Query {i+1} failed: {result}")
            else:
                print(f"✅ Query {i+1}: {result[:50]}...")
        
        return successful == len(queries)
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Ollama Integration Tests with gemma3n:e2b")
    print("=" * 60)
    
    tests = [
        ("Ollama Connection", test_ollama_connection),
        ("Symbolic Reasoner", test_symbolic_reasoner),
        ("Performance Test", test_performance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Ollama integration is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
