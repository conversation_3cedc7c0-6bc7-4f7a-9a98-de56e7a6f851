# Windows Environment Setup Script for Symbolic Language Model
# This script automates the setup of a Conda environment with FAISS-GPU for Windows users

Write-Host "========================================="
Write-Host "Symbolic Language Model Environment Setup"
Write-Host "========================================="

# Define environment name and Python version
$ENV_NAME = "symblang"
$PYTHON_VERSION = "3.10"
$PYTORCH_VERSION = "2.4.0"

# Step 1: Check for Conda installation
Write-Host "[1/7] Checking for Conda installation..."
$condaPath = $null

# Try to find conda in common locations
$possiblePaths = @(
    "$env:USERPROFILE\miniconda3\Scripts\conda.exe",
    "$env:USERPROFILE\Anaconda3\Scripts\conda.exe",
    "$env:LOCALAPPDATA\miniconda3\Scripts\conda.exe",
    "$env:LOCALAPPDATA\Anaconda3\Scripts\conda.exe",
    "$env:ProgramFiles\miniconda3\Scripts\conda.exe",
    "$env:ProgramFiles\Anaconda3\Scripts\conda.exe"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $condaPath = $path
        break
    }
}

if ($null -eq $condaPath) {
    Write-Host "Conda not found! Please install Miniconda from: https://docs.conda.io/en/latest/miniconda.html"
    Write-Host "After installation, run this script again."
    exit 1
} else {
    Write-Host "Found Conda at: $condaPath"
}

# Add conda to the path for this session
$condaDir = Split-Path -Parent (Split-Path -Parent $condaPath)
$env:Path = "$condaDir;$condaDir\Scripts;$condaDir\Library\bin;$env:Path"

# Step 2: Check for Visual C++ Redistributable
Write-Host "[2/7] Checking for Microsoft Visual C++ Redistributable..."

$vcRedistInstalled = $false

# Check 64-bit registry for VC++ Redist 2015-2022
$regPaths = @(
    "HKLM:\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64",
    "HKLM:\SOFTWARE\Classes\Installer\Dependencies\Microsoft.VS.VC_RuntimeMinimum_x64,v14"
)

foreach ($regPath in $regPaths) {
    if (Test-Path $regPath) {
        $vcRedistInstalled = $true
        break
    }
}

if (-not $vcRedistInstalled) {
    Write-Host "Microsoft Visual C++ Redistributable not found!"
    Write-Host "Please download and install from: https://aka.ms/vs/17/release/vc_redist.x64.exe"
    Write-Host "After installation, run this script again."
    exit 1
} else {
    Write-Host "Microsoft Visual C++ Redistributable is installed."
}

# Step 3: Create a Conda environment
Write-Host "[3/7] Creating Conda environment '$ENV_NAME' with Python $PYTHON_VERSION..."

# Check if environment already exists
$envExists = conda env list | Select-String -Pattern "^$ENV_NAME\s"

if ($null -eq $envExists) {
    & conda create -n $ENV_NAME python=$PYTHON_VERSION -y
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to create Conda environment! Please check your Conda installation."
        exit 1
    }
} else {
    Write-Host "Environment '$ENV_NAME' already exists. Continuing with setup..."
}

# Step 4: Activate environment and install PyTorch with CUDA
Write-Host "[4/7] Installing PyTorch with CUDA support..."
& conda activate $ENV_NAME
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to activate Conda environment! Please check your Conda installation."
    exit 1
}

& conda install -c pytorch -c nvidia pytorch=$PYTORCH_VERSION torchvision torchaudio cudatoolkit -y
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install PyTorch! Please check your internet connection and try again."
    exit 1
}

# Step 5: Install FAISS-GPU from conda-forge
Write-Host "[5/7] Installing FAISS-GPU from conda-forge..."
& conda install -c conda-forge faiss-gpu -y
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install FAISS-GPU! Please check your internet connection and try again."
    exit 1
}

# Step 6: Install remaining dependencies
Write-Host "[6/7] Installing remaining dependencies..."
$currentDir = Get-Location
cd (Split-Path -Parent $currentDir)
& pip install -r requirements.txt
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install some dependencies. Please check the error messages above."
} else {
    Write-Host "Successfully installed dependencies."
}

# Step 7: Verify installation
Write-Host "[7/7] Verifying installation..."

# Create a temporary verification script
$verifyScript = @"
import sys
import torch
import faiss

print("Python version:", sys.version)
print("PyTorch version:", torch.__version__)
print("CUDA available:", torch.cuda.is_available())
if torch.cuda.is_available():
    print("CUDA version:", torch.version.cuda)
    print("GPU device:", torch.cuda.get_device_name(0))

print("\nFAISS version:", faiss.__version__)
try:
    faiss_gpu_resources = faiss.StandardGpuResources()
    print("FAISS-GPU resources created successfully!")
    
    # Create a small index on GPU to test
    dimension = 64
    index = faiss.IndexFlatL2(dimension)
    gpu_index = faiss.index_cpu_to_gpu(faiss_gpu_resources, 0, index)
    
    # Add some vectors
    vectors = torch.randn(100, dimension).numpy().astype('float32')
    gpu_index.add(vectors)
    
    print("FAISS-GPU is working correctly!")
except Exception as e:
    print("Error testing FAISS-GPU:", str(e))
"@

$verifyScript | Out-File -FilePath "verify_install.py" -Encoding utf8

# Run the verification script
Write-Host "Running verification..."
& python verify_install.py
if ($LASTEXITCODE -ne 0) {
    Write-Host "There were issues with the installation. Please review the error messages above."
} else {
    Write-Host "Installation verified successfully!"
}

# Clean up
Remove-Item -Path "verify_install.py" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "========================================="
Write-Host "Setup Complete!"
Write-Host "========================================="
Write-Host "To activate this environment, run:"
Write-Host "conda activate $ENV_NAME"
Write-Host ""
Write-Host "To verify FAISS-GPU is working in your projects:"
Write-Host "python scripts/verify_faiss_gpu.py"
Write-Host ""
Write-Host "For troubleshooting, see the README.md file."
