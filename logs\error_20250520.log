2025-05-20 00:41:49,879 - main - ERROR - Error processing request: 'MockRetriever' object has no attribute 'query'
Traceback (most recent call last):
  File "\\WDMyCloud\Marshall\Symbolic Language Model\src\main.py", line 699, in openai_chat
    retrieved_info = await asyncio.to_thread(retriever.query, last_message)
AttributeError: 'MockRetriever' object has no attribute 'query'
2025-05-20 00:41:50,047 - main - ERROR - Error processing streaming request: 'MockRetriever' object has no attribute 'query'
Traceback (most recent call last):
  File "\\WDMyCloud\Marshall\Symbolic Language Model\src\main.py", line 551, in openai_chat_stream
    retrieved_info = await asyncio.to_thread(retriever.query, last_message)
AttributeError: 'MockRetriever' object has no attribute 'query'
2025-05-20 04:06:08,151 - main - ERROR - Error processing request: 'MockRetriever' object has no attribute 'query'
Traceback (most recent call last):
  File "\\WDMyCloud\Marshall\Symbolic Language Model\src\main.py", line 699, in openai_chat
    retrieved_info = await asyncio.to_thread(retriever.query, last_message)
AttributeError: 'MockRetriever' object has no attribute 'query'
2025-05-20 04:06:08,279 - main - ERROR - Error processing streaming request: 'MockRetriever' object has no attribute 'query'
Traceback (most recent call last):
  File "\\WDMyCloud\Marshall\Symbolic Language Model\src\main.py", line 551, in openai_chat_stream
    retrieved_info = await asyncio.to_thread(retriever.query, last_message)
AttributeError: 'MockRetriever' object has no attribute 'query'
