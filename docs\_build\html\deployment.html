

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Deployment Guide &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=39bd3b11" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=01f34227"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Architecture Overview" href="architecture.html" />
    <link rel="prev" title="API Reference" href="api_reference.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="getting_started.html">Getting Started</a></li>
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="api_reference.html">API Reference</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Deployment Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#deployment-options">Deployment Options</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#local-development">Local Development</a></li>
<li class="toctree-l3"><a class="reference internal" href="#single-server-deployment">Single Server Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="#docker-deployment">Docker Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="#docker-compose">Docker Compose</a></li>
<li class="toctree-l3"><a class="reference internal" href="#kubernetes-deployment">Kubernetes Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#production-configuration">Production Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#environment-variables">Environment Variables</a></li>
<li class="toctree-l3"><a class="reference internal" href="#database-configuration">Database Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#load-balancing">Load Balancing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#nginx-configuration">Nginx Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#haproxy-configuration">HAProxy Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#monitoring-and-observability">Monitoring and Observability</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#prometheus-metrics">Prometheus Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="#grafana-dashboard">Grafana Dashboard</a></li>
<li class="toctree-l3"><a class="reference internal" href="#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security-hardening">Security Hardening</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#ssl-tls-configuration">SSL/TLS Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#api-security">API Security</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#auto-scaling">Auto-scaling</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#kubernetes-hpa">Kubernetes HPA</a></li>
<li class="toctree-l3"><a class="reference internal" href="#docker-swarm">Docker Swarm</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#backup-and-recovery">Backup and Recovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#data-backup">Data Backup</a></li>
<li class="toctree-l3"><a class="reference internal" href="#disaster-recovery">Disaster Recovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-tuning">Performance Tuning</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture.html">Architecture Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Deployment Guide</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/deployment.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="deployment-guide">
<h1>Deployment Guide<a class="headerlink" href="#deployment-guide" title="Link to this heading"></a></h1>
<p>This guide covers deploying the Neural Symbolic Language Model in production environments.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Neural Symbolic Language Model is designed for production deployment with:</p>
<ul class="simple">
<li><p><strong>Containerized deployment</strong> with Docker</p></li>
<li><p><strong>Kubernetes orchestration</strong> support</p></li>
<li><p><strong>Load balancing</strong> and auto-scaling</p></li>
<li><p><strong>Monitoring and observability</strong> integration</p></li>
<li><p><strong>Security hardening</strong> for production use</p></li>
</ul>
</section>
<section id="deployment-options">
<h2>Deployment Options<a class="headerlink" href="#deployment-options" title="Link to this heading"></a></h2>
<section id="local-development">
<h3>Local Development<a class="headerlink" href="#local-development" title="Link to this heading"></a></h3>
<p>For development and testing:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start development server</span>
python<span class="w"> </span>src/main.py

<span class="c1"># With auto-reload</span>
uvicorn<span class="w"> </span>main:app<span class="w"> </span>--reload<span class="w"> </span>--host<span class="w"> </span><span class="m">0</span>.0.0.0<span class="w"> </span>--port<span class="w"> </span><span class="m">8000</span>
</pre></div>
</div>
</section>
<section id="single-server-deployment">
<h3>Single Server Deployment<a class="headerlink" href="#single-server-deployment" title="Link to this heading"></a></h3>
<p>For small-scale production deployments:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Production server with multiple workers</span>
uvicorn<span class="w"> </span>main:app<span class="w"> </span>--host<span class="w"> </span><span class="m">0</span>.0.0.0<span class="w"> </span>--port<span class="w"> </span><span class="m">8000</span><span class="w"> </span>--workers<span class="w"> </span><span class="m">4</span>
</pre></div>
</div>
</section>
<section id="docker-deployment">
<h3>Docker Deployment<a class="headerlink" href="#docker-deployment" title="Link to this heading"></a></h3>
<p><strong>Dockerfile:</strong></p>
<div class="highlight-dockerfile notranslate"><div class="highlight"><pre><span></span><span class="k">FROM</span><span class="w"> </span><span class="s">python:3.10-slim</span>

<span class="c"># Set working directory</span>
<span class="k">WORKDIR</span><span class="w"> </span><span class="s">/app</span>

<span class="c"># Install system dependencies</span>
<span class="k">RUN</span><span class="w"> </span>apt-get<span class="w"> </span>update<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span><span class="se">\</span>
<span class="w">    </span>build-essential<span class="w"> </span><span class="se">\</span>
<span class="w">    </span>curl<span class="w"> </span><span class="se">\</span>
<span class="w">    </span><span class="o">&amp;&amp;</span><span class="w"> </span>rm<span class="w"> </span>-rf<span class="w"> </span>/var/lib/apt/lists/*

<span class="c"># Copy requirements and install Python dependencies</span>
<span class="k">COPY</span><span class="w"> </span>requirements.txt<span class="w"> </span>.
<span class="k">RUN</span><span class="w"> </span>pip<span class="w"> </span>install<span class="w"> </span>--no-cache-dir<span class="w"> </span>-r<span class="w"> </span>requirements.txt

<span class="c"># Copy application code</span>
<span class="k">COPY</span><span class="w"> </span>src/<span class="w"> </span>./src/
<span class="k">COPY</span><span class="w"> </span>config/<span class="w"> </span>./config/

<span class="c"># Create non-root user</span>
<span class="k">RUN</span><span class="w"> </span>useradd<span class="w"> </span>-m<span class="w"> </span>-u<span class="w"> </span><span class="m">1000</span><span class="w"> </span>appuser<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>chown<span class="w"> </span>-R<span class="w"> </span>appuser:appuser<span class="w"> </span>/app
<span class="k">USER</span><span class="w"> </span><span class="s">appuser</span>

<span class="c"># Expose port</span>
<span class="k">EXPOSE</span><span class="w"> </span><span class="s">8000</span>

<span class="c"># Health check</span>
<span class="k">HEALTHCHECK</span><span class="w"> </span>--interval<span class="o">=</span>30s<span class="w"> </span>--timeout<span class="o">=</span>30s<span class="w"> </span>--start-period<span class="o">=</span>5s<span class="w"> </span>--retries<span class="o">=</span><span class="m">3</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>CMD<span class="w"> </span>curl<span class="w"> </span>-f<span class="w"> </span>http://localhost:8000/health<span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nb">exit</span><span class="w"> </span><span class="m">1</span>

<span class="c"># Start application</span>
<span class="k">CMD</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;uvicorn&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;src.main:app&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;--host&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;0.0.0.0&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;--port&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;8000&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;--workers&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;4&quot;</span><span class="p">]</span>
</pre></div>
</div>
<p><strong>Build and run:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Build image</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>neural-symbolic-ai:latest<span class="w"> </span>.

<span class="c1"># Run container</span>
docker<span class="w"> </span>run<span class="w"> </span>-d<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--name<span class="w"> </span>neural-symbolic-api<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-p<span class="w"> </span><span class="m">8000</span>:8000<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-e<span class="w"> </span><span class="nv">APP_ENVIRONMENT</span><span class="o">=</span>production<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-e<span class="w"> </span><span class="nv">SECURITY_API_KEYS</span><span class="o">=</span><span class="s1">&#39;{&quot;prod&quot;: &quot;your-secure-api-key&quot;}&#39;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>neural-symbolic-ai:latest
</pre></div>
</div>
</section>
<section id="docker-compose">
<h3>Docker Compose<a class="headerlink" href="#docker-compose" title="Link to this heading"></a></h3>
<p><strong>docker-compose.yml:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.8&#39;</span>

<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">neural-symbolic-api</span><span class="p">:</span>
<span class="w">    </span><span class="nt">build</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">.</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;8000:8000&quot;</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">APP_ENVIRONMENT=production</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">APP_WORKERS=4</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CACHE_REDIS_URL=redis://redis:6379/0</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">LOG_LEVEL=INFO</span>
<span class="w">    </span><span class="nt">depends_on</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
<span class="w">    </span><span class="nt">restart</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
<span class="w">    </span><span class="nt">healthcheck</span><span class="p">:</span>
<span class="w">      </span><span class="nt">test</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;CMD&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;curl&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;-f&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;http://localhost:8000/health&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30s</span>
<span class="w">      </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10s</span>
<span class="w">      </span><span class="nt">retries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>

<span class="w">  </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis:7-alpine</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;6379:6379&quot;</span>
<span class="w">    </span><span class="nt">restart</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
<span class="w">    </span><span class="nt">healthcheck</span><span class="p">:</span>
<span class="w">      </span><span class="nt">test</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;CMD&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;redis-cli&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;ping&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30s</span>
<span class="w">      </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10s</span>
<span class="w">      </span><span class="nt">retries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>

<span class="w">  </span><span class="nt">nginx</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nginx:alpine</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;80:80&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;443:443&quot;</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./nginx.conf:/etc/nginx/nginx.conf</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./ssl:/etc/nginx/ssl</span>
<span class="w">    </span><span class="nt">depends_on</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-api</span>
<span class="w">    </span><span class="nt">restart</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">unless-stopped</span>
</pre></div>
</div>
<p><strong>Start services:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
</section>
<section id="kubernetes-deployment">
<h3>Kubernetes Deployment<a class="headerlink" href="#kubernetes-deployment" title="Link to this heading"></a></h3>
<p><strong>deployment.yaml:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">apps/v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deployment</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-api</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">    </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-api</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">  </span><span class="nt">selector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">      </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-api</span>
<span class="w">  </span><span class="nt">template</span><span class="p">:</span>
<span class="w">    </span><span class="nt">metadata</span><span class="p">:</span>
<span class="w">      </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">        </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-api</span>
<span class="w">    </span><span class="nt">spec</span><span class="p">:</span>
<span class="w">      </span><span class="nt">containers</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-api</span>
<span class="w">        </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-ai:latest</span>
<span class="w">        </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">containerPort</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8000</span>
<span class="w">        </span><span class="nt">env</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">APP_ENVIRONMENT</span>
<span class="w">          </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">APP_WORKERS</span>
<span class="w">          </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;4&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CACHE_REDIS_URL</span>
<span class="w">          </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;redis://redis-service:6379/0&quot;</span>
<span class="w">        </span><span class="nt">resources</span><span class="p">:</span>
<span class="w">          </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">            </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;2Gi&quot;</span>
<span class="w">            </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1000m&quot;</span>
<span class="w">          </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">            </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;4Gi&quot;</span>
<span class="w">            </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;2000m&quot;</span>
<span class="w">        </span><span class="nt">livenessProbe</span><span class="p">:</span>
<span class="w">          </span><span class="nt">httpGet</span><span class="p">:</span>
<span class="w">            </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/health</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8000</span>
<span class="w">          </span><span class="nt">initialDelaySeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">          </span><span class="nt">periodSeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">        </span><span class="nt">readinessProbe</span><span class="p">:</span>
<span class="w">          </span><span class="nt">httpGet</span><span class="p">:</span>
<span class="w">            </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/ready</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8000</span>
<span class="w">          </span><span class="nt">initialDelaySeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">          </span><span class="nt">periodSeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
</pre></div>
</div>
<p><strong>service.yaml:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Service</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-service</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">selector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-api</span>
<span class="w">  </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">    </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
<span class="w">    </span><span class="nt">targetPort</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8000</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">LoadBalancer</span>
</pre></div>
</div>
<p><strong>Deploy to Kubernetes:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>deployment.yaml
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>service.yaml
</pre></div>
</div>
</section>
</section>
<section id="production-configuration">
<h2>Production Configuration<a class="headerlink" href="#production-configuration" title="Link to this heading"></a></h2>
<section id="environment-variables">
<h3>Environment Variables<a class="headerlink" href="#environment-variables" title="Link to this heading"></a></h3>
<p><strong>Production .env file:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Application</span>
<span class="nv">APP_ENVIRONMENT</span><span class="o">=</span>production
<span class="nv">APP_DEBUG</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">APP_WORKERS</span><span class="o">=</span><span class="m">4</span>
<span class="nv">APP_HOST</span><span class="o">=</span><span class="m">0</span>.0.0.0
<span class="nv">APP_PORT</span><span class="o">=</span><span class="m">8000</span>

<span class="c1"># Security</span>
<span class="nv">SECURITY_API_KEYS</span><span class="o">=</span><span class="s1">&#39;{&quot;prod&quot;: &quot;your-very-secure-api-key-here&quot;}&#39;</span>
<span class="nv">SECURITY_CORS_ORIGINS</span><span class="o">=</span><span class="s1">&#39;[&quot;https://yourdomain.com&quot;]&#39;</span>
<span class="nv">SECURITY_RATE_LIMIT_REQUESTS</span><span class="o">=</span><span class="m">100</span>

<span class="c1"># Performance</span>
<span class="nv">MODEL_USE_GPU</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">CACHE_REDIS_URL</span><span class="o">=</span>redis://redis-server:6379/0
<span class="nv">CACHE_MAX_SIZE</span><span class="o">=</span><span class="m">10000</span>

<span class="c1"># Logging</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span>INFO
<span class="nv">LOG_STRUCTURED_LOGGING</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">LOG_FILE_PATH</span><span class="o">=</span>/var/log/neural-symbolic
</pre></div>
</div>
</section>
<section id="database-configuration">
<h3>Database Configuration<a class="headerlink" href="#database-configuration" title="Link to this heading"></a></h3>
<p>For production, use PostgreSQL instead of SQLite:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Database connection</span>
<span class="nv">DATABASE_URL</span><span class="o">=</span>*****************************************/neural_symbolic
<span class="nv">DATABASE_POOL_SIZE</span><span class="o">=</span><span class="m">20</span>
<span class="nv">DATABASE_MAX_OVERFLOW</span><span class="o">=</span><span class="m">30</span>
</pre></div>
</div>
</section>
</section>
<section id="load-balancing">
<h2>Load Balancing<a class="headerlink" href="#load-balancing" title="Link to this heading"></a></h2>
<section id="nginx-configuration">
<h3>Nginx Configuration<a class="headerlink" href="#nginx-configuration" title="Link to this heading"></a></h3>
<p><strong>nginx.conf:</strong></p>
<div class="highlight-nginx notranslate"><div class="highlight"><pre><span></span><span class="k">upstream</span><span class="w"> </span><span class="s">neural_symbolic_backend</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kn">server</span><span class="w"> </span><span class="n">neural-symbolic-api-1</span><span class="p">:</span><span class="mi">8000</span><span class="p">;</span>
<span class="w">    </span><span class="kn">server</span><span class="w"> </span><span class="n">neural-symbolic-api-2</span><span class="p">:</span><span class="mi">8000</span><span class="p">;</span>
<span class="w">    </span><span class="kn">server</span><span class="w"> </span><span class="n">neural-symbolic-api-3</span><span class="p">:</span><span class="mi">8000</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">server</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kn">listen</span><span class="w"> </span><span class="mi">80</span><span class="p">;</span>
<span class="w">    </span><span class="kn">server_name</span><span class="w"> </span><span class="s">yourdomain.com</span><span class="p">;</span>
<span class="w">    </span><span class="kn">return</span><span class="w"> </span><span class="mi">301</span><span class="w"> </span><span class="s">https://</span><span class="nv">$server_name$request_uri</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">server</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kn">listen</span><span class="w"> </span><span class="mi">443</span><span class="w"> </span><span class="s">ssl</span><span class="w"> </span><span class="s">http2</span><span class="p">;</span>
<span class="w">    </span><span class="kn">server_name</span><span class="w"> </span><span class="s">yourdomain.com</span><span class="p">;</span>

<span class="w">    </span><span class="kn">ssl_certificate</span><span class="w"> </span><span class="s">/etc/nginx/ssl/cert.pem</span><span class="p">;</span>
<span class="w">    </span><span class="kn">ssl_certificate_key</span><span class="w"> </span><span class="s">/etc/nginx/ssl/key.pem</span><span class="p">;</span>

<span class="w">    </span><span class="c1"># Security headers</span>
<span class="w">    </span><span class="kn">add_header</span><span class="w"> </span><span class="s">Strict-Transport-Security</span><span class="w"> </span><span class="s">&quot;max-age=31536000</span><span class="p">;</span><span class="w"> </span><span class="kn">includeSubDomains&quot;</span><span class="w"> </span><span class="s">always</span><span class="p">;</span>
<span class="w">    </span><span class="kn">add_header</span><span class="w"> </span><span class="s">X-Frame-Options</span><span class="w"> </span><span class="s">DENY</span><span class="w"> </span><span class="s">always</span><span class="p">;</span>
<span class="w">    </span><span class="kn">add_header</span><span class="w"> </span><span class="s">X-Content-Type-Options</span><span class="w"> </span><span class="s">nosniff</span><span class="w"> </span><span class="s">always</span><span class="p">;</span>

<span class="w">    </span><span class="c1"># Rate limiting</span>
<span class="w">    </span><span class="kn">limit_req_zone</span><span class="w"> </span><span class="nv">$binary_remote_addr</span><span class="w"> </span><span class="s">zone=api:10m</span><span class="w"> </span><span class="s">rate=10r/s</span><span class="p">;</span>

<span class="w">    </span><span class="kn">location</span><span class="w"> </span><span class="s">/</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kn">limit_req</span><span class="w"> </span><span class="s">zone=api</span><span class="w"> </span><span class="s">burst=20</span><span class="w"> </span><span class="s">nodelay</span><span class="p">;</span>

<span class="w">        </span><span class="kn">proxy_pass</span><span class="w"> </span><span class="s">http://neural_symbolic_backend</span><span class="p">;</span>
<span class="w">        </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">Host</span><span class="w"> </span><span class="nv">$host</span><span class="p">;</span>
<span class="w">        </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">X-Real-IP</span><span class="w"> </span><span class="nv">$remote_addr</span><span class="p">;</span>
<span class="w">        </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">X-Forwarded-For</span><span class="w"> </span><span class="nv">$proxy_add_x_forwarded_for</span><span class="p">;</span>
<span class="w">        </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">X-Forwarded-Proto</span><span class="w"> </span><span class="nv">$scheme</span><span class="p">;</span>

<span class="w">        </span><span class="c1"># Timeouts</span>
<span class="w">        </span><span class="kn">proxy_connect_timeout</span><span class="w"> </span><span class="s">30s</span><span class="p">;</span>
<span class="w">        </span><span class="kn">proxy_send_timeout</span><span class="w"> </span><span class="s">30s</span><span class="p">;</span>
<span class="w">        </span><span class="kn">proxy_read_timeout</span><span class="w"> </span><span class="s">30s</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kn">location</span><span class="w"> </span><span class="s">/health</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kn">access_log</span><span class="w"> </span><span class="no">off</span><span class="p">;</span>
<span class="w">        </span><span class="kn">proxy_pass</span><span class="w"> </span><span class="s">http://neural_symbolic_backend</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="haproxy-configuration">
<h3>HAProxy Configuration<a class="headerlink" href="#haproxy-configuration" title="Link to this heading"></a></h3>
<p><strong>haproxy.cfg:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>global
    daemon
    maxconn 4096

defaults
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms

frontend neural_symbolic_frontend
    bind *:80
    bind *:443 ssl crt /etc/ssl/certs/yourdomain.pem
    redirect scheme https if !{ ssl_fc }
    default_backend neural_symbolic_backend

backend neural_symbolic_backend
    balance roundrobin
    option httpchk GET /health
    server api1 neural-symbolic-api-1:8000 check
    server api2 neural-symbolic-api-2:8000 check
    server api3 neural-symbolic-api-3:8000 check
</pre></div>
</div>
</section>
</section>
<section id="monitoring-and-observability">
<h2>Monitoring and Observability<a class="headerlink" href="#monitoring-and-observability" title="Link to this heading"></a></h2>
<section id="prometheus-metrics">
<h3>Prometheus Metrics<a class="headerlink" href="#prometheus-metrics" title="Link to this heading"></a></h3>
<p>The application exposes Prometheus-compatible metrics:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Scrape configuration</span>
curl<span class="w"> </span>http://localhost:8000/metrics
</pre></div>
</div>
<p><strong>prometheus.yml:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">global</span><span class="p">:</span>
<span class="w">  </span><span class="nt">scrape_interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">15s</span>

<span class="nt">scrape_configs</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">job_name</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;neural-symbolic-api&#39;</span>
<span class="w">    </span><span class="nt">static_configs</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">targets</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&#39;neural-symbolic-api:8000&#39;</span><span class="p p-Indicator">]</span>
<span class="w">    </span><span class="nt">metrics_path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/metrics</span>
<span class="w">    </span><span class="nt">scrape_interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30s</span>
</pre></div>
</div>
</section>
<section id="grafana-dashboard">
<h3>Grafana Dashboard<a class="headerlink" href="#grafana-dashboard" title="Link to this heading"></a></h3>
<p>Import the provided Grafana dashboard for monitoring:</p>
<ul class="simple">
<li><p><strong>Request rate and latency</strong></p></li>
<li><p><strong>Error rates and status codes</strong></p></li>
<li><p><strong>Cache hit rates and performance</strong></p></li>
<li><p><strong>GPU utilization and memory</strong></p></li>
<li><p><strong>System resources (CPU, memory)</strong></p></li>
</ul>
</section>
<section id="logging">
<h3>Logging<a class="headerlink" href="#logging" title="Link to this heading"></a></h3>
<p><strong>Centralized logging with ELK stack:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># docker-compose.logging.yml</span>
<span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.8&#39;</span>

<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">elasticsearch</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker.elastic.co/elasticsearch/elasticsearch:8.8.0</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">discovery.type=single-node</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">xpack.security.enabled=false</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;9200:9200&quot;</span>

<span class="w">  </span><span class="nt">logstash</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker.elastic.co/logstash/logstash:8.8.0</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./logstash.conf:/usr/share/logstash/pipeline/logstash.conf</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;5044:5044&quot;</span>
<span class="w">    </span><span class="nt">depends_on</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">elasticsearch</span>

<span class="w">  </span><span class="nt">kibana</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker.elastic.co/kibana/kibana:8.8.0</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;5601:5601&quot;</span>
<span class="w">    </span><span class="nt">depends_on</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">elasticsearch</span>
</pre></div>
</div>
</section>
</section>
<section id="security-hardening">
<h2>Security Hardening<a class="headerlink" href="#security-hardening" title="Link to this heading"></a></h2>
<section id="ssl-tls-configuration">
<h3>SSL/TLS Configuration<a class="headerlink" href="#ssl-tls-configuration" title="Link to this heading"></a></h3>
<p><strong>Generate SSL certificates:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Self-signed certificate (development)</span>
openssl<span class="w"> </span>req<span class="w"> </span>-x509<span class="w"> </span>-newkey<span class="w"> </span>rsa:4096<span class="w"> </span>-keyout<span class="w"> </span>key.pem<span class="w"> </span>-out<span class="w"> </span>cert.pem<span class="w"> </span>-days<span class="w"> </span><span class="m">365</span><span class="w"> </span>-nodes

<span class="c1"># Let&#39;s Encrypt (production)</span>
certbot<span class="w"> </span>certonly<span class="w"> </span>--standalone<span class="w"> </span>-d<span class="w"> </span>yourdomain.com
</pre></div>
</div>
</section>
<section id="api-security">
<h3>API Security<a class="headerlink" href="#api-security" title="Link to this heading"></a></h3>
<p><strong>Security checklist:</strong></p>
<ul class="simple">
<li><p>✅ Use strong API keys (32+ characters)</p></li>
<li><p>✅ Enable HTTPS only in production</p></li>
<li><p>✅ Configure CORS properly</p></li>
<li><p>✅ Set up rate limiting</p></li>
<li><p>✅ Enable request size limits</p></li>
<li><p>✅ Use security headers</p></li>
<li><p>✅ Regular security updates</p></li>
</ul>
<p><strong>Environment security:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Secure file permissions</span>
chmod<span class="w"> </span><span class="m">600</span><span class="w"> </span>.env
chmod<span class="w"> </span><span class="m">600</span><span class="w"> </span>ssl/key.pem

<span class="c1"># Use secrets management</span>
kubectl<span class="w"> </span>create<span class="w"> </span>secret<span class="w"> </span>generic<span class="w"> </span>api-keys<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span>production-key<span class="o">=</span>your-secure-key
</pre></div>
</div>
</section>
</section>
<section id="auto-scaling">
<h2>Auto-scaling<a class="headerlink" href="#auto-scaling" title="Link to this heading"></a></h2>
<section id="kubernetes-hpa">
<h3>Kubernetes HPA<a class="headerlink" href="#kubernetes-hpa" title="Link to this heading"></a></h3>
<p><strong>hpa.yaml:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">autoscaling/v2</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">HorizontalPodAutoscaler</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-hpa</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">scaleTargetRef</span><span class="p">:</span>
<span class="w">    </span><span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">apps/v1</span>
<span class="w">    </span><span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deployment</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-api</span>
<span class="w">  </span><span class="nt">minReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">  </span><span class="nt">maxReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">  </span><span class="nt">metrics</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Resource</span>
<span class="w">    </span><span class="nt">resource</span><span class="p">:</span>
<span class="w">      </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cpu</span>
<span class="w">      </span><span class="nt">target</span><span class="p">:</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Utilization</span>
<span class="w">        </span><span class="nt">averageUtilization</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">70</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Resource</span>
<span class="w">    </span><span class="nt">resource</span><span class="p">:</span>
<span class="w">      </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">memory</span>
<span class="w">      </span><span class="nt">target</span><span class="p">:</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Utilization</span>
<span class="w">        </span><span class="nt">averageUtilization</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
</pre></div>
</div>
</section>
<section id="docker-swarm">
<h3>Docker Swarm<a class="headerlink" href="#docker-swarm" title="Link to this heading"></a></h3>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.8&#39;</span>

<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">neural-symbolic-api</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">neural-symbolic-ai:latest</span>
<span class="w">    </span><span class="nt">deploy</span><span class="p">:</span>
<span class="w">      </span><span class="nt">replicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">      </span><span class="nt">update_config</span><span class="p">:</span>
<span class="w">        </span><span class="nt">parallelism</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">        </span><span class="nt">delay</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10s</span>
<span class="w">      </span><span class="nt">restart_policy</span><span class="p">:</span>
<span class="w">        </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">on-failure</span>
<span class="w">      </span><span class="nt">resources</span><span class="p">:</span>
<span class="w">        </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">          </span><span class="nt">cpus</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;2&#39;</span>
<span class="w">          </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">4G</span>
<span class="w">        </span><span class="nt">reservations</span><span class="p">:</span>
<span class="w">          </span><span class="nt">cpus</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;1&#39;</span>
<span class="w">          </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2G</span>
</pre></div>
</div>
</section>
</section>
<section id="backup-and-recovery">
<h2>Backup and Recovery<a class="headerlink" href="#backup-and-recovery" title="Link to this heading"></a></h2>
<section id="data-backup">
<h3>Data Backup<a class="headerlink" href="#data-backup" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Backup vector indices</span>
tar<span class="w"> </span>-czf<span class="w"> </span>vector_indices_backup.tar.gz<span class="w"> </span>data/indices/

<span class="c1"># Backup configuration</span>
tar<span class="w"> </span>-czf<span class="w"> </span>config_backup.tar.gz<span class="w"> </span>config/<span class="w"> </span>.env

<span class="c1"># Automated backup script</span>
<span class="c1">#!/bin/bash</span>
<span class="nv">DATE</span><span class="o">=</span><span class="k">$(</span>date<span class="w"> </span>+%Y%m%d_%H%M%S<span class="k">)</span>
docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>neural-symbolic-api<span class="w"> </span>tar<span class="w"> </span>-czf<span class="w"> </span>/tmp/backup_<span class="nv">$DATE</span>.tar.gz<span class="w"> </span>/app/data
docker<span class="w"> </span>cp<span class="w"> </span>neural-symbolic-api:/tmp/backup_<span class="nv">$DATE</span>.tar.gz<span class="w"> </span>./backups/
</pre></div>
</div>
</section>
<section id="disaster-recovery">
<h3>Disaster Recovery<a class="headerlink" href="#disaster-recovery" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Restore from backup</span>
tar<span class="w"> </span>-xzf<span class="w"> </span>vector_indices_backup.tar.gz<span class="w"> </span>-C<span class="w"> </span>data/

<span class="c1"># Restart services</span>
docker-compose<span class="w"> </span>restart
</pre></div>
</div>
</section>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<p><strong>High memory usage:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Monitor memory usage</span>
docker<span class="w"> </span>stats<span class="w"> </span>neural-symbolic-api

<span class="c1"># Reduce cache size</span>
<span class="nv">CACHE_MAX_SIZE</span><span class="o">=</span><span class="m">1000</span>
</pre></div>
</div>
<p><strong>Slow response times:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check GPU utilization</span>
nvidia-smi

<span class="c1"># Enable GPU acceleration</span>
<span class="nv">MODEL_USE_GPU</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
<p><strong>Connection timeouts:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Increase timeout values</span>
proxy_read_timeout<span class="w"> </span>60s<span class="p">;</span>

<span class="c1"># Check network connectivity</span>
curl<span class="w"> </span>-v<span class="w"> </span>http://neural-symbolic-api:8000/health
</pre></div>
</div>
</section>
<section id="performance-tuning">
<h3>Performance Tuning<a class="headerlink" href="#performance-tuning" title="Link to this heading"></a></h3>
<p><strong>Optimize for your workload:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># CPU-intensive workloads</span>
<span class="nv">APP_WORKERS</span><span class="o">=</span><span class="m">8</span><span class="w">  </span><span class="c1"># 2x CPU cores</span>

<span class="c1"># Memory-intensive workloads</span>
<span class="nv">CACHE_MAX_SIZE</span><span class="o">=</span><span class="m">50000</span>

<span class="c1"># GPU workloads</span>
<span class="nv">MODEL_GPU_MEMORY_FRACTION</span><span class="o">=</span><span class="m">0</span>.9
</pre></div>
</div>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="api_reference.html" class="btn btn-neutral float-left" title="API Reference" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="architecture.html" class="btn btn-neutral float-right" title="Architecture Overview" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>