# Core components
symbolicai
lightrag
ollama>=0.5.1

# API and web server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
slowapi>=0.1.9
python-multipart>=0.0.6
python-dotenv>=1.0.0
PyYAML>=6.0.0

# Vector database
# Note: Using faiss-cpu temporarily, will switch to faiss-gpu when installation issues are resolved
faiss-cpu
chromadb

# GPU acceleration
torch>=2.0.0
torchvision>=0.15.0

# System monitoring
psutil>=5.9.0

# Testing and development
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0
pytest-timeout>=2.1.0
matplotlib>=3.6.0
numpy>=1.24.0
requests>=2.28.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=2.0.0
sphinx-autodoc-typehints>=1.24.0

# Monitoring and observability (optional)
structlog>=23.1.0
opentelemetry-api>=1.20.0
opentelemetry-sdk>=1.20.0
opentelemetry-exporter-jaeger>=1.20.0
prometheus-client>=0.17.0

# Development tools
pre-commit>=3.0.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.0.0
bandit>=1.7.0
pydocstyle>=6.3.0
