"""Main application module for the Neural Symbolic Language Model.

This module provides the FastAPI server implementation and core API endpoints
for the Neural Symbolic Language Model. It combines symbolic reasoning with
neural networks and retrieval-augmented generation to provide intelligent
responses to user queries.

The application provides:
    - OpenAI-compatible chat completion endpoints
    - Document management for retrieval-augmented generation
    - Performance monitoring and system health checks
    - Comprehensive security with API key authentication
    - GPU acceleration support for optimal performance

Key Components:
    - FastAPI application with CORS and security middleware
    - Symbolic reasoning engine integration
    - Vector-based document retrieval system
    - Performance monitoring and metrics collection
    - Comprehensive error handling and logging

Example:
    Run the application server::

        $ python src/main.py

    Or use uvicorn directly::

        $ uvicorn main:app --host 0.0.0.0 --port 8080

Note:
    This module requires proper configuration of environment variables
    for security, model settings, and external service connections.

Author:
    AI Assistant

Date:
    2025-06-29

Version:
    0.1.0
"""

# Standard library imports
import asyncio
import json
import os
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional

# Third-party imports
import torch
import uvicorn
from fastapi import (
    BackgroundTasks,
    Depends,
    <PERSON><PERSON><PERSON>,
    HTTPException,
    Request,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import (
    FileResponse,
    HTMLResponse,
    JSONResponse,
    StreamingResponse,
)
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel

# Optional third-party imports with fallback
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    faiss = None

# Local application imports
from document_loader import DocumentLoader
from exceptions import (
    AuthenticationError,
    ReasoningError,
    RetrievalError,
    SymbolicAIException,
    ValidationError,
    handle_exception,
)
from logging_config import get_logger, setup_logging
from models import (
    ChatChoice,
    ChatMessage,
    ChatRequest,
    ChatResponse,
    DocumentAddRequest,
    DocumentAddResponse,
    ModelRole,
    OpenAIChatChoice,
    OpenAIChatRequest,
    OpenAIChatResponse,
    OpenAIMessage,
    PerformanceMetrics,
    SystemInfo,
    TokenUsage,
)
from monitoring import PerformanceMonitor
from retrieval import Retriever
from security import (
    check_request_size,
    get_client_ip,
    get_security_headers,
    rate_limiter,
    security_manager,
    verify_api_key,
)
from symbolic_reasoning import SymbolicReasoner

# Initialize logging
logger = get_logger(__name__)

# Check FAISS availability and GPU support
if FAISS_AVAILABLE:
    logger.info("Imported FAISS successfully")
    # Check if GPU FAISS is available
    GPU_FAISS_AVAILABLE = hasattr(faiss, 'StandardGpuResources')
    if GPU_FAISS_AVAILABLE:
        logger.info("GPU FAISS is available")
    else:
        logger.info("Using CPU version of FAISS")
else:
    logger.warning("FAISS not available. Using fallback vector storage.")
    GPU_FAISS_AVAILABLE = False
from retrieval import Retriever
from logging_config import setup_logging, get_logger
from monitoring import PerformanceMonitor

# Set up logging
setup_logging()
logger = get_logger(__name__)

# Initialize performance monitoring
monitor = PerformanceMonitor()

# Create FastAPI app with security configuration
app = FastAPI(
    title="Neural Symbolic Language Model API",
    description="""A production-ready neural-symbolic language model with retrieval-augmented generation.

    Features:
    - Neural-symbolic reasoning with explainable AI
    - Hybrid retrieval with FAISS and LightRAG
    - GPU acceleration for optimal performance
    - Comprehensive security and monitoring
    - OpenAI-compatible API endpoints

    Authentication:
    - All endpoints require API key authentication
    - Use Bearer token in Authorization header

    Available endpoints:
    - GET /: Web interface
    - GET /docs: Interactive API documentation
    - GET /redoc: ReDoc API documentation
    - POST /v1/chat/completions: OpenAI-compatible chat endpoint
    - POST /documents/add: Add documents to retrieval system
    - GET /system/info: Get system information
    - GET /performance: Get performance metrics
    """,
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # Add your frontend URLs
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

# Add security headers middleware
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    """Add comprehensive security headers to all HTTP responses.

    This middleware function adds essential security headers to protect
    against common web vulnerabilities including XSS, clickjacking,
    and content type sniffing attacks.

    Args:
        request (Request): The incoming HTTP request object
        call_next: The next middleware or route handler in the chain

    Returns:
        Response: The HTTP response with added security headers

    Note:
        This middleware is applied to all routes automatically.
        Security headers include X-Content-Type-Options, X-Frame-Options,
        X-XSS-Protection, and Content-Security-Policy.
    """
    # Check request headers for security issues
    client_ip = get_client_ip(request)
    headers_dict = dict(request.headers)

    # Use security manager to check headers
    header_warnings = security_manager.check_request_headers(headers_dict)
    if header_warnings:
        logger.warning(f"Security warnings for {client_ip}: {header_warnings}")

    # Validate content type for POST/PUT requests
    if request.method in ["POST", "PUT", "PATCH"]:
        content_type = request.headers.get("content-type", "")
        if content_type and not security_manager.validate_content_type(content_type):
            logger.warning(f"Invalid content type from {client_ip}: {content_type}")
            return JSONResponse(
                status_code=400,
                content={"error": {"code": "INVALID_CONTENT_TYPE", "message": "Invalid content type"}}
            )

    response = await call_next(request)

    # Add comprehensive security headers
    security_headers = get_security_headers()

    # Add additional security headers
    additional_headers = {
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
        "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
    }

    # Merge headers
    all_headers = {**security_headers, **additional_headers}

    for header, value in all_headers.items():
        response.headers[header] = value

    return response

# Add request size checking middleware
@app.middleware("http")
async def check_request_size_middleware(request: Request, call_next):
    """Check request size limits."""
    try:
        await check_request_size(request)
        response = await call_next(request)
        return response
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code,
            content={"error": {"code": "REQUEST_TOO_LARGE", "message": e.detail}}
        )

# Add rate limiting middleware
@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    """Apply rate limiting."""
    client_ip = get_client_ip(request)

    # Check if IP is blocked
    if security_manager.is_ip_blocked(client_ip):
        return JSONResponse(
            status_code=429,
            content={
                "error": {
                    "code": "IP_BLOCKED",
                    "message": "IP address is temporarily blocked"
                }
            }
        )

    # Check rate limit
    if not rate_limiter.is_allowed(client_ip):
        security_manager.record_failed_attempt(client_ip)
        return JSONResponse(
            status_code=429,
            content={
                "error": {
                    "code": "RATE_LIMIT_EXCEEDED",
                    "message": "Rate limit exceeded"
                }
            }
        )

    response = await call_next(request)
    return response

# Global exception handlers
@app.exception_handler(SymbolicAIException)
async def symbolic_ai_exception_handler(request: Request, exc: SymbolicAIException):
    """Handle SymbolicAI custom exceptions."""
    logger.error(f"SymbolicAI exception: {exc.message}", exc_info=True)
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.to_dict()
    )

@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    """Handle validation errors."""
    logger.warning(f"Validation error: {exc.message}")
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.to_dict()
    )

@app.exception_handler(AuthenticationError)
async def authentication_exception_handler(request: Request, exc: AuthenticationError):
    """Handle authentication errors."""
    client_ip = get_client_ip(request)
    security_manager.record_failed_attempt(client_ip)
    logger.warning(f"Authentication failed for IP {client_ip}: {exc.message}")
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.to_dict(),
        headers={"WWW-Authenticate": "Bearer"}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle all other exceptions."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)

    # Convert to SymbolicAI exception
    symbolic_exc = handle_exception(exc)
    return JSONResponse(
        status_code=symbolic_exc.status_code,
        content=symbolic_exc.to_dict()
    )

# Set up templates and static files
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
static_dir = os.path.join(BASE_DIR, 'static')
app.mount("/static", StaticFiles(directory=static_dir), name="static")

templates = Jinja2Templates(directory=os.path.join(BASE_DIR, 'templates'))

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    # Serve the main web interface.
    return FileResponse(os.path.join(static_dir, 'index.html'))

@app.get("/api/info")
async def api_info():
    """Get API information and available endpoints."""
    return {
        "name": "Neural Symbolic Language Model API",
        "version": "0.1.0",
        "description": "A local GPT-4o alternative using SymbolicAI and LightRAG",
        "endpoints": {
            "root": {
                "path": "/",
                "method": "GET",
                "description": "Web interface"
            },
            "api_info": {
                "path": "/api/info",
                "method": "GET",
                "description": "This API documentation"
            },
            "docs": {
                "path": "/docs",
                "method": "GET",
                "description": "Interactive API documentation (Swagger UI)"
            },
            "redoc": {
                "path": "/redoc",
                "method": "GET",
                "description": "ReDoc API documentation"
            },
            "chat": {
                "path": "/chat",
                "method": "POST",
                "description": "Send a chat message"
            },
            "system_info": {
                "path": "/system/info",
                "method": "GET",
                "description": "Get system information"
            }
        }
    }

# Security and environment validation
logger.info("Validating security configuration...")

# Check for production environment security requirements
environment = os.getenv('APP_ENVIRONMENT', 'development')
if environment == 'production':
    # Validate required environment variables for production
    required_env_vars = ['API_KEYS_JSON', 'SECRET_KEY']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"Missing required environment variables for production: {missing_vars}")
        raise RuntimeError(f"Production deployment requires: {', '.join(missing_vars)}")

    # Validate API keys format
    try:
        api_keys_json = os.getenv('API_KEYS_JSON')
        if api_keys_json:
            json.loads(api_keys_json)  # Validate JSON format
    except json.JSONDecodeError:
        logger.error("Invalid API_KEYS_JSON format")
        raise RuntimeError("API_KEYS_JSON must be valid JSON")

    logger.info("Production security validation passed")
else:
    logger.info(f"Running in {environment} mode")

# Initialize components
logger.info("Initializing components...")
# Check GPU and FAISS availability
gpu_available = torch.cuda.is_available()
gpu_faiss_available = GPU_FAISS_AVAILABLE if 'GPU_FAISS_AVAILABLE' in globals() else False

if gpu_available:
    logger.info(f"GPU available: {torch.cuda.get_device_name(0)}")
    if not gpu_faiss_available:
        logger.warning("GPU FAISS not available. Will use CPU version of FAISS.")
else:
    logger.warning("GPU not available. Running in CPU mode.")

# Initialize components with appropriate GPU settings
use_gpu = gpu_available and (not 'FAISS_AVAILABLE' in globals() or FAISS_AVAILABLE)
reasoner = SymbolicReasoner(use_gpu=use_gpu)
retriever = Retriever(use_gpu=use_gpu and gpu_faiss_available, embedding_backend="random")
logger.info("Components initialized successfully")

# Load documents from data directory (if exists)
data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
if os.path.exists(data_dir):
    logger.info(f"Loading documents from {data_dir}")
    # Load documents with supported extensions
    documents_data = DocumentLoader.load_directory(
        data_dir, 
        extensions=['.txt', '.md']
    )
    
    # Add documents to retriever
    documents = list(documents_data.values())
    if documents:
        logger.info(f"Loading {len(documents)} documents from data directory")
        retriever.add_documents(documents)
        logger.info("Documents loaded successfully")
    else:
        logger.warning("No documents found in data directory")

# Create advanced response cache
class ResponseCache:
    def __init__(self, max_size=1000):
        self.cache = {}
        self.timestamps = {}
        self.max_size = max_size
    
    def get(self, key):
        """Get a value from the cache.
        
        Args:
            key (str): The cache key
            
        Returns:
            The cached value, or None if not found
        """
        if key in self.cache:
            # Update access timestamp
            self.timestamps[key] = time.time()
            return self.cache[key]
        return None
    
    def set(self, key, value):
        """Set a value in the cache.
        
        Args:
            key (str): The cache key
            value: The value to cache
        """
        # Clean cache if needed
        if len(self.cache) >= self.max_size:
            self.clean(int(self.max_size * 0.2))  # Remove 20% of entries
        
        # Store value and timestamp
        self.cache[key] = value
        self.timestamps[key] = time.time()
    
    def clean(self, count=None):
        """Clean old entries from the cache.
        
        Args:
            count (int, optional): Number of entries to remove
        """
        if not self.cache:
            return
        
        # Sort keys by timestamp
        sorted_keys = sorted(self.timestamps.keys(), key=lambda k: self.timestamps[k])
        
        # Determine how many entries to remove
        remove_count = count if count is not None else len(sorted_keys) // 2
        
        # Remove oldest entries
        for key in sorted_keys[:remove_count]:
            del self.cache[key]
            del self.timestamps[key]
    
    def size(self):
        """Get the current cache size.
        
        Returns:
            int: The number of entries in the cache
        """
        return len(self.cache)

# Initialize improved cache
response_cache = ResponseCache(max_size=1000)

# Legacy models for backward compatibility (will be removed)
class LegacyChatRequest(BaseModel):
    text: str

class LegacyChatResponse(BaseModel):
    response: str
    cached: bool = False

# Optimize GPU memory usage
def optimize_gpu_memory():
    """Configure PyTorch for optimal GPU memory usage"""
    if torch.cuda.is_available():
        # Empty cache before processing
        torch.cuda.empty_cache()
        
        # Set memory allocation strategy
        torch.cuda.set_per_process_memory_fraction(0.8)  # Use 80% of available GPU memory
        
        # Enable memory optimization
        torch.backends.cudnn.benchmark = True
        
        return True
    return False

# Call GPU optimization at startup
optimize_gpu_memory()

# Add sample documents for testing
sample_documents = [
    "Neural-Symbolic AI integrates logic-based reasoning with deep learning models.",
    "FAISS is a library for efficient similarity search developed by Facebook AI.",
    "Retrieval-Augmented Generation improves language model outputs by incorporating external knowledge.",
    "GPT models are transformer-based language models developed by OpenAI.",
    "Vector databases store and retrieve high-dimensional vectors for similarity search."
]

# Add documents to retriever
documents_with_ids = [{'id': str(i), 'text': doc} for i, doc in enumerate(sample_documents)]
retriever.add_documents(documents_with_ids)

# Helper function to process queries
async def process_query(query: str) -> str:
    """Process a query using both retrieval and reasoning components with comprehensive error handling.

    This function orchestrates the complete query processing pipeline including
    input validation, document retrieval, context formatting, and symbolic reasoning.
    It implements robust error handling with fallback mechanisms for resilient operation.

    Args:
        query (str): The user's query text to process

    Returns:
        str: The generated response from the reasoning engine

    Raises:
        ValidationError: If the query is invalid or empty
        RetrievalError: If document retrieval fails
        ReasoningError: If symbolic reasoning fails

    Example:
        >>> response = await process_query("What is artificial intelligence?")
        >>> print(response)
        "Artificial intelligence is a field of computer science..."
    """
    try:
        # Input validation
        if not query or not isinstance(query, str):
            raise ValidationError("Query must be a non-empty string")

        query = query.strip()
        if not query:
            raise ValidationError("Query cannot be empty or whitespace only")

        if len(query) > 10000:  # 10KB limit
            raise ValidationError("Query exceeds maximum length limit")

        logger.info(f"Processing query with {len(query)} characters")

        # Retrieve relevant information with error handling
        try:
            logger.debug("Starting document retrieval")
            retrieved_info = await asyncio.to_thread(retriever.search, query, k=2)
            logger.debug(f"Retrieved {len(retrieved_info)} documents")
        except Exception as retrieval_error:
            logger.warning(f"Document retrieval failed: {retrieval_error}")
            # Continue with empty context if retrieval fails
            retrieved_info = []

        # Format retrieved information as context
        if retrieved_info:
            context = "\n".join([f"Context {i+1}: {result['text']}" for i, result in enumerate(retrieved_info)])
            logger.debug(f"Formatted context with {len(context)} characters")
        else:
            context = None
            logger.debug("No context available, proceeding without retrieval")

        # Use symbolic reasoner to process query with context
        try:
            logger.debug("Starting symbolic reasoning")
            response = await reasoner.process_query(query, context=context)
            logger.info("Query processed successfully")
            return response
        except ReasoningError:
            # Re-raise reasoning errors as they contain specific error information
            raise
        except Exception as reasoning_error:
            logger.error(f"Symbolic reasoning failed: {reasoning_error}", exc_info=True)
            raise ReasoningError(f"Failed to process query: {str(reasoning_error)}")

    except ValidationError:
        # Re-raise validation errors
        raise
    except (RetrievalError, ReasoningError):
        # Re-raise domain-specific errors
        raise
    except Exception as e:
        logger.error(f"Unexpected error in query processing: {str(e)}", exc_info=True)
        raise ReasoningError(f"Query processing failed due to unexpected error: {str(e)}")

# API endpoints
@app.post("/documents/add", response_model=DocumentAddResponse)
async def add_document(request: DocumentAddRequest):
    """Add a document to the retrieval system.
    
    Args:
        request: DocumentAddRequest object containing the document content
        
    Returns:
        DocumentAddResponse object indicating success
    """
    try:
        # Validate document content
        if not request.content or not request.content.strip():
            raise ValidationError("Document content cannot be empty")

        if len(request.content) > 100000:  # 100KB limit
            raise ValidationError("Document content exceeds maximum size limit")

        logger.info(f"Adding document with {len(request.content)} characters")

        # Add document to retriever with error handling
        try:
            retriever.add_documents([{'text': request.content}])
            logger.info("Document added successfully to retriever")
        except Exception as retrieval_error:
            logger.error(f"Failed to add document to retriever: {retrieval_error}", exc_info=True)
            raise RetrievalError(
                f"Failed to add document to retrieval system: {str(retrieval_error)}"
            )

        return DocumentAddResponse(
            success=True,
            message="Document added successfully"
        )

    except ValidationError as e:
        logger.warning(f"Document validation failed: {e.message}")
        raise HTTPException(status_code=400, detail=e.message)
    except RetrievalError as e:
        logger.error(f"Document retrieval error: {e.message}")
        raise HTTPException(status_code=500, detail=e.message)
    except Exception as e:
        logger.error(f"Unexpected error adding document: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/documents/count")
async def get_document_count():
    """Get the number of documents in the retrieval system.
    
    Returns:
        dict: Number of documents
    """
    # This is a placeholder - actual implementation depends on LightRAG
    # Assuming retriever has a `document_count` method
    if hasattr(retriever, 'document_count'):
        count = retriever.document_count()
    else:
        count = -1  # Unknown if not supported
        
    return {"count": count}

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest, background_tasks: BackgroundTasks):
    """Process a chat request.
    
    Args:
        request: ChatRequest object containing the text query
        background_tasks: FastAPI BackgroundTasks for async operations
        
    Returns:
        ChatResponse: Response from the system
    """
    return await process_chat_request(request, background_tasks)

@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """Process a chat request with streaming response.
    
    Args:
        request: ChatRequest object containing the text query
        
    Returns:
        StreamingResponse: Streaming response generator
    """
    # Check cache for identical queries
    cached_response = response_cache.get(request.text)
    if cached_response:
        # For cached responses, stream the entire response at once
        async def cached_generator():
            yield f"data: {json.dumps({'response': cached_response, 'cached': True})}\n\n"
            yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            cached_generator(),
            media_type="text/event-stream"
        )
    
    try:
        # Perform retrieval
        retrieved_info = await asyncio.to_thread(retriever.query, request.text)
        
        # Combine input with retrieved context
        enriched_query = f"{request.text}"
        if retrieved_info:
            enriched_query += f"\n\nAdditional context: {retrieved_info}"
        
        # This is a placeholder for streaming - actual implementation depends on SymbolicAI
        # Assuming reasoner has a `process_query_stream` method
        async def generator():
            if hasattr(reasoner, 'process_query_stream'):
                # Use streaming if available
                async for chunk in reasoner.process_query_stream(enriched_query):
                    yield f"data: {json.dumps({'response': chunk, 'finished': False})}\n\n"
            else:
                # Fall back to non-streaming
                response = await reasoner.process_query(enriched_query)
                yield f"data: {json.dumps({'response': response, 'finished': True})}\n\n"
            
            # Signal completion
            yield "data: [DONE]\n\n"
            
            # Cache the complete response
            if not hasattr(reasoner, 'process_query_stream'):
                response_cache.set(request.text, response)
        
        return StreamingResponse(
            generator(),
            media_type="text/event-stream"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")
    """Process a chat request.
    
    Args:
        request: The chat request containing the user's query
        background_tasks: FastAPI background tasks handler
        
    Returns:
        ChatResponse: The response to the user's query
    """
    query = request.text
    
    # Check cache first
    cached_response = response_cache.get(query)
    if cached_response:
        return ChatResponse(
            response=cached_response,
            cached=True
        )
    
    try:
        # Process the query
        response = await process_query(query)
        
        # Cache the response in the background
        background_tasks.add_task(lambda: response_cache.set(query, response))
        
        return ChatResponse(
            response=response,
            cached=False
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def clean_cache():
    """Clean old entries from the response cache.
    
    Keeps the cache size manageable by removing oldest entries.
    """
    # Keep cache size manageable
    if len(response_cache) > 100:
        # Remove oldest entries
        for key in list(response_cache.keys())[:50]:
            del response_cache[key]

async def openai_chat_stream(request: OpenAIChatRequest):
    """Handle streaming for OpenAI-compatible chat endpoint.
    
    Args:
        request: OpenAIChatRequest object
        
    Returns:
        StreamingResponse: Server-sent events stream
    """
    logger.info(f"Received streaming chat request for model: {request.model}")
    
    # Extract the last user message
    last_message = None
    for msg in reversed(request.messages):
        if msg.role == "user":
            last_message = msg.content
            break
    
    if not last_message:
        logger.warning("No user message found in streaming request")
        raise HTTPException(status_code=400, detail="No user message found")
    
    logger.debug(f"Processing streaming message: {last_message[:100]}...")
    
    # Check cache for identical queries
    cached_response = response_cache.get(last_message)
    if cached_response:
        logger.info("Found response in cache for streaming request")
        async def cached_generator():
            yield json.dumps({
                "id": f"chatcmpl-{uuid.uuid4()}",
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "delta": {
                        "role": "assistant",
                        "content": cached_response
                    },
                    "finish_reason": None
                }]
            }) + "\n"
            
            # Send completion message
            yield json.dumps({
                "id": f"chatcmpl-{uuid.uuid4()}",
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }]
            }) + "\n"
        
        return StreamingResponse(
            cached_generator(),
            media_type="text/event-stream"
        )
    
    try:
        # Perform retrieval
        logger.info("Performing retrieval operation for streaming request")
        search_results = await asyncio.to_thread(retriever.search, last_message)
        
        # Extract text from search results
        retrieved_info = "\n".join([result['text'] for result in search_results]) if search_results else ""
        
        # Combine input with retrieved context
        enriched_query = f"{last_message}"
        if retrieved_info:
            logger.info("Retrieved relevant context for streaming request")
            enriched_query += f"\n\nAdditional context: {retrieved_info}"
        else:
            logger.info("No relevant context found for streaming request")
        
        async def generator():
            if hasattr(reasoner, 'process_query_stream'):
                # Use streaming if available
                logger.info("Using native streaming capability")
                async for chunk in reasoner.process_query_stream(enriched_query):
                    logger.debug(f"Streaming chunk: {chunk[:50]}...")
                    yield json.dumps({
                        "id": f"chatcmpl-{uuid.uuid4()}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": request.model,
                        "choices": [{
                            "index": 0,
                            "delta": {
                                "role": "assistant",
                                "content": chunk
                            },
                            "finish_reason": None
                        }]
                    }) + "\n"
            else:
                # Fall back to non-streaming
                logger.info("Native streaming not available, falling back to single response")
                response = await reasoner.process_query(enriched_query)
                logger.debug(f"Generated full response: {response[:100]}...")
                yield json.dumps({
                    "id": f"chatcmpl-{uuid.uuid4()}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": request.model,
                    "choices": [{
                        "index": 0,
                        "delta": {
                            "role": "assistant",
                            "content": response
                        },
                        "finish_reason": None
                    }]
                }) + "\n"
            
            # Send completion message
            logger.debug("Sending completion message")
            yield json.dumps({
                "id": f"chatcmpl-{uuid.uuid4()}",
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }]
            }) + "\n"
            
            # Cache the complete response if not using streaming
            if not hasattr(reasoner, 'process_query_stream'):
                logger.debug("Caching non-streaming response")
                response_cache.set(last_message, response)
            
            logger.info("Streaming response completed successfully")
        
        return StreamingResponse(
            generator(),
            media_type="text/event-stream"
        )
        
    except Exception as e:
        logger.error(f"Error processing streaming request: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")

@app.post("/v1/chat/completions", response_model=OpenAIChatResponse)
async def openai_chat(request: OpenAIChatRequest):
    """OpenAI-compatible chat endpoint.
    
    Args:
        request: OpenAIChatRequest object
        
    Returns:
        OpenAIChatResponse object or StreamingResponse if streaming
    """
    request_id = f"chat-{uuid.uuid4()}"
    monitor.start_request(request_id, "/v1/chat/completions")
    logger.info(f"Received chat request for model: {request.model}")
    
    # Handle streaming requests
    if request.stream:
        logger.info("Request requires streaming, forwarding to streaming endpoint")
        monitor.end_request(request_id)
        return await openai_chat_stream(request)
    
    # Extract the last user message
    last_message = None
    for msg in reversed(request.messages):
        if msg.role == "user":
            last_message = msg.content
            break
    
    if not last_message:
        logger.warning("No user message found in request")
        monitor.end_request(request_id, error="No user message found")
        raise HTTPException(status_code=400, detail="No user message found")
    
    logger.debug(f"Processing message: {last_message[:100]}...")
    
    # Check cache for identical queries
    cached_response = response_cache.get(last_message)
    if cached_response:
        logger.info("Found response in cache")
        monitor.record_cache_hit(request_id)
        response_obj = OpenAIChatResponse(
            id=f"chatcmpl-{uuid.uuid4()}",
            created=int(time.time()),
            model=request.model,
            choices=[
                OpenAIChatChoice(
                    index=0,
                    message=OpenAIMessage(
                        role="assistant",
                        content=cached_response
                    ),
                    finish_reason="stop"
                )
            ],
            usage={
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        )
        monitor.end_request(request_id)
        return response_obj
    
    monitor.record_cache_miss(request_id)
    
    try:
        # Perform retrieval
        logger.info("Performing retrieval operation")
        retrieval_start = time.time()
        search_results = await asyncio.to_thread(retriever.search, last_message)
        monitor.record_retrieval_time(request_id, time.time() - retrieval_start)
        
        # Extract text from search results
        retrieved_info = "\n".join([result['text'] for result in search_results]) if search_results else ""
        
        # Combine input with retrieved context
        enriched_query = f"{last_message}"
        if retrieved_info:
            logger.info("Retrieved relevant context")
            enriched_query += f"\n\nAdditional context: {retrieved_info}"
        else:
            logger.info("No relevant context found")
        
        # Process with symbolic reasoning
        logger.info("Processing with symbolic reasoning")
        reasoning_start = time.time()
        response = await reasoner.process_query(enriched_query)
        monitor.record_reasoning_time(request_id, time.time() - reasoning_start)
        logger.info("Symbolic reasoning completed")
        
        # Cache the response for future use
        logger.debug("Caching response")
        response_cache.set(last_message, response)
        
        # Calculate token counts
        total_tokens = len(enriched_query.split()) + len(response.split())
        monitor.record_token_count(request_id, total_tokens)
        
        response_obj = OpenAIChatResponse(
            id=f"chatcmpl-{uuid.uuid4()}",
            created=int(time.time()),
            model=request.model,
            choices=[
                OpenAIChatChoice(
                    index=0,
                    message=OpenAIMessage(
                        role="assistant",
                        content=response
                    ),
                    finish_reason="stop"
                )
            ],
            usage={
                "prompt_tokens": len(enriched_query.split()),
                "completion_tokens": len(response.split()),
                "total_tokens": total_tokens
            }
        )
        
        logger.info("Successfully generated response")
        monitor.end_request(request_id)
        return response_obj
    
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}", exc_info=True)
        monitor.end_request(request_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")

@app.get("/performance")
async def performance_stats():
    """Get performance statistics for the system.
    
    Returns:
        dict: Performance statistics
    """
    logger.info("Retrieving performance statistics")
    try:
        # Get cache info
        cache_size = response_cache.size()
        logger.debug(f"Current cache size: {cache_size}")
        
        # Get GPU info
        gpu_available = torch.cuda.is_available()
        gpu_name = torch.cuda.get_device_name(0) if gpu_available else None
        logger.debug(f"GPU status - Available: {gpu_available}, Name: {gpu_name}")
        
        # Get monitoring metrics
        monitoring_metrics = monitor.get_recent_metrics(minutes=5)
        
        stats = {
            "cache": {
                "size": cache_size,
                "max_size": response_cache.max_size,
                "hits": monitoring_metrics["cache"]["hits"],
                "misses": monitoring_metrics["cache"]["misses"],
                "hit_rate": monitoring_metrics["cache"]["hit_rate"]
            },
            "system": {
                "gpu_available": gpu_available,
                "gpu_name": gpu_name,
                "cpu_percent": monitoring_metrics["system"]["cpu_percent"],
                "memory_percent": monitoring_metrics["system"]["memory_percent"],
                "active_requests": monitoring_metrics["system"]["active_requests"],
                "reasoner": reasoner.get_system_info(),
                "retriever": retriever.get_system_info()
            },
            "requests": {
                "total": monitoring_metrics["requests"]["total"],
                "avg_duration": monitoring_metrics["requests"]["avg_duration"],
                "error_rate": monitoring_metrics["requests"]["error_rate"],
                "cache_hit_rate": monitoring_metrics["requests"]["cache_hit_rate"]
            }
        }
        
        logger.info("Successfully retrieved performance statistics")
        return stats
    except Exception as e:
        logger.error(f"Error retrieving performance statistics: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system/info")
async def system_info():
    """Get system configuration information."""
    logger.info("Retrieving system configuration information")
    try:
        gpu_optimized = torch.cuda.is_available() and torch.backends.cudnn.benchmark
        logger.debug(f"GPU optimization status: {gpu_optimized}")
        
        reasoner_info = reasoner.get_system_info()
        logger.debug("Retrieved reasoner system info")
        
        retriever_info = retriever.get_system_info()
        logger.debug("Retrieved retriever system info")
        
        info = {
            "reasoner": reasoner_info,
            "retriever": retriever_info,
            "gpu_optimized": gpu_optimized
        }
        
        logger.info("Successfully retrieved system configuration")
        return info
    except Exception as e:
        logger.error(f"Error retrieving system configuration: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Enhanced health check including Ollama."""
    from datetime import datetime
    from src.core.config import get_settings

    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "components": {
            "api": "healthy",
            "gpu": "available" if torch.cuda.is_available() else "unavailable"
        }
    }

    # Add Ollama health check if using Ollama engine
    try:
        settings = get_settings()
        if settings.model.reasoning_engine == "ollama":
            try:
                import ollama
                client = ollama.Client(host=settings.model.ollama_host)
                models = client.list()
                health_status["components"]["ollama"] = "healthy"
                health_status["components"]["ollama_models"] = len(models.get('models', []))
            except Exception as e:
                health_status["components"]["ollama"] = f"unhealthy: {str(e)}"
                health_status["status"] = "degraded"
    except Exception as e:
        logger.warning(f"Could not check Ollama health: {e}")

    return health_status

@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint for Kubernetes."""
    try:
        # Check if core components are ready
        reasoner_ready = reasoner is not None
        retriever_ready = retriever is not None

        status = {
            "status": "ready" if reasoner_ready and retriever_ready else "not_ready",
            "components": {
                "reasoner": "ready" if reasoner_ready else "not_ready",
                "retriever": "ready" if retriever_ready else "not_ready",
                "cache": "ready"  # Cache is always ready
            }
        }

        return status
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return {
            "status": "not_ready",
            "error": str(e)
        }

@app.get("/ollama/models")
async def list_ollama_models():
    """List available Ollama models - complements existing /system/info"""
    from src.core.config import get_settings
    settings = get_settings()

    if settings.model.reasoning_engine == "ollama":
        try:
            import ollama
            client = ollama.Client(host=settings.model.ollama_host)
            models = client.list()
            return {"models": [m['name'] for m in models.get('models', [])]}
        except Exception as e:
            logger.error(f"Failed to list Ollama models: {e}")
            return {"error": f"Failed to list models: {str(e)}"}
    return {"error": "Ollama engine not active"}

@app.post("/ollama/models/{model_name}/pull")
async def pull_ollama_model(model_name: str):
    """Pull Ollama model - enhances existing system management"""
    from src.core.config import get_settings
    settings = get_settings()

    if settings.model.reasoning_engine == "ollama":
        try:
            import ollama
            client = ollama.Client(host=settings.model.ollama_host)
            client.pull(model_name)
            logger.info(f"Successfully pulled Ollama model: {model_name}")
            return {"status": "success", "model": model_name}
        except Exception as e:
            logger.error(f"Failed to pull Ollama model {model_name}: {e}")
            return {"status": "error", "message": str(e)}
    return {"error": "Ollama engine not active"}

@app.on_event("shutdown")
async def shutdown_event():
    """Perform cleanup on shutdown."""
    logger.info("Application shutting down")
    monitor.shutdown()

if __name__ == "__main__":
    # Run the API with development settings
    uvicorn.run(
        "main:app",    # Import string format
        host="127.0.0.1",  # Local connections only for development
        port=8080,        # Alternative port
        reload=True       # Enable auto-reload during development
    )
