#!/usr/bin/env python3
"""
Demonstration of Neural Symbolic Language Model integration with client applications.

This script shows practical examples of how to integrate the Neural Symbolic Language Model
with various client applications and frameworks, including:
- OpenAI SDK compatibility
- Custom REST API clients
- Streaming responses
- Document management
- Performance monitoring
"""

import asyncio
import json
import time
import requests
from typing import List, Dict, Any, Optional
import openai


class NeuralSymbolicClient:
    """Comprehensive client for Neural Symbolic Language Model integration."""
    
    def __init__(self, api_key: str, base_url: str = "http://localhost:8080"):
        """Initialize the client.
        
        Args:
            api_key: API key for authentication
            base_url: Base URL for the API (without /v1)
        """
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Configure OpenAI SDK
        openai.api_key = api_key
        openai.api_base = f"{base_url}/v1"
    
    def health_check(self) -> bool:
        """Check if the API is healthy and accessible."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"Health check failed: {e}")
            return False
    
    def openai_sdk_example(self):
        """Demonstrate OpenAI SDK compatibility."""
        print("🔗 OpenAI SDK Integration Example")
        print("-" * 50)
        
        try:
            # Basic chat completion
            response = openai.ChatCompletion.create(
                model="gemma3n:e2b",
                messages=[
                    {"role": "system", "content": "You are an expert in symbolic reasoning and logic."},
                    {"role": "user", "content": "Explain the difference between deductive and inductive reasoning with examples."}
                ],
                temperature=0.7,
                max_tokens=500
            )
            
            print("✅ OpenAI SDK Response:")
            print(response.choices[0].message.content[:200] + "...")
            print(f"📊 Usage: {response.usage.total_tokens} tokens")
            
        except Exception as e:
            print(f"❌ OpenAI SDK Error: {e}")
    
    def direct_api_example(self):
        """Demonstrate direct REST API usage."""
        print("\n🌐 Direct REST API Example")
        print("-" * 50)
        
        try:
            payload = {
                "model": "gemma3n:e2b",
                "messages": [
                    {"role": "user", "content": "What is symbolic AI and how does it differ from neural networks?"}
                ],
                "temperature": 0.7,
                "max_tokens": 300
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                content = data["choices"][0]["message"]["content"]
                print("✅ Direct API Response:")
                print(content[:200] + "...")
                print(f"⏱️ Response time: {duration:.2f}s")
            else:
                print(f"❌ API Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Direct API Error: {e}")
    
    def streaming_example(self):
        """Demonstrate streaming responses."""
        print("\n🔄 Streaming Response Example")
        print("-" * 50)
        
        try:
            payload = {
                "model": "gemma3n:e2b",
                "messages": [
                    {"role": "user", "content": "Explain machine learning in simple terms, step by step."}
                ],
                "stream": True,
                "temperature": 0.7,
                "max_tokens": 400
            }
            
            print("📡 Streaming response:")
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=30
            )
            
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        line_str = line.decode('utf-8')
                        if line_str.startswith('data: '):
                            data_str = line_str[6:]  # Remove 'data: ' prefix
                            if data_str.strip() == '[DONE]':
                                break
                            try:
                                data = json.loads(data_str)
                                if 'choices' in data and data['choices']:
                                    delta = data['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        print(content, end='', flush=True)
                            except json.JSONDecodeError:
                                continue
                print("\n✅ Streaming completed")
            else:
                print(f"❌ Streaming Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Streaming Error: {e}")
    
    def document_management_example(self):
        """Demonstrate document management capabilities."""
        print("\n📚 Document Management Example")
        print("-" * 50)
        
        try:
            # Add a document
            document = {
                "content": """
                Neural-symbolic AI represents a paradigm that combines the learning capabilities 
                of neural networks with the reasoning capabilities of symbolic AI systems. 
                This hybrid approach enables systems to both learn from data and perform 
                logical reasoning, making them more interpretable and capable of handling 
                complex tasks that require both pattern recognition and logical inference.
                """,
                "metadata": {
                    "title": "Neural-Symbolic AI Overview",
                    "category": "ai_concepts",
                    "source": "integration_demo",
                    "timestamp": time.time()
                }
            }
            
            response = requests.post(
                f"{self.base_url}/documents/add",
                headers=self.headers,
                json=document,
                timeout=10
            )
            
            if response.status_code == 200:
                doc_data = response.json()
                print(f"✅ Document added: {doc_data.get('document_id', 'Unknown ID')}")
                
                # Query with enhanced context
                enhanced_query = {
                    "model": "gemma3n:e2b",
                    "messages": [
                        {"role": "user", "content": "Based on the documents you have access to, explain how neural-symbolic AI works and its advantages."}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 300
                }
                
                query_response = requests.post(
                    f"{self.base_url}/v1/chat/completions",
                    headers=self.headers,
                    json=enhanced_query,
                    timeout=30
                )
                
                if query_response.status_code == 200:
                    query_data = query_response.json()
                    content = query_data["choices"][0]["message"]["content"]
                    print("📖 Enhanced context response:")
                    print(content[:200] + "...")
                else:
                    print(f"❌ Query Error: {query_response.status_code}")
            else:
                print(f"❌ Document Add Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Document Management Error: {e}")
    
    def performance_monitoring_example(self):
        """Demonstrate performance monitoring capabilities."""
        print("\n📊 Performance Monitoring Example")
        print("-" * 50)
        
        try:
            response = requests.get(
                f"{self.base_url}/performance",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                metrics = response.json()
                
                print("📈 Performance Metrics:")
                
                # Cache statistics
                if 'cache_stats' in metrics:
                    cache = metrics['cache_stats']
                    hit_rate = cache.get('hit_rate', 0) * 100
                    print(f"  💾 Cache Hit Rate: {hit_rate:.1f}%")
                    print(f"  📦 Cache Size: {cache.get('size', 0)}/{cache.get('max_size', 0)}")
                
                # System metrics
                if 'system_stats' in metrics:
                    system = metrics['system_stats']
                    print(f"  🖥️ CPU Usage: {system.get('cpu_usage', 0):.1f}%")
                    print(f"  💾 Memory Usage: {system.get('memory_usage_mb', 0):.1f} MB")
                    if 'gpu_usage' in system:
                        print(f"  🎮 GPU Usage: {system.get('gpu_usage', 0):.1f}%")
                
                # Response times
                if 'response_times' in metrics:
                    times = metrics['response_times']
                    print(f"  ⏱️ Avg Response Time: {times.get('avg', 0):.2f}s")
                    print(f"  🚀 Min Response Time: {times.get('min', 0):.2f}s")
                    print(f"  🐌 Max Response Time: {times.get('max', 0):.2f}s")
                
                print("✅ Performance monitoring successful")
            else:
                print(f"❌ Performance Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Performance Monitoring Error: {e}")
    
    def reasoning_capabilities_demo(self):
        """Demonstrate advanced reasoning capabilities."""
        print("\n🧠 Advanced Reasoning Capabilities Demo")
        print("-" * 50)
        
        reasoning_tasks = [
            {
                "name": "Logical Deduction",
                "prompt": "If all programmers drink coffee, and Alice is a programmer, what can we conclude about Alice's coffee consumption? Explain your reasoning step by step."
            },
            {
                "name": "Mathematical Reasoning",
                "prompt": "A train travels 120 miles in 2 hours. At this constant rate, how far will it travel in 5 hours? Show your calculation and reasoning."
            },
            {
                "name": "Comparative Analysis",
                "prompt": "Compare and contrast supervised learning and unsupervised learning in machine learning. Provide examples of each."
            }
        ]
        
        for task in reasoning_tasks:
            print(f"\n🎯 {task['name']}:")
            try:
                payload = {
                    "model": "gemma3n:e2b",
                    "messages": [
                        {"role": "system", "content": "You are an expert in logic and reasoning. Provide clear, step-by-step explanations."},
                        {"role": "user", "content": task['prompt']}
                    ],
                    "temperature": 0.3,  # Lower temperature for more focused reasoning
                    "max_tokens": 400
                }
                
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/v1/chat/completions",
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                duration = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    content = data["choices"][0]["message"]["content"]
                    print(f"Response ({duration:.2f}s): {content[:150]}...")
                else:
                    print(f"❌ Error: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def client_compatibility_test(self):
        """Test compatibility with different client patterns."""
        print("\n🔧 Client Compatibility Test")
        print("-" * 50)
        
        # Test different message formats
        test_cases = [
            {
                "name": "Simple User Message",
                "messages": [{"role": "user", "content": "Hello, how are you?"}]
            },
            {
                "name": "System + User Messages",
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "What is AI?"}
                ]
            },
            {
                "name": "Multi-turn Conversation",
                "messages": [
                    {"role": "user", "content": "What is machine learning?"},
                    {"role": "assistant", "content": "Machine learning is a subset of AI that enables computers to learn from data."},
                    {"role": "user", "content": "Can you give me an example?"}
                ]
            }
        ]
        
        for test_case in test_cases:
            print(f"\n🧪 Testing: {test_case['name']}")
            try:
                payload = {
                    "model": "gemma3n:e2b",
                    "messages": test_case['messages'],
                    "temperature": 0.7,
                    "max_tokens": 100
                }
                
                response = requests.post(
                    f"{self.base_url}/v1/chat/completions",
                    headers=self.headers,
                    json=payload,
                    timeout=15
                )
                
                if response.status_code == 200:
                    print("  ✅ Compatible")
                else:
                    print(f"  ❌ Error: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Exception: {e}")


def main():
    """Main demonstration function."""
    print("🚀 Neural Symbolic Language Model - Client Integration Demo")
    print("=" * 70)
    
    # Configuration
    API_KEY = "your-api-key"  # Replace with your actual API key
    BASE_URL = "http://localhost:8080"
    
    # Initialize client
    client = NeuralSymbolicClient(API_KEY, BASE_URL)
    
    # Health check
    print("🔍 Checking API health...")
    if not client.health_check():
        print("❌ API is not accessible. Please ensure:")
        print("   1. Neural Symbolic Language Model is running: python src/main.py")
        print("   2. API key is configured correctly")
        print("   3. Port 8080 is accessible")
        return
    
    print("✅ API is healthy and accessible")
    
    # Run demonstrations
    try:
        client.openai_sdk_example()
        client.direct_api_example()
        client.streaming_example()
        client.document_management_example()
        client.performance_monitoring_example()
        client.reasoning_capabilities_demo()
        client.client_compatibility_test()
        
        print("\n" + "=" * 70)
        print("🎉 All integration examples completed successfully!")
        print("\n📝 Next Steps:")
        print("1. Replace 'your-api-key' with your actual API key")
        print("2. Integrate with your preferred client application")
        print("3. Explore the comprehensive integration guide: docs/client_integration_guide.md")
        print("4. Test with your specific use cases")
        
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":
    main()
