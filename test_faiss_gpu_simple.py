import numpy as np
import time
import faiss

def benchmark_faiss_gpu():
    print("FAISS GPU Simple Benchmark")
    print("=========================")
    
    # Print FAISS and GPU info
    print(f"FAISS version: {faiss.__version__}")
    print(f"Number of GPUs available: {faiss.get_num_gpus()}")
    
    # Parameters for different dataset sizes
    dimensions = 768  # Fixed dimension (e.g., BERT embeddings)
    dataset_sizes = [
        10_000,     # Small
        100_000,    # Medium
        1_000_000,  # Large
        5_000_000   # Very Large (may require significant GPU memory)
    ]
    
    query_size = 100  # Number of query vectors
    k = 10  # Number of nearest neighbors to retrieve
    
    # Initialize GPU resources
    res = faiss.StandardGpuResources()
    
    print("\n=== Benchmark Results ===")
    print(f"{'Dataset Size':<12} | {'CPU Time (s)':<12} | {'GPU Time (s)':<12} | {'Speedup':<10}")
    print("-" * 60)
    
    for num_vectors in dataset_sizes:
        print(f"\n=== Testing with {num_vectors:,} vectors ===")
        
        # Generate random database vectors
        print(f"Generating {num_vectors:,} random vectors...")
        np.random.seed(123)  # For reproducibility
        database_vectors = np.random.random((num_vectors, dimensions)).astype('float32')
        
        # Generate random query vectors
        query_vectors = np.random.random((query_size, dimensions)).astype('float32')
        
        # Create and test CPU index
        print("Testing CPU index...")
        cpu_index = faiss.IndexFlatL2(dimensions)
        cpu_index.add(database_vectors)
        
        # Time CPU search
        start_time = time.time()
        cpu_distances, cpu_indices = cpu_index.search(query_vectors, k)
        cpu_time = time.time() - start_time
        
        print(f"CPU search time for {query_size} queries: {cpu_time:.4f} seconds")
        
        # Create and test GPU index
        print("Testing GPU index...")
        gpu_index = faiss.index_cpu_to_gpu(res, 0, faiss.IndexFlatL2(dimensions))
        gpu_index.add(database_vectors)
        
        # Time GPU search
        start_time = time.time()
        gpu_distances, gpu_indices = gpu_index.search(query_vectors, k)
        gpu_time = time.time() - start_time
        
        print(f"GPU search time for {query_size} queries: {gpu_time:.4f} seconds")
        
        # Calculate speedup
        speedup = cpu_time / gpu_time if gpu_time > 0 else float('inf')
        
        # Print results
        print(f"Speedup: {speedup:.2f}x")
        print(f"{num_vectors:<12,} | {cpu_time:<12.4f} | {gpu_time:<12.4f} | {speedup:<10.2f}x")
        
        # Verify results (just check first query to save time)
        indices_match = np.array_equal(cpu_indices[0], gpu_indices[0])
        distances_close = np.allclose(cpu_distances[0], gpu_distances[0], rtol=1e-4)
        print(f"Results match: {indices_match and distances_close}")
    
    print("\nBenchmark completed!")

if __name__ == "__main__":
    benchmark_faiss_gpu()
