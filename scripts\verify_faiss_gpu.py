#!/usr/bin/env python
"""
FAISS-GPU Verification Script

This script verifies that FAISS-GPU is correctly installed and functioning by:
1. Checking if FAISS can be imported
2. Verifying CUDA availability
3. Testing basic FAISS-GPU functionality by creating a simple index
4. Benchmarking search performance on CPU vs GPU
"""

import os
import sys
import time
import numpy as np
import traceback

def print_section(title):
    """Print a section title with formatting."""
    print("\n" + "="*50)
    print(f" {title} ".center(50, "-"))
    print("="*50)

def print_result(test_name, result):
    """Print a test result with consistent formatting."""
    status = "✅ PASS" if result else "❌ FAIL"
    print(f"{test_name}: {status}")
    return result

def check_import():
    """Check if FAISS can be imported."""
    print_section("IMPORT CHECK")
    
    try:
        import faiss
        print(f"FAISS version: {faiss.__version__}")
        return print_result("Import FAISS", True)
    except ImportError as e:
        print(f"Error importing FAISS: {e}")
        print("\nPossible solutions:")
        print("1. Make sure you've installed FAISS-GPU via Conda: conda install -c conda-forge faiss-gpu")
        print("2. Check if you're using the correct Python environment")
        print("3. Verify Visual C++ Redistributable is installed (Windows)")
        return print_result("Import FAISS", False)

def check_cuda():
    """Check if CUDA is available."""
    print_section("CUDA CHECK")
    
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        
        if cuda_available:
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            print(f"PyTorch version: {torch.__version__}")
            print(f"CUDA version: {torch.version.cuda}")
            print(f"GPU devices available: {device_count}")
            print(f"GPU device name: {device_name}")
            return print_result("CUDA Available", True)
        else:
            print("CUDA is not available through PyTorch.")
            print("\nPossible solutions:")
            print("1. Make sure you have NVIDIA GPU drivers installed")
            print("2. Check if PyTorch is installed with CUDA support")
            print("3. Try reinstalling PyTorch with CUDA: conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia")
            return print_result("CUDA Available", False)
    except ImportError:
        print("PyTorch is not installed, cannot check CUDA availability.")
        return print_result("CUDA Available", False)

def test_faiss_gpu():
    """Test basic FAISS-GPU functionality."""
    print_section("FAISS-GPU FUNCTIONALITY")
    
    try:
        import faiss
        
        # Basic parameters
        d = 128                           # Dimension
        nb = 100000                       # Database size
        nq = 1000                         # Number of queries
        
        print(f"Creating dataset with {nb} vectors of dimension {d}...")
        xb = np.random.random((nb, d)).astype('float32')
        xb[:, 0] += np.arange(nb) / 1000.  # Add some structure to the data
        xq = np.random.random((nq, d)).astype('float32')
        xq[:, 0] += np.arange(nq) / 1000.
        
        # CPU index
        print("\nTesting on CPU...")
        cpu_index = faiss.IndexFlatL2(d)
        cpu_index.add(xb)
        
        cpu_start = time.time()
        D1, I1 = cpu_index.search(xq, 5)  # Search 5 nearest neighbors
        cpu_time = time.time() - cpu_start
        print(f"CPU search time: {cpu_time:.4f} seconds")
        
        # GPU index
        print("\nTesting on GPU...")
        try:
            # Create GPU resource manager
            res = faiss.StandardGpuResources()
            
            # Transfer index from CPU to GPU
            gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)
            
            gpu_start = time.time()
            D2, I2 = gpu_index.search(xq, 5)  # Search 5 nearest neighbors
            gpu_time = time.time() - gpu_start
            print(f"GPU search time: {gpu_time:.4f} seconds")
            
            # Check if results are consistent
            if np.allclose(D1, D2) and np.allclose(I1, I2):
                print("CPU and GPU results match!")
            else:
                print("Note: CPU and GPU results differ slightly (this is normal due to floating-point precision)")
            
            # Compare performance
            speedup = cpu_time / gpu_time
            print(f"GPU speedup: {speedup:.2f}x faster than CPU")
            
            if speedup > 1:
                print("\nGPU acceleration is working correctly!")
            else:
                print("\nGPU is slower than CPU, which is unexpected. Check your GPU configuration.")
            
            return print_result("FAISS-GPU Functionality", True)
        
        except Exception as e:
            print(f"Error using GPU index: {e}")
            print("\nPossible solutions:")
            print("1. Make sure FAISS-GPU is installed correctly")
            print("2. Verify that Visual C++ Redistributable is installed (Windows)")
            print("3. Check if CUDA is properly configured")
            return print_result("FAISS-GPU Functionality", False)
    
    except Exception as e:
        print(f"Error testing FAISS-GPU: {e}")
        traceback.print_exc()
        return print_result("FAISS-GPU Functionality", False)

def check_environment():
    """Check the Python environment for potential issues."""
    print_section("ENVIRONMENT CHECK")
    
    # Python executable
    python_path = sys.executable
    print(f"Python executable: {python_path}")
    
    # Python version
    python_version = sys.version
    print(f"Python version: {python_version}")
    
    # Check if running in Conda environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"Conda environment: {conda_env}")
    else:
        print("Not running in a Conda environment")
        print("WARNING: FAISS-GPU on Windows works best with Conda")
    
    # Check site-packages for problematic installations
    import site
    site_packages = site.getsitepackages()[0]
    print(f"Site-packages directory: {site_packages}")
    
    # Check for problematic FAISS directories
    problematic_dirs = []
    for item in os.listdir(site_packages):
        if item.startswith('~faiss') or (item.startswith('faiss') and item.endswith('.dist-info')):
            problematic_dirs.append(item)
    
    if problematic_dirs:
        print("Potentially problematic directories found:")
        for dir_name in problematic_dirs:
            print(f"- {dir_name}")
        print("\nConsider removing these directories and reinstalling FAISS-GPU")
    else:
        print("No problematic FAISS directories found")
    
    return print_result("Environment Check", len(problematic_dirs) == 0)

def main():
    """Run all verification checks."""
    print_section("FAISS-GPU VERIFICATION")
    print("This script checks if FAISS-GPU is properly installed and working.")
    
    # Run all checks
    import_ok = check_import()
    if not import_ok:
        print("\nFAISS import failed. Skipping remaining tests.")
        sys.exit(1)
    
    cuda_ok = check_cuda()
    env_ok = check_environment()
    gpu_ok = test_faiss_gpu()
    
    # Final summary
    print_section("SUMMARY")
    print(f"FAISS Import: {'✅' if import_ok else '❌'}")
    print(f"CUDA Available: {'✅' if cuda_ok else '❌'}")
    print(f"Environment Check: {'✅' if env_ok else '❌'}")
    print(f"FAISS-GPU Functionality: {'✅' if gpu_ok else '❌'}")
    
    all_ok = import_ok and cuda_ok and env_ok and gpu_ok
    print("\nOverall result:", "✅ PASS" if all_ok else "❌ ISSUES DETECTED")
    
    if not all_ok:
        print("\nPlease check the output above for error details and suggested solutions.")
        print("For more help, see the troubleshooting section in the README.md file.")
    else:
        print("\nCongratulations! FAISS-GPU is correctly installed and working.")

if __name__ == "__main__":
    main()
