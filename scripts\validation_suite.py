#!/usr/bin/env python3
"""Comprehensive validation suite for Neural Symbolic Language Model.

This script runs all validation checks required before deployment:
- Code quality checks (linting, formatting, type checking)
- Test suite execution with coverage analysis
- Security scanning and vulnerability assessment
- Performance benchmarking and validation
- Documentation completeness verification
"""

import subprocess
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import argparse


class ValidationSuite:
    """Comprehensive validation suite for the project."""
    
    def __init__(self, project_root: Optional[Path] = None, verbose: bool = False):
        """Initialize the validation suite.
        
        Args:
            project_root: Root directory of the project
            verbose: Enable verbose output
        """
        self.project_root = project_root or Path(__file__).parent.parent
        self.verbose = verbose
        self.results = {
            'code_quality': {},
            'tests': {},
            'security': {},
            'performance': {},
            'documentation': {},
            'overall': {}
        }
    
    def run_full_validation(self) -> Dict[str, Any]:
        """Run the complete validation suite.
        
        Returns:
            Dictionary containing all validation results
        """
        print("🔍 Starting comprehensive validation suite...")
        start_time = time.time()
        
        # Run all validation checks
        checks = [
            ("Code Quality", self.validate_code_quality),
            ("Test Suite", self.validate_tests),
            ("Security", self.validate_security),
            ("Performance", self.validate_performance),
            ("Documentation", self.validate_documentation)
        ]
        
        for check_name, check_func in checks:
            print(f"\n{'='*60}")
            print(f"🔍 {check_name} Validation")
            print('='*60)
            
            try:
                check_func()
            except Exception as e:
                print(f"❌ {check_name} validation failed: {e}")
                self.results[check_name.lower().replace(' ', '_')]['status'] = 'FAILED'
                self.results[check_name.lower().replace(' ', '_')]['error'] = str(e)
        
        # Generate overall results
        self.generate_overall_results(time.time() - start_time)
        
        return self.results
    
    def validate_code_quality(self):
        """Validate code quality with linting, formatting, and type checking."""
        self.results['code_quality'] = {
            'black': self.run_black_check(),
            'isort': self.run_isort_check(),
            'flake8': self.run_flake8_check(),
            'mypy': self.run_mypy_check(),
            'status': 'PENDING'
        }
        
        # Determine overall status
        all_passed = all(
            result.get('passed', False) 
            for result in self.results['code_quality'].values() 
            if isinstance(result, dict)
        )
        
        self.results['code_quality']['status'] = 'PASSED' if all_passed else 'FAILED'
        
        if all_passed:
            print("✅ All code quality checks passed")
        else:
            print("❌ Some code quality checks failed")
    
    def run_black_check(self) -> Dict[str, Any]:
        """Run Black code formatting check."""
        print("🎨 Checking code formatting with Black...")
        
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'black', '--check', '--diff', 'src/', 'tests/'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            passed = result.returncode == 0
            
            if passed:
                print("  ✅ Code formatting is correct")
            else:
                print("  ❌ Code formatting issues found")
                if self.verbose:
                    print(f"  Output: {result.stdout[:500]}")
            
            return {
                'passed': passed,
                'output': result.stdout,
                'errors': result.stderr
            }
            
        except FileNotFoundError:
            print("  ⚠️  Black not installed")
            return {'passed': False, 'error': 'Black not installed'}
    
    def run_isort_check(self) -> Dict[str, Any]:
        """Run isort import sorting check."""
        print("📦 Checking import sorting with isort...")
        
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'isort', '--check-only', '--diff', 'src/', 'tests/'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            passed = result.returncode == 0
            
            if passed:
                print("  ✅ Import sorting is correct")
            else:
                print("  ❌ Import sorting issues found")
                if self.verbose:
                    print(f"  Output: {result.stdout[:500]}")
            
            return {
                'passed': passed,
                'output': result.stdout,
                'errors': result.stderr
            }
            
        except FileNotFoundError:
            print("  ⚠️  isort not installed")
            return {'passed': False, 'error': 'isort not installed'}
    
    def run_flake8_check(self) -> Dict[str, Any]:
        """Run flake8 linting check."""
        print("🔍 Running linting with flake8...")
        
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'flake8', 'src/', 'tests/'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            passed = result.returncode == 0
            
            if passed:
                print("  ✅ No linting issues found")
            else:
                print("  ❌ Linting issues found")
                if self.verbose:
                    print(f"  Issues: {result.stdout[:500]}")
            
            return {
                'passed': passed,
                'output': result.stdout,
                'errors': result.stderr
            }
            
        except FileNotFoundError:
            print("  ⚠️  flake8 not installed")
            return {'passed': False, 'error': 'flake8 not installed'}
    
    def run_mypy_check(self) -> Dict[str, Any]:
        """Run mypy type checking."""
        print("🔍 Running type checking with mypy...")
        
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'mypy', 'src/'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            # mypy returns 0 for success, 1 for type errors, 2 for other errors
            passed = result.returncode == 0
            
            if passed:
                print("  ✅ No type checking issues found")
            else:
                print("  ❌ Type checking issues found")
                if self.verbose:
                    print(f"  Issues: {result.stdout[:500]}")
            
            return {
                'passed': passed,
                'output': result.stdout,
                'errors': result.stderr
            }
            
        except FileNotFoundError:
            print("  ⚠️  mypy not installed")
            return {'passed': False, 'error': 'mypy not installed'}
    
    def validate_tests(self):
        """Validate test suite execution and coverage."""
        print("🧪 Running test suite with coverage...")
        
        try:
            # Run pytest with coverage
            result = subprocess.run(
                [sys.executable, '-m', 'pytest', 'tests/', '-v', '--cov=src', '--cov-report=json', '--cov-report=term'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            passed = result.returncode == 0
            
            # Parse coverage report if available
            coverage_data = self.parse_coverage_report()
            
            self.results['tests'] = {
                'passed': passed,
                'output': result.stdout,
                'errors': result.stderr,
                'coverage': coverage_data,
                'status': 'PASSED' if passed else 'FAILED'
            }
            
            if passed:
                coverage_percent = coverage_data.get('totals', {}).get('percent_covered', 0)
                print(f"  ✅ All tests passed (Coverage: {coverage_percent:.1f}%)")
                
                if coverage_percent < 90:
                    print(f"  ⚠️  Coverage below 90% threshold")
                    self.results['tests']['status'] = 'WARNING'
            else:
                print("  ❌ Some tests failed")
                if self.verbose:
                    print(f"  Output: {result.stdout[-1000:]}")
            
        except Exception as e:
            print(f"  ❌ Error running tests: {e}")
            self.results['tests'] = {
                'passed': False,
                'error': str(e),
                'status': 'FAILED'
            }
    
    def parse_coverage_report(self) -> Dict[str, Any]:
        """Parse coverage report from JSON file."""
        coverage_file = self.project_root / 'coverage.json'
        
        if coverage_file.exists():
            try:
                with open(coverage_file) as f:
                    return json.load(f)
            except Exception:
                pass
        
        return {}
    
    def validate_security(self):
        """Validate security scanning results."""
        print("🔒 Running security validation...")
        
        try:
            # Run the security scanner
            result = subprocess.run(
                [sys.executable, 'scripts/security_scan.py'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            # Security scanner returns 0 for pass, 1 for fail, 2 for warnings
            if result.returncode == 0:
                status = 'PASSED'
                print("  ✅ Security scan passed")
            elif result.returncode == 2:
                status = 'WARNING'
                print("  ⚠️  Security scan has warnings")
            else:
                status = 'FAILED'
                print("  ❌ Security scan failed")
            
            self.results['security'] = {
                'status': status,
                'output': result.stdout,
                'errors': result.stderr,
                'return_code': result.returncode
            }
            
        except Exception as e:
            print(f"  ❌ Error running security scan: {e}")
            self.results['security'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    def validate_performance(self):
        """Validate performance benchmarks."""
        print("⚡ Running performance validation...")
        
        try:
            # Run the benchmark script
            result = subprocess.run(
                [sys.executable, 'simple_ollama_benchmark.py'],
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=300  # 5 minute timeout
            )
            
            passed = result.returncode == 0
            
            # Parse performance metrics from output
            performance_metrics = self.parse_performance_output(result.stdout)
            
            self.results['performance'] = {
                'passed': passed,
                'metrics': performance_metrics,
                'output': result.stdout,
                'errors': result.stderr,
                'status': 'PASSED' if passed else 'FAILED'
            }
            
            if passed:
                print("  ✅ Performance benchmarks passed")
                if performance_metrics:
                    avg_time = performance_metrics.get('average_response_time', 0)
                    success_rate = performance_metrics.get('success_rate', 0)
                    print(f"  📊 Avg response time: {avg_time:.2f}s, Success rate: {success_rate:.1f}%")
            else:
                print("  ❌ Performance benchmarks failed")
            
        except subprocess.TimeoutExpired:
            print("  ⏰ Performance benchmark timed out")
            self.results['performance'] = {
                'passed': False,
                'status': 'FAILED',
                'error': 'Benchmark timed out'
            }
        except Exception as e:
            print(f"  ❌ Error running performance benchmark: {e}")
            self.results['performance'] = {
                'passed': False,
                'status': 'FAILED',
                'error': str(e)
            }
    
    def parse_performance_output(self, output: str) -> Dict[str, Any]:
        """Parse performance metrics from benchmark output."""
        metrics = {}
        
        # Simple parsing - look for key metrics in output
        lines = output.split('\n')
        for line in lines:
            if 'Average Response Time:' in line:
                try:
                    time_str = line.split(':')[1].strip().replace('s', '')
                    metrics['average_response_time'] = float(time_str)
                except (IndexError, ValueError):
                    pass
            elif 'Success Rate:' in line:
                try:
                    rate_str = line.split(':')[1].strip().replace('%', '')
                    metrics['success_rate'] = float(rate_str)
                except (IndexError, ValueError):
                    pass
        
        return metrics
    
    def validate_documentation(self):
        """Validate documentation completeness."""
        print("📚 Validating documentation...")
        
        required_docs = [
            'README.md',
            'docs/api.md',
            'docs/deployment_guide.md',
            'docs/ollama_gemma3n_guide.md',
            'docs/dependencies.md'
        ]
        
        missing_docs = []
        for doc in required_docs:
            doc_path = self.project_root / doc
            if not doc_path.exists():
                missing_docs.append(doc)
        
        passed = len(missing_docs) == 0
        
        self.results['documentation'] = {
            'passed': passed,
            'required_docs': required_docs,
            'missing_docs': missing_docs,
            'status': 'PASSED' if passed else 'FAILED'
        }
        
        if passed:
            print("  ✅ All required documentation present")
        else:
            print(f"  ❌ Missing documentation: {', '.join(missing_docs)}")
    
    def generate_overall_results(self, total_time: float):
        """Generate overall validation results."""
        # Count passed/failed checks
        statuses = []
        for category in ['code_quality', 'tests', 'security', 'performance', 'documentation']:
            status = self.results[category].get('status', 'UNKNOWN')
            statuses.append(status)
        
        # Determine overall status
        if 'FAILED' in statuses:
            overall_status = 'FAILED'
        elif 'WARNING' in statuses:
            overall_status = 'WARNING'
        else:
            overall_status = 'PASSED'
        
        self.results['overall'] = {
            'status': overall_status,
            'total_time': total_time,
            'summary': {
                'passed': statuses.count('PASSED'),
                'failed': statuses.count('FAILED'),
                'warnings': statuses.count('WARNING'),
                'total': len(statuses)
            }
        }
    
    def print_summary(self):
        """Print a summary of validation results."""
        overall = self.results['overall']
        summary = overall['summary']
        
        print("\n" + "="*60)
        print("📊 VALIDATION SUMMARY")
        print("="*60)
        
        status_emoji = {
            'PASSED': '✅',
            'WARNING': '⚠️',
            'FAILED': '❌'
        }
        
        print(f"Overall Status: {status_emoji.get(overall['status'], '❓')} {overall['status']}")
        print(f"Total Time: {overall['total_time']:.1f} seconds")
        print()
        print(f"✅ Passed: {summary['passed']}")
        print(f"⚠️  Warnings: {summary['warnings']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"📊 Total: {summary['total']}")
        
        # Category breakdown
        print("\nCategory Results:")
        for category in ['code_quality', 'tests', 'security', 'performance', 'documentation']:
            status = self.results[category].get('status', 'UNKNOWN')
            emoji = status_emoji.get(status, '❓')
            print(f"  {emoji} {category.replace('_', ' ').title()}: {status}")
        
        if overall['status'] == 'FAILED':
            print("\n❌ VALIDATION FAILED - Fix issues before deployment")
        elif overall['status'] == 'WARNING':
            print("\n⚠️  VALIDATION WARNINGS - Review before deployment")
        else:
            print("\n✅ ALL VALIDATIONS PASSED - Ready for deployment")


def main():
    """Main entry point for the validation suite."""
    parser = argparse.ArgumentParser(description='Comprehensive validation suite')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    parser.add_argument('--project-root', type=Path, help='Project root directory')
    parser.add_argument('--output', '-o', type=Path, help='Output file for results')
    
    args = parser.parse_args()
    
    # Initialize validation suite
    validator = ValidationSuite(args.project_root, args.verbose)
    
    # Run validation
    results = validator.run_full_validation()
    
    # Print summary
    validator.print_summary()
    
    # Save results if requested
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Results saved to: {args.output}")
    
    # Exit with appropriate code
    status = results['overall']['status']
    if status == 'FAILED':
        sys.exit(1)
    elif status == 'WARNING':
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == '__main__':
    main()
