Neural Symbolic Language Model Documentation
==========================================

Welcome to the Neural Symbolic Language Model documentation. This is a production-ready
AI system that combines symbolic reasoning with neural language processing capabilities,
optimized for Ollama and the gemma3n:e2b model with multi-modal support.

.. image:: https://img.shields.io/badge/version-1.0.0-blue.svg
   :target: https://github.com/your-org/neural-symbolic-language-model
   :alt: Version

.. image:: https://img.shields.io/badge/python-3.10+-blue.svg
   :target: https://python.org
   :alt: Python Version

.. image:: https://img.shields.io/badge/ollama-compatible-green.svg
   :target: https://ollama.com
   :alt: Ollama Compatible

.. image:: https://img.shields.io/badge/gemma3n:e2b-optimized-orange.svg
   :target: https://ollama.com/library/gemma3n
   :alt: Gemma3n Optimized

.. image:: https://img.shields.io/badge/license-MIT-green.svg
   :target: https://opensource.org/licenses/MIT
   :alt: License

Overview
--------

The Neural Symbolic Language Model is an enterprise-grade AI system that provides:

* **🤖 Ollama Integration**: Optimized for local LLM deployment with gemma3n:e2b
* **🌐 OpenAI-Compatible API**: Drop-in replacement for OpenAI's chat completions API
* **🧠 Symbolic Reasoning**: Advanced logical reasoning and multi-modal capabilities
* **📚 Hybrid Retrieval**: Intelligent document processing with vector search, keyword search, website content extraction, GitHub repository analysis, and comprehensive document management
* **🔗 Client Integration**: Works with Open WebUI, LibreChat, Continue.dev, and more
* **🔒 Production Security**: Enterprise-grade authentication, rate limiting, and monitoring
* **⚡ High Performance**: GPU acceleration, concurrent processing, and intelligent caching
* **📊 Comprehensive Monitoring**: Full observability with structured logging and metrics
* **🎯 Multi-Modal Support**: Text, code, and structured data processing with gemma3n:e2b

Quick Start
-----------

**Option 1: Docker Deployment (Recommended)**

.. code-block:: bash

   # Clone and start with Docker Compose
   git clone https://github.com/your-org/neural-symbolic-language-model.git
   cd neural-symbolic-language-model
   docker-compose up -d

   # Pull the gemma3n:e2b model
   docker exec ollama ollama pull gemma3n:e2b

   # Verify installation
   curl http://localhost:8080/health

**Option 2: Native Installation**

.. code-block:: bash

   # 1. Install Ollama and pull model (http://localhost:11434)
   curl -fsSL https://ollama.com/install.sh | sh
   ollama serve
   ollama pull gemma3n:e2b

   # 2. Install dependencies
   pip install -r requirements-prod.txt

   # 3. Configure environment (MODEL_REASONING_ENGINE=ollama, MODEL_REASONING_MODEL=gemma3n:e2b)
   cp config/development.env .env
   # Edit .env with your settings

   # 4. Run the server
   python src/main.py

**Option 3: Quick Setup Script**

.. code-block:: bash

   # Run automated setup_client_integration.sh script
   bash scripts/setup_client_integration.sh

   # Choose your preferred client applications
   # Script handles Ollama, model pulling, and client configuration

**API Usage Examples**

.. code-block:: python

   # Basic chat completion with gemma3n:e2b
   import requests

   response = requests.post(
       "http://localhost:8080/v1/chat/completions",
       headers={"Authorization": "Bearer your-api-key"},
       json={
           "model": "gemma3n:e2b",
           "messages": [
               {"role": "user", "content": "Explain symbolic reasoning in AI"}
           ],
           "temperature": 0.7,
           "max_tokens": 1000
       }
   )

   print(response.json())

.. code-block:: python

   # Add documents to hybrid retrieval system
   doc_response = requests.post(
       "http://localhost:8080/documents/add",
       headers={"Authorization": "Bearer your-api-key"},
       json={
           "content": "Neural networks learn through backpropagation...",
           "metadata": {
               "title": "Neural Network Basics",
               "category": "ai_concepts",
               "tags": ["neural-networks", "machine-learning"]
           }
       }
   )

.. code-block:: python

   # OpenAI SDK compatibility
   import openai

   openai.api_base = "http://localhost:8080/v1"
   openai.api_key = "your-api-key"

   response = openai.ChatCompletion.create(
       model="gemma3n:e2b",
       messages=[
           {"role": "system", "content": "You are an expert in symbolic reasoning."},
           {"role": "user", "content": "Compare deductive and inductive reasoning."}
       ]
   )

   print(response.choices[0].message.content)

**Try the Examples**

.. code-block:: bash

   # Run the comprehensive hybrid retrieval demo
   python examples/hybrid_retrieval_demo.py

   # Try the enhanced retriever example
   python examples/hybrid_retriever_example.py

   # Test client integration
   python examples/client_integration_demo.py

Table of Contents
-----------------

.. toctree::
   :maxdepth: 2
   :caption: User Guide

   getting_started
   ollama_gemma3n_guide
   client_integration_guide
   hybrid_retrieval_guide
   configuration
   deployment_guide

.. toctree::
   :maxdepth: 2
   :caption: API Documentation

   api_reference
   hybrid_retrieval_api_reference
   openai_compatibility
   multi_modal_support

.. toctree::
   :maxdepth: 2
   :caption: Developer Guide

   architecture
   modules
   contributing
   testing
   performance_optimization

.. toctree::
   :maxdepth: 2
   :caption: Integration Examples

   client_applications
   python_sdk_examples
   javascript_examples
   rest_api_examples
   hybrid_retrieval_demo
   client_integration_examples

.. toctree::
   :maxdepth: 1
   :caption: Additional Resources

   examples
   troubleshooting
   dependencies
   security_guide
   changelog
   license

Features
--------

Core Components
~~~~~~~~~~~~~~~

* **FastAPI Application**: Modern, fast web framework with automatic API documentation
* **Symbolic Reasoning Engine**: Advanced logical reasoning with multiple backend support
* **Vector Retrieval System**: Efficient semantic search using FAISS or ChromaDB
* **Security Layer**: Comprehensive authentication, authorization, and rate limiting
* **Monitoring System**: Real-time performance metrics and health monitoring
* **Configuration Management**: Environment-based configuration with validation

Security Features
~~~~~~~~~~~~~~~~~

* API key authentication with Bearer token support
* Rate limiting with configurable limits per client
* Input sanitization and validation
* CORS configuration for cross-origin requests
* Security headers for enhanced protection
* IP blocking for failed authentication attempts
* Request size limits and timeout handling

Performance Features
~~~~~~~~~~~~~~~~~~~~

* GPU acceleration for neural computations
* Intelligent caching with LRU and TTL policies
* Async/await patterns for concurrent processing
* Connection pooling for database operations
* Vector operation optimization
* Memory management with context managers

Monitoring & Observability
~~~~~~~~~~~~~~~~~~~~~~~~~~~

* Structured JSON logging with correlation IDs
* Performance metrics collection (Prometheus compatible)
* Health check endpoints for load balancers
* Request/response tracking and timing
* Error rate monitoring and alerting
* Cache performance metrics
* System resource monitoring

Architecture
------------

The system follows a modular architecture with clear separation of concerns:

.. code-block:: text

   ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
   │   FastAPI       │    │   Security      │    │   Monitoring    │
   │   Application   │────│   Layer         │────│   System        │
   └─────────────────┘    └─────────────────┘    └─────────────────┘
            │                       │                       │
   ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
   │   Symbolic      │    │   Vector        │    │   Configuration │
   │   Reasoning     │────│   Retrieval     │────│   Management    │
   └─────────────────┘    └─────────────────┘    └─────────────────┘

Production Deployment
---------------------

The system is production-ready with:

* **Docker Support**: Containerized deployment with multi-stage builds
* **Environment Configuration**: Separate configs for dev/staging/production
* **Health Checks**: Kubernetes-ready health and readiness probes
* **Scaling**: Horizontal scaling with multiple worker processes
* **Monitoring**: Integration with Prometheus, Grafana, and ELK stack
* **Security**: Production-hardened security configuration

Support
-------

* **Documentation**: Comprehensive API and user documentation
* **Examples**: Working examples for common use cases
* **Testing**: Full test suite with >85% coverage
* **Community**: GitHub issues and discussions

License
-------

This project is licensed under the MIT License - see the LICENSE file for details.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
