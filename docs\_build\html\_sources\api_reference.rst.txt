API Reference
=============

This section provides comprehensive API documentation for the Neural Symbolic Language Model.

REST API Endpoints
------------------

The application provides a RESTful API with OpenAI-compatible endpoints.

Base URL
~~~~~~~~

.. code-block:: text

   http://localhost:8000

Authentication
~~~~~~~~~~~~~~

All API endpoints require authentication using Bearer tokens:

.. code-block:: http

   Authorization: Bearer your-api-key-here

Chat Completions API
--------------------

OpenAI-Compatible Endpoint
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /v1/chat/completions

   Create a chat completion response.

   **Request Headers:**

   * ``Authorization`` -- Bearer token for authentication
   * ``Content-Type`` -- application/json

   **Request Body:**

   .. code-block:: json

      {
        "model": "local",
        "messages": [
          {
            "role": "user",
            "content": "What is symbolic reasoning?"
          }
        ],
        "max_tokens": 150,
        "temperature": 0.7,
        "stream": false
      }

   **Response:**

   .. code-block:: json

      {
        "id": "chatcmpl-123",
        "object": "chat.completion",
        "created": 1677652288,
        "model": "local",
        "choices": [
          {
            "index": 0,
            "message": {
              "role": "assistant",
              "content": "Symbolic reasoning is a method of problem-solving..."
            },
            "finish_reason": "stop"
          }
        ],
        "usage": {
          "prompt_tokens": 10,
          "completion_tokens": 20,
          "total_tokens": 30
        }
      }

   :statuscode 200: Success
   :statuscode 400: Bad Request - Invalid parameters
   :statuscode 401: Unauthorized - Invalid API key
   :statuscode 429: Too Many Requests - Rate limit exceeded
   :statuscode 500: Internal Server Error

Streaming Responses
~~~~~~~~~~~~~~~~~~~

Enable streaming by setting ``"stream": true`` in the request:

.. code-block:: json

   {
     "model": "local",
     "messages": [...],
     "stream": true
   }

**Streaming Response Format:**

.. code-block:: text

   data: {"id":"chatcmpl-123","object":"chat.completion.chunk",...}
   data: {"id":"chatcmpl-123","object":"chat.completion.chunk",...}
   data: [DONE]

System Information API
----------------------

System Info
~~~~~~~~~~~

.. http:get:: /system/info

   Get system information and status.

   **Response:**

   .. code-block:: json

      {
        "reasoner": {
          "status": "operational",
          "engine": "local",
          "model": "llama"
        },
        "retriever": {
          "status": "operational",
          "vector_db": "faiss",
          "index_size": 1000
        },
        "gpu_optimized": true,
        "version": "0.1.0"
      }

Health Check
~~~~~~~~~~~~

.. http:get:: /health

   Health check endpoint for load balancers.

   **Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "timestamp": "2025-06-29T12:00:00Z"
      }

Readiness Check
~~~~~~~~~~~~~~~

.. http:get:: /ready

   Readiness check endpoint for Kubernetes.

   **Response:**

   .. code-block:: json

      {
        "status": "ready",
        "components": {
          "reasoner": "ready",
          "retriever": "ready",
          "cache": "ready"
        }
      }

Performance Monitoring API
--------------------------

Performance Metrics
~~~~~~~~~~~~~~~~~~~

.. http:get:: /performance

   Get detailed performance metrics.

   **Response:**

   .. code-block:: json

      {
        "cache": {
          "size": 100,
          "max_size": 1000,
          "hits": 80,
          "misses": 20,
          "hit_rate": 0.8
        },
        "system": {
          "cpu_percent": 25.5,
          "memory_percent": 60.2,
          "gpu_available": true,
          "gpu_name": "NVIDIA RTX 4090",
          "active_requests": 5
        },
        "requests": {
          "total": 1000,
          "avg_duration": 0.5,
          "error_rate": 0.01,
          "cache_hit_rate": 0.8
        }
      }

Request Parameters
------------------

Common Parameters
~~~~~~~~~~~~~~~~~

All chat completion requests support these parameters:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Type
     - Description
   * - ``model``
     - string
     - Model identifier (required)
   * - ``messages``
     - array
     - List of conversation messages (required)
   * - ``max_tokens``
     - integer
     - Maximum tokens in response (optional, default: 150)
   * - ``temperature``
     - number
     - Response randomness 0.0-1.0 (optional, default: 0.7)
   * - ``top_p``
     - number
     - Nucleus sampling parameter (optional, default: 1.0)
   * - ``stream``
     - boolean
     - Enable streaming response (optional, default: false)
   * - ``stop``
     - array
     - Stop sequences (optional)

Message Format
~~~~~~~~~~~~~~

Each message in the ``messages`` array has this format:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Field
     - Type
     - Description
   * - ``role``
     - string
     - Message role: "user", "assistant", or "system"
   * - ``content``
     - string
     - Message content text
   * - ``name``
     - string
     - Optional message author name

Error Responses
---------------

Error Format
~~~~~~~~~~~~

All errors return a structured response:

.. code-block:: json

   {
     "error": {
       "code": "ERROR_CODE",
       "message": "Human-readable error message",
       "details": {
         "field": "additional_context"
       }
     }
   }

Error Codes
~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 30 20 50

   * - Error Code
     - HTTP Status
     - Description
   * - ``VALIDATION_ERROR``
     - 400
     - Invalid request format or parameters
   * - ``AUTHENTICATION_ERROR``
     - 401
     - Invalid or missing API key
   * - ``AUTHORIZATION_ERROR``
     - 403
     - Access denied or IP blocked
   * - ``RATE_LIMIT_ERROR``
     - 429
     - Rate limit exceeded
   * - ``REASONING_ERROR``
     - 500
     - Symbolic reasoning operation failed
   * - ``RETRIEVAL_ERROR``
     - 500
     - Vector retrieval operation failed
   * - ``INTERNAL_ERROR``
     - 500
     - Unexpected server error

Rate Limiting
-------------

Rate Limit Headers
~~~~~~~~~~~~~~~~~~

Responses include rate limiting information:

.. code-block:: http

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1677652348

Default Limits
~~~~~~~~~~~~~~

* **Requests per minute:** 100 (configurable)
* **Request size limit:** 10MB (configurable)
* **Concurrent requests:** 10 per client

CORS Support
------------

The API supports Cross-Origin Resource Sharing (CORS) with configurable origins.

**Allowed Methods:** GET, POST, PUT, DELETE, OPTIONS
**Allowed Headers:** Authorization, Content-Type, X-API-Key
**Credentials:** Supported

OpenAPI Documentation
---------------------

Interactive API documentation is available at:

* **Swagger UI:** ``/docs``
* **ReDoc:** ``/redoc``
* **OpenAPI JSON:** ``/openapi.json``

These endpoints provide interactive documentation where you can test API calls directly.
