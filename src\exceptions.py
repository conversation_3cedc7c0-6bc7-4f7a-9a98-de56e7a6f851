"""
Custom exception classes for the Neural Symbolic Language Model.

This module defines domain-specific exceptions with proper error codes
and structured error responses for better error handling and debugging.

Author: AI Assistant
Date: 2025-06-29
"""

from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class SymbolicAIException(Exception):
    """Base exception class for all SymbolicAI errors.

    Attributes:
        message: Human-readable error message
        error_code: Machine-readable error code
        details: Additional error details
        status_code: HTTP status code for API responses
    """

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 500
    ):
        """Initialize the exception.

        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
            status_code: HTTP status code for API responses
        """
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.details = details or {}
        self.status_code = status_code
        super().__init__(self.message)

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses.

        Returns:
            Dictionary representation of the exception
        """
        return {
            "error": {
                "code": self.error_code,
                "message": self.message,
                "details": self.details
            }
        }


class ValidationError(SymbolicAIException):
    """Raised when input validation fails."""

    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        """Initialize validation error.

        Args:
            message: Error message
            field: Field that failed validation
            **kwargs: Additional arguments for base class
        """
        details = kwargs.pop('details', {})
        if field:
            details['field'] = field

        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details,
            status_code=400,
            **kwargs
        )


class AuthenticationError(SymbolicAIException):
    """Raised when authentication fails."""

    def __init__(self, message: str = "Authentication failed", **kwargs):
        """Initialize authentication error.

        Args:
            message: Error message
            **kwargs: Additional arguments for base class
        """
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401,
            **kwargs
        )


class AuthorizationError(SymbolicAIException):
    """Raised when authorization fails."""

    def __init__(self, message: str = "Access denied", **kwargs):
        """Initialize authorization error.

        Args:
            message: Error message
            **kwargs: Additional arguments for base class
        """
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403,
            **kwargs
        )


class RateLimitError(SymbolicAIException):
    """Raised when rate limit is exceeded."""

    def __init__(self, message: str = "Rate limit exceeded", retry_after: Optional[int] = None, **kwargs):
        """Initialize rate limit error.

        Args:
            message: Error message
            retry_after: Seconds to wait before retrying
            **kwargs: Additional arguments for base class
        """
        details = kwargs.pop('details', {})
        if retry_after:
            details['retry_after'] = retry_after

        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=details,
            status_code=429,
            **kwargs
        )


class RetrievalError(SymbolicAIException):
    """Raised when retrieval operations fail."""

    def __init__(self, message: str, operation: Optional[str] = None, **kwargs):
        """Initialize retrieval error.

        Args:
            message: Error message
            operation: The retrieval operation that failed
            **kwargs: Additional arguments for base class
        """
        details = kwargs.pop('details', {})
        if operation:
            details['operation'] = operation

        super().__init__(
            message=message,
            error_code="RETRIEVAL_ERROR",
            details=details,
            status_code=500,
            **kwargs
        )


class ReasoningError(SymbolicAIException):
    """Raised when reasoning operations fail."""

    def __init__(self, message: str, reasoning_type: Optional[str] = None, **kwargs):
        """Initialize reasoning error.

        Args:
            message: Error message
            reasoning_type: The type of reasoning that failed
            **kwargs: Additional arguments for base class
        """
        details = kwargs.pop('details', {})
        if reasoning_type:
            details['reasoning_type'] = reasoning_type

        super().__init__(
            message=message,
            error_code="REASONING_ERROR",
            details=details,
            status_code=500,
            **kwargs
        )


class VectorStoreError(SymbolicAIException):
    """Raised when vector store operations fail."""

    def __init__(self, message: str, operation: Optional[str] = None, **kwargs):
        """Initialize vector store error.

        Args:
            message: Error message
            operation: The vector store operation that failed
            **kwargs: Additional arguments for base class
        """
        details = kwargs.pop('details', {})
        if operation:
            details['operation'] = operation

        super().__init__(
            message=message,
            error_code="VECTOR_STORE_ERROR",
            details=details,
            status_code=500,
            **kwargs
        )


class ConfigurationError(SymbolicAIException):
    """Raised when configuration is invalid or missing."""

    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        """Initialize configuration error.

        Args:
            message: Error message
            config_key: The configuration key that caused the error
            **kwargs: Additional arguments for base class
        """
        details = kwargs.pop('details', {})
        if config_key:
            details['config_key'] = config_key

        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details=details,
            status_code=500,
            **kwargs
        )


class ResourceNotFoundError(SymbolicAIException):
    """Raised when a requested resource is not found."""

    def __init__(self, message: str, resource_type: Optional[str] = None, resource_id: Optional[str] = None, **kwargs):
        """Initialize resource not found error.

        Args:
            message: Error message
            resource_type: Type of resource that was not found
            resource_id: ID of the resource that was not found
            **kwargs: Additional arguments for base class
        """
        details = kwargs.pop('details', {})
        if resource_type:
            details['resource_type'] = resource_type
        if resource_id:
            details['resource_id'] = resource_id

        super().__init__(
            message=message,
            error_code="RESOURCE_NOT_FOUND",
            details=details,
            status_code=404,
            **kwargs
        )


class ServiceUnavailableError(SymbolicAIException):
    """Raised when a service is temporarily unavailable."""

    def __init__(self, message: str, service_name: Optional[str] = None, **kwargs):
        """Initialize service unavailable error.

        Args:
            message: Error message
            service_name: Name of the unavailable service
            **kwargs: Additional arguments for base class
        """
        details = kwargs.pop('details', {})
        if service_name:
            details['service_name'] = service_name

        super().__init__(
            message=message,
            error_code="SERVICE_UNAVAILABLE",
            details=details,
            status_code=503,
            **kwargs
        )


def handle_exception(exc: Exception) -> SymbolicAIException:
    """Convert generic exceptions to SymbolicAI exceptions.

    Args:
        exc: The exception to convert

    Returns:
        SymbolicAIException instance
    """
    if isinstance(exc, SymbolicAIException):
        return exc

    # Map common exceptions to specific types
    if isinstance(exc, ValueError):
        return ValidationError(str(exc))
    elif isinstance(exc, FileNotFoundError):
        return ResourceNotFoundError(str(exc), resource_type="file")
    elif isinstance(exc, ConnectionError):
        return ServiceUnavailableError(str(exc))
    elif isinstance(exc, TimeoutError):
        return ServiceUnavailableError(f"Operation timed out: {str(exc)}")
    else:
        # Generic fallback
        return SymbolicAIException(
            message=f"Unexpected error: {str(exc)}",
            error_code="INTERNAL_ERROR",
            details={"exception_type": type(exc).__name__}
        )