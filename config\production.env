# Production Environment Configuration
# This file contains configuration settings for production environment
# IMPORTANT: Do not commit actual secrets to version control

# Application Settings
APP_TITLE="Neural Symbolic Language Model"
APP_VERSION="0.1.0"
APP_DEBUG=false
APP_ENVIRONMENT="production"
APP_HOST="0.0.0.0"
APP_PORT=8080
APP_WORKERS=4
APP_RELOAD=false

# Security Settings (Use environment variables for actual secrets)
# SECURITY_API_KEYS_JSON should be set via environment variable
SECURITY_RATE_LIMIT_REQUESTS=100
SECURITY_RATE_LIMIT_WINDOW=60
SECURITY_MAX_REQUEST_SIZE=1048576
# SECURITY_CORS_ORIGINS should be set to actual production domains
SECURITY_BLOCK_DURATION=3600
SECURITY_MAX_FAILED_ATTEMPTS=5

# Model Settings
MODEL_REASONING_ENGINE="ollama"
MODEL_REASONING_MODEL="llama2"
MODEL_EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
MODEL_EMBEDDING_DIMENSION=384
MODEL_USE_GPU=true
MODEL_GPU_MEMORY_FRACTION=0.8
MODEL_OLLAMA_HOST="http://ollama:11434"
MODEL_OLLAMA_TIMEOUT=60
MODEL_VECTOR_DB_BACKEND="faiss"

# Cache Settings
CACHE_ENABLED=true
CACHE_TTL=7200
CACHE_MAX_SIZE=10000
CACHE_BACKEND="redis"

# Logging Settings
LOG_LEVEL="INFO"
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE_ENABLED=true
LOG_FILE_PATH="/var/log/neural-symbolic-lm"
LOG_MAX_FILE_SIZE=104857600
LOG_BACKUP_COUNT=10
LOG_STRUCTURED=true

# Production-specific settings
PROD_ENABLE_MONITORING=true
PROD_ENABLE_METRICS=true
PROD_HEALTH_CHECK_INTERVAL=30
PROD_BACKUP_ENABLED=true
