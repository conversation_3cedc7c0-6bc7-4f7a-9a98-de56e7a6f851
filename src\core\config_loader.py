"""Configuration loader for environment-specific settings.

This module provides functionality to load configuration settings based on
the current environment (development, staging, production) with proper
validation and fallback mechanisms.
"""

# Standard library imports
import os
import json
import logging
from pathlib import Path
from typing import Dict, Optional, Any

# Third-party imports
from pydantic import ValidationError

# Local application imports
from .config import AppSettings

logger = logging.getLogger(__name__)


class ConfigurationLoader:
    """Loads and validates configuration based on environment.
    
    This class handles loading configuration from environment-specific files,
    environment variables, and provides validation and fallback mechanisms
    for robust configuration management.
    
    Attributes:
        environment: Current environment name
        config_dir: Directory containing configuration files
        settings: Loaded and validated application settings
        
    Example:
        Load configuration for current environment::
        
            loader = ConfigurationLoader()
            settings = loader.load_config()
            
            print(f"Running in {settings.environment} mode")
            print(f"API host: {settings.host}:{settings.port}")
    """
    
    def __init__(self, environment: Optional[str] = None, config_dir: Optional[str] = None):
        """Initialize the configuration loader.
        
        Args:
            environment: Environment name (development, staging, production).
                        If None, will be determined from APP_ENVIRONMENT env var.
            config_dir: Directory containing configuration files.
                       If None, will use default config directory.
        """
        self.environment = environment or os.getenv('APP_ENVIRONMENT', 'development')
        self.config_dir = Path(config_dir) if config_dir else self._get_default_config_dir()
        self.settings: Optional[AppSettings] = None
        
        logger.info(f"Initializing configuration loader for environment: {self.environment}")
    
    def _get_default_config_dir(self) -> Path:
        """Get the default configuration directory.
        
        Returns:
            Path to the configuration directory
        """
        # Look for config directory relative to project root
        current_dir = Path(__file__).parent
        project_root = current_dir.parent.parent  # Go up from src/core to project root
        config_dir = project_root / "config"
        
        if not config_dir.exists():
            # Fallback to creating config directory
            config_dir.mkdir(parents=True, exist_ok=True)
            logger.warning(f"Created config directory: {config_dir}")
        
        return config_dir
    
    def load_config(self) -> AppSettings:
        """Load and validate configuration for the current environment.
        
        This method loads configuration in the following order of precedence:
        1. Environment variables
        2. Environment-specific configuration file
        3. Default configuration file
        4. Built-in defaults
        
        Returns:
            Validated application settings
            
        Raises:
            ConfigurationError: If configuration loading or validation fails
        """
        try:
            logger.info(f"Loading configuration for environment: {self.environment}")
            
            # Load environment-specific configuration file
            env_config = self._load_env_file()
            
            # Merge with environment variables (higher precedence)
            merged_config = self._merge_with_env_vars(env_config)
            
            # Validate configuration
            self.settings = self._validate_config(merged_config)
            
            logger.info("Configuration loaded and validated successfully")
            return self.settings
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise ConfigurationError(f"Configuration loading failed: {str(e)}")
    
    def _load_env_file(self) -> Dict[str, Any]:
        """Load environment-specific configuration file.
        
        Returns:
            Dictionary of configuration values
        """
        env_file_path = self.config_dir / f"{self.environment}.env"
        
        if not env_file_path.exists():
            logger.warning(f"Environment file not found: {env_file_path}")
            return {}
        
        config = {}
        
        try:
            with open(env_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue
                    
                    # Parse key=value pairs
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # Remove quotes if present
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        
                        # Convert boolean strings
                        if value.lower() in ('true', 'false'):
                            value = value.lower() == 'true'
                        # Convert numeric strings
                        elif value.isdigit():
                            value = int(value)
                        elif self._is_float(value):
                            value = float(value)
                        
                        config[key] = value
                    else:
                        logger.warning(f"Invalid line in {env_file_path}:{line_num}: {line}")
            
            logger.info(f"Loaded {len(config)} configuration values from {env_file_path}")
            return config
            
        except Exception as e:
            logger.error(f"Error reading environment file {env_file_path}: {e}")
            return {}
    
    def _is_float(self, value: str) -> bool:
        """Check if a string represents a float value.
        
        Args:
            value: String to check
            
        Returns:
            True if the string represents a float
        """
        try:
            float(value)
            return True
        except ValueError:
            return False
    
    def _merge_with_env_vars(self, file_config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge file configuration with environment variables.
        
        Environment variables take precedence over file configuration.
        
        Args:
            file_config: Configuration loaded from file
            
        Returns:
            Merged configuration dictionary
        """
        merged_config = file_config.copy()
        
        # Get all environment variables
        env_vars = dict(os.environ)
        
        # Override file config with environment variables
        for key, value in env_vars.items():
            if key in file_config or key.startswith(('APP_', 'SECURITY_', 'MODEL_', 'CACHE_', 'LOG_')):
                # Convert string values to appropriate types
                if isinstance(file_config.get(key), bool):
                    merged_config[key] = value.lower() in ('true', '1', 'yes', 'on')
                elif isinstance(file_config.get(key), int):
                    try:
                        merged_config[key] = int(value)
                    except ValueError:
                        merged_config[key] = value
                elif isinstance(file_config.get(key), float):
                    try:
                        merged_config[key] = float(value)
                    except ValueError:
                        merged_config[key] = value
                else:
                    merged_config[key] = value
        
        logger.debug(f"Merged configuration with {len(env_vars)} environment variables")
        return merged_config
    
    def _validate_config(self, config: Dict[str, Any]) -> AppSettings:
        """Validate configuration using Pydantic models.
        
        Args:
            config: Configuration dictionary to validate
            
        Returns:
            Validated AppSettings instance
            
        Raises:
            ValidationError: If configuration validation fails
        """
        try:
            # Create AppSettings instance with validation
            settings = AppSettings(**config)
            
            # Additional custom validation
            self._perform_custom_validation(settings)
            
            return settings
            
        except ValidationError as e:
            logger.error(f"Configuration validation failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during validation: {e}")
            raise ConfigurationError(f"Configuration validation error: {str(e)}")
    
    def _perform_custom_validation(self, settings: AppSettings) -> None:
        """Perform additional custom validation on settings.
        
        Args:
            settings: Settings to validate
            
        Raises:
            ConfigurationError: If custom validation fails
        """
        # Validate production environment requirements
        if settings.environment == "production":
            if settings.debug:
                raise ConfigurationError("Debug mode must be disabled in production")
            
            if not os.getenv('SECURITY_API_KEYS_JSON'):
                raise ConfigurationError("API keys must be configured for production")
        
        # Validate GPU settings
        if settings.model.use_gpu:
            try:
                import torch
                if not torch.cuda.is_available():
                    logger.warning("GPU requested but CUDA not available, falling back to CPU")
                    settings.model.use_gpu = False
            except ImportError:
                logger.warning("PyTorch not available, disabling GPU")
                settings.model.use_gpu = False
        
        # Validate cache settings
        if settings.cache.enabled and settings.cache.max_size <= 0:
            raise ConfigurationError("Cache max_size must be positive when cache is enabled")
        
        logger.debug("Custom validation completed successfully")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration.
        
        Returns:
            Dictionary containing configuration summary (without sensitive data)
        """
        if not self.settings:
            return {"error": "Configuration not loaded"}
        
        return {
            "environment": self.settings.environment,
            "debug": self.settings.debug,
            "host": self.settings.host,
            "port": self.settings.port,
            "model_engine": self.settings.model.reasoning_engine,
            "gpu_enabled": self.settings.model.use_gpu,
            "cache_enabled": self.settings.cache.enabled,
            "log_level": self.settings.logging.level,
        }


class ConfigurationError(Exception):
    """Exception raised for configuration-related errors."""
    pass


# Global configuration loader instance
_config_loader: Optional[ConfigurationLoader] = None


def get_config_loader() -> ConfigurationLoader:
    """Get the global configuration loader instance.
    
    Returns:
        ConfigurationLoader instance
    """
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigurationLoader()
    return _config_loader


def load_settings() -> AppSettings:
    """Load application settings using the global configuration loader.
    
    Returns:
        Validated application settings
    """
    loader = get_config_loader()
    return loader.load_config()
