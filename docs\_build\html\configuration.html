

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Configuration Guide &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=39bd3b11" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=01f34227"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="API Reference" href="api_reference.html" />
    <link rel="prev" title="Neural Symbolic Language Model Documentation" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#environment-variables">Environment Variables</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#core-application-settings">Core Application Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="#model-configuration">Model Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#security-configuration">Security Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#cache-configuration">Cache Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#logging-configuration">Logging Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration-files">Configuration Files</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#environment-specific-yaml-files">Environment-Specific YAML Files</a></li>
<li class="toctree-l3"><a class="reference internal" href="#loading-configuration-files">Loading Configuration Files</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#environment-setup">Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration-validation">Configuration Validation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#built-in-validation">Built-in Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#custom-validation">Custom Validation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#configuration-templates">Configuration Templates</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#generate-environment-template">Generate Environment Template</a></li>
<li class="toctree-l3"><a class="reference internal" href="#docker-configuration">Docker Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#security">Security</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance">Performance</a></li>
<li class="toctree-l3"><a class="reference internal" href="#monitoring">Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="#environment-management">Environment Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-issues">Common Issues</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Configuration Guide</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/configuration.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="configuration-guide">
<h1>Configuration Guide<a class="headerlink" href="#configuration-guide" title="Link to this heading"></a></h1>
<p>This guide covers how to configure the Neural Symbolic Language Model for different
environments and use cases.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The application uses a hierarchical configuration system based on:</p>
<ul class="simple">
<li><p>Environment variables</p></li>
<li><p>Configuration files (YAML/JSON)</p></li>
<li><p>Default values with validation</p></li>
<li><p>Environment-specific overrides</p></li>
</ul>
<p>Configuration is managed through Pydantic BaseSettings for type safety and validation.</p>
</section>
<section id="environment-variables">
<h2>Environment Variables<a class="headerlink" href="#environment-variables" title="Link to this heading"></a></h2>
<section id="core-application-settings">
<h3>Core Application Settings<a class="headerlink" href="#core-application-settings" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Basic application configuration</span>
<span class="nv">APP_TITLE</span><span class="o">=</span><span class="s2">&quot;Neural Symbolic Language Model API&quot;</span>
<span class="nv">APP_VERSION</span><span class="o">=</span><span class="s2">&quot;0.1.0&quot;</span>
<span class="nv">APP_DEBUG</span><span class="o">=</span><span class="nb">false</span>
<span class="nv">APP_ENVIRONMENT</span><span class="o">=</span>development<span class="w">  </span><span class="c1"># development, staging, production</span>
<span class="nv">APP_HOST</span><span class="o">=</span><span class="m">0</span>.0.0.0
<span class="nv">APP_PORT</span><span class="o">=</span><span class="m">8000</span>
<span class="nv">APP_WORKERS</span><span class="o">=</span><span class="m">1</span>
<span class="nv">APP_RELOAD</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
</section>
<section id="model-configuration">
<h3>Model Configuration<a class="headerlink" href="#model-configuration" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Reasoning engine settings</span>
<span class="nv">MODEL_REASONING_ENGINE</span><span class="o">=</span><span class="nb">local</span><span class="w">  </span><span class="c1"># local, openai, anthropic</span>
<span class="nv">MODEL_REASONING_MODEL</span><span class="o">=</span>llama<span class="w">   </span><span class="c1"># llama, gpt-3.5-turbo, claude-3-sonnet</span>
<span class="nv">MODEL_EMBEDDING_MODEL</span><span class="o">=</span>BAAI/bge-small-en-v1.5
<span class="nv">MODEL_EMBEDDING_DIMENSION</span><span class="o">=</span><span class="m">768</span>
<span class="nv">MODEL_USE_GPU</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">MODEL_GPU_MEMORY_FRACTION</span><span class="o">=</span><span class="m">0</span>.8
<span class="nv">MODEL_VECTOR_DB_BACKEND</span><span class="o">=</span>faiss<span class="w">  </span><span class="c1"># faiss, chromadb, pinecone</span>
</pre></div>
</div>
</section>
<section id="security-configuration">
<h3>Security Configuration<a class="headerlink" href="#security-configuration" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># API authentication (JSON format for multiple keys)</span>
<span class="nv">SECURITY_API_KEYS</span><span class="o">=</span><span class="s1">&#39;{&quot;user1&quot;: &quot;key1&quot;, &quot;user2&quot;: &quot;key2&quot;}&#39;</span>

<span class="c1"># Rate limiting</span>
<span class="nv">SECURITY_RATE_LIMIT_REQUESTS</span><span class="o">=</span><span class="m">100</span>
<span class="nv">SECURITY_RATE_LIMIT_WINDOW</span><span class="o">=</span><span class="m">60</span>
<span class="nv">SECURITY_MAX_REQUEST_SIZE</span><span class="o">=</span><span class="m">10485760</span><span class="w">  </span><span class="c1"># 10MB</span>

<span class="c1"># CORS settings (JSON array format)</span>
<span class="nv">SECURITY_CORS_ORIGINS</span><span class="o">=</span><span class="s1">&#39;[&quot;http://localhost:3000&quot;, &quot;https://yourdomain.com&quot;]&#39;</span>

<span class="c1"># Security timeouts</span>
<span class="nv">SECURITY_BLOCK_DURATION</span><span class="o">=</span><span class="m">3600</span>
<span class="nv">SECURITY_MAX_FAILED_ATTEMPTS</span><span class="o">=</span><span class="m">5</span>
</pre></div>
</div>
</section>
<section id="cache-configuration">
<h3>Cache Configuration<a class="headerlink" href="#cache-configuration" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Cache settings</span>
<span class="nv">CACHE_MAX_SIZE</span><span class="o">=</span><span class="m">1000</span>
<span class="nv">CACHE_TTL_SECONDS</span><span class="o">=</span><span class="m">3600</span>
<span class="nv">CACHE_CLEANUP_INTERVAL</span><span class="o">=</span><span class="m">300</span>

<span class="c1"># Redis configuration (optional)</span>
<span class="nv">CACHE_REDIS_URL</span><span class="o">=</span>redis://localhost:6379/0
</pre></div>
</div>
</section>
<section id="logging-configuration">
<h3>Logging Configuration<a class="headerlink" href="#logging-configuration" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Logging settings</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span>INFO
<span class="nv">LOG_FORMAT</span><span class="o">=</span><span class="s2">&quot;%(asctime)s - %(name)s - %(levelname)s - %(message)s&quot;</span>
<span class="nv">LOG_FILE_ENABLED</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">LOG_FILE_PATH</span><span class="o">=</span>logs
<span class="nv">LOG_MAX_FILE_SIZE</span><span class="o">=</span><span class="m">10485760</span>
<span class="nv">LOG_BACKUP_COUNT</span><span class="o">=</span><span class="m">5</span>
<span class="nv">LOG_STRUCTURED_LOGGING</span><span class="o">=</span><span class="nb">false</span>
</pre></div>
</div>
</section>
</section>
<section id="configuration-files">
<h2>Configuration Files<a class="headerlink" href="#configuration-files" title="Link to this heading"></a></h2>
<section id="environment-specific-yaml-files">
<h3>Environment-Specific YAML Files<a class="headerlink" href="#environment-specific-yaml-files" title="Link to this heading"></a></h3>
<p>The application supports YAML configuration files for different environments:</p>
<p><strong>config/development.yaml</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">app</span><span class="p">:</span>
<span class="w">  </span><span class="nt">title</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Neural</span><span class="nv"> </span><span class="s">Symbolic</span><span class="nv"> </span><span class="s">Language</span><span class="nv"> </span><span class="s">Model</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">(Development)&quot;</span>
<span class="w">  </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">environment</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;development&quot;</span>
<span class="w">  </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;127.0.0.1&quot;</span>
<span class="w">  </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8000</span>
<span class="w">  </span><span class="nt">reload</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="nt">model</span><span class="p">:</span>
<span class="w">  </span><span class="nt">reasoning_engine</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;local&quot;</span>
<span class="w">  </span><span class="nt">use_gpu</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span><span class="w">  </span><span class="c1"># Disabled for development</span>
<span class="w">  </span><span class="nt">vector_db_backend</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;faiss&quot;</span>

<span class="nt">security</span><span class="p">:</span>
<span class="w">  </span><span class="nt">api_keys</span><span class="p">:</span>
<span class="w">    </span><span class="nt">dev</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;dev-key-12345&quot;</span>
<span class="w">    </span><span class="nt">test</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;test-key-67890&quot;</span>
<span class="w">  </span><span class="nt">rate_limit_requests</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span><span class="w">  </span><span class="c1"># Higher limit for development</span>
<span class="w">  </span><span class="nt">cors_origins</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;http://localhost:3000&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;http://127.0.0.1:3000&quot;</span>

<span class="nt">cache</span><span class="p">:</span>
<span class="w">  </span><span class="nt">max_size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span><span class="w">  </span><span class="c1"># Smaller cache for development</span>
<span class="w">  </span><span class="nt">redis_url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">null</span><span class="w">  </span><span class="c1"># Use in-memory cache</span>

<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;DEBUG&quot;</span>
<span class="w">  </span><span class="nt">file_path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;logs/dev&quot;</span>
</pre></div>
</div>
<p><strong>config/production.yaml</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">app</span><span class="p">:</span>
<span class="w">  </span><span class="nt">title</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Neural</span><span class="nv"> </span><span class="s">Symbolic</span><span class="nv"> </span><span class="s">Language</span><span class="nv"> </span><span class="s">Model</span><span class="nv"> </span><span class="s">API&quot;</span>
<span class="w">  </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span><span class="w">  </span><span class="c1"># Always disabled in production</span>
<span class="w">  </span><span class="nt">environment</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="w">  </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;0.0.0.0&quot;</span>
<span class="w">  </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8000</span>
<span class="w">  </span><span class="nt">workers</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">4</span><span class="w">  </span><span class="c1"># Multiple workers for production</span>
<span class="w">  </span><span class="nt">reload</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="nt">model</span><span class="p">:</span>
<span class="w">  </span><span class="nt">reasoning_engine</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;local&quot;</span>
<span class="w">  </span><span class="nt">use_gpu</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span><span class="w">  </span><span class="c1"># Enable GPU for production</span>
<span class="w">  </span><span class="nt">vector_db_backend</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;faiss&quot;</span>

<span class="nt">security</span><span class="p">:</span>
<span class="w">  </span><span class="nt">api_keys</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{}</span><span class="w">  </span><span class="c1"># Populated from environment variables</span>
<span class="w">  </span><span class="nt">rate_limit_requests</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">  </span><span class="nt">cors_origins</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;https://yourdomain.com&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;https://api.yourdomain.com&quot;</span>
<span class="w">  </span><span class="nt">block_duration</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3600</span><span class="w">  </span><span class="c1"># 1 hour block</span>

<span class="nt">cache</span><span class="p">:</span>
<span class="w">  </span><span class="nt">max_size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10000</span><span class="w">  </span><span class="c1"># Larger cache for production</span>
<span class="w">  </span><span class="nt">redis_url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;redis://redis-server:6379/0&quot;</span>

<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INFO&quot;</span>
<span class="w">  </span><span class="nt">file_path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/var/log/neural-symbolic&quot;</span>
<span class="w">  </span><span class="nt">structured_logging</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</pre></div>
</div>
</section>
<section id="loading-configuration-files">
<h3>Loading Configuration Files<a class="headerlink" href="#loading-configuration-files" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">core.config</span><span class="w"> </span><span class="kn">import</span> <span class="n">load_config_from_file</span><span class="p">,</span> <span class="n">AppSettings</span>

<span class="c1"># Load configuration from file</span>
<span class="n">config_data</span> <span class="o">=</span> <span class="n">load_config_from_file</span><span class="p">(</span><span class="s2">&quot;config/production.yaml&quot;</span><span class="p">)</span>

<span class="c1"># Create settings with file data</span>
<span class="n">settings</span> <span class="o">=</span> <span class="n">AppSettings</span><span class="p">(</span><span class="o">**</span><span class="n">config_data</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="environment-setup">
<h2>Environment Setup<a class="headerlink" href="#environment-setup" title="Link to this heading"></a></h2>
<section id="development-environment">
<h3>Development Environment<a class="headerlink" href="#development-environment" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Copy the environment template:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>cp<span class="w"> </span>.env.example<span class="w"> </span>.env
</pre></div>
</div>
</li>
<li><p><strong>Edit the .env file:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Development settings</span>
<span class="nv">APP_DEBUG</span><span class="o">=</span><span class="nb">true</span>
<span class="nv">APP_ENVIRONMENT</span><span class="o">=</span>development
<span class="nv">APP_RELOAD</span><span class="o">=</span><span class="nb">true</span>

<span class="c1"># Use CPU for development</span>
<span class="nv">MODEL_USE_GPU</span><span class="o">=</span><span class="nb">false</span>

<span class="c1"># Development API keys</span>
<span class="nv">SECURITY_API_KEYS</span><span class="o">=</span><span class="s1">&#39;{&quot;dev&quot;: &quot;dev-key-12345&quot;}&#39;</span>

<span class="c1"># Higher rate limits for development</span>
<span class="nv">SECURITY_RATE_LIMIT_REQUESTS</span><span class="o">=</span><span class="m">1000</span>
</pre></div>
</div>
</li>
<li><p><strong>Start the development server:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python<span class="w"> </span>src/main.py
</pre></div>
</div>
</li>
</ol>
</section>
<section id="production-environment">
<h3>Production Environment<a class="headerlink" href="#production-environment" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p><strong>Set production environment variables:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">export</span><span class="w"> </span><span class="nv">APP_ENVIRONMENT</span><span class="o">=</span>production
<span class="nb">export</span><span class="w"> </span><span class="nv">APP_DEBUG</span><span class="o">=</span><span class="nb">false</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">APP_WORKERS</span><span class="o">=</span><span class="m">4</span>

<span class="c1"># Production API keys (use secure key management)</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">SECURITY_API_KEYS</span><span class="o">=</span><span class="s1">&#39;{&quot;prod&quot;: &quot;secure-production-key&quot;}&#39;</span>

<span class="c1"># Production database</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CACHE_REDIS_URL</span><span class="o">=</span>redis://redis-server:6379/0

<span class="c1"># Production logging</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">LOG_LEVEL</span><span class="o">=</span>INFO
<span class="nb">export</span><span class="w"> </span><span class="nv">LOG_STRUCTURED_LOGGING</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
</li>
<li><p><strong>Use production configuration:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Load production config</span>
python<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;</span>
<span class="s2">from core.config import load_config_from_file, AppSettings</span>
<span class="s2">config = load_config_from_file(&#39;config/production.yaml&#39;)</span>
<span class="s2">settings = AppSettings(**config)</span>
<span class="s2">&quot;</span>
</pre></div>
</div>
</li>
<li><p><strong>Deploy with production settings:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>uvicorn<span class="w"> </span>main:app<span class="w"> </span>--host<span class="w"> </span><span class="m">0</span>.0.0.0<span class="w"> </span>--port<span class="w"> </span><span class="m">8000</span><span class="w"> </span>--workers<span class="w"> </span><span class="m">4</span>
</pre></div>
</div>
</li>
</ol>
</section>
</section>
<section id="configuration-validation">
<h2>Configuration Validation<a class="headerlink" href="#configuration-validation" title="Link to this heading"></a></h2>
<section id="built-in-validation">
<h3>Built-in Validation<a class="headerlink" href="#built-in-validation" title="Link to this heading"></a></h3>
<p>The configuration system includes comprehensive validation:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">core.config</span><span class="w"> </span><span class="kn">import</span> <span class="n">validate_configuration</span><span class="p">,</span> <span class="n">get_settings</span>

<span class="c1"># Get current settings</span>
<span class="n">settings</span> <span class="o">=</span> <span class="n">get_settings</span><span class="p">()</span>

<span class="c1"># Validate configuration</span>
<span class="n">warnings</span> <span class="o">=</span> <span class="n">validate_configuration</span><span class="p">(</span><span class="n">settings</span><span class="p">)</span>

<span class="k">if</span> <span class="n">warnings</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">warning</span> <span class="ow">in</span> <span class="n">warnings</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Warning: </span><span class="si">{</span><span class="n">warning</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Configuration is valid&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Common Validation Checks:</p>
<ul class="simple">
<li><p>Production environment safety (debug disabled, secure origins)</p></li>
<li><p>GPU availability when requested</p></li>
<li><p>Database connection validity</p></li>
<li><p>API key security</p></li>
<li><p>Rate limit reasonableness</p></li>
</ul>
</section>
<section id="custom-validation">
<h3>Custom Validation<a class="headerlink" href="#custom-validation" title="Link to this heading"></a></h3>
<p>You can add custom validation rules:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">validate_custom_settings</span><span class="p">(</span><span class="n">settings</span><span class="p">):</span>
    <span class="n">warnings</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="c1"># Custom business logic validation</span>
    <span class="k">if</span> <span class="n">settings</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">use_gpu</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">():</span>
        <span class="n">warnings</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;GPU requested but not available&quot;</span><span class="p">)</span>

    <span class="k">if</span> <span class="n">settings</span><span class="o">.</span><span class="n">environment</span> <span class="o">==</span> <span class="s1">&#39;production&#39;</span> <span class="ow">and</span> <span class="n">settings</span><span class="o">.</span><span class="n">debug</span><span class="p">:</span>
        <span class="n">warnings</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;Debug mode enabled in production&quot;</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">warnings</span>
</pre></div>
</div>
</section>
</section>
<section id="configuration-templates">
<h2>Configuration Templates<a class="headerlink" href="#configuration-templates" title="Link to this heading"></a></h2>
<section id="generate-environment-template">
<h3>Generate Environment Template<a class="headerlink" href="#generate-environment-template" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">core.config</span><span class="w"> </span><span class="kn">import</span> <span class="n">create_env_template</span>

<span class="c1"># Create .env template with all available settings</span>
<span class="n">create_env_template</span><span class="p">(</span><span class="s2">&quot;.env.template&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>This generates a comprehensive template with:</p>
<ul class="simple">
<li><p>All available configuration options</p></li>
<li><p>Default values and examples</p></li>
<li><p>Documentation for each setting</p></li>
<li><p>Environment-specific recommendations</p></li>
</ul>
</section>
<section id="docker-configuration">
<h3>Docker Configuration<a class="headerlink" href="#docker-configuration" title="Link to this heading"></a></h3>
<p><strong>Dockerfile environment:</strong></p>
<div class="highlight-dockerfile notranslate"><div class="highlight"><pre><span></span><span class="c"># Set production environment</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">APP_ENVIRONMENT</span><span class="o">=</span>production
<span class="k">ENV</span><span class="w"> </span><span class="nv">APP_DEBUG</span><span class="o">=</span><span class="nb">false</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">APP_WORKERS</span><span class="o">=</span><span class="m">4</span>

<span class="c"># Model configuration</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">MODEL_USE_GPU</span><span class="o">=</span><span class="nb">true</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">MODEL_VECTOR_DB_BACKEND</span><span class="o">=</span>faiss

<span class="c"># Security configuration</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">SECURITY_RATE_LIMIT_REQUESTS</span><span class="o">=</span><span class="m">100</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">SECURITY_CORS_ORIGINS</span><span class="o">=</span><span class="s1">&#39;[&quot;https://yourdomain.com&quot;]&#39;</span>
</pre></div>
</div>
<p><strong>Docker Compose:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.8&#39;</span>
<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">neural-symbolic-api</span><span class="p">:</span>
<span class="w">    </span><span class="nt">build</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">.</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">APP_ENVIRONMENT=production</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CACHE_REDIS_URL=redis://redis:6379/0</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">LOG_LEVEL=INFO</span>
<span class="w">    </span><span class="nt">depends_on</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>

<span class="w">  </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis:alpine</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;6379:6379&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="security">
<h3>Security<a class="headerlink" href="#security" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Never commit .env files to version control</p></li>
<li><p>Use environment variables for sensitive data</p></li>
<li><p>Rotate API keys regularly</p></li>
<li><p>Use different keys for different environments</p></li>
<li><p>Validate CORS origins in production</p></li>
</ul>
</section>
<section id="performance">
<h3>Performance<a class="headerlink" href="#performance" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Enable GPU acceleration in production</p></li>
<li><p>Use Redis for caching in production</p></li>
<li><p>Configure appropriate worker counts</p></li>
<li><p>Set reasonable rate limits</p></li>
<li><p>Monitor and adjust cache sizes</p></li>
</ul>
</section>
<section id="monitoring">
<h3>Monitoring<a class="headerlink" href="#monitoring" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Enable structured logging in production</p></li>
<li><p>Configure appropriate log levels</p></li>
<li><p>Set up log rotation and retention</p></li>
<li><p>Monitor configuration validation warnings</p></li>
<li><p>Track configuration changes</p></li>
</ul>
</section>
<section id="environment-management">
<h3>Environment Management<a class="headerlink" href="#environment-management" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Use separate configurations for each environment</p></li>
<li><p>Validate configurations before deployment</p></li>
<li><p>Document configuration changes</p></li>
<li><p>Use configuration management tools</p></li>
<li><p>Test configuration changes in staging first</p></li>
</ul>
</section>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="common-issues">
<h3>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h3>
<p><strong>Configuration not loading:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check environment variables</span>
env<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>APP_

<span class="c1"># Validate configuration file syntax</span>
python<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;import yaml; yaml.safe_load(open(&#39;config/production.yaml&#39;))&quot;</span>
</pre></div>
</div>
<p><strong>GPU not available:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check GPU availability</span>
python<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;import torch; print(torch.cuda.is_available())&quot;</span>

<span class="c1"># Disable GPU if not available</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">MODEL_USE_GPU</span><span class="o">=</span><span class="nb">false</span>
</pre></div>
</div>
<p><strong>Redis connection issues:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Test Redis connection</span>
redis-cli<span class="w"> </span>-u<span class="w"> </span>redis://localhost:6379<span class="w"> </span>ping

<span class="c1"># Use in-memory cache as fallback</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CACHE_REDIS_URL</span><span class="o">=</span><span class="s2">&quot;&quot;</span>
</pre></div>
</div>
<p><strong>API key authentication failing:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check API key format</span>
<span class="nb">echo</span><span class="w"> </span><span class="nv">$SECURITY_API_KEYS</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>json.tool

<span class="c1"># Test API key</span>
curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer your-key&quot;</span><span class="w"> </span>http://localhost:8000/system/info
</pre></div>
</div>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Neural Symbolic Language Model Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="api_reference.html" class="btn btn-neutral float-right" title="API Reference" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>