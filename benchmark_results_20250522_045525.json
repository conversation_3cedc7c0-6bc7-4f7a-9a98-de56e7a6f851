{"first_query_times": [NaN, NaN, NaN, NaN, NaN], "cached_query_times": [NaN, NaN, NaN, NaN, NaN], "queries": ["What is Neural-Symbolic AI?", "Explain the difference between FAISS and ChromaDB", "How does GPU acceleration improve vector search?", "What are the benefits of symbolic reasoning?", "How to implement RAG in a production system?"], "timestamp": "2025-05-22T04:54:38.181112", "raw_data": [{"query": "What is Neural-Symbolic AI?", "iterations": [{"iteration": 1, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 2, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 3, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}]}, {"query": "Explain the difference between FAISS and ChromaDB", "iterations": [{"iteration": 1, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 2, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 3, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}]}, {"query": "How does GPU acceleration improve vector search?", "iterations": [{"iteration": 1, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 2, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 3, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}]}, {"query": "What are the benefits of symbolic reasoning?", "iterations": [{"iteration": 1, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 2, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 3, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}]}, {"query": "How to implement RAG in a production system?", "iterations": [{"iteration": 1, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 2, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}, {"iteration": 3, "error": "500 Server Error: Internal Server Error for url: http://localhost:8080/chat"}]}]}