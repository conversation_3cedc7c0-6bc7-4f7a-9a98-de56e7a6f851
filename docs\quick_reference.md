# Quick Reference

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/v1/chat/completions` | POST | OpenAI-compatible chat endpoint |
| `/chat/stream` | POST | Process a chat request with streaming response |
| `/performance` | GET | Get performance statistics for the system |
| `/system/info` | GET | Get system configuration information |

## Request Examples

### Chat Completion

```bash
curl -X POST "http://localhost:8080/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "local",
    "messages": [
      {"role": "user", "content": "What is symbolic AI?"}
    ]
  }'
```

### Streaming Chat

```bash
curl -X POST "http://localhost:8080/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "local",
    "messages": [
      {"role": "user", "content": "Explain quantum computing"}
    ],
    "stream": true
  }'
```

### Get Performance Metrics

```bash
curl -X GET "http://localhost:8080/performance"
```

### Get System Info

```bash
curl -X GET "http://localhost:8080/system/info"
```

## Python Client Examples

### Basic Chat Request

```python
import requests

def chat(message):
    response = requests.post(
        "http://localhost:8080/v1/chat/completions",
        json={
            "model": "local",
            "messages": [
                {"role": "user", "content": message}
            ]
        }
    )
    return response.json()["choices"][0]["message"]["content"]
```

### Streaming Chat Request

```python
import requests

def chat_stream(message):
    response = requests.post(
        "http://localhost:8080/v1/chat/completions",
        json={
            "model": "local",
            "messages": [
                {"role": "user", "content": message}
            ],
            "stream": True
        },
        stream=True
    )
    
    for line in response.iter_lines():
        if line:
            # Process each chunk
            print(line.decode())
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LOG_LEVEL` | Logging level (DEBUG, INFO, WARNING, ERROR) | INFO |
| `CACHE_SIZE` | Maximum number of responses to cache | 1000 |
| `GPU_ENABLED` | Enable GPU acceleration if available | True |

### Configuration File (.symai/symai.config.json)

```json
{
  "model": {
    "type": "local",
    "path": "models/default"
  },
  "retriever": {
    "type": "lightrag",
    "index_path": "data/index",
    "chunk_size": 512,
    "overlap": 128
  }
}
```

## Common Commands

### Start Development Server

```bash
python src/main.py
```

### Start Production Server

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Run Tests

```bash
pytest
```

### Format Code

```bash
black src/
isort src/
```

## Monitoring

### Log Files

- Application logs: `logs/app.log`
- API request logs: `logs/api.log`

### Metrics Available

- System metrics (CPU, memory, GPU usage)
- Request metrics (duration, errors, cache hits)
- Cache performance
- Retrieval and reasoning times

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input |
| 404 | Not Found - Resource not found |
| 500 | Internal Server Error |
| 503 | Service Unavailable - Server overloaded |

## Best Practices

1. **Performance**
   - Use streaming for long responses
   - Monitor cache hit rates
   - Keep GPU memory usage in check

2. **Development**
   - Follow type hints
   - Run tests before commits
   - Keep dependencies up to date

3. **Production**
   - Use appropriate number of workers
   - Monitor system resources
   - Regular log rotation
