# Neural Symbolic Language Model

A production-ready hybrid AI system that combines neural networks with symbolic reasoning capabilities, providing both learning and logical inference in a unified framework. Optimized for use with Ollama and the gemma3n:e2b model.

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![Ollama](https://img.shields.io/badge/Ollama-Compatible-green.svg)](https://ollama.com/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

## 🎯 Features

### Core Capabilities
- **🧠 Hybrid Architecture**: Combines neural learning with symbolic reasoning
- **🤖 Ollama Integration**: Optimized for local LLM deployment with gemma3n:e2b
- **🔍 Vector Retrieval**: Semantic search and document retrieval capabilities
- **🌐 OpenAI-Compatible API**: Drop-in replacement for OpenAI API endpoints
- **📊 Performance Monitoring**: Built-in metrics, profiling, and health checks
- **⚡ Intelligent Caching**: LRU and TTL-based response caching
- **🔒 Production Security**: API authentication, rate limiting, input sanitization

### Performance & Scalability
- **🚀 Async/Await**: Full asynchronous processing for optimal performance
- **⚡ Concurrent Processing**: 3x performance improvement with parallel requests
- **🎯 Memory Optimization**: Automatic garbage collection and memory management
- **📈 Auto-scaling**: Connection pooling and request batching
- **🔄 Streaming Responses**: Real-time response streaming support

### Developer Experience
- **📚 Comprehensive Documentation**: Complete API docs, examples, and guides
- **🧪 Extensive Testing**: 90%+ test coverage with performance benchmarks
- **🔧 Type Safety**: Full type hints and static analysis support
- **📝 Sphinx Documentation**: Professional documentation with examples
- **🐳 Docker Ready**: Production-ready containerization

## 📊 Benchmark Results

Our comprehensive testing with Ollama gemma3n:e2b shows exceptional performance:

| Metric | Performance | Details |
|--------|-------------|---------|
| **Success Rate** | 100% | All queries processed successfully |
| **Average Response Time** | 24.5s | For complex reasoning tasks |
| **Simple Queries** | 0.98s | Basic math and definitions |
| **Concurrent Throughput** | 68.9 WPS | 3x improvement over sequential |
| **Reasoning Accuracy** | Excellent | Perfect logical deduction |
| **Memory Efficiency** | Optimized | Automatic garbage collection |

## 🚀 Quick Start

### Prerequisites

- **Python**: 3.10+ (3.11 recommended)
- **Ollama**: Latest version with gemma3n:e2b model
- **System**: 16GB+ RAM, GPU recommended for optimal performance
- **OS**: Windows 10+, macOS 10.15+, or Linux

### Option 1: Docker Deployment (Recommended)

```bash
# Clone the repository
git clone https://github.com/your-username/neural-symbolic-language-model.git
cd neural-symbolic-language-model

# Start with Docker Compose
docker-compose up -d

# Pull the gemma3n:e2b model
docker exec ollama ollama pull gemma3n:e2b

# Verify installation
curl http://localhost:8080/health
```

### Option 2: Native Installation

```bash
# 1. Install Ollama
curl -fsSL https://ollama.com/install.sh | sh
ollama serve

# 2. Pull the model
ollama pull gemma3n:e2b

# 3. Clone and setup the application
git clone https://github.com/your-username/neural-symbolic-language-model.git
cd neural-symbolic-language-model

# 4. Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 5. Install dependencies
pip install -r requirements.txt

# 6. Configure environment
cp config/development.env .env
# Edit .env with your settings

# 7. Run the application
python src/main.py
```

### First API Call

```bash
# Test the API
curl -X POST "http://localhost:8080/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-api-key" \
     -d '{
       "model": "gemma3n:e2b",
       "messages": [
         {"role": "user", "content": "What is symbolic reasoning?"}
       ]
     }'
```

## 📖 Usage Examples

### Python SDK (OpenAI Compatible)

```python
import openai

# Configure for local API
openai.api_base = "http://localhost:8080/v1"
openai.api_key = "your-api-key"

# Chat completion
response = openai.ChatCompletion.create(
    model="gemma3n:e2b",
    messages=[
        {"role": "user", "content": "Explain symbolic reasoning in AI"}
    ]
)

print(response.choices[0].message.content)
```

### Direct API Usage

```python
import requests

response = requests.post(
    "http://localhost:8080/v1/chat/completions",
    headers={"Authorization": "Bearer your-api-key"},
    json={
        "model": "gemma3n:e2b",
        "messages": [
            {"role": "user", "content": "What is machine learning?"}
        ]
    }
)

print(response.json())
```

### Reasoning Tasks

```python
# Logical deduction
response = requests.post(
    "http://localhost:8080/v1/chat/completions",
    headers={"Authorization": "Bearer your-api-key"},
    json={
        "model": "gemma3n:e2b",
        "messages": [
            {"role": "system", "content": "You are an expert in logic and reasoning."},
            {"role": "user", "content": "If A implies B and B implies C, what can we conclude?"}
        ]
    }
)
```

## 🔧 Configuration

### Environment Variables

```bash
# Model Configuration
MODEL_REASONING_ENGINE=ollama
MODEL_REASONING_MODEL=gemma3n:e2b
MODEL_OLLAMA_HOST=http://localhost:11434
MODEL_USE_GPU=true

# Security Configuration
SECURITY_API_KEYS_JSON='{"user": {"key": "your-api-key", "permissions": ["read", "write"]}}'
SECURITY_RATE_LIMIT_REQUESTS=100

# Performance Configuration
CACHE_ENABLED=true
CACHE_MAX_SIZE=10000
PERFORMANCE_MAX_CONCURRENT_REQUESTS=50
```

### Development vs Production

**Development:**
```bash
APP_ENVIRONMENT=development
APP_DEBUG=true
LOG_LEVEL=DEBUG
```

**Production:**
```bash
APP_ENVIRONMENT=production
APP_DEBUG=false
LOG_LEVEL=INFO
SECURITY_RATE_LIMIT_REQUESTS=100
```

## 📚 Documentation

- **[API Documentation](docs/api.md)** - Complete API reference
- **[Ollama gemma3n:e2b Guide](docs/ollama_gemma3n_guide.md)** - Model-specific setup
- **[Deployment Guide](docs/deployment_guide.md)** - Production deployment
- **[Configuration Reference](docs/configuration.rst)** - All configuration options
- **[Examples](examples/)** - Code examples and use cases

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Unit tests
python -m pytest tests/ -v

# Performance benchmarks
python simple_ollama_benchmark.py

# Integration tests
python test_ollama_direct.py
```

## 📈 Performance Optimization

### Concurrent Processing

The system supports concurrent request processing with significant performance improvements:

- **Sequential**: 22.8 words/second
- **Concurrent**: 68.9 words/second (3x improvement)

### Caching Strategy

Intelligent caching reduces response times for repeated queries:

- **LRU Cache**: Automatic eviction of least recently used items
- **TTL Support**: Time-based cache expiration
- **Memory Optimization**: Automatic garbage collection

### GPU Acceleration

Enable GPU acceleration for optimal performance:

```bash
MODEL_USE_GPU=true
MODEL_GPU_MEMORY_FRACTION=0.8
```

## 🔒 Security

### API Authentication

```bash
# Generate secure API keys
python -c "import secrets; print(secrets.token_urlsafe(32))"

# Configure in environment
SECURITY_API_KEYS_JSON='{"admin": {"key": "your-secure-key", "permissions": ["read", "write", "admin"]}}'
```

### Rate Limiting

Built-in rate limiting protects against abuse:

- **Default**: 100 requests per minute
- **Configurable**: Adjust via environment variables
- **Per-key limits**: Different limits for different API keys

### Input Sanitization

Comprehensive input validation and sanitization:

- **XSS Protection**: HTML/JavaScript sanitization
- **SQL Injection**: Input validation and escaping
- **Size Limits**: Request size limitations

## 🐳 Docker Deployment

### Quick Start

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Scale the API
docker-compose up -d --scale neural-symbolic-api=3
```

### Production Deployment

```bash
# Use production configuration
docker-compose -f docker-compose.prod.yml up -d

# With SSL and load balancing
docker-compose -f docker-compose.prod.yml -f docker-compose.ssl.yml up -d
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone and setup development environment
git clone https://github.com/your-username/neural-symbolic-language-model.git
cd neural-symbolic-language-model

# Install development dependencies
pip install -r requirements-dev.txt

# Run pre-commit hooks
pre-commit install

# Run tests
python -m pytest tests/ -v --cov=src
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Ollama Team** - For the excellent local LLM platform
- **Google** - For the gemma3n:e2b model
- **FastAPI** - For the high-performance web framework
- **Pydantic** - For data validation and settings management

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-username/neural-symbolic-language-model/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/neural-symbolic-language-model/discussions)

---

**Ready to get started?** Follow our [Quick Start Guide](#-quick-start) or check out the [examples](examples/) directory for more detailed usage patterns.
