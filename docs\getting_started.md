# Getting Started with Symbolic Language Model

This guide will help you get started with the Symbolic Language Model, a local language model implementation using SymbolicAI and LightRAG with an OpenAI-compatible API.

## Prerequisites

- Python 3.10 or higher
- CUDA-capable GPU (recommended for optimal performance)
- Git

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/symbolic-language-model.git
   cd symbolic-language-model
   ```

2. Install the package and its dependencies:
   ```bash
   pip install -e .
   ```

   For development, install with extra dependencies:
   ```bash
   pip install -e ".[dev]"
   ```

## Configuration

1. Create a configuration file at `.symai/symai.config.json`:
   ```json
   {
     "model": {
       "type": "local",
       "path": "models/default"
     },
     "retriever": {
       "type": "lightrag",
       "index_path": "data/index",
       "chunk_size": 512,
       "overlap": 128
     }
   }
   ```

2. (Optional) Configure logging in `src/logging_config.py` if you need to adjust log levels or handlers.

## Running the Server

1. Start the development server:
   ```bash
   python src/main.py
   ```

   The server will start at `http://127.0.0.1:8080`

2. For production deployment:
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
   ```

## Using the API

The API is compatible with OpenAI's chat completions endpoint. Here's how to use it:

1. Simple chat request:
   ```python
   import requests

   response = requests.post(
       "http://localhost:8080/v1/chat/completions",
       json={
           "model": "local",
           "messages": [
               {"role": "user", "content": "What is symbolic AI?"}
           ]
       }
   )
   print(response.json()["choices"][0]["message"]["content"])
   ```

2. Streaming response:
   ```python
   response = requests.post(
       "http://localhost:8080/v1/chat/completions",
       json={
           "model": "local",
           "messages": [
               {"role": "user", "content": "Explain quantum computing"}
           ],
           "stream": True
       },
       stream=True
   )
   
   for line in response.iter_lines():
       if line:
           print(line.decode())
   ```

## Web Interface

A simple web interface is available at `http://localhost:8080/` for testing the model interactively.

## Monitoring

1. View performance metrics:
   ```bash
   curl http://localhost:8080/performance
   ```

2. Monitor the logs:
   - Application logs: `logs/app.log`
   - API request logs: `logs/api.log`

## Development

1. Run tests:
   ```bash
   pytest
   ```

2. Format code:
   ```bash
   black src/
   isort src/
   ```

3. Type checking:
   ```bash
   mypy src/
   ```

## Common Issues

1. **GPU Memory Issues**
   - Reduce batch size in configuration
   - Use CPU fallback if necessary

2. **Slow Response Times**
   - Check the performance metrics
   - Adjust cache size
   - Optimize retrieval parameters

## Support

For issues and feature requests, please use the GitHub issue tracker.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
