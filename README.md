# Neural Symbolic Language Model

A local, GPU-accelerated language model combining Neural-Symbolic Reasoning with Retrieval-Augmented Generation for enhanced explainability and factual grounding.

## Features

- **Neural-Symbolic Reasoning**: Leverages SymbolicAI for structured, explainable decision-making
- **Hybrid Retrieval**: Combines FAISS for fast similarity search with LightRAG for accurate reranking
- **GPU Acceleration**: Optimized for NVIDIA GPUs with FAISS similarity search
- **Flexible Backend**: Supports both FAISS and PyTorch-based vector stores
- **FastAPI Backend**: High-performance API with async support
- **Advanced Caching**: LRU cache with timestamp-based eviction
- **Docker Support**: Easy deployment with GPU passthrough
- **Comprehensive Testing**: Unit and integration tests included

## Requirements

- Python 3.10+ (3.10 recommended for best compatibility)
- CUDA-compatible GPU (optional but recommended)
- Docker (optional, for containerized deployment)
- Microsoft Visual C++ Redistributable for Visual Studio 2015-2022 (Windows only)

## Installation

### Windows with GPU (Recommended Approach)

For Windows users with NVIDIA GPUs, we strongly recommend using Conda for installation due to compatibility issues with FAISS-GPU on Windows:

1. Install Miniconda: https://docs.conda.io/en/latest/miniconda.html

2. Install Microsoft Visual C++ Redistributable (required for FAISS-GPU):
   - Download from: https://aka.ms/vs/17/release/vc_redist.x64.exe

3. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/neural-symbolic-model.git
   cd neural-symbolic-model
   ```

4. Create and activate a Conda environment:
   ```bash
   conda create -n symblang python=3.10
   conda activate symblang
   ```

5. Install PyTorch and FAISS-GPU via Conda:
   ```bash
   conda install -c pytorch -c nvidia pytorch=2.4.0 torchvision torchaudio cudatoolkit
   conda install -c conda-forge faiss-gpu
   ```

6. Install remaining dependencies:
   ```bash
   pip install -r requirements.txt
   ```

7. Verify GPU support:
   ```bash
   python scripts/verify_faiss_gpu.py
   ```

### Linux/MacOS or Windows without GPU

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/neural-symbolic-model.git
   cd neural-symbolic-model
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Try the HybridRetriever example:
   ```bash
   python examples/hybrid_retriever_example.py
   ```

4. Start the API server:
   ```bash
   uvicorn src.api.main:app --reload
   ```

5. Open your browser to http://localhost:8000/docs to test the API endpoints.

## Using the HybridRetriever

The HybridRetriever combines the speed of FAISS with the accuracy of LightRAG's reranking:

```python
from src.hybrid_retriever import HybridRetriever

# Initialize with default settings (uses FAISS if available)
retriever = HybridRetriever(
    vector_db="faiss",  # or "torch" for PyTorch-only
    use_gpu=True,
    first_stage_k=100,  # Number of candidates to retrieve initially
    rerank_k=10,       # Number of results after reranking
    model_name="BAAI/bge-small-en-v1.5"  # Embedding model
)

# Add documents
documents = [
    {"id": "doc1", "text": "Example document 1", "metadata": {"source": "example"}},
    # ... more documents
]
retriever.add_documents(documents)

# Search with hybrid retrieval
results = retriever.search("your query")
for result in results:
    print(f"{result.text} (Score: {result.score:.4f})")
```

### Key Features

- **Two-Stage Retrieval**: Fast FAISS search followed by precise LightRAG reranking
- **Flexible Backend**: Switch between FAISS and PyTorch backends
- **GPU Acceleration**: Optimized for CUDA when available
- **Metadata Support**: Store and filter by document metadata
- **Efficient Batching**: Optimized for large document collections

## Performance Tips

1. **GPU Acceleration**: Ensure CUDA is properly configured for best performance
2. **Batch Sizes**: For large document collections, add documents in batches
3. **First-Stage K**: Adjust `first_stage_k` based on your collection size (larger = more accurate but slower)
4. **Model Selection**: Choose appropriate embedding models based on your use case (larger models are more accurate but slower)

## GPU Acceleration Benefits

This project leverages GPU acceleration for significant performance improvements, particularly for vector similarity search operations using FAISS-GPU:

| Operation          | Performance Improvement |
|--------------------|------------------------|
| Vector Search      | 7.9x-13.5x faster      |
| High-Dimensional   | Up to 13.5x speedup    |
| Result Quality     | Identical to CPU       |

Benchmarks show that tasks requiring 1 hour on CPU can be completed in approximately 5 minutes with GPU acceleration. For detailed performance metrics, run:

```bash
python scripts/gpu_impact.py
```

To verify your GPU is being properly utilized:

```bash
python scripts/verify_faiss_gpu.py
```

If you encounter issues with GPU acceleration, use our diagnostic tool:

```bash
python scripts/fix_faiss_gpu.py
```

## Usage

### Running Locally

1. Start the API server:
```bash
python -m uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload
```

2. Make API requests:
```python
import requests

# Chat endpoint
response = requests.post("http://localhost:8080/chat", 
                        json={"text": "What is Neural-Symbolic AI?"})
print(response.json())

# Performance endpoint
stats = requests.get("http://localhost:8080/performance")
print(stats.json())
```

### Docker Deployment

1. Build the Docker image:
```bash
docker build -t neural-symbolic-ai .
```

2. Run with Docker Compose:
```bash
docker-compose up
```

## Project Structure

```
.
├── src/
│   ├── main.py              # FastAPI application
│   ├── symbolic_reasoning.py # Neural-symbolic reasoning module
│   └── retrieval.py         # LightRAG retrieval module
├── tests/
│   ├── test_main.py         # API tests
│   ├── test_symbolic_reasoning.py
│   ├── test_gpu.py          # GPU functionality tests
│   └── test_retrieval.py
├── scripts/
│   ├── benchmark.py         # Performance benchmarking
│   ├── verify_faiss_gpu.py  # FAISS-GPU verification
│   ├── fix_faiss_gpu.py     # FAISS-GPU diagnostic and fix tool
│   ├── gpu_impact.py        # GPU vs CPU benchmarking
│   ├── setup_windows_env.ps1 # Windows environment setup
│   ├── load_test.py         # API load testing
│   └── test_api.py          # API testing utilities
├── docs/
│   └── WINDOWS_FAISS_GUIDE.md # Detailed Windows setup guide
├── .symai/                  # SymbolicAI configuration
├── Dockerfile
├── docker-compose.yml
├── environment.yml          # Conda environment definition
├── QUICKSTART.md            # Quick start guide
├── RELEASE_NOTES.md         # Release notes and known issues
└── requirements.txt
```

## API Documentation

### Chat Endpoint

`POST /chat`

Request body:
```json
{
    "text": "Your query here"
}
```

Response:
```json
{
    "response": "AI response",
    "cached": false
}
```

### Performance Endpoint

`GET /performance`

Response:
```json
{
    "cache": {
        "size": 42,
        "max_size": 1000
    },
    "system_info": {
        "gpu_available": true,
        "gpu_name": "NVIDIA GeForce RTX 3080",
        "reasoner": {...},
        "retriever": {...}
    }
}
```

## Client Application Integration

The Neural Symbolic Language Model provides an OpenAI-compatible API that works seamlessly with popular LLM client applications. Here are examples for common integrations:

### Open WebUI Integration

[Open WebUI](https://github.com/open-webui/open-webui) is a popular web interface for LLMs.

**1. Start Neural Symbolic Language Model:**
```bash
# Start the API server
python src/main.py

# Verify it's running
curl http://localhost:8080/health
```

**2. Install and Configure Open WebUI:**
```bash
# Install Open WebUI
pip install open-webui

# Start Open WebUI
open-webui serve --port 3000
```

**3. Add Neural Symbolic Language Model in Open WebUI:**
- Open http://localhost:3000 in your browser
- Go to Settings → Connections
- Add new connection:
  - **API Base URL**: `http://localhost:8080/v1`
  - **API Key**: `your-api-key`
  - **Model**: `gemma3n:e2b`

**4. Start chatting with enhanced symbolic reasoning capabilities!**

### LibreChat Integration

For [LibreChat](https://github.com/danny-avila/LibreChat):

```yaml
# librechat.yaml
version: 1.0.5
cache: true
endpoints:
  custom:
    - name: "Neural Symbolic LM"
      apiKey: "your-api-key"
      baseURL: "http://localhost:8080/v1"
      models:
        default: ["gemma3n:e2b"]
      titleConvo: true
      titleModel: "gemma3n:e2b"
      summarize: false
      summaryModel: "gemma3n:e2b"
      forcePrompt: false
      modelDisplayLabel: "Neural Symbolic Language Model"
```

### Continue.dev Integration

For [Continue.dev](https://continue.dev/) VS Code extension:

```json
{
  "models": [
    {
      "title": "Neural Symbolic Language Model",
      "provider": "openai",
      "model": "gemma3n:e2b",
      "apiKey": "your-api-key",
      "apiBase": "http://localhost:8080/v1",
      "contextLength": 4096,
      "completionOptions": {
        "temperature": 0.7,
        "topP": 1.0,
        "presencePenalty": 0.0,
        "frequencyPenalty": 0.0
      }
    }
  ],
  "tabAutocompleteModel": {
    "title": "Neural Symbolic Language Model",
    "provider": "openai",
    "model": "gemma3n:e2b",
    "apiKey": "your-api-key",
    "apiBase": "http://localhost:8080/v1"
  }
}
```

### Chatbot UI Integration

For [Chatbot UI](https://github.com/mckaywrigley/chatbot-ui):

```bash
# Environment variables
OPENAI_API_KEY=your-api-key
OPENAI_API_HOST=http://localhost:8080/v1
DEFAULT_MODEL=gemma3n:e2b
```

### Python OpenAI SDK

```python
import openai

# Configure for local API
openai.api_base = "http://localhost:8080/v1"
openai.api_key = "your-api-key"

# Chat completion with symbolic reasoning
response = openai.ChatCompletion.create(
    model="gemma3n:e2b",
    messages=[
        {"role": "system", "content": "You are an expert in symbolic reasoning and logic."},
        {"role": "user", "content": "If A implies B and B implies C, what can we conclude about A and C?"}
    ],
    temperature=0.7,
    max_tokens=1000
)

print(response.choices[0].message.content)
```

### JavaScript/Node.js Integration

```javascript
const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: "your-api-key",
  basePath: "http://localhost:8080/v1"
});

const openai = new OpenAIApi(configuration);

async function chatWithSymbolicReasoning() {
  const response = await openai.createChatCompletion({
    model: "gemma3n:e2b",
    messages: [
      {
        role: "user",
        content: "Explain the difference between deductive and inductive reasoning with examples."
      }
    ],
    temperature: 0.7,
    max_tokens: 1000
  });

  console.log(response.data.choices[0].message.content);
}

chatWithSymbolicReasoning();
```

### cURL Examples

```bash
# Basic chat completion
curl -X POST "http://localhost:8080/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-api-key" \
     -d '{
       "model": "gemma3n:e2b",
       "messages": [
         {"role": "user", "content": "What is symbolic reasoning in AI?"}
       ],
       "temperature": 0.7,
       "max_tokens": 1000
     }'

# Streaming response
curl -X POST "http://localhost:8080/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-api-key" \
     -d '{
       "model": "gemma3n:e2b",
       "messages": [
         {"role": "user", "content": "Explain machine learning step by step."}
       ],
       "stream": true
     }'
```

### Docker Compose for Client Integration

```yaml
version: '3.8'
services:
  neural-symbolic-lm:
    build: .
    ports:
      - "8080:8080"
    environment:
      - MODEL_REASONING_ENGINE=ollama
      - MODEL_REASONING_MODEL=gemma3n:e2b
      - SECURITY_API_KEYS_JSON={"client": {"key": "your-api-key", "permissions": ["read", "write"]}}
    volumes:
      - ./data:/app/data

  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    ports:
      - "3000:8080"
    environment:
      - OPENAI_API_BASE_URL=http://neural-symbolic-lm:8080/v1
      - OPENAI_API_KEY=your-api-key
    depends_on:
      - neural-symbolic-lm
```

## Testing

Run unit tests:
```bash
pytest tests/test_*.py -v
```

## Benchmarking

Run performance benchmarks:
```bash
python scripts/benchmark.py
```

## Troubleshooting

### FAISS-GPU Issues on Windows

#### ImportError: No module named 'faiss'

**Problem**: You might be running with the system Python while FAISS-GPU is installed in a Conda environment.

**Solution**: 
- Ensure you've activated the correct Conda environment: `conda activate symblang`
- Verify the Python path is from your Conda environment: `python -c "import sys; print(sys.executable)"`

#### ModuleNotFoundError: No module named 'faiss.swigfaiss_avx2'

**Problem**: Missing Microsoft Visual C++ Redistributables, which are required runtime libraries for FAISS's compiled C++ components.

**Solution**:
- Download and install the latest Visual C++ Redistributable: https://aka.ms/vs/17/release/vc_redist.x64.exe
- Reinstall FAISS-GPU through Conda: `conda install -c conda-forge faiss-gpu`

#### Dependency Conflicts

**Problem**: Packages like vllm 0.6.3 may require specific PyTorch versions (e.g., torch==2.4.0).

**Solution**:
- Use the recommended Conda setup with the specified PyTorch version: `conda install -c pytorch pytorch=2.4.0`
- If needed, create a clean environment: `conda create -n symblang_clean python=3.10`

#### Problematic FAISS Installations

**Problem**: Corrupt or incomplete FAISS installations can prevent proper functioning.

**Solution**:
- Check for problematic directories like '~faiss_cpu-*.dist-info' in your site-packages
- Remove these directories if found
- Reinstall FAISS-GPU through Conda

### Other Common Issues

#### GPU Not Detected

**Problem**: The system cannot detect your GPU.

**Solution**:
- Verify CUDA installation: `python -c "import torch; print(torch.cuda.is_available())"` 
- Update GPU drivers
- Check that your GPU is CUDA-compatible

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the Apache v2.0 License - see the LICENSE file for details.

## Acknowledgments

- SymbolicAI team for the symbolic reasoning engine
- LightRAG team for the retrieval-augmented generation framework
- FastAPI team for the excellent web framework
