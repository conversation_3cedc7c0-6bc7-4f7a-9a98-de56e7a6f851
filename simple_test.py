import sys
print("Python version:", sys.version)
print("Python executable:", sys.executable)
print("\nPython path:")
for p in sys.path:
    print(f"  - {p}")

# Try to import the HybridRetriever
try:
    from src.hybrid_retriever import HybridRetriever
    print("\nSuccessfully imported HybridRetriever!")
    
    # Test initialization
    print("\nTesting HybridRetriever initialization...")
    retriever = HybridRetriever(
        vector_db="torch",  # Use torch for this test to avoid FAISS dependency issues
        use_gpu=False,      # Disable GPU for this test
        first_stage_k=10,
        rerank_k=5
    )
    print("HybridRetriever initialized successfully!")
    
except Exception as e:
    print(f"\nError: {e}")
    import traceback
    traceback.print_exc()
