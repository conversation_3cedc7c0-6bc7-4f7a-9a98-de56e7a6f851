#!/usr/bin/env python3
"""
Comprehensive examples for using the Neural Symbolic Language Model with Ollama gemma3n:e2b.

This script demonstrates various use cases including:
- Basic chat interactions
- Reasoning and logic tasks
- Mathematical computations
- Code analysis and generation
- Performance optimization techniques
"""

import asyncio
import time
import json
import requests
from typing import List, Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8080"
API_KEY = "your-api-key-here"  # Replace with your actual API key
MODEL = "gemma3n:e2b"

# Headers for API requests
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}"
}


def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print('='*60)


def print_example(title: str, description: str):
    """Print a formatted example header."""
    print(f"\n📝 {title}")
    print(f"   {description}")
    print("-" * 50)


async def basic_chat_example():
    """Demonstrate basic chat functionality."""
    print_section("BASIC CHAT EXAMPLES")
    
    # Example 1: Simple question
    print_example("Simple Question", "Basic factual query")
    
    response = requests.post(
        f"{API_BASE_URL}/v1/chat/completions",
        headers=HEADERS,
        json={
            "model": MODEL,
            "messages": [
                {"role": "user", "content": "What is artificial intelligence?"}
            ]
        }
    )
    
    if response.status_code == 200:
        data = response.json()
        content = data["choices"][0]["message"]["content"]
        print(f"Response: {content[:200]}...")
    else:
        print(f"Error: {response.status_code} - {response.text}")


async def reasoning_examples():
    """Demonstrate reasoning and logic capabilities."""
    print_section("REASONING & LOGIC EXAMPLES")
    
    reasoning_tasks = [
        {
            "title": "Logical Deduction",
            "description": "Testing transitive logic",
            "query": "If A implies B and B implies C, what can we conclude about A and C? Provide a step-by-step explanation."
        },
        {
            "title": "Syllogistic Reasoning",
            "description": "Classical logic puzzle",
            "query": "All cats are animals. Fluffy is a cat. What can we conclude about Fluffy? Explain your reasoning."
        },
        {
            "title": "Comparative Analysis",
            "description": "Comparing concepts",
            "query": "Compare and contrast deductive reasoning and inductive reasoning. Provide examples of each."
        }
    ]
    
    for task in reasoning_tasks:
        print_example(task["title"], task["description"])
        
        start_time = time.time()
        
        response = requests.post(
            f"{API_BASE_URL}/v1/chat/completions",
            headers=HEADERS,
            json={
                "model": MODEL,
                "messages": [
                    {"role": "system", "content": "You are an expert in logic and reasoning. Provide clear, structured explanations."},
                    {"role": "user", "content": task["query"]}
                ]
            }
        )
        
        duration = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            content = data["choices"][0]["message"]["content"]
            word_count = len(content.split())
            
            print(f"Duration: {duration:.2f}s")
            print(f"Words: {word_count}")
            print(f"Preview: {content[:150]}...")
        else:
            print(f"Error: {response.status_code}")


async def mathematical_examples():
    """Demonstrate mathematical computation capabilities."""
    print_section("MATHEMATICAL COMPUTATION EXAMPLES")
    
    math_problems = [
        {
            "title": "Basic Arithmetic",
            "query": "What is 127 * 89? Show your calculation."
        },
        {
            "title": "Algebraic Problem",
            "query": "Solve for x: 2x + 5 = 17. Show each step."
        },
        {
            "title": "Word Problem",
            "query": "A train travels 120 miles in 2 hours. At this rate, how far will it travel in 5 hours? Explain your reasoning."
        },
        {
            "title": "Probability",
            "query": "What is the probability of rolling two dice and getting a sum of 7? Explain your calculation."
        }
    ]
    
    for problem in math_problems:
        print_example(problem["title"], "Mathematical reasoning task")
        
        start_time = time.time()
        
        response = requests.post(
            f"{API_BASE_URL}/v1/chat/completions",
            headers=HEADERS,
            json={
                "model": MODEL,
                "messages": [
                    {"role": "system", "content": "You are a mathematics expert. Show your work step by step."},
                    {"role": "user", "content": problem["query"]}
                ]
            }
        )
        
        duration = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            content = data["choices"][0]["message"]["content"]
            print(f"Duration: {duration:.2f}s")
            print(f"Answer: {content[:200]}...")
        else:
            print(f"Error: {response.status_code}")


async def code_analysis_examples():
    """Demonstrate code analysis and generation capabilities."""
    print_section("CODE ANALYSIS EXAMPLES")
    
    # Example 1: Code explanation
    print_example("Code Explanation", "Analyzing Python code")
    
    code_to_analyze = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
"""
    
    response = requests.post(
        f"{API_BASE_URL}/v1/chat/completions",
        headers=HEADERS,
        json={
            "model": MODEL,
            "messages": [
                {"role": "user", "content": f"Explain this Python code and suggest improvements:\n\n{code_to_analyze}"}
            ]
        }
    )
    
    if response.status_code == 200:
        data = response.json()
        content = data["choices"][0]["message"]["content"]
        print(f"Analysis: {content[:300]}...")
    
    # Example 2: Code generation
    print_example("Code Generation", "Generating optimized code")
    
    response = requests.post(
        f"{API_BASE_URL}/v1/chat/completions",
        headers=HEADERS,
        json={
            "model": MODEL,
            "messages": [
                {"role": "user", "content": "Write an optimized Python function to calculate the nth Fibonacci number using dynamic programming. Include comments and error handling."}
            ]
        }
    )
    
    if response.status_code == 200:
        data = response.json()
        content = data["choices"][0]["message"]["content"]
        print(f"Generated Code: {content[:400]}...")


async def streaming_example():
    """Demonstrate streaming responses."""
    print_section("STREAMING RESPONSE EXAMPLE")
    
    print_example("Streaming Chat", "Real-time response streaming")
    
    # Note: This example shows the concept, but requests doesn't handle SSE well
    # In practice, you'd use a library like httpx or implement WebSocket
    
    response = requests.post(
        f"{API_BASE_URL}/v1/chat/completions",
        headers=HEADERS,
        json={
            "model": MODEL,
            "messages": [
                {"role": "user", "content": "Explain the concept of neural-symbolic AI in detail."}
            ],
            "stream": True
        },
        stream=True
    )
    
    print("Streaming response (concept):")
    print("In a real implementation, you would process each chunk as it arrives...")
    
    if response.status_code == 200:
        # In practice, you'd iterate over response.iter_lines()
        # and parse each Server-Sent Event
        print("✅ Streaming endpoint is available")
    else:
        print(f"❌ Streaming failed: {response.status_code}")


async def performance_optimization_examples():
    """Demonstrate performance optimization techniques."""
    print_section("PERFORMANCE OPTIMIZATION EXAMPLES")
    
    # Example 1: Concurrent requests
    print_example("Concurrent Processing", "Multiple queries in parallel")
    
    queries = [
        "What is machine learning?",
        "Explain deep learning.",
        "What is natural language processing?",
        "How does computer vision work?",
        "What is reinforcement learning?"
    ]
    
    start_time = time.time()
    
    # Sequential processing
    sequential_times = []
    for query in queries[:2]:  # Test with 2 queries for demo
        query_start = time.time()
        response = requests.post(
            f"{API_BASE_URL}/v1/chat/completions",
            headers=HEADERS,
            json={
                "model": MODEL,
                "messages": [{"role": "user", "content": query}]
            }
        )
        query_time = time.time() - query_start
        sequential_times.append(query_time)
    
    sequential_total = time.time() - start_time
    
    print(f"Sequential processing: {sequential_total:.2f}s")
    print(f"Average per query: {sum(sequential_times)/len(sequential_times):.2f}s")
    
    # Example 2: Caching demonstration
    print_example("Response Caching", "Demonstrating cache benefits")
    
    repeated_query = "What is 2 + 2?"
    
    # First request (uncached)
    start_time = time.time()
    response1 = requests.post(
        f"{API_BASE_URL}/v1/chat/completions",
        headers=HEADERS,
        json={
            "model": MODEL,
            "messages": [{"role": "user", "content": repeated_query}]
        }
    )
    first_time = time.time() - start_time
    
    # Second request (potentially cached)
    start_time = time.time()
    response2 = requests.post(
        f"{API_BASE_URL}/v1/chat/completions",
        headers=HEADERS,
        json={
            "model": MODEL,
            "messages": [{"role": "user", "content": repeated_query}]
        }
    )
    second_time = time.time() - start_time
    
    print(f"First request: {first_time:.2f}s")
    print(f"Second request: {second_time:.2f}s")
    if second_time < first_time:
        print(f"Speed improvement: {first_time/second_time:.2f}x")


async def document_management_examples():
    """Demonstrate document management capabilities."""
    print_section("DOCUMENT MANAGEMENT EXAMPLES")
    
    # Example 1: Adding documents
    print_example("Adding Documents", "Enhancing context with documents")
    
    sample_document = {
        "content": """
        Neural-symbolic AI combines the learning capabilities of neural networks 
        with the reasoning capabilities of symbolic AI. This hybrid approach 
        allows systems to both learn from data and perform logical reasoning, 
        making them more interpretable and capable of handling complex tasks 
        that require both pattern recognition and logical inference.
        """,
        "metadata": {
            "title": "Neural-Symbolic AI Overview",
            "category": "artificial_intelligence",
            "source": "research_paper",
            "date": "2025-06-29"
        }
    }
    
    response = requests.post(
        f"{API_BASE_URL}/documents/add",
        headers=HEADERS,
        json=sample_document
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Document added: {data.get('document_id', 'Unknown ID')}")
    else:
        print(f"❌ Failed to add document: {response.status_code}")
    
    # Example 2: Querying with enhanced context
    print_example("Context-Enhanced Query", "Using added documents for better responses")
    
    response = requests.post(
        f"{API_BASE_URL}/v1/chat/completions",
        headers=HEADERS,
        json={
            "model": MODEL,
            "messages": [
                {"role": "user", "content": "Based on the documents you have, explain how neural-symbolic AI works and its advantages."}
            ]
        }
    )
    
    if response.status_code == 200:
        data = response.json()
        content = data["choices"][0]["message"]["content"]
        print(f"Enhanced response: {content[:300]}...")


async def monitoring_examples():
    """Demonstrate monitoring and health check capabilities."""
    print_section("MONITORING & HEALTH EXAMPLES")
    
    # Example 1: Health check
    print_example("Health Check", "Checking system status")
    
    response = requests.get(f"{API_BASE_URL}/health")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Status: {data.get('status', 'Unknown')}")
        print(f"Services: {data.get('services', {})}")
    else:
        print(f"❌ Health check failed: {response.status_code}")
    
    # Example 2: Performance metrics
    print_example("Performance Metrics", "Getting system performance data")
    
    response = requests.get(
        f"{API_BASE_URL}/performance",
        headers={"Authorization": f"Bearer {API_KEY}"}
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"Cache stats: {data.get('cache_stats', {})}")
        print(f"Memory usage: {data.get('memory', {})}")
        print(f"Profiler data: {list(data.get('profiler', {}).keys())}")
    else:
        print(f"❌ Performance metrics failed: {response.status_code}")


async def main():
    """Run all examples."""
    print("🚀 Neural Symbolic Language Model - Ollama gemma3n:e2b Examples")
    print(f"📡 API Base URL: {API_BASE_URL}")
    print(f"🤖 Model: {MODEL}")
    
    try:
        # Check if API is available
        health_response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ API server is not responding. Please start the server first.")
            return
        
        print("✅ API server is running")
        
        # Run examples
        await basic_chat_example()
        await reasoning_examples()
        await mathematical_examples()
        await code_analysis_examples()
        await streaming_example()
        await performance_optimization_examples()
        await document_management_examples()
        await monitoring_examples()
        
        print_section("EXAMPLES COMPLETED")
        print("🎉 All examples have been executed successfully!")
        print("\n📝 Next Steps:")
        print("1. Modify the API_KEY variable with your actual API key")
        print("2. Experiment with different queries and parameters")
        print("3. Try the streaming endpoints with proper SSE handling")
        print("4. Explore the performance optimization techniques")
        print("5. Add your own documents to enhance context")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the API server.")
        print("Please ensure the Neural Symbolic Language Model server is running:")
        print("   python src/main.py")
    except Exception as e:
        print(f"❌ An error occurred: {e}")


if __name__ == "__main__":
    asyncio.run(main())
