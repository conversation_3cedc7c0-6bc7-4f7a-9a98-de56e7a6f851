# Symbolic Language Model User's Guide

This guide explains how to use the Symbolic Language Model as a powerful local replacement for cloud-based foundation models, focusing on practical usage patterns, configuration options, and integration strategies.

## Table of Contents

- [Introduction](#introduction)
- [Key Advantages](#key-advantages)
- [Detailed Comparison: Response Time, Accuracy, and Trade-offs](#detailed-comparison-response-time-accuracy-and-trade-offs)
- [Getting Started](#getting-started)
- [API Usage](#api-usage)
- [Advanced Configuration](#advanced-configuration)
- [Integration Patterns](#integration-patterns)
- [Performance Optimization](#performance-optimization)
- [Use Cases](#use-cases)
- [Limitations](#limitations)

## Introduction

The Symbolic Language Model combines neural-symbolic reasoning with retrieval-augmented generation to provide a locally-run alternative to cloud-based foundation models like GPT-4. By running entirely on your local hardware, it offers privacy, customization, and cost advantages while maintaining high-quality responses through its hybrid architecture.

### Core Components

1. **Neural-Symbolic Reasoning Engine**: Powered by SymbolicAI, enables structured, explainable decision-making
2. **Retrieval-Augmented Generation**: Leverages LightRAG for context-aware responses grounded in your data
3. **GPU-Accelerated Vector Search**: Uses FAISS-GPU for high-performance similarity search
4. **Local API Server**: FastAPI-based endpoint compatible with OpenAI's API format

## Key Advantages

Compared to cloud-based foundation models, the Symbolic Language Model offers:

| Feature | Cloud LLMs | Symbolic Language Model |
|---------|------------|-------------------------|
| Privacy | Data leaves your system | Complete data privacy |
| Cost | Pay-per-token or subscription | One-time setup, no ongoing costs |
| Latency | Network-dependent | Local execution speed |
| Customization | Limited to API parameters | Full system customization |
| Data Control | Limited context window | Unlimited knowledge integration |
| Explainability | Black-box responses | Transparent reasoning process |

## Detailed Comparison: Response Time, Accuracy, and Trade-offs

For a comprehensive analysis of how the Symbolic Language Model compares to cloud-based foundation models, refer to our detailed [Comparison Guide](./COMPARISON.md).

This in-depth comparison covers:

- **Response Time Analysis**: Benchmarks showing 3.5-4.5x faster responses on average
- **Accuracy Comparison**: Detailed breakdown of accuracy trade-offs and enhancement strategies
- **Cost-Benefit Analysis**: Quantified savings based on usage patterns
- **Hardware Requirements**: Specific needs for optimal performance
- **Integration Decision Framework**: Guidelines for when to use each approach

Key findings on response time and accuracy:

### Response Time

The Symbolic Language Model offers significantly faster response times (0.5-2 seconds) compared to cloud models (2-5 seconds) due to:
- Elimination of network latency
- No server queuing or throttling
- GPU-accelerated vector search (7.9-13.5x faster than CPU)
- Effective caching for repeated queries

### Accuracy

Accuracy varies by context:
- **General Knowledge**: Cloud models have an edge (85-95% vs 75-85%)
- **Domain-Specific Knowledge**: Symbolic Language Model excels (90-99%) when properly indexed
- **Reasoning**: Both provide strong capabilities, with different approaches

The Symbolic Language Model's accuracy can be significantly enhanced through comprehensive knowledge indexing, regular updates, and result verification strategies outlined in the comparison document.

## Getting Started

After [installing the system](../README.md#installation), you can interact with the Symbolic Language Model in several ways:

### 1. Using the API Server

Start the API server:

```bash
# Make sure you're in the conda environment (Windows)
conda activate symblang

# Start the server
python -m uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload
```

### 2. Direct Python Integration

```python
from src.symbolic_reasoning import SymbolicReasoner
from src.retrieval import LightRAGRetriever

# Initialize components
reasoner = SymbolicReasoner()
retriever = LightRAGRetriever()

# Process a query
query = "What is the relationship between neural networks and symbolic reasoning?"
context = retriever.get_relevant_context(query)
response = reasoner.generate_response(query, context)
print(response)
```

## API Usage

The Symbolic Language Model provides an API compatible with OpenAI's interface, making it easy to substitute in existing applications.

### Chat Endpoint

```python
import requests

response = requests.post(
    "http://localhost:8080/chat",
    json={"text": "Explain the advantages of neural-symbolic AI"}
)
print(response.json())
```

### OpenAI-Compatible Endpoint

```python
import openai

# Point to your local server instead of OpenAI
client = openai.OpenAI(
    base_url="http://localhost:8080/v1/",
    api_key="dummy-key"  # Not actually used but required
)

# Use like regular OpenAI client
response = client.chat.completions.create(
    model="symbolic-model",  # Model name is configurable
    messages=[
        {"role": "user", "content": "Explain the advantages of neural-symbolic AI"}
    ]
)
print(response.choices[0].message.content)
```

### Streaming Responses

For chat interfaces, streaming provides a more interactive experience:

```python
import openai

client = openai.OpenAI(
    base_url="http://localhost:8080/v1/",
    api_key="dummy-key"
)

response = client.chat.completions.create(
    model="symbolic-model",
    messages=[
        {"role": "user", "content": "Write a detailed explanation of neural-symbolic AI"}
    ],
    stream=True  # Enable streaming
)

for chunk in response:
    content = chunk.choices[0].delta.content
    if content:
        print(content, end="", flush=True)
```

## Advanced Configuration

The Symbolic Language Model can be customized through various configuration options:

### Environment Variables

```bash
# Memory management
export MAX_CACHE_SIZE=2000         # Maximum LRU cache size
export CACHE_EXPIRY_MINUTES=60     # Cache entry expiration time

# Performance tuning
export OMP_NUM_THREADS=1           # Control thread usage
export FAISS_GPU_MEMORY_LIMIT=4096 # Limit FAISS GPU memory usage (MB)

# Retrieval configuration
export MAX_CHUNKS_TO_RETRIEVE=5    # Maximum context chunks per query
export SIMILARITY_THRESHOLD=0.75   # Minimum similarity score for retrieval
```

### Configuration File

Create a `config.yaml` file in the project root:

```yaml
system:
  cache_size: 2000
  log_level: "info"
  
reasoner:
  temperature: 0.7
  max_tokens: 4096
  top_p: 0.95
  
retriever:
  chunk_size: 512
  overlap: 128
  distance_metric: "cosine"
  embedding_model: "local"
```

## Integration Patterns

### 1. Direct Replacement for OpenAI

The Symbolic Language Model provides a drop-in replacement for applications built with the OpenAI API. Here's a complete example showing how to migrate an existing application:

#### Before: Using OpenAI API

```python
import os
from openai import OpenAI

# Initialize OpenAI client
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

def generate_content(prompt, max_tokens=500):
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a helpful assistant with expertise in AI technologies."},
            {"role": "user", "content": prompt}
        ],
        max_tokens=max_tokens,
        temperature=0.7
    )
    return response.choices[0].message.content

def summarize_document(document_text):
    prompt = f"Please summarize this document: {document_text[:2000]}..."
    return generate_content(prompt)

def answer_question(question, context=None):
    if context:
        prompt = f"Context: {context}\n\nQuestion: {question}"
    else:
        prompt = question
    return generate_content(prompt)

# Example usage
summary = summarize_document(long_document)
response = answer_question("What are the advantages of neural-symbolic AI?")
```

#### After: Using Symbolic Language Model

```python
import os
from openai import OpenAI

# Initialize client with local Symbolic Language Model endpoint
client = OpenAI(
    base_url="http://localhost:8080/v1/",  # Point to your local server
    api_key="dummy-key"  # API key not used but required by the client library
)

def generate_content(prompt, max_tokens=500):
    response = client.chat.completions.create(
        model="symbolic-model",  # Use your local model identifier
        messages=[
            {"role": "system", "content": "You are a helpful assistant with expertise in AI technologies."},
            {"role": "user", "content": prompt}
        ],
        max_tokens=max_tokens,
        temperature=0.7
    )
    return response.choices[0].message.content

def summarize_document(document_text):
    prompt = f"Please summarize this document: {document_text[:2000]}..."
    return generate_content(prompt)

def answer_question(question, context=None):
    if context:
        prompt = f"Context: {context}\n\nQuestion: {question}"
    else:
        prompt = question
    return generate_content(prompt)

# Example usage - identical to before!
summary = summarize_document(long_document)
response = answer_question("What are the advantages of neural-symbolic AI?")
```

Notice that the only changes required are:
1. Changing the `base_url` to point to your local server
2. Using a dummy API key
3. Updating the model name to your local model

The rest of your application code remains unchanged, making migration simple and straightforward.

### 2. Chat Application Integration

For chat applications, you can use the streaming endpoint:

```python
import gradio as gr
import openai

client = openai.OpenAI(
    base_url="http://localhost:8080/v1/",
    api_key="dummy-key"
)

def chat(message, history):
    response = client.chat.completions.create(
        model="symbolic-model",
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            *[{"role": "user" if i % 2 == 0 else "assistant", "content": m} 
              for i, m in enumerate(sum(history, []))],
            {"role": "user", "content": message}
        ],
        stream=True
    )
    
    partial_message = ""
    for chunk in response:
        if chunk.choices[0].delta.content:
            partial_message += chunk.choices[0].delta.content
            yield partial_message

gr.ChatInterface(chat).launch()
```

### 3. Document Processing Pipeline

```python
import os
from src.retrieval import LightRAGRetriever
from src.symbolic_reasoning import SymbolicReasoner

# Initialize components
retriever = LightRAGRetriever()
reasoner = SymbolicReasoner()

# Process multiple documents
def process_documents(folder_path, query_template):
    results = []
    for filename in os.listdir(folder_path):
        if filename.endswith('.txt'):
            with open(os.path.join(folder_path, filename), 'r') as f:
                document = f.read()
            
            # Index the document
            retriever.add_document(document, metadata={"filename": filename})
            
            # Generate query based on document
            query = query_template.format(filename=filename)
            
            # Get response
            context = retriever.get_relevant_context(query)
            response = reasoner.generate_response(query, context)
            
            results.append({
                "filename": filename,
                "query": query,
                "response": response
            })
    
    return results

# Example usage
summaries = process_documents(
    "path/to/documents", 
    "Summarize the key points in {filename}"
)
```

## Performance Optimization

### GPU Acceleration

The Symbolic Language Model leverages GPU acceleration for vector similarity search, providing significant performance improvements:

| Dimension | CPU Search | GPU Search | Speedup |
|-----------|------------|------------|---------|
| 64        | 0.0603s    | 0.0076s    | 7.94x   |
| 128       | 0.0789s    | 0.0060s    | 13.07x  |
| 256       | 0.1371s    | 0.0101s    | 13.56x  |

To ensure optimal GPU performance:

1. Verify GPU acceleration is working:
   ```bash
   python scripts/verify_faiss_gpu.py
   ```

2. Monitor GPU usage during operation:
   ```bash
   python -c "import torch; print(torch.cuda.memory_summary())"
   ```

3. Adjust memory usage if needed:
   ```python
   # In your code
   import faiss
   res = faiss.StandardGpuResources()
   res.setTempMemory(1024 * 1024 * 1024)  # 1GB limit
   ```

### Caching Strategy

The system implements an LRU (Least Recently Used) cache with timestamp-based expiration:

1. Frequent queries are cached to avoid redundant processing
2. Cache entries expire after a configurable time period
3. Cache size is limited to prevent memory issues

Adjust cache settings based on your usage patterns:

```python
# In your configuration
{
    "cache": {
        "max_size": 2000,         # More entries for diverse workloads
        "expiry_minutes": 60,      # Shorter for rapidly changing data
        "preload_queries": ["What is neural-symbolic AI?"]  # Preload common queries
    }
}
```

## Use Cases

### 1. Privacy-Sensitive Applications

When data cannot leave your environment due to regulatory requirements (HIPAA, GDPR) or internal policies:

```python
# Process medical records locally
patient_data = load_patient_data()
medical_query = f"Summarize patient history for {patient_id}"
response = client.chat.completions.create(
    model="symbolic-model",
    messages=[{"role": "user", "content": medical_query}]
)
```

### 2. Offline Environments

For systems without internet access:

```python
# Load local knowledge base
system_manager.load_knowledge_base("./offline_data")

# Process queries without internet
while True:
    query = input("Enter query (or 'exit' to quit): ")
    if query.lower() == 'exit':
        break
    
    response = process_query(query)
    print(response)
```

### 3. Custom Domain Adaptation

Specialize in your organization's knowledge domain:

```python
# Index company-specific documents
indexer = DocumentIndexer()
indexer.index_directory("./company_documents")

# Query with domain-specific context
query = "What is our return policy for international customers?"
response = client.chat.completions.create(
    model="symbolic-model",
    messages=[{"role": "user", "content": query}]
)
```

### 4. Explainable AI Requirements

When you need to understand the reasoning process:

```python
response = client.chat.completions.create(
    model="symbolic-model",
    messages=[{"role": "user", "content": query}],
    parameters={"explain_reasoning": True}
)

# Extract the reasoning steps
explanation = response.choices[0].message.content
reasoning_steps = parse_reasoning_steps(explanation)

# Display structured reasoning
for i, step in enumerate(reasoning_steps):
    print(f"Step {i+1}: {step['action']} - {step['justification']}")
```

## Limitations

While the Symbolic Language Model provides a powerful local alternative to cloud-based foundation models, it's important to understand its limitations:

1. **Computational Resources**: Requires sufficient local hardware resources, particularly for GPU acceleration

2. **Domain Knowledge**: Pre-trained knowledge is more limited than the largest cloud models; relies heavily on your indexed knowledge

3. **Parameter Count**: Uses smaller parameter counts than the largest foundation models

4. **Fine-tuning**: More complex to fine-tune than API-based models

5. **Multi-modal Capabilities**: Currently limited to text (images, audio, and video support planned for future releases)

When using the Symbolic Language Model, consider these mitigation strategies:

- **Resource Constraints**: Use efficient indexing and caching to optimize performance
- **Knowledge Gaps**: Thoroughly index domain-specific knowledge
- **Task Complexity**: Decompose complex tasks into simpler components
- **Evaluation**: Regularly evaluate outputs against reference sources

## Conclusion

The Symbolic Language Model provides a powerful, local alternative to cloud-based foundation models, offering privacy, cost, and customization advantages. By combining neural-symbolic reasoning with retrieval-augmented generation and GPU acceleration, it delivers high-quality responses while keeping your data local and under your control.

For technical setup details, refer to the [README](../README.md). For troubleshooting GPU issues, see the [Windows FAISS Guide](./WINDOWS_FAISS_GUIDE.md).

---

**© 2025 Symbolic Language Model Team**
