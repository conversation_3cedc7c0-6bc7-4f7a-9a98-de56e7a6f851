@echo off
echo Starting Windows Environment Setup for Symbolic Language Model
echo ========================================================
echo.

:: Check for Administrator privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires elevated privileges to install components properly.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Running PowerShell setup script...
echo.

:: Run the PowerShell script with execution policy bypass
powershell.exe -ExecutionPolicy Bypass -File "%~dp0scripts\setup_windows_env.ps1"

:: Check if the script ran successfully
if %errorlevel% neq 0 (
    echo.
    echo Setup encountered an error. Please check the output above.
    pause
    exit /b 1
)

echo.
echo Setup completed successfully!
echo.
echo To verify your installation, activate your Conda environment and run:
echo python scripts\verify_faiss_gpu.py
echo.

pause
