

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Data Models Module &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="prev" title="Security Module" href="security.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../getting_started.html">Getting Started</a></li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="../deployment.html">Deployment Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../architecture.html">Architecture Overview</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../modules.html">API Reference</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="../modules.html#core-modules">Core Modules</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="main.html">Main Application Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="security.html">Security Module</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">Data Models Module</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#models.ModelRole"><code class="docutils literal notranslate"><span class="pre">ModelRole</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.ChatMessage"><code class="docutils literal notranslate"><span class="pre">ChatMessage</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.ChatRequest"><code class="docutils literal notranslate"><span class="pre">ChatRequest</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.ChatChoice"><code class="docutils literal notranslate"><span class="pre">ChatChoice</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.TokenUsage"><code class="docutils literal notranslate"><span class="pre">TokenUsage</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.ChatResponse"><code class="docutils literal notranslate"><span class="pre">ChatResponse</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.DocumentAddRequest"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.DocumentAddResponse"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.SystemInfo"><code class="docutils literal notranslate"><span class="pre">SystemInfo</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.PerformanceMetrics"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.OpenAIMessage"><code class="docutils literal notranslate"><span class="pre">OpenAIMessage</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.OpenAIChatRequest"><code class="docutils literal notranslate"><span class="pre">OpenAIChatRequest</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.OpenAIChatChoice"><code class="docutils literal notranslate"><span class="pre">OpenAIChatChoice</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#models.OpenAIChatResponse"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="#request-models">Request Models</a></li>
<li class="toctree-l4"><a class="reference internal" href="#response-models">Response Models</a></li>
<li class="toctree-l4"><a class="reference internal" href="#streaming-models">Streaming Models</a></li>
<li class="toctree-l4"><a class="reference internal" href="#error-models">Error Models</a></li>
<li class="toctree-l4"><a class="reference internal" href="#system-models">System Models</a></li>
<li class="toctree-l4"><a class="reference internal" href="#validation-features">Validation Features</a></li>
<li class="toctree-l4"><a class="reference internal" href="#serialization">Serialization</a></li>
<li class="toctree-l4"><a class="reference internal" href="#example-usage">Example Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="#best-practices">Best Practices</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../modules.html#module-main">Core Components</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#utility-modules">Utility Modules</a></li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#api-routes">API Routes</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="security.html">Security Module</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Data Models Module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#models.ModelRole"><code class="docutils literal notranslate"><span class="pre">ModelRole</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.ModelRole.SYSTEM"><code class="docutils literal notranslate"><span class="pre">ModelRole.SYSTEM</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ModelRole.USER"><code class="docutils literal notranslate"><span class="pre">ModelRole.USER</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ModelRole.ASSISTANT"><code class="docutils literal notranslate"><span class="pre">ModelRole.ASSISTANT</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ModelRole.__format__"><code class="docutils literal notranslate"><span class="pre">ModelRole.__format__()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.ChatMessage"><code class="docutils literal notranslate"><span class="pre">ChatMessage</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatMessage.role"><code class="docutils literal notranslate"><span class="pre">ChatMessage.role</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatMessage.content"><code class="docutils literal notranslate"><span class="pre">ChatMessage.content</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatMessage.timestamp"><code class="docutils literal notranslate"><span class="pre">ChatMessage.timestamp</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatMessage.model_config"><code class="docutils literal notranslate"><span class="pre">ChatMessage.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id0"><code class="docutils literal notranslate"><span class="pre">ChatMessage.role</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id1"><code class="docutils literal notranslate"><span class="pre">ChatMessage.content</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id2"><code class="docutils literal notranslate"><span class="pre">ChatMessage.timestamp</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatMessage.validate_content"><code class="docutils literal notranslate"><span class="pre">ChatMessage.validate_content()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.ChatRequest"><code class="docutils literal notranslate"><span class="pre">ChatRequest</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatRequest.messages"><code class="docutils literal notranslate"><span class="pre">ChatRequest.messages</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatRequest.model"><code class="docutils literal notranslate"><span class="pre">ChatRequest.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatRequest.temperature"><code class="docutils literal notranslate"><span class="pre">ChatRequest.temperature</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatRequest.max_tokens"><code class="docutils literal notranslate"><span class="pre">ChatRequest.max_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatRequest.stream"><code class="docutils literal notranslate"><span class="pre">ChatRequest.stream</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatRequest.top_p"><code class="docutils literal notranslate"><span class="pre">ChatRequest.top_p</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatRequest.model_config"><code class="docutils literal notranslate"><span class="pre">ChatRequest.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id3"><code class="docutils literal notranslate"><span class="pre">ChatRequest.messages</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id4"><code class="docutils literal notranslate"><span class="pre">ChatRequest.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id5"><code class="docutils literal notranslate"><span class="pre">ChatRequest.temperature</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id6"><code class="docutils literal notranslate"><span class="pre">ChatRequest.max_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id7"><code class="docutils literal notranslate"><span class="pre">ChatRequest.stream</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id8"><code class="docutils literal notranslate"><span class="pre">ChatRequest.top_p</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatRequest.validate_messages"><code class="docutils literal notranslate"><span class="pre">ChatRequest.validate_messages()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.ChatChoice"><code class="docutils literal notranslate"><span class="pre">ChatChoice</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatChoice.index"><code class="docutils literal notranslate"><span class="pre">ChatChoice.index</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatChoice.message"><code class="docutils literal notranslate"><span class="pre">ChatChoice.message</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatChoice.finish_reason"><code class="docutils literal notranslate"><span class="pre">ChatChoice.finish_reason</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatChoice.model_config"><code class="docutils literal notranslate"><span class="pre">ChatChoice.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id9"><code class="docutils literal notranslate"><span class="pre">ChatChoice.index</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id10"><code class="docutils literal notranslate"><span class="pre">ChatChoice.message</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id11"><code class="docutils literal notranslate"><span class="pre">ChatChoice.finish_reason</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.TokenUsage"><code class="docutils literal notranslate"><span class="pre">TokenUsage</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.TokenUsage.prompt_tokens"><code class="docutils literal notranslate"><span class="pre">TokenUsage.prompt_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.TokenUsage.completion_tokens"><code class="docutils literal notranslate"><span class="pre">TokenUsage.completion_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.TokenUsage.total_tokens"><code class="docutils literal notranslate"><span class="pre">TokenUsage.total_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.TokenUsage.model_config"><code class="docutils literal notranslate"><span class="pre">TokenUsage.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id12"><code class="docutils literal notranslate"><span class="pre">TokenUsage.prompt_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id13"><code class="docutils literal notranslate"><span class="pre">TokenUsage.completion_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id14"><code class="docutils literal notranslate"><span class="pre">TokenUsage.total_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.TokenUsage.validate_total_tokens"><code class="docutils literal notranslate"><span class="pre">TokenUsage.validate_total_tokens()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.ChatResponse"><code class="docutils literal notranslate"><span class="pre">ChatResponse</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatResponse.id"><code class="docutils literal notranslate"><span class="pre">ChatResponse.id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatResponse.object"><code class="docutils literal notranslate"><span class="pre">ChatResponse.object</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatResponse.created"><code class="docutils literal notranslate"><span class="pre">ChatResponse.created</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatResponse.model"><code class="docutils literal notranslate"><span class="pre">ChatResponse.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatResponse.choices"><code class="docutils literal notranslate"><span class="pre">ChatResponse.choices</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatResponse.usage"><code class="docutils literal notranslate"><span class="pre">ChatResponse.usage</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.ChatResponse.model_config"><code class="docutils literal notranslate"><span class="pre">ChatResponse.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id15"><code class="docutils literal notranslate"><span class="pre">ChatResponse.id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id16"><code class="docutils literal notranslate"><span class="pre">ChatResponse.object</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id17"><code class="docutils literal notranslate"><span class="pre">ChatResponse.created</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id18"><code class="docutils literal notranslate"><span class="pre">ChatResponse.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id19"><code class="docutils literal notranslate"><span class="pre">ChatResponse.choices</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id20"><code class="docutils literal notranslate"><span class="pre">ChatResponse.usage</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.DocumentAddRequest"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddRequest.text"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest.text</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddRequest.metadata"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest.metadata</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddRequest.document_id"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest.document_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddRequest.model_config"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id21"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest.text</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id22"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest.metadata</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id23"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest.document_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddRequest.validate_text"><code class="docutils literal notranslate"><span class="pre">DocumentAddRequest.validate_text()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.DocumentAddResponse"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddResponse.success"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.success</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddResponse.message"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.message</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddResponse.document_id"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.document_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddResponse.metadata"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.metadata</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.DocumentAddResponse.model_config"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id24"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.success</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id25"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.message</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id26"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.document_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id27"><code class="docutils literal notranslate"><span class="pre">DocumentAddResponse.metadata</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.SystemInfo"><code class="docutils literal notranslate"><span class="pre">SystemInfo</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.SystemInfo.gpu_available"><code class="docutils literal notranslate"><span class="pre">SystemInfo.gpu_available</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.SystemInfo.gpu_name"><code class="docutils literal notranslate"><span class="pre">SystemInfo.gpu_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.SystemInfo.gpu_optimized"><code class="docutils literal notranslate"><span class="pre">SystemInfo.gpu_optimized</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.SystemInfo.reasoner_info"><code class="docutils literal notranslate"><span class="pre">SystemInfo.reasoner_info</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.SystemInfo.retriever_info"><code class="docutils literal notranslate"><span class="pre">SystemInfo.retriever_info</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.SystemInfo.model_config"><code class="docutils literal notranslate"><span class="pre">SystemInfo.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id28"><code class="docutils literal notranslate"><span class="pre">SystemInfo.gpu_available</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id29"><code class="docutils literal notranslate"><span class="pre">SystemInfo.gpu_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id30"><code class="docutils literal notranslate"><span class="pre">SystemInfo.gpu_optimized</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id31"><code class="docutils literal notranslate"><span class="pre">SystemInfo.reasoner_info</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id32"><code class="docutils literal notranslate"><span class="pre">SystemInfo.retriever_info</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.PerformanceMetrics"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.PerformanceMetrics.cache"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics.cache</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.PerformanceMetrics.system"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics.system</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.PerformanceMetrics.requests"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics.requests</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.PerformanceMetrics.model_config"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id33"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics.cache</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id34"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics.system</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#id35"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics.requests</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.OpenAIMessage"><code class="docutils literal notranslate"><span class="pre">OpenAIMessage</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIMessage.model_config"><code class="docutils literal notranslate"><span class="pre">OpenAIMessage.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIMessage.role"><code class="docutils literal notranslate"><span class="pre">OpenAIMessage.role</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIMessage.content"><code class="docutils literal notranslate"><span class="pre">OpenAIMessage.content</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.OpenAIChatRequest"><code class="docutils literal notranslate"><span class="pre">OpenAIChatRequest</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatRequest.model_config"><code class="docutils literal notranslate"><span class="pre">OpenAIChatRequest.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatRequest.model"><code class="docutils literal notranslate"><span class="pre">OpenAIChatRequest.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatRequest.messages"><code class="docutils literal notranslate"><span class="pre">OpenAIChatRequest.messages</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatRequest.temperature"><code class="docutils literal notranslate"><span class="pre">OpenAIChatRequest.temperature</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatRequest.max_tokens"><code class="docutils literal notranslate"><span class="pre">OpenAIChatRequest.max_tokens</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatRequest.stream"><code class="docutils literal notranslate"><span class="pre">OpenAIChatRequest.stream</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.OpenAIChatChoice"><code class="docutils literal notranslate"><span class="pre">OpenAIChatChoice</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatChoice.model_config"><code class="docutils literal notranslate"><span class="pre">OpenAIChatChoice.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatChoice.index"><code class="docutils literal notranslate"><span class="pre">OpenAIChatChoice.index</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatChoice.message"><code class="docutils literal notranslate"><span class="pre">OpenAIChatChoice.message</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatChoice.finish_reason"><code class="docutils literal notranslate"><span class="pre">OpenAIChatChoice.finish_reason</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#models.OpenAIChatResponse"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatResponse.model_config"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse.model_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatResponse.id"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse.id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatResponse.object"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse.object</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatResponse.created"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse.created</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatResponse.model"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatResponse.choices"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse.choices</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#models.OpenAIChatResponse.usage"><code class="docutils literal notranslate"><span class="pre">OpenAIChatResponse.usage</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#request-models">Request Models</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#chatcompletionrequest">ChatCompletionRequest</a></li>
<li class="toctree-l3"><a class="reference internal" href="#message">Message</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#response-models">Response Models</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#chatcompletionresponse">ChatCompletionResponse</a></li>
<li class="toctree-l3"><a class="reference internal" href="#choice">Choice</a></li>
<li class="toctree-l3"><a class="reference internal" href="#usage">Usage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#streaming-models">Streaming Models</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#chatcompletionchunk">ChatCompletionChunk</a></li>
<li class="toctree-l3"><a class="reference internal" href="#choicedelta">ChoiceDelta</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-models">Error Models</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#errorresponse">ErrorResponse</a></li>
<li class="toctree-l3"><a class="reference internal" href="#errordetail">ErrorDetail</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#system-models">System Models</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#systeminfo">SystemInfo</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id36"><code class="docutils literal notranslate"><span class="pre">SystemInfo</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#performancemetrics">PerformanceMetrics</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id48"><code class="docutils literal notranslate"><span class="pre">PerformanceMetrics</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#validation-features">Validation Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#field-validation">Field Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#custom-validators">Custom Validators</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#serialization">Serialization</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#json-serialization">JSON Serialization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dictionary-conversion">Dictionary Conversion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#complete-request-response-cycle">Complete Request/Response Cycle</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#model-design">Model Design</a></li>
<li class="toctree-l3"><a class="reference internal" href="#validation">Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance">Performance</a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../modules.html">API Reference</a></li>
      <li class="breadcrumb-item active">Data Models Module</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api/models.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-models">
<span id="data-models-module"></span><h1>Data Models Module<a class="headerlink" href="#module-models" title="Link to this heading"></a></h1>
<p>Pydantic v2 models for the Neural Symbolic Language Model.</p>
<p>This module defines all data models using Pydantic v2 with proper validation,
field constraints, and comprehensive documentation.</p>
<p>Author: AI Assistant
Date: 2025-06-29</p>
<dl class="py class">
<dt class="sig sig-object py" id="models.ModelRole">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">ModelRole</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#ModelRole"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.ModelRole" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference external" href="https://docs.python.org/3/library/enum.html#enum.Enum" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a></p>
<p>Enumeration of valid message roles.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.ModelRole.SYSTEM">
<span class="sig-name descname"><span class="pre">SYSTEM</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'system'</span></em><a class="headerlink" href="#models.ModelRole.SYSTEM" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ModelRole.USER">
<span class="sig-name descname"><span class="pre">USER</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'user'</span></em><a class="headerlink" href="#models.ModelRole.USER" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ModelRole.ASSISTANT">
<span class="sig-name descname"><span class="pre">ASSISTANT</span></span><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">'assistant'</span></em><a class="headerlink" href="#models.ModelRole.ASSISTANT" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="models.ModelRole.__format__">
<span class="sig-name descname"><span class="pre">__format__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format_spec</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#models.ModelRole.__format__" title="Link to this definition"></a></dt>
<dd><p>Returns format using actual value type unless __str__ has been overridden.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.ChatMessage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">ChatMessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">*,</span> <span class="pre">role:</span> <span class="pre">~models.ModelRole,</span> <span class="pre">content:</span> <span class="pre">~typing.Annotated[str,</span> <span class="pre">~annotated_types.MinLen(min_length=1),</span> <span class="pre">~annotated_types.MaxLen(max_length=10000)],</span> <span class="pre">timestamp:</span> <span class="pre">~datetime.datetime</span> <span class="pre">|</span> <span class="pre">None</span> <span class="pre">=</span> <span class="pre">&lt;factory&gt;</span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#ChatMessage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.ChatMessage" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Represents a single chat message.</p>
<p>This model validates chat messages with proper role validation
and content length constraints.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatMessage.role">
<span class="sig-name descname"><span class="pre">role</span></span><a class="headerlink" href="#models.ChatMessage.role" title="Link to this definition"></a></dt>
<dd><p>The role of the message sender</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="../modules.html#models.ModelRole" title="models.ModelRole">models.ModelRole</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatMessage.content">
<span class="sig-name descname"><span class="pre">content</span></span><a class="headerlink" href="#models.ChatMessage.content" title="Link to this definition"></a></dt>
<dd><p>The message content</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatMessage.timestamp">
<span class="sig-name descname"><span class="pre">timestamp</span></span><a class="headerlink" href="#models.ChatMessage.timestamp" title="Link to this definition"></a></dt>
<dd><p>When the message was created</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/datetime.html#datetime.datetime" title="(in Python v3.13)">datetime.datetime</a> | None</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatMessage.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'json_schema_extra':</span> <span class="pre">{'example':</span> <span class="pre">{'content':</span> <span class="pre">'What</span> <span class="pre">is</span> <span class="pre">neural-symbolic</span> <span class="pre">AI?',</span> <span class="pre">'role':</span> <span class="pre">'user',</span> <span class="pre">'timestamp':</span> <span class="pre">'2025-06-29T12:00:00Z'}},</span> <span class="pre">'str_strip_whitespace':</span> <span class="pre">True,</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.ChatMessage.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id0">
<span class="sig-name descname"><span class="pre">role</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="../modules.html#models.ModelRole" title="models.ModelRole"><span class="pre">ModelRole</span></a></em><a class="headerlink" href="#id0" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id1">
<span class="sig-name descname"><span class="pre">content</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#id1" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id2">
<span class="sig-name descname"><span class="pre">timestamp</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/datetime.html#datetime.datetime" title="(in Python v3.13)"><span class="pre">datetime</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#id2" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="models.ChatMessage.validate_content">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">validate_content</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">v</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/models.html#ChatMessage.validate_content"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.ChatMessage.validate_content" title="Link to this definition"></a></dt>
<dd><p>Validate message content.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>v</strong> – The content to validate</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Validated content</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – If content is invalid</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.ChatRequest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">ChatRequest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">messages</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.ChatMessage" title="models.ChatMessage"><span class="pre">ChatMessage</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MaxLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">max_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">100</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MaxLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">max_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">100</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'local'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">temperature</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="pre">0.0</span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Le</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">le</span></span><span class="o"><span class="pre">=</span></span><span class="pre">2.0</span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">0.7</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_tokens</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Gt</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">gt</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Le</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">le</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">4096</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stream</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">top_p</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="pre">0.0</span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Le</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">le</span></span><span class="o"><span class="pre">=</span></span><span class="pre">1.0</span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">1.0</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#ChatRequest"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.ChatRequest" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Request model for chat endpoints.</p>
<p>This model validates chat requests with proper message validation,
parameter constraints, and security considerations.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatRequest.messages">
<span class="sig-name descname"><span class="pre">messages</span></span><a class="headerlink" href="#models.ChatRequest.messages" title="Link to this definition"></a></dt>
<dd><p>List of chat messages</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List[<a class="reference internal" href="../modules.html#models.ChatMessage" title="models.ChatMessage">models.ChatMessage</a>]</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatRequest.model">
<span class="sig-name descname"><span class="pre">model</span></span><a class="headerlink" href="#models.ChatRequest.model" title="Link to this definition"></a></dt>
<dd><p>Model identifier to use</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatRequest.temperature">
<span class="sig-name descname"><span class="pre">temperature</span></span><a class="headerlink" href="#models.ChatRequest.temperature" title="Link to this definition"></a></dt>
<dd><p>Response randomness (0.0-1.0)</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)">float</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatRequest.max_tokens">
<span class="sig-name descname"><span class="pre">max_tokens</span></span><a class="headerlink" href="#models.ChatRequest.max_tokens" title="Link to this definition"></a></dt>
<dd><p>Maximum tokens in response</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a> | None</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatRequest.stream">
<span class="sig-name descname"><span class="pre">stream</span></span><a class="headerlink" href="#models.ChatRequest.stream" title="Link to this definition"></a></dt>
<dd><p>Whether to stream the response</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatRequest.top_p">
<span class="sig-name descname"><span class="pre">top_p</span></span><a class="headerlink" href="#models.ChatRequest.top_p" title="Link to this definition"></a></dt>
<dd><p>Nucleus sampling parameter</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)">float</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatRequest.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'json_schema_extra':</span> <span class="pre">{'example':</span> <span class="pre">{'max_tokens':</span> <span class="pre">1000,</span> <span class="pre">'messages':</span> <span class="pre">[{'content':</span> <span class="pre">'What</span> <span class="pre">is</span> <span class="pre">symbolic</span> <span class="pre">reasoning?',</span> <span class="pre">'role':</span> <span class="pre">'user'}],</span> <span class="pre">'model':</span> <span class="pre">'local',</span> <span class="pre">'stream':</span> <span class="pre">False,</span> <span class="pre">'temperature':</span> <span class="pre">0.7}},</span> <span class="pre">'str_strip_whitespace':</span> <span class="pre">True,</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.ChatRequest.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id3">
<span class="sig-name descname"><span class="pre">messages</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.ChatMessage" title="models.ChatMessage"><span class="pre">ChatMessage</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#id3" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id4">
<span class="sig-name descname"><span class="pre">model</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#id4" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id5">
<span class="sig-name descname"><span class="pre">temperature</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#id5" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id6">
<span class="sig-name descname"><span class="pre">max_tokens</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Gt</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">gt</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#id6" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id7">
<span class="sig-name descname"><span class="pre">stream</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#id7" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id8">
<span class="sig-name descname"><span class="pre">top_p</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#id8" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="models.ChatRequest.validate_messages">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">validate_messages</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">v</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.ChatMessage" title="models.ChatMessage"><span class="pre">ChatMessage</span></a><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.ChatMessage" title="models.ChatMessage"><span class="pre">ChatMessage</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/models.html#ChatRequest.validate_messages"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.ChatRequest.validate_messages" title="Link to this definition"></a></dt>
<dd><p>Validate chat messages.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>v</strong> – List of messages to validate</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Validated messages</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – If messages are invalid</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.ChatChoice">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">ChatChoice</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#models.ChatMessage" title="models.ChatMessage"><span class="pre">ChatMessage</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">finish_reason</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#ChatChoice"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.ChatChoice" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Represents a single chat completion choice.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatChoice.index">
<span class="sig-name descname"><span class="pre">index</span></span><a class="headerlink" href="#models.ChatChoice.index" title="Link to this definition"></a></dt>
<dd><p>Choice index</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatChoice.message">
<span class="sig-name descname"><span class="pre">message</span></span><a class="headerlink" href="#models.ChatChoice.message" title="Link to this definition"></a></dt>
<dd><p>The generated message</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="../modules.html#models.ChatMessage" title="models.ChatMessage">models.ChatMessage</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatChoice.finish_reason">
<span class="sig-name descname"><span class="pre">finish_reason</span></span><a class="headerlink" href="#models.ChatChoice.finish_reason" title="Link to this definition"></a></dt>
<dd><p>Why generation stopped</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatChoice.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.ChatChoice.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id9">
<span class="sig-name descname"><span class="pre">index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></em><a class="headerlink" href="#id9" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id10">
<span class="sig-name descname"><span class="pre">message</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="../modules.html#models.ChatMessage" title="models.ChatMessage"><span class="pre">ChatMessage</span></a></em><a class="headerlink" href="#id10" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id11">
<span class="sig-name descname"><span class="pre">finish_reason</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#id11" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.TokenUsage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">TokenUsage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prompt_tokens</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">completion_tokens</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">total_tokens</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#TokenUsage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.TokenUsage" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Token usage statistics.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.TokenUsage.prompt_tokens">
<span class="sig-name descname"><span class="pre">prompt_tokens</span></span><a class="headerlink" href="#models.TokenUsage.prompt_tokens" title="Link to this definition"></a></dt>
<dd><p>Tokens in the prompt</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.TokenUsage.completion_tokens">
<span class="sig-name descname"><span class="pre">completion_tokens</span></span><a class="headerlink" href="#models.TokenUsage.completion_tokens" title="Link to this definition"></a></dt>
<dd><p>Tokens in the completion</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.TokenUsage.total_tokens">
<span class="sig-name descname"><span class="pre">total_tokens</span></span><a class="headerlink" href="#models.TokenUsage.total_tokens" title="Link to this definition"></a></dt>
<dd><p>Total tokens used</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.TokenUsage.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.TokenUsage.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id12">
<span class="sig-name descname"><span class="pre">prompt_tokens</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></em><a class="headerlink" href="#id12" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id13">
<span class="sig-name descname"><span class="pre">completion_tokens</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></em><a class="headerlink" href="#id13" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id14">
<span class="sig-name descname"><span class="pre">total_tokens</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></em><a class="headerlink" href="#id14" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="models.TokenUsage.validate_total_tokens">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">validate_total_tokens</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">v</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">info</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></span><a class="reference internal" href="../_modules/models.html#TokenUsage.validate_total_tokens"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.TokenUsage.validate_total_tokens" title="Link to this definition"></a></dt>
<dd><p>Validate that total tokens equals sum of prompt and completion tokens.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>v</strong> – Total tokens value</p></li>
<li><p><strong>info</strong> – Validation info containing other field values</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Validated total tokens</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – If total doesn’t match sum</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.ChatResponse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">ChatResponse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'chat.completion'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">created</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">choices</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.ChatChoice" title="models.ChatChoice"><span class="pre">ChatChoice</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usage</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#models.TokenUsage" title="models.TokenUsage"><span class="pre">TokenUsage</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#ChatResponse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.ChatResponse" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Response model for chat completions.</p>
<p>This model represents the complete response from a chat completion request,
including all choices, usage statistics, and metadata.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatResponse.id">
<span class="sig-name descname"><span class="pre">id</span></span><a class="headerlink" href="#models.ChatResponse.id" title="Link to this definition"></a></dt>
<dd><p>Unique identifier for the completion</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatResponse.object">
<span class="sig-name descname"><span class="pre">object</span></span><a class="headerlink" href="#models.ChatResponse.object" title="Link to this definition"></a></dt>
<dd><p>Object type (always “chat.completion”)</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatResponse.created">
<span class="sig-name descname"><span class="pre">created</span></span><a class="headerlink" href="#models.ChatResponse.created" title="Link to this definition"></a></dt>
<dd><p>Unix timestamp of creation</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)">int</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatResponse.model">
<span class="sig-name descname"><span class="pre">model</span></span><a class="headerlink" href="#models.ChatResponse.model" title="Link to this definition"></a></dt>
<dd><p>Model used for generation</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatResponse.choices">
<span class="sig-name descname"><span class="pre">choices</span></span><a class="headerlink" href="#models.ChatResponse.choices" title="Link to this definition"></a></dt>
<dd><p>List of completion choices</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List[<a class="reference internal" href="../modules.html#models.ChatChoice" title="models.ChatChoice">models.ChatChoice</a>]</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatResponse.usage">
<span class="sig-name descname"><span class="pre">usage</span></span><a class="headerlink" href="#models.ChatResponse.usage" title="Link to this definition"></a></dt>
<dd><p>Token usage statistics</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="../modules.html#models.TokenUsage" title="models.TokenUsage">models.TokenUsage</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.ChatResponse.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'json_schema_extra':</span> <span class="pre">{'example':</span> <span class="pre">{'choices':</span> <span class="pre">[{'finish_reason':</span> <span class="pre">'stop',</span> <span class="pre">'index':</span> <span class="pre">0,</span> <span class="pre">'message':</span> <span class="pre">{'content':</span> <span class="pre">'Hello!</span> <span class="pre">How</span> <span class="pre">can</span> <span class="pre">I</span> <span class="pre">help</span> <span class="pre">you</span> <span class="pre">today?',</span> <span class="pre">'role':</span> <span class="pre">'assistant'}}],</span> <span class="pre">'created':</span> <span class="pre">1677652288,</span> <span class="pre">'id':</span> <span class="pre">'chatcmpl-123',</span> <span class="pre">'model':</span> <span class="pre">'local',</span> <span class="pre">'object':</span> <span class="pre">'chat.completion',</span> <span class="pre">'usage':</span> <span class="pre">{'completion_tokens':</span> <span class="pre">12,</span> <span class="pre">'prompt_tokens':</span> <span class="pre">9,</span> <span class="pre">'total_tokens':</span> <span class="pre">21}}},</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.ChatResponse.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id15">
<span class="sig-name descname"><span class="pre">id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#id15" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id16">
<span class="sig-name descname"><span class="pre">object</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#id16" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id17">
<span class="sig-name descname"><span class="pre">created</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></em><a class="headerlink" href="#id17" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id18">
<span class="sig-name descname"><span class="pre">model</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#id18" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id19">
<span class="sig-name descname"><span class="pre">choices</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.ChatChoice" title="models.ChatChoice"><span class="pre">ChatChoice</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#id19" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id20">
<span class="sig-name descname"><span class="pre">usage</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="../modules.html#models.TokenUsage" title="models.TokenUsage"><span class="pre">TokenUsage</span></a></em><a class="headerlink" href="#id20" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.DocumentAddRequest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">DocumentAddRequest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MaxLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">max_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">100000</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">metadata</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">document_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MaxLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">max_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">100</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#DocumentAddRequest"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.DocumentAddRequest" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Request model for adding documents to the retrieval system.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddRequest.text">
<span class="sig-name descname"><span class="pre">text</span></span><a class="headerlink" href="#models.DocumentAddRequest.text" title="Link to this definition"></a></dt>
<dd><p>Document content</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddRequest.metadata">
<span class="sig-name descname"><span class="pre">metadata</span></span><a class="headerlink" href="#models.DocumentAddRequest.metadata" title="Link to this definition"></a></dt>
<dd><p>Optional document metadata</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a> | None</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddRequest.document_id">
<span class="sig-name descname"><span class="pre">document_id</span></span><a class="headerlink" href="#models.DocumentAddRequest.document_id" title="Link to this definition"></a></dt>
<dd><p>Optional document identifier</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a> | None</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddRequest.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'json_schema_extra':</span> <span class="pre">{'example':</span> <span class="pre">{'document_id':</span> <span class="pre">'doc_123',</span> <span class="pre">'metadata':</span> <span class="pre">{'author':</span> <span class="pre">'Dr.</span> <span class="pre">Smith',</span> <span class="pre">'source':</span> <span class="pre">'research_paper'},</span> <span class="pre">'text':</span> <span class="pre">'This</span> <span class="pre">is</span> <span class="pre">a</span> <span class="pre">sample</span> <span class="pre">document</span> <span class="pre">about</span> <span class="pre">neural</span> <span class="pre">networks.'}},</span> <span class="pre">'str_strip_whitespace':</span> <span class="pre">True,</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.DocumentAddRequest.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id21">
<span class="sig-name descname"><span class="pre">text</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#id21" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id22">
<span class="sig-name descname"><span class="pre">metadata</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#id22" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id23">
<span class="sig-name descname"><span class="pre">document_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#id23" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="models.DocumentAddRequest.validate_text">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">validate_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">v</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/models.html#DocumentAddRequest.validate_text"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.DocumentAddRequest.validate_text" title="Link to this definition"></a></dt>
<dd><p>Validate document text.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>v</strong> – The text to validate</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Validated text</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – If text is invalid</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.DocumentAddResponse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">DocumentAddResponse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">success</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">document_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">metadata</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#DocumentAddResponse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.DocumentAddResponse" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Response model for document addition.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddResponse.success">
<span class="sig-name descname"><span class="pre">success</span></span><a class="headerlink" href="#models.DocumentAddResponse.success" title="Link to this definition"></a></dt>
<dd><p>Whether the operation was successful</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddResponse.message">
<span class="sig-name descname"><span class="pre">message</span></span><a class="headerlink" href="#models.DocumentAddResponse.message" title="Link to this definition"></a></dt>
<dd><p>Human-readable status message</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddResponse.document_id">
<span class="sig-name descname"><span class="pre">document_id</span></span><a class="headerlink" href="#models.DocumentAddResponse.document_id" title="Link to this definition"></a></dt>
<dd><p>ID of the added document</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a> | None</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddResponse.metadata">
<span class="sig-name descname"><span class="pre">metadata</span></span><a class="headerlink" href="#models.DocumentAddResponse.metadata" title="Link to this definition"></a></dt>
<dd><p>Additional response metadata</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a> | None</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.DocumentAddResponse.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.DocumentAddResponse.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id24">
<span class="sig-name descname"><span class="pre">success</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#id24" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id25">
<span class="sig-name descname"><span class="pre">message</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#id25" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id26">
<span class="sig-name descname"><span class="pre">document_id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#id26" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id27">
<span class="sig-name descname"><span class="pre">metadata</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#id27" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.SystemInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">SystemInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gpu_available</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">gpu_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gpu_optimized</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">reasoner_info</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">retriever_info</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#SystemInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.SystemInfo" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>System information model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.SystemInfo.gpu_available">
<span class="sig-name descname"><span class="pre">gpu_available</span></span><a class="headerlink" href="#models.SystemInfo.gpu_available" title="Link to this definition"></a></dt>
<dd><p>Whether GPU is available</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.SystemInfo.gpu_name">
<span class="sig-name descname"><span class="pre">gpu_name</span></span><a class="headerlink" href="#models.SystemInfo.gpu_name" title="Link to this definition"></a></dt>
<dd><p>Name of the GPU</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a> | None</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.SystemInfo.gpu_optimized">
<span class="sig-name descname"><span class="pre">gpu_optimized</span></span><a class="headerlink" href="#models.SystemInfo.gpu_optimized" title="Link to this definition"></a></dt>
<dd><p>Whether GPU optimizations are enabled</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.SystemInfo.reasoner_info">
<span class="sig-name descname"><span class="pre">reasoner_info</span></span><a class="headerlink" href="#models.SystemInfo.reasoner_info" title="Link to this definition"></a></dt>
<dd><p>Information about the reasoning system</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.SystemInfo.retriever_info">
<span class="sig-name descname"><span class="pre">retriever_info</span></span><a class="headerlink" href="#models.SystemInfo.retriever_info" title="Link to this definition"></a></dt>
<dd><p>Information about the retrieval system</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.SystemInfo.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.SystemInfo.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id28">
<span class="sig-name descname"><span class="pre">gpu_available</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#id28" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id29">
<span class="sig-name descname"><span class="pre">gpu_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#id29" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id30">
<span class="sig-name descname"><span class="pre">gpu_optimized</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#id30" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id31">
<span class="sig-name descname"><span class="pre">reasoner_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id31" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id32">
<span class="sig-name descname"><span class="pre">retriever_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id32" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.PerformanceMetrics">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">PerformanceMetrics</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cache</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">system</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">requests</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#PerformanceMetrics"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.PerformanceMetrics" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Performance metrics model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.PerformanceMetrics.cache">
<span class="sig-name descname"><span class="pre">cache</span></span><a class="headerlink" href="#models.PerformanceMetrics.cache" title="Link to this definition"></a></dt>
<dd><p>Cache performance metrics</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.PerformanceMetrics.system">
<span class="sig-name descname"><span class="pre">system</span></span><a class="headerlink" href="#models.PerformanceMetrics.system" title="Link to this definition"></a></dt>
<dd><p>System performance metrics</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.PerformanceMetrics.requests">
<span class="sig-name descname"><span class="pre">requests</span></span><a class="headerlink" href="#models.PerformanceMetrics.requests" title="Link to this definition"></a></dt>
<dd><p>Request performance metrics</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.PerformanceMetrics.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.PerformanceMetrics.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id33">
<span class="sig-name descname"><span class="pre">cache</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id33" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id34">
<span class="sig-name descname"><span class="pre">system</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id34" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id35">
<span class="sig-name descname"><span class="pre">requests</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id35" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.OpenAIMessage">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">OpenAIMessage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">role</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">content</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MaxLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">max_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">10000</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#OpenAIMessage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.OpenAIMessage" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>OpenAI-compatible message model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIMessage.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'str_strip_whitespace':</span> <span class="pre">True,</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.OpenAIMessage.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIMessage.role">
<span class="sig-name descname"><span class="pre">role</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#models.OpenAIMessage.role" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIMessage.content">
<span class="sig-name descname"><span class="pre">content</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#models.OpenAIMessage.content" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.OpenAIChatRequest">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">OpenAIChatRequest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">messages</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.OpenAIMessage" title="models.OpenAIMessage"><span class="pre">OpenAIMessage</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">temperature</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="pre">0.0</span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Le</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">le</span></span><span class="o"><span class="pre">=</span></span><span class="pre">2.0</span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">0.7</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_tokens</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Le</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">le</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">4096</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stream</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#OpenAIChatRequest"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.OpenAIChatRequest" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>OpenAI-compatible chat request model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatRequest.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'str_strip_whitespace':</span> <span class="pre">True,</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.OpenAIChatRequest.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatRequest.model">
<span class="sig-name descname"><span class="pre">model</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#models.OpenAIChatRequest.model" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatRequest.messages">
<span class="sig-name descname"><span class="pre">messages</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.OpenAIMessage" title="models.OpenAIMessage"><span class="pre">OpenAIMessage</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#models.OpenAIChatRequest.messages" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatRequest.temperature">
<span class="sig-name descname"><span class="pre">temperature</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a></em><a class="headerlink" href="#models.OpenAIChatRequest.temperature" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatRequest.max_tokens">
<span class="sig-name descname"><span class="pre">max_tokens</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#models.OpenAIChatRequest.max_tokens" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatRequest.stream">
<span class="sig-name descname"><span class="pre">stream</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#models.OpenAIChatRequest.stream" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.OpenAIChatChoice">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">OpenAIChatChoice</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">index</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="../modules.html#models.OpenAIMessage" title="models.OpenAIMessage"><span class="pre">OpenAIMessage</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">finish_reason</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#OpenAIChatChoice"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.OpenAIChatChoice" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>OpenAI-compatible chat choice model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatChoice.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.OpenAIChatChoice.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatChoice.index">
<span class="sig-name descname"><span class="pre">index</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></em><a class="headerlink" href="#models.OpenAIChatChoice.index" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatChoice.message">
<span class="sig-name descname"><span class="pre">message</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="../modules.html#models.OpenAIMessage" title="models.OpenAIMessage"><span class="pre">OpenAIMessage</span></a></em><a class="headerlink" href="#models.OpenAIChatChoice.message" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatChoice.finish_reason">
<span class="sig-name descname"><span class="pre">finish_reason</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#models.OpenAIChatChoice.finish_reason" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="models.OpenAIChatResponse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">OpenAIChatResponse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">object</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'chat.completion'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">created</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Ge</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">ge</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">0</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">choices</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Annotated" title="(in Python v3.13)"><span class="pre">Annotated</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.OpenAIChatChoice" title="models.OpenAIChatChoice"><span class="pre">OpenAIChatChoice</span></a><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">MinLen</span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">min_length</span></span><span class="o"><span class="pre">=</span></span><span class="m"><span class="pre">1</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">usage</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#OpenAIChatResponse"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#models.OpenAIChatResponse" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>OpenAI-compatible chat response model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatResponse.model_config">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#models.OpenAIChatResponse.model_config" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatResponse.id">
<span class="sig-name descname"><span class="pre">id</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#models.OpenAIChatResponse.id" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatResponse.object">
<span class="sig-name descname"><span class="pre">object</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#models.OpenAIChatResponse.object" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatResponse.created">
<span class="sig-name descname"><span class="pre">created</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></em><a class="headerlink" href="#models.OpenAIChatResponse.created" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatResponse.model">
<span class="sig-name descname"><span class="pre">model</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></em><a class="headerlink" href="#models.OpenAIChatResponse.model" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatResponse.choices">
<span class="sig-name descname"><span class="pre">choices</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference internal" href="../modules.html#models.OpenAIChatChoice" title="models.OpenAIChatChoice"><span class="pre">OpenAIChatChoice</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#models.OpenAIChatResponse.choices" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="models.OpenAIChatResponse.usage">
<span class="sig-name descname"><span class="pre">usage</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#models.OpenAIChatResponse.usage" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The models module defines Pydantic data models for request/response validation,
configuration management, and data serialization throughout the application.</p>
<p>Key Features:
* Pydantic v2 models with comprehensive validation
* OpenAI-compatible request/response formats
* Type safety with Python type hints
* Automatic JSON serialization/deserialization
* Field validation with custom validators</p>
</section>
<section id="request-models">
<h2>Request Models<a class="headerlink" href="#request-models" title="Link to this heading"></a></h2>
<section id="chatcompletionrequest">
<h3>ChatCompletionRequest<a class="headerlink" href="#chatcompletionrequest" title="Link to this heading"></a></h3>
<p>Main request model for chat completions:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">ChatCompletionRequest</span><span class="p">,</span> <span class="n">Message</span>

<span class="c1"># Create a chat completion request</span>
<span class="n">request</span> <span class="o">=</span> <span class="n">ChatCompletionRequest</span><span class="p">(</span>
    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
    <span class="n">messages</span><span class="o">=</span><span class="p">[</span>
        <span class="n">Message</span><span class="p">(</span><span class="n">role</span><span class="o">=</span><span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="n">content</span><span class="o">=</span><span class="s2">&quot;What is symbolic reasoning?&quot;</span><span class="p">)</span>
    <span class="p">],</span>
    <span class="n">max_tokens</span><span class="o">=</span><span class="mi">150</span><span class="p">,</span>
    <span class="n">temperature</span><span class="o">=</span><span class="mf">0.7</span><span class="p">,</span>
    <span class="n">stream</span><span class="o">=</span><span class="kc">False</span>
<span class="p">)</span>
</pre></div>
</div>
<p>Fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">model</span></code>: Model identifier (required)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">messages</span></code>: List of conversation messages (required)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">max_tokens</span></code>: Maximum tokens in response (optional)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">temperature</span></code>: Response randomness 0.0-1.0 (optional)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">top_p</span></code>: Nucleus sampling parameter (optional)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">stream</span></code>: Enable streaming response (optional)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">stop</span></code>: Stop sequences (optional)</p></li>
</ul>
</section>
<section id="message">
<h3>Message<a class="headerlink" href="#message" title="Link to this heading"></a></h3>
<p>Individual message in a conversation:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">Message</span>

<span class="c1"># User message</span>
<span class="n">user_msg</span> <span class="o">=</span> <span class="n">Message</span><span class="p">(</span><span class="n">role</span><span class="o">=</span><span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="n">content</span><span class="o">=</span><span class="s2">&quot;Hello, AI!&quot;</span><span class="p">)</span>

<span class="c1"># Assistant message</span>
<span class="n">assistant_msg</span> <span class="o">=</span> <span class="n">Message</span><span class="p">(</span><span class="n">role</span><span class="o">=</span><span class="s2">&quot;assistant&quot;</span><span class="p">,</span> <span class="n">content</span><span class="o">=</span><span class="s2">&quot;Hello! How can I help?&quot;</span><span class="p">)</span>

<span class="c1"># System message</span>
<span class="n">system_msg</span> <span class="o">=</span> <span class="n">Message</span><span class="p">(</span><span class="n">role</span><span class="o">=</span><span class="s2">&quot;system&quot;</span><span class="p">,</span> <span class="n">content</span><span class="o">=</span><span class="s2">&quot;You are a helpful assistant.&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">role</span></code>: Message role (“user”, “assistant”, “system”)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">content</span></code>: Message content text</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">name</span></code>: Optional message author name</p></li>
</ul>
</section>
</section>
<section id="response-models">
<h2>Response Models<a class="headerlink" href="#response-models" title="Link to this heading"></a></h2>
<section id="chatcompletionresponse">
<h3>ChatCompletionResponse<a class="headerlink" href="#chatcompletionresponse" title="Link to this heading"></a></h3>
<p>Response model for chat completions:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">ChatCompletionResponse</span><span class="p">,</span> <span class="n">Choice</span><span class="p">,</span> <span class="n">Usage</span>

<span class="c1"># Create response</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">ChatCompletionResponse</span><span class="p">(</span>
    <span class="nb">id</span><span class="o">=</span><span class="s2">&quot;chatcmpl-123&quot;</span><span class="p">,</span>
    <span class="nb">object</span><span class="o">=</span><span class="s2">&quot;chat.completion&quot;</span><span class="p">,</span>
    <span class="n">created</span><span class="o">=</span><span class="mi">1677652288</span><span class="p">,</span>
    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
    <span class="n">choices</span><span class="o">=</span><span class="p">[</span>
        <span class="n">Choice</span><span class="p">(</span>
            <span class="n">index</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
            <span class="n">message</span><span class="o">=</span><span class="n">Message</span><span class="p">(</span><span class="n">role</span><span class="o">=</span><span class="s2">&quot;assistant&quot;</span><span class="p">,</span> <span class="n">content</span><span class="o">=</span><span class="s2">&quot;Response text&quot;</span><span class="p">),</span>
            <span class="n">finish_reason</span><span class="o">=</span><span class="s2">&quot;stop&quot;</span>
        <span class="p">)</span>
    <span class="p">],</span>
    <span class="n">usage</span><span class="o">=</span><span class="n">Usage</span><span class="p">(</span>
        <span class="n">prompt_tokens</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span>
        <span class="n">completion_tokens</span><span class="o">=</span><span class="mi">20</span><span class="p">,</span>
        <span class="n">total_tokens</span><span class="o">=</span><span class="mi">30</span>
    <span class="p">)</span>
<span class="p">)</span>
</pre></div>
</div>
<p>Fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">id</span></code>: Unique response identifier</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">object</span></code>: Object type (“chat.completion”)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">created</span></code>: Unix timestamp</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">model</span></code>: Model used for generation</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">choices</span></code>: List of response choices</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">usage</span></code>: Token usage information</p></li>
</ul>
</section>
<section id="choice">
<h3>Choice<a class="headerlink" href="#choice" title="Link to this heading"></a></h3>
<p>Individual choice in a response:</p>
<p>Fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">index</span></code>: Choice index in the list</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code>: Response message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">finish_reason</span></code>: Reason for completion (“stop”, “length”, “content_filter”)</p></li>
</ul>
</section>
<section id="usage">
<h3>Usage<a class="headerlink" href="#usage" title="Link to this heading"></a></h3>
<p>Token usage information:</p>
<p>Fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">prompt_tokens</span></code>: Tokens in the prompt</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">completion_tokens</span></code>: Tokens in the completion</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">total_tokens</span></code>: Total tokens used</p></li>
</ul>
</section>
</section>
<section id="streaming-models">
<h2>Streaming Models<a class="headerlink" href="#streaming-models" title="Link to this heading"></a></h2>
<section id="chatcompletionchunk">
<h3>ChatCompletionChunk<a class="headerlink" href="#chatcompletionchunk" title="Link to this heading"></a></h3>
<p>Streaming response chunk:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">ChatCompletionChunk</span><span class="p">,</span> <span class="n">ChoiceDelta</span>

<span class="c1"># Create streaming chunk</span>
<span class="n">chunk</span> <span class="o">=</span> <span class="n">ChatCompletionChunk</span><span class="p">(</span>
    <span class="nb">id</span><span class="o">=</span><span class="s2">&quot;chatcmpl-123&quot;</span><span class="p">,</span>
    <span class="nb">object</span><span class="o">=</span><span class="s2">&quot;chat.completion.chunk&quot;</span><span class="p">,</span>
    <span class="n">created</span><span class="o">=</span><span class="mi">1677652288</span><span class="p">,</span>
    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
    <span class="n">choices</span><span class="o">=</span><span class="p">[</span>
        <span class="n">ChoiceDelta</span><span class="p">(</span>
            <span class="n">index</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
            <span class="n">delta</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;Hello&quot;</span><span class="p">},</span>
            <span class="n">finish_reason</span><span class="o">=</span><span class="kc">None</span>
        <span class="p">)</span>
    <span class="p">]</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="choicedelta">
<h3>ChoiceDelta<a class="headerlink" href="#choicedelta" title="Link to this heading"></a></h3>
<p>Delta information for streaming:</p>
<p>Fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">index</span></code>: Choice index</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">delta</span></code>: Incremental content</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">finish_reason</span></code>: Completion reason (when finished)</p></li>
</ul>
</section>
</section>
<section id="error-models">
<h2>Error Models<a class="headerlink" href="#error-models" title="Link to this heading"></a></h2>
<section id="errorresponse">
<h3>ErrorResponse<a class="headerlink" href="#errorresponse" title="Link to this heading"></a></h3>
<p>Structured error response:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">ErrorResponse</span><span class="p">,</span> <span class="n">ErrorDetail</span>

<span class="c1"># Create error response</span>
<span class="n">error</span> <span class="o">=</span> <span class="n">ErrorResponse</span><span class="p">(</span>
    <span class="n">error</span><span class="o">=</span><span class="n">ErrorDetail</span><span class="p">(</span>
        <span class="n">code</span><span class="o">=</span><span class="s2">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span>
        <span class="n">message</span><span class="o">=</span><span class="s2">&quot;Invalid request format&quot;</span><span class="p">,</span>
        <span class="n">details</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;field&quot;</span><span class="p">:</span> <span class="s2">&quot;messages&quot;</span><span class="p">}</span>
    <span class="p">)</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="errordetail">
<h3>ErrorDetail<a class="headerlink" href="#errordetail" title="Link to this heading"></a></h3>
<p>Detailed error information:</p>
<p>Fields:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">code</span></code>: Machine-readable error code</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">message</span></code>: Human-readable error message</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">details</span></code>: Additional error context</p></li>
</ul>
</section>
</section>
<section id="system-models">
<h2>System Models<a class="headerlink" href="#system-models" title="Link to this heading"></a></h2>
<section id="systeminfo">
<h3>SystemInfo<a class="headerlink" href="#systeminfo" title="Link to this heading"></a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="id36">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">SystemInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gpu_available</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">gpu_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gpu_optimized</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">reasoner_info</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">retriever_info</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#SystemInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id36" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>System information model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="id37">
<span class="sig-name descname"><span class="pre">gpu_available</span></span><a class="headerlink" href="#id37" title="Link to this definition"></a></dt>
<dd><p>Whether GPU is available</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id38">
<span class="sig-name descname"><span class="pre">gpu_name</span></span><a class="headerlink" href="#id38" title="Link to this definition"></a></dt>
<dd><p>Name of the GPU</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)">str</a> | None</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id39">
<span class="sig-name descname"><span class="pre">gpu_optimized</span></span><a class="headerlink" href="#id39" title="Link to this definition"></a></dt>
<dd><p>Whether GPU optimizations are enabled</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)">bool</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id40">
<span class="sig-name descname"><span class="pre">reasoner_info</span></span><a class="headerlink" href="#id40" title="Link to this definition"></a></dt>
<dd><p>Information about the reasoning system</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id41">
<span class="sig-name descname"><span class="pre">retriever_info</span></span><a class="headerlink" href="#id41" title="Link to this definition"></a></dt>
<dd><p>Information about the retrieval system</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id42">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#id42" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id43">
<span class="sig-name descname"><span class="pre">gpu_available</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#id43" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id44">
<span class="sig-name descname"><span class="pre">gpu_name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></em><a class="headerlink" href="#id44" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id45">
<span class="sig-name descname"><span class="pre">gpu_optimized</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></em><a class="headerlink" href="#id45" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id46">
<span class="sig-name descname"><span class="pre">reasoner_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id46" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id47">
<span class="sig-name descname"><span class="pre">retriever_info</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id47" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<p>System information response:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">SystemInfo</span>

<span class="n">info</span> <span class="o">=</span> <span class="n">SystemInfo</span><span class="p">(</span>
    <span class="n">reasoner</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;status&quot;</span><span class="p">:</span> <span class="s2">&quot;operational&quot;</span><span class="p">,</span> <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;llama&quot;</span><span class="p">},</span>
    <span class="n">retriever</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;status&quot;</span><span class="p">:</span> <span class="s2">&quot;operational&quot;</span><span class="p">,</span> <span class="s2">&quot;index_size&quot;</span><span class="p">:</span> <span class="mi">1000</span><span class="p">},</span>
    <span class="n">gpu_optimized</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">version</span><span class="o">=</span><span class="s2">&quot;0.1.0&quot;</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="performancemetrics">
<h3>PerformanceMetrics<a class="headerlink" href="#performancemetrics" title="Link to this heading"></a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="id48">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">models.</span></span><span class="sig-name descname"><span class="pre">PerformanceMetrics</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cache</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">system</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">requests</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/models.html#PerformanceMetrics"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id48" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">BaseModel</span></code></p>
<p>Performance metrics model.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="id49">
<span class="sig-name descname"><span class="pre">cache</span></span><a class="headerlink" href="#id49" title="Link to this definition"></a></dt>
<dd><p>Cache performance metrics</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id50">
<span class="sig-name descname"><span class="pre">system</span></span><a class="headerlink" href="#id50" title="Link to this definition"></a></dt>
<dd><p>System performance metrics</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id51">
<span class="sig-name descname"><span class="pre">requests</span></span><a class="headerlink" href="#id51" title="Link to this definition"></a></dt>
<dd><p>Request performance metrics</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)">dict</a></p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id52">
<span class="sig-name descname"><span class="pre">model_config</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">ClassVar</span><span class="p"><span class="pre">[</span></span><span class="pre">ConfigDict</span><span class="p"><span class="pre">]</span></span></em><em class="property"><span class="w"> </span><span class="p"><span class="pre">=</span></span><span class="w"> </span><span class="pre">{'extra':</span> <span class="pre">'forbid',</span> <span class="pre">'validate_assignment':</span> <span class="pre">True}</span></em><a class="headerlink" href="#id52" title="Link to this definition"></a></dt>
<dd><p>Configuration for the model, should be a dictionary conforming to [<cite>ConfigDict</cite>][pydantic.config.ConfigDict].</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id53">
<span class="sig-name descname"><span class="pre">cache</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id53" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id54">
<span class="sig-name descname"><span class="pre">system</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id54" title="Link to this definition"></a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id55">
<span class="sig-name descname"><span class="pre">requests</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#dict" title="(in Python v3.13)"><span class="pre">dict</span></a></em><a class="headerlink" href="#id55" title="Link to this definition"></a></dt>
<dd></dd></dl>

</dd></dl>

<p>Performance monitoring data:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">PerformanceMetrics</span><span class="p">,</span> <span class="n">CacheMetrics</span><span class="p">,</span> <span class="n">SystemMetrics</span>

<span class="n">metrics</span> <span class="o">=</span> <span class="n">PerformanceMetrics</span><span class="p">(</span>
    <span class="n">cache</span><span class="o">=</span><span class="n">CacheMetrics</span><span class="p">(</span>
        <span class="n">size</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
        <span class="n">max_size</span><span class="o">=</span><span class="mi">1000</span><span class="p">,</span>
        <span class="n">hits</span><span class="o">=</span><span class="mi">80</span><span class="p">,</span>
        <span class="n">misses</span><span class="o">=</span><span class="mi">20</span><span class="p">,</span>
        <span class="n">hit_rate</span><span class="o">=</span><span class="mf">0.8</span>
    <span class="p">),</span>
    <span class="n">system</span><span class="o">=</span><span class="n">SystemMetrics</span><span class="p">(</span>
        <span class="n">cpu_percent</span><span class="o">=</span><span class="mf">25.5</span><span class="p">,</span>
        <span class="n">memory_percent</span><span class="o">=</span><span class="mf">60.2</span><span class="p">,</span>
        <span class="n">gpu_available</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">active_requests</span><span class="o">=</span><span class="mi">5</span>
    <span class="p">)</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="validation-features">
<h2>Validation Features<a class="headerlink" href="#validation-features" title="Link to this heading"></a></h2>
<section id="field-validation">
<h3>Field Validation<a class="headerlink" href="#field-validation" title="Link to this heading"></a></h3>
<p>Models include comprehensive field validation:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">ChatCompletionRequest</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pydantic</span><span class="w"> </span><span class="kn">import</span> <span class="n">ValidationError</span>

<span class="k">try</span><span class="p">:</span>
    <span class="c1"># This will raise ValidationError</span>
    <span class="n">request</span> <span class="o">=</span> <span class="n">ChatCompletionRequest</span><span class="p">(</span>
        <span class="n">model</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">,</span>  <span class="c1"># Empty model name</span>
        <span class="n">messages</span><span class="o">=</span><span class="p">[],</span>  <span class="c1"># Empty messages list</span>
        <span class="n">temperature</span><span class="o">=</span><span class="mf">2.0</span>  <span class="c1"># Invalid temperature &gt; 1.0</span>
    <span class="p">)</span>
<span class="k">except</span> <span class="n">ValidationError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">e</span><span class="o">.</span><span class="n">errors</span><span class="p">())</span>
</pre></div>
</div>
</section>
<section id="custom-validators">
<h3>Custom Validators<a class="headerlink" href="#custom-validators" title="Link to this heading"></a></h3>
<p>Models include custom validation logic:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Temperature validation</span>
<span class="nd">@field_validator</span><span class="p">(</span><span class="s1">&#39;temperature&#39;</span><span class="p">)</span>
<span class="nd">@classmethod</span>
<span class="k">def</span><span class="w"> </span><span class="nf">validate_temperature</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">v</span> <span class="o">&lt;</span> <span class="mf">0.0</span> <span class="ow">or</span> <span class="n">v</span> <span class="o">&gt;</span> <span class="mf">1.0</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Temperature must be between 0.0 and 1.0&#39;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">v</span>

<span class="c1"># Messages validation</span>
<span class="nd">@field_validator</span><span class="p">(</span><span class="s1">&#39;messages&#39;</span><span class="p">)</span>
<span class="nd">@classmethod</span>
<span class="k">def</span><span class="w"> </span><span class="nf">validate_messages</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">):</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">v</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Messages list cannot be empty&#39;</span><span class="p">)</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="nb">any</span><span class="p">(</span><span class="n">msg</span><span class="o">.</span><span class="n">role</span> <span class="o">==</span> <span class="s1">&#39;user&#39;</span> <span class="k">for</span> <span class="n">msg</span> <span class="ow">in</span> <span class="n">v</span><span class="p">):</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;At least one user message is required&#39;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">v</span>
</pre></div>
</div>
</section>
</section>
<section id="serialization">
<h2>Serialization<a class="headerlink" href="#serialization" title="Link to this heading"></a></h2>
<section id="json-serialization">
<h3>JSON Serialization<a class="headerlink" href="#json-serialization" title="Link to this heading"></a></h3>
<p>All models support JSON serialization:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="n">ChatCompletionRequest</span><span class="p">,</span> <span class="n">Message</span>

<span class="c1"># Create model</span>
<span class="n">request</span> <span class="o">=</span> <span class="n">ChatCompletionRequest</span><span class="p">(</span>
    <span class="n">model</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
    <span class="n">messages</span><span class="o">=</span><span class="p">[</span><span class="n">Message</span><span class="p">(</span><span class="n">role</span><span class="o">=</span><span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="n">content</span><span class="o">=</span><span class="s2">&quot;Hello&quot;</span><span class="p">)]</span>
<span class="p">)</span>

<span class="c1"># Serialize to JSON</span>
<span class="n">json_data</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">model_dump_json</span><span class="p">()</span>

<span class="c1"># Deserialize from JSON</span>
<span class="n">request_copy</span> <span class="o">=</span> <span class="n">ChatCompletionRequest</span><span class="o">.</span><span class="n">model_validate_json</span><span class="p">(</span><span class="n">json_data</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="dictionary-conversion">
<h3>Dictionary Conversion<a class="headerlink" href="#dictionary-conversion" title="Link to this heading"></a></h3>
<p>Models can be converted to/from dictionaries:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># To dictionary</span>
<span class="n">data</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">model_dump</span><span class="p">()</span>

<span class="c1"># From dictionary</span>
<span class="n">request_copy</span> <span class="o">=</span> <span class="n">ChatCompletionRequest</span><span class="o">.</span><span class="n">model_validate</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>

<span class="c1"># Exclude certain fields</span>
<span class="n">public_data</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">model_dump</span><span class="p">(</span><span class="n">exclude</span><span class="o">=</span><span class="p">{</span><span class="s1">&#39;internal_field&#39;</span><span class="p">})</span>
</pre></div>
</div>
</section>
</section>
<section id="example-usage">
<h2>Example Usage<a class="headerlink" href="#example-usage" title="Link to this heading"></a></h2>
<section id="complete-request-response-cycle">
<h3>Complete Request/Response Cycle<a class="headerlink" href="#complete-request-response-cycle" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">models</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">ChatCompletionRequest</span><span class="p">,</span> <span class="n">ChatCompletionResponse</span><span class="p">,</span>
    <span class="n">Message</span><span class="p">,</span> <span class="n">Choice</span><span class="p">,</span> <span class="n">Usage</span>
<span class="p">)</span>

<span class="c1"># Parse incoming request</span>
<span class="n">request_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;local&quot;</span><span class="p">,</span>
    <span class="s2">&quot;messages&quot;</span><span class="p">:</span> <span class="p">[</span>
        <span class="p">{</span><span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;What is AI?&quot;</span><span class="p">}</span>
    <span class="p">],</span>
    <span class="s2">&quot;max_tokens&quot;</span><span class="p">:</span> <span class="mi">100</span><span class="p">,</span>
    <span class="s2">&quot;temperature&quot;</span><span class="p">:</span> <span class="mf">0.7</span>
<span class="p">}</span>

<span class="n">request</span> <span class="o">=</span> <span class="n">ChatCompletionRequest</span><span class="o">.</span><span class="n">model_validate</span><span class="p">(</span><span class="n">request_data</span><span class="p">)</span>

<span class="c1"># Process request (business logic)</span>
<span class="n">response_content</span> <span class="o">=</span> <span class="n">process_chat_request</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>

<span class="c1"># Create response</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">ChatCompletionResponse</span><span class="p">(</span>
    <span class="nb">id</span><span class="o">=</span><span class="s2">&quot;chatcmpl-123&quot;</span><span class="p">,</span>
    <span class="nb">object</span><span class="o">=</span><span class="s2">&quot;chat.completion&quot;</span><span class="p">,</span>
    <span class="n">created</span><span class="o">=</span><span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()),</span>
    <span class="n">model</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
    <span class="n">choices</span><span class="o">=</span><span class="p">[</span>
        <span class="n">Choice</span><span class="p">(</span>
            <span class="n">index</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
            <span class="n">message</span><span class="o">=</span><span class="n">Message</span><span class="p">(</span><span class="n">role</span><span class="o">=</span><span class="s2">&quot;assistant&quot;</span><span class="p">,</span> <span class="n">content</span><span class="o">=</span><span class="n">response_content</span><span class="p">),</span>
            <span class="n">finish_reason</span><span class="o">=</span><span class="s2">&quot;stop&quot;</span>
        <span class="p">)</span>
    <span class="p">],</span>
    <span class="n">usage</span><span class="o">=</span><span class="n">Usage</span><span class="p">(</span>
        <span class="n">prompt_tokens</span><span class="o">=</span><span class="nb">len</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">messages</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">content</span><span class="o">.</span><span class="n">split</span><span class="p">()),</span>
        <span class="n">completion_tokens</span><span class="o">=</span><span class="nb">len</span><span class="p">(</span><span class="n">response_content</span><span class="o">.</span><span class="n">split</span><span class="p">()),</span>
        <span class="n">total_tokens</span><span class="o">=</span><span class="nb">len</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">messages</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">content</span><span class="o">.</span><span class="n">split</span><span class="p">())</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">response_content</span><span class="o">.</span><span class="n">split</span><span class="p">())</span>
    <span class="p">)</span>
<span class="p">)</span>

<span class="c1"># Return JSON response</span>
<span class="k">return</span> <span class="n">response</span><span class="o">.</span><span class="n">model_dump_json</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="model-design">
<h3>Model Design<a class="headerlink" href="#model-design" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Use descriptive field names and types</p></li>
<li><p>Include comprehensive docstrings</p></li>
<li><p>Add validation for business rules</p></li>
<li><p>Use optional fields with sensible defaults</p></li>
</ul>
</section>
<section id="validation">
<h3>Validation<a class="headerlink" href="#validation" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Validate inputs at API boundaries</p></li>
<li><p>Use custom validators for complex rules</p></li>
<li><p>Provide clear error messages</p></li>
<li><p>Handle validation errors gracefully</p></li>
</ul>
</section>
<section id="performance">
<h3>Performance<a class="headerlink" href="#performance" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Use model_dump() for serialization</p></li>
<li><p>Cache validated models when appropriate</p></li>
<li><p>Consider using model_validate() for trusted data</p></li>
<li><p>Profile serialization performance for high-throughput scenarios</p></li>
</ul>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="security.html" class="btn btn-neutral float-left" title="Security Module" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>