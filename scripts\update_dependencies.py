#!/usr/bin/env python3
"""Automated dependency update script for Neural Symbolic Language Model.

This script provides automated dependency management including:
- Safe dependency updates with compatibility checking
- Automated testing after updates
- Rollback capability if tests fail
- Security-focused updates prioritization
"""

import subprocess
import sys
import json
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import argparse
import tempfile


class DependencyUpdater:
    """Automated dependency updater with safety checks."""
    
    def __init__(self, project_root: Optional[Path] = None, dry_run: bool = False):
        """Initialize the dependency updater.
        
        Args:
            project_root: Root directory of the project
            dry_run: If True, only show what would be updated
        """
        self.project_root = project_root or Path(__file__).parent.parent
        self.dry_run = dry_run
        self.backup_dir: Optional[Path] = None
        self.update_results = {
            'updated_packages': [],
            'failed_updates': [],
            'security_updates': [],
            'test_results': None
        }
    
    def update_dependencies(self, security_only: bool = False, packages: Optional[List[str]] = None) -> Dict[str, Any]:
        """Update dependencies with safety checks.
        
        Args:
            security_only: Only update packages with security vulnerabilities
            packages: Specific packages to update (if None, update all)
            
        Returns:
            Dictionary containing update results
        """
        print("🔄 Starting dependency update process...")
        
        if self.dry_run:
            print("🔍 DRY RUN MODE - No actual changes will be made")
        
        # Create backup
        self.create_backup()
        
        try:
            # Get outdated packages
            outdated_packages = self.get_outdated_packages()
            
            if not outdated_packages:
                print("✅ All dependencies are up to date")
                return self.update_results
            
            # Filter packages if needed
            packages_to_update = self.filter_packages_to_update(
                outdated_packages, security_only, packages
            )
            
            if not packages_to_update:
                print("✅ No packages need updating based on criteria")
                return self.update_results
            
            print(f"📦 Found {len(packages_to_update)} packages to update")
            
            # Update packages
            self.perform_updates(packages_to_update)
            
            # Run tests if not dry run
            if not self.dry_run:
                self.run_tests()
            
            # Generate summary
            self.generate_summary()
            
        except Exception as e:
            print(f"❌ Error during update process: {e}")
            if not self.dry_run:
                self.rollback()
            raise
        
        return self.update_results
    
    def get_outdated_packages(self) -> List[Dict[str, Any]]:
        """Get list of outdated packages.
        
        Returns:
            List of outdated package information
        """
        print("🔍 Checking for outdated packages...")
        
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'list', '--outdated', '--format=json'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                outdated = json.loads(result.stdout)
                print(f"📦 Found {len(outdated)} outdated packages")
                return outdated
            else:
                print(f"❌ Failed to get outdated packages: {result.stderr}")
                return []
                
        except Exception as e:
            print(f"❌ Error checking outdated packages: {e}")
            return []
    
    def get_security_vulnerabilities(self) -> List[Dict[str, Any]]:
        """Get packages with known security vulnerabilities.
        
        Returns:
            List of vulnerable packages
        """
        print("🔒 Checking for security vulnerabilities...")
        
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'safety', 'check', '--json'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode != 0:
                # Safety returns non-zero when vulnerabilities are found
                try:
                    vulnerabilities = json.loads(result.stdout)
                    print(f"🚨 Found {len(vulnerabilities)} security vulnerabilities")
                    return vulnerabilities
                except json.JSONDecodeError:
                    print("⚠️  Could not parse safety output")
                    return []
            else:
                print("✅ No security vulnerabilities found")
                return []
                
        except FileNotFoundError:
            print("⚠️  Safety not installed, skipping vulnerability check")
            return []
        except Exception as e:
            print(f"❌ Error checking vulnerabilities: {e}")
            return []
    
    def filter_packages_to_update(self, outdated_packages: List[Dict[str, Any]], 
                                 security_only: bool, specific_packages: Optional[List[str]]) -> List[Dict[str, Any]]:
        """Filter packages based on update criteria.
        
        Args:
            outdated_packages: List of outdated packages
            security_only: Only include packages with security issues
            specific_packages: Only include these specific packages
            
        Returns:
            Filtered list of packages to update
        """
        packages_to_update = outdated_packages.copy()
        
        # Filter by specific packages if provided
        if specific_packages:
            packages_to_update = [
                pkg for pkg in packages_to_update 
                if pkg['name'].lower() in [p.lower() for p in specific_packages]
            ]
            print(f"🎯 Filtered to {len(packages_to_update)} specific packages")
        
        # Filter by security vulnerabilities if requested
        if security_only:
            vulnerabilities = self.get_security_vulnerabilities()
            vulnerable_packages = {vuln.get('package', '').lower() for vuln in vulnerabilities}
            
            packages_to_update = [
                pkg for pkg in packages_to_update 
                if pkg['name'].lower() in vulnerable_packages
            ]
            
            # Store security updates for reporting
            self.update_results['security_updates'] = packages_to_update
            print(f"🔒 Filtered to {len(packages_to_update)} packages with security issues")
        
        return packages_to_update
    
    def perform_updates(self, packages_to_update: List[Dict[str, Any]]):
        """Perform the actual package updates.
        
        Args:
            packages_to_update: List of packages to update
        """
        print(f"🔄 Updating {len(packages_to_update)} packages...")
        
        for package in packages_to_update:
            package_name = package['name']
            current_version = package['version']
            latest_version = package['latest_version']
            
            print(f"📦 Updating {package_name}: {current_version} → {latest_version}")
            
            if self.dry_run:
                print(f"   [DRY RUN] Would update {package_name}")
                self.update_results['updated_packages'].append({
                    'name': package_name,
                    'old_version': current_version,
                    'new_version': latest_version,
                    'status': 'dry_run'
                })
                continue
            
            # Perform the update
            success = self.update_single_package(package_name, latest_version)
            
            if success:
                self.update_results['updated_packages'].append({
                    'name': package_name,
                    'old_version': current_version,
                    'new_version': latest_version,
                    'status': 'success'
                })
                print(f"   ✅ Successfully updated {package_name}")
            else:
                self.update_results['failed_updates'].append({
                    'name': package_name,
                    'old_version': current_version,
                    'new_version': latest_version,
                    'status': 'failed'
                })
                print(f"   ❌ Failed to update {package_name}")
    
    def update_single_package(self, package_name: str, version: str) -> bool:
        """Update a single package.
        
        Args:
            package_name: Name of the package to update
            version: Version to update to
            
        Returns:
            True if update was successful
        """
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'install', f'{package_name}=={version}'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"   ❌ Exception updating {package_name}: {e}")
            return False
    
    def run_tests(self) -> bool:
        """Run tests to verify updates didn't break anything.
        
        Returns:
            True if tests passed
        """
        print("🧪 Running tests to verify updates...")
        
        try:
            # Run pytest
            result = subprocess.run(
                [sys.executable, '-m', 'pytest', 'tests/', '-v', '--tb=short'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            test_passed = result.returncode == 0
            
            self.update_results['test_results'] = {
                'passed': test_passed,
                'output': result.stdout,
                'errors': result.stderr
            }
            
            if test_passed:
                print("✅ All tests passed")
            else:
                print("❌ Tests failed - consider rollback")
                print("Test output:")
                print(result.stdout[-500:])  # Last 500 chars
            
            return test_passed
            
        except Exception as e:
            print(f"❌ Error running tests: {e}")
            self.update_results['test_results'] = {
                'passed': False,
                'output': '',
                'errors': str(e)
            }
            return False
    
    def create_backup(self):
        """Create a backup of current environment."""
        if self.dry_run:
            return
        
        print("💾 Creating backup of current environment...")
        
        try:
            # Create temporary backup directory
            self.backup_dir = Path(tempfile.mkdtemp(prefix='dep_backup_'))
            
            # Save current requirements
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'freeze'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                backup_file = self.backup_dir / 'requirements_backup.txt'
                with open(backup_file, 'w') as f:
                    f.write(result.stdout)
                
                print(f"💾 Backup saved to: {backup_file}")
            
        except Exception as e:
            print(f"⚠️  Failed to create backup: {e}")
    
    def rollback(self):
        """Rollback to previous state if backup exists."""
        if not self.backup_dir or not self.backup_dir.exists():
            print("❌ No backup available for rollback")
            return
        
        print("🔄 Rolling back to previous state...")
        
        try:
            backup_file = self.backup_dir / 'requirements_backup.txt'
            
            if backup_file.exists():
                # Reinstall from backup
                result = subprocess.run(
                    [sys.executable, '-m', 'pip', 'install', '-r', str(backup_file)],
                    capture_output=True,
                    text=True,
                    cwd=self.project_root
                )
                
                if result.returncode == 0:
                    print("✅ Successfully rolled back to previous state")
                else:
                    print(f"❌ Rollback failed: {result.stderr}")
            
        except Exception as e:
            print(f"❌ Error during rollback: {e}")
        finally:
            # Clean up backup
            if self.backup_dir and self.backup_dir.exists():
                shutil.rmtree(self.backup_dir)
    
    def generate_summary(self):
        """Generate a summary of the update process."""
        updated_count = len(self.update_results['updated_packages'])
        failed_count = len(self.update_results['failed_updates'])
        security_count = len(self.update_results['security_updates'])
        
        print("\n" + "="*60)
        print("📊 DEPENDENCY UPDATE SUMMARY")
        print("="*60)
        print(f"✅ Successfully updated: {updated_count} packages")
        print(f"❌ Failed updates: {failed_count} packages")
        print(f"🔒 Security updates: {security_count} packages")
        
        if self.update_results['test_results']:
            test_status = "✅ PASSED" if self.update_results['test_results']['passed'] else "❌ FAILED"
            print(f"🧪 Tests: {test_status}")
        
        if updated_count > 0:
            print("\nUpdated packages:")
            for pkg in self.update_results['updated_packages']:
                print(f"  - {pkg['name']}: {pkg['old_version']} → {pkg['new_version']}")
        
        if failed_count > 0:
            print("\nFailed updates:")
            for pkg in self.update_results['failed_updates']:
                print(f"  - {pkg['name']}: {pkg['old_version']} → {pkg['new_version']}")


def main():
    """Main entry point for the dependency updater."""
    parser = argparse.ArgumentParser(description='Automated dependency updater')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be updated without making changes')
    parser.add_argument('--security-only', action='store_true', help='Only update packages with security vulnerabilities')
    parser.add_argument('--packages', nargs='+', help='Specific packages to update')
    parser.add_argument('--project-root', type=Path, help='Project root directory')
    
    args = parser.parse_args()
    
    # Initialize updater
    updater = DependencyUpdater(args.project_root, args.dry_run)
    
    # Run updates
    try:
        results = updater.update_dependencies(
            security_only=args.security_only,
            packages=args.packages
        )
        
        # Exit with appropriate code
        if results['failed_updates']:
            sys.exit(1)
        elif results['test_results'] and not results['test_results']['passed']:
            sys.exit(2)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n⚠️  Update process interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Update process failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
