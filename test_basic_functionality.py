#!/usr/bin/env python3
"""
Basic functionality test for Ollama integration.
Tests core functionality without requiring all dependencies.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_symbolic_reasoning_basic():
    """Test basic symbolic reasoning functionality."""
    try:
        # Mock torch to avoid dependency
        import types
        torch_mock = types.ModuleType('torch')
        torch_mock.cuda = types.ModuleType('cuda')
        torch_mock.cuda.is_available = lambda: False
        sys.modules['torch'] = torch_mock
        
        # Mock exceptions
        class ReasoningError(Exception):
            def __init__(self, message, reasoning_type=None):
                super().__init__(message)
                self.reasoning_type = reasoning_type

        class ConfigurationError(Exception):
            pass
        
        exceptions_mock = types.ModuleType('exceptions')
        exceptions_mock.ReasoningError = ReasoningError
        exceptions_mock.ConfigurationError = ConfigurationError
        sys.modules['exceptions'] = exceptions_mock
        
        from symbolic_reasoning import SymbolicReasoner
        
        # Test local engine
        reasoner = SymbolicReasoner(engine="local")
        assert reasoner.engine == "local"
        
        response = reasoner.process_query("What is symbolic reasoning?")
        assert isinstance(response, str)
        assert len(response) > 0
        print("✅ Local engine works")
        
        # Test Ollama engine initialization (should handle missing ollama gracefully)
        try:
            ollama_reasoner = SymbolicReasoner(engine="ollama")
            print("❌ Ollama engine should have failed without ollama library")
        except Exception as e:
            print("✅ Ollama engine properly handles missing dependency")
        
        return True
        
    except Exception as e:
        print(f"❌ Symbolic reasoning test failed: {e}")
        return False

def test_configuration():
    """Test configuration functionality."""
    try:
        # Mock pydantic
        import types
        
        class Field:
            def __init__(self, default=None, description=None, **kwargs):
                self.default = default
                self.description = description
        
        class BaseSettings:
            def __init__(self):
                pass
        
        pydantic_mock = types.ModuleType('pydantic')
        pydantic_mock.BaseSettings = BaseSettings
        pydantic_mock.Field = Field
        pydantic_mock.validator = lambda *args, **kwargs: lambda f: f
        sys.modules['pydantic'] = pydantic_mock
        
        pydantic_types_mock = types.ModuleType('pydantic.types')
        pydantic_types_mock.PositiveInt = int
        pydantic_types_mock.NonNegativeFloat = float
        sys.modules['pydantic.types'] = pydantic_types_mock
        
        from core.config import ModelSettings
        
        settings = ModelSettings()
        assert hasattr(settings, 'ollama_host')
        assert hasattr(settings, 'ollama_timeout')
        print("✅ Configuration includes Ollama settings")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_retrieval_basic():
    """Test basic retrieval functionality."""
    try:
        # Mock dependencies
        import types

        # Mock numpy
        try:
            import numpy as np
        except ImportError:
            # Create a minimal numpy mock
            class ndarray:
                def __init__(self, data):
                    self.data = data
                def reshape(self, *args):
                    return self

            np_mock = types.ModuleType('numpy')
            np_mock.random = types.ModuleType('random')
            np_mock.random.randn = lambda *args: ndarray([[0.1] * args[-1]] * args[0] if len(args) > 1 else [0.1] * args[0])
            np_mock.array = lambda x, dtype=None: ndarray(x)
            np_mock.ndarray = ndarray
            sys.modules['numpy'] = np_mock
            import numpy as np
        
        # Mock torch
        torch_mock = types.ModuleType('torch')
        torch_mock.cuda = types.ModuleType('cuda')
        torch_mock.cuda.is_available = lambda: False
        sys.modules['torch'] = torch_mock
        
        # Mock exceptions
        class RetrievalError(Exception):
            pass
        class ConfigurationError(Exception):
            pass
        class VectorStoreError(Exception):
            pass
        
        exceptions_mock = types.ModuleType('exceptions')
        exceptions_mock.RetrievalError = RetrievalError
        exceptions_mock.ConfigurationError = ConfigurationError
        exceptions_mock.VectorStoreError = VectorStoreError
        sys.modules['exceptions'] = exceptions_mock
        
        # Mock vector_store
        class TorchVectorStore:
            def __init__(self, dimension=768, use_gpu=False):
                self.dimension = dimension
                self.use_gpu = use_gpu
        
        vector_store_mock = types.ModuleType('vector_store')
        vector_store_mock.TorchVectorStore = TorchVectorStore
        sys.modules['vector_store'] = vector_store_mock
        
        from retrieval import Retriever
        
        # Test basic retriever initialization
        retriever = Retriever(vector_db="faiss", embedding_backend="random")
        assert retriever.embedding_backend == "random"
        print("✅ Retriever initializes with random embeddings")
        
        # Test Ollama embedding backend initialization
        ollama_retriever = Retriever(vector_db="faiss", embedding_backend="ollama")
        # Should fallback to random if ollama not available
        assert ollama_retriever.embedding_backend in ["ollama", "random"]
        print("✅ Retriever handles Ollama backend gracefully")
        
        return True
        
    except Exception as e:
        print(f"❌ Retrieval test failed: {e}")
        return False

def main():
    """Run all basic functionality tests."""
    print("🧪 Testing Ollama Integration - Basic Functionality")
    print("=" * 50)
    
    tests = [
        ("Symbolic Reasoning", test_symbolic_reasoning_basic),
        ("Configuration", test_configuration),
        ("Retrieval", test_retrieval_basic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} test failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic functionality tests passed!")
        print("✅ Ollama integration is working correctly")
        return True
    else:
        print("⚠️  Some tests failed - check implementation")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
