"""
Force setup script to thoroughly remove and reinstall FAISS-GPU.
"""

import os
import subprocess
import sys
import shutil
import site

def run_command(cmd):
    """Run a command and print its output."""
    print(f"Running: {cmd}")
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.STDOUT,  # Combine stdout and stderr
        shell=True,
        text=True,
        encoding='utf-8'
    )
    for line in process.stdout:
        print(line, end='')
    process.wait()
    if process.returncode != 0:
        print(f"Command failed with return code {process.returncode}: {cmd}")
        return False
    return True

def get_package_paths(package_name):
    """Get potential paths for a package."""
    paths = []
    for site_packages_path in site.getsitepackages():
        paths.append(os.path.join(site_packages_path, package_name))
        paths.append(os.path.join(site_packages_path, f"{package_name.replace('-', '_')}.dist-info"))
        paths.append(os.path.join(site_packages_path, f"{package_name.replace('_', '-')}.dist-info"))
    # Add user site-packages
    user_site = site.getusersitepackages()
    paths.append(os.path.join(user_site, package_name))
    paths.append(os.path.join(user_site, f"{package_name.replace('-', '_')}.dist-info"))
    paths.append(os.path.join(user_site, f"{package_name.replace('_', '-')}.dist-info"))
    return paths

def remove_package_manually(package_name):
    """Attempt to manually remove package directories."""
    print(f"Attempting to manually remove {package_name}...")
    paths_to_remove = get_package_paths(package_name)
    removed_something = False
    for path in paths_to_remove:
        if os.path.exists(path):
            print(f"Found {package_name} at: {path}")
            try:
                if os.path.isdir(path):
                    shutil.rmtree(path)
                    print(f"Successfully removed directory: {path}")
                else:
                    os.remove(path)
                    print(f"Successfully removed file: {path}")
                removed_something = True
            except Exception as e:
                print(f"Error removing {path}: {e}")
        else:
            #print(f"Path not found: {path}")
            pass # Be less verbose for non-existent paths
    if not removed_something:
        print(f"No manual removal targets found for {package_name}.")
    return removed_something

def verify_faiss_gpu():
    """Verify FAISS-GPU installation and GPU support."""
    print("Verifying FAISS-GPU installation...")
    try:
        import faiss
        print("Successfully imported faiss.")
        if hasattr(faiss, 'StandardGpuResources'):
            print("faiss.StandardGpuResources found. GPU support is likely available.")
            try:
                print("Attempting to initialize GPU resources...")
                res = faiss.StandardGpuResources()  # Attempt to initialize GPU resources
                index = faiss.IndexFlatL2(128)    # Create a dummy CPU index
                gpu_index = faiss.index_cpu_to_gpu(res, 0, index) # Move to GPU
                print("FAISS-GPU is working correctly with your GPU!")
                return True
            except Exception as e:
                print(f"Error during FAISS GPU test: {e}")
                print("FAISS-GPU might be installed, but there's an issue using it with the GPU.")
                return False
        else:
            print("faiss.StandardGpuResources NOT found. This is likely faiss-cpu or a corrupted install.")
            return False
    except ImportError:
        print("Failed to import faiss. Installation was not successful.")
        return False
    except Exception as e:
        print(f"An unexpected error occurred during FAISS verification: {e}")
        return False

if __name__ == "__main__":
    print("Starting aggressive FAISS-GPU setup...")

    # Step 1: Attempt pip uninstall (it might still clean up some things)
    print("\n--- Step 1: Attempting pip uninstall for faiss-cpu and faiss-gpu ---")
    run_command(f"{sys.executable} -m pip uninstall -y faiss-cpu")
    run_command(f"{sys.executable} -m pip uninstall -y faiss-gpu")

    # Step 2: Manually remove faiss-cpu and faiss-gpu directories
    print("\n--- Step 2: Manually removing any leftover faiss-cpu and faiss-gpu directories ---")
    remove_package_manually("faiss-cpu")
    remove_package_manually("faiss_cpu") # common variation
    remove_package_manually("faiss-gpu")
    remove_package_manually("faiss_gpu") # common variation
    remove_package_manually("faiss") # also try to remove 'faiss' folder itself

    # Step 3: Attempt clean install of faiss-gpu
    print("\n--- Step 3: Attempting clean installation of faiss-gpu ---")
    install_success = run_command(f"{sys.executable} -m pip install --no-cache-dir faiss-gpu")
    
    if not install_success:
        print("Primary pip install command failed. Trying with --upgrade.")
        install_success = run_command(f"{sys.executable} -m pip install --upgrade --no-cache-dir faiss-gpu")

    # Step 4: Verify installation
    print("\n--- Step 4: Verifying FAISS-GPU installation ---")
    if install_success:
        if verify_faiss_gpu():
            print("\nFAISS-GPU setup appears to be successful!")
        else:
            print("\nFAISS-GPU installation or GPU usage verification failed.")
            print("Please check the output above for errors. You might need to consult FAISS documentation for your specific CUDA version.")
    else:
        print("\nFAISS-GPU installation failed. Cannot verify.")

    print("\nSetup script finished.")
