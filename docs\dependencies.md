# Dependency Management Guide

This document provides comprehensive information about dependency management for the Neural Symbolic Language Model project.

## Overview

The project uses a multi-tier dependency management approach:
- **Production dependencies** (`requirements-prod.txt`) - Core runtime dependencies
- **Development dependencies** (`requirements-dev.txt`) - Tools for development and testing
- **Security scanning** - Automated vulnerability detection
- **Update automation** - Safe dependency updates with testing

## Dependency Categories

### Core Framework Dependencies

| Package | Version | Purpose | Rationale |
|---------|---------|---------|-----------|
| `fastapi` | 0.104.1 | Web framework | High-performance async API framework |
| `uvicorn` | 0.24.0 | ASGI server | Production-ready server with async support |
| `pydantic` | 2.5.0 | Data validation | Type-safe configuration and data models |
| `httpx` | 0.25.2 | HTTP client | Async HTTP client for external API calls |

### AI/ML Dependencies

| Package | Version | Purpose | Rationale |
|---------|---------|---------|-----------|
| `ollama` | 0.1.7 | Local LLM interface | Primary interface for gemma3n:e2b model |
| `transformers` | 4.35.2 | ML models | Hugging Face transformers for embeddings |
| `torch` | 2.1.1 | Deep learning | PyTorch for neural network operations |
| `numpy` | 1.24.4 | Numerical computing | Fundamental array operations |
| `scikit-learn` | 1.3.2 | ML utilities | Additional ML algorithms and utilities |

### Vector Database Dependencies

| Package | Version | Purpose | Rationale |
|---------|---------|---------|-----------|
| `faiss-cpu` | 1.7.4 | Vector similarity search | Fast similarity search for embeddings |
| `faiss-gpu` | 1.7.4 | GPU-accelerated search | Optional GPU acceleration |

### Security Dependencies

| Package | Version | Purpose | Rationale |
|---------|---------|---------|-----------|
| `cryptography` | 41.0.7 | Cryptographic operations | Secure encryption and hashing |
| `passlib` | 1.7.4 | Password hashing | Secure password handling |
| `python-jose` | 3.3.0 | JWT tokens | Secure API authentication |

### Development Dependencies

| Package | Version | Purpose | Rationale |
|---------|---------|---------|-----------|
| `pytest` | 7.4.3 | Testing framework | Comprehensive testing capabilities |
| `black` | 23.11.0 | Code formatting | Consistent code style |
| `mypy` | 1.7.1 | Type checking | Static type analysis |
| `bandit` | 1.7.5 | Security scanning | Code security analysis |
| `safety` | 2.3.5 | Vulnerability scanning | Dependency vulnerability detection |

## Installation Instructions

### Production Installation

```bash
# Install production dependencies
pip install -r requirements-prod.txt

# For GPU support, also install:
pip install faiss-gpu
```

### Development Installation

```bash
# Install all dependencies (production + development)
pip install -r requirements-prod.txt
pip install -r requirements-dev.txt

# Or install in development mode
pip install -e ".[dev]"
```

### Docker Installation

```bash
# Build production image
docker build -f Dockerfile.prod -t neural-symbolic-lm:prod .

# Build development image
docker build -f Dockerfile.dev -t neural-symbolic-lm:dev .
```

## Security Management

### Vulnerability Scanning

Run automated security scans:

```bash
# Full security scan
python scripts/security_scan.py

# Save results to file
python scripts/security_scan.py --output security_report.json

# Dependency vulnerabilities only
safety check

# Code security issues only
bandit -r src/
```

### Security Update Process

1. **Automated scanning** - Run daily vulnerability checks
2. **Priority assessment** - Evaluate severity and impact
3. **Testing** - Update in development environment first
4. **Deployment** - Apply updates to production after testing

### Security Policies

- **Critical vulnerabilities** - Update within 24 hours
- **High severity** - Update within 1 week
- **Medium/Low severity** - Update in next maintenance window
- **Zero-day vulnerabilities** - Emergency update process

## Dependency Updates

### Automated Updates

Use the automated update script:

```bash
# Check what would be updated (dry run)
python scripts/update_dependencies.py --dry-run

# Update all outdated packages
python scripts/update_dependencies.py

# Security updates only
python scripts/update_dependencies.py --security-only

# Update specific packages
python scripts/update_dependencies.py --packages fastapi pydantic
```

### Manual Update Process

1. **Check outdated packages**:
   ```bash
   pip list --outdated
   ```

2. **Review changes**:
   - Check changelog for breaking changes
   - Review security advisories
   - Assess compatibility impact

3. **Update in development**:
   ```bash
   pip install package_name==new_version
   ```

4. **Run tests**:
   ```bash
   pytest tests/ -v
   python scripts/security_scan.py
   ```

5. **Update requirements files**:
   ```bash
   pip freeze > requirements-prod.txt
   ```

### Version Pinning Strategy

- **Production** - Pin exact versions for stability
- **Development** - Allow compatible version ranges for flexibility
- **Security** - Immediate updates for critical vulnerabilities
- **Major versions** - Careful evaluation and testing required

## Compatibility Matrix

### Python Versions

| Python Version | Support Status | Notes |
|----------------|----------------|-------|
| 3.10 | ✅ Recommended | Primary development version |
| 3.11 | ✅ Supported | Full compatibility |
| 3.12 | ⚠️ Beta | Limited testing |
| 3.9 | ❌ Deprecated | End of support |

### Operating Systems

| OS | Support Status | Notes |
|----|----------------|-------|
| Ubuntu 20.04+ | ✅ Primary | Main development platform |
| CentOS 8+ | ✅ Supported | Production deployment |
| Windows 10+ | ✅ Supported | Development only |
| macOS 10.15+ | ✅ Supported | Development only |

### Hardware Requirements

| Component | Minimum | Recommended | Notes |
|-----------|---------|-------------|-------|
| CPU | 4 cores | 8+ cores | For concurrent processing |
| RAM | 8GB | 16GB+ | For model loading |
| GPU | None | 8GB VRAM | For acceleration |
| Storage | 10GB | 50GB+ | For models and data |

## Troubleshooting

### Common Issues

**1. FAISS Installation Fails**
```bash
# Solution: Use conda for Windows
conda install -c conda-forge faiss-gpu

# Or use CPU version
pip install faiss-cpu
```

**2. PyTorch CUDA Issues**
```bash
# Check CUDA availability
python -c "import torch; print(torch.cuda.is_available())"

# Install CUDA-specific version
pip install torch --index-url https://download.pytorch.org/whl/cu118
```

**3. Dependency Conflicts**
```bash
# Check for conflicts
pip check

# Resolve with pip-tools
pip-compile requirements.in
```

**4. Memory Issues**
```bash
# Reduce memory usage
export OMP_NUM_THREADS=1
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

### Performance Optimization

**1. Production Optimizations**
```bash
# Install optimized packages
pip install torch --index-url https://download.pytorch.org/whl/cu118
pip install faiss-gpu

# Set environment variables
export OMP_NUM_THREADS=4
export CUDA_VISIBLE_DEVICES=0
```

**2. Development Optimizations**
```bash
# Use development server
uvicorn main:app --reload --workers 1

# Enable debug mode
export APP_DEBUG=true
```

## Maintenance Schedule

### Daily Tasks
- Automated vulnerability scanning
- Dependency update checks
- Security alert monitoring

### Weekly Tasks
- Review and apply security updates
- Update development dependencies
- Performance monitoring review

### Monthly Tasks
- Major version update evaluation
- Dependency audit and cleanup
- License compliance review

### Quarterly Tasks
- Full dependency refresh
- Security policy review
- Performance benchmarking

## Contributing

### Adding New Dependencies

1. **Evaluate necessity** - Is the dependency really needed?
2. **Security review** - Check for known vulnerabilities
3. **License compatibility** - Ensure license compliance
4. **Performance impact** - Assess resource usage
5. **Maintenance status** - Check if actively maintained

### Dependency Guidelines

- **Prefer established packages** with good maintenance records
- **Minimize dependencies** to reduce attack surface
- **Pin versions** in production for stability
- **Document rationale** for each dependency
- **Regular reviews** to remove unused dependencies

### Testing Requirements

All dependency changes must include:
- Unit tests passing
- Integration tests passing
- Security scan passing
- Performance benchmarks within acceptable range
- Documentation updates

## Support

For dependency-related issues:
1. Check this documentation
2. Review GitHub issues
3. Run automated diagnostics
4. Contact the development team

## References

- [Python Package Index (PyPI)](https://pypi.org/)
- [pip documentation](https://pip.pypa.io/)
- [Safety database](https://pyup.io/safety/)
- [Bandit security linter](https://bandit.readthedocs.io/)
- [NIST vulnerability database](https://nvd.nist.gov/)
