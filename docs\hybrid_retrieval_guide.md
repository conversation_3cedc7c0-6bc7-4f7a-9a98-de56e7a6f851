# Hybrid Retrieval System Guide

This comprehensive guide explains how to use the Neural Symbolic Language Model's Hybrid Retrieval system to add and manage various types of content including documents, websites, and GitHub repositories.

## Overview

The Hybrid Retrieval system combines multiple retrieval methods to provide enhanced context for the Neural Symbolic Language Model:

- **Vector Search**: Semantic similarity using embeddings
- **Keyword Search**: Traditional text-based search
- **Graph-based Retrieval**: Relationship-aware document retrieval
- **Hybrid Scoring**: Combines multiple retrieval methods for optimal results

## Supported Content Types

| Content Type | Format | API Endpoint | Features |
|--------------|--------|--------------|----------|
| **Documents** | PDF, TXT, MD, DOCX | `/documents/add` | Text extraction, chunking |
| **Websites** | URLs | `/documents/add-url` | Web scraping, content extraction |
| **GitHub Repos** | Repository URLs | `/documents/add-github` | Code analysis, documentation |
| **Raw Text** | Plain text | `/documents/add` | Direct text ingestion |
| **JSON Data** | Structured data | `/documents/add` | Metadata preservation |

## API Endpoints

### Core Document Management

#### Add Document
```http
POST /documents/add
Content-Type: application/json
Authorization: Bearer your-api-key

{
    "content": "Your document content here...",
    "metadata": {
        "title": "Document Title",
        "category": "documentation",
        "source": "manual_upload",
        "tags": ["ai", "machine-learning"],
        "author": "John Doe",
        "created_at": "2024-06-29T12:00:00Z"
    }
}
```

#### Add Website Content
```http
POST /documents/add-url
Content-Type: application/json
Authorization: Bearer your-api-key

{
    "url": "https://example.com/article",
    "extract_links": true,
    "max_depth": 2,
    "include_images": false,
    "metadata": {
        "category": "web_content",
        "source": "web_scraping"
    }
}
```

#### Add GitHub Repository
```http
POST /documents/add-github
Content-Type: application/json
Authorization: Bearer your-api-key

{
    "repository_url": "https://github.com/user/repo",
    "include_code": true,
    "include_docs": true,
    "include_issues": false,
    "file_extensions": [".py", ".md", ".txt"],
    "exclude_paths": ["node_modules", ".git", "__pycache__"],
    "metadata": {
        "category": "source_code",
        "project_type": "python"
    }
}
```

#### Search Documents
```http
POST /documents/search
Content-Type: application/json
Authorization: Bearer your-api-key

{
    "query": "machine learning algorithms",
    "method": "hybrid",
    "limit": 10,
    "filters": {
        "category": "documentation",
        "tags": ["ai"]
    }
}
```

#### Get Document Count
```http
GET /documents/count
Authorization: Bearer your-api-key
```

#### Delete Document
```http
DELETE /documents/{document_id}
Authorization: Bearer your-api-key
```

## Python Examples

### Basic Document Management

```python
import requests
import json
from pathlib import Path

class HybridRetrievalClient:
    """Client for interacting with the Hybrid Retrieval system."""
    
    def __init__(self, api_key: str, base_url: str = "http://localhost:8080"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def add_document(self, content: str, metadata: dict = None) -> dict:
        """Add a document to the retrieval system."""
        payload = {
            "content": content,
            "metadata": metadata or {}
        }
        
        response = requests.post(
            f"{self.base_url}/documents/add",
            headers=self.headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def add_file(self, file_path: Path, metadata: dict = None) -> dict:
        """Add a file to the retrieval system."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if metadata is None:
            metadata = {}
        
        # Add file-specific metadata
        metadata.update({
            "filename": file_path.name,
            "file_extension": file_path.suffix,
            "file_size": file_path.stat().st_size
        })
        
        return self.add_document(content, metadata)
    
    def add_website(self, url: str, **options) -> dict:
        """Add website content to the retrieval system."""
        payload = {
            "url": url,
            **options
        }
        
        response = requests.post(
            f"{self.base_url}/documents/add-url",
            headers=self.headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def add_github_repo(self, repository_url: str, **options) -> dict:
        """Add GitHub repository to the retrieval system."""
        payload = {
            "repository_url": repository_url,
            **options
        }
        
        response = requests.post(
            f"{self.base_url}/documents/add-github",
            headers=self.headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def search(self, query: str, method: str = "hybrid", limit: int = 10, filters: dict = None) -> dict:
        """Search documents in the retrieval system."""
        payload = {
            "query": query,
            "method": method,
            "limit": limit,
            "filters": filters or {}
        }
        
        response = requests.post(
            f"{self.base_url}/documents/search",
            headers=self.headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def get_document_count(self) -> dict:
        """Get the total number of documents."""
        response = requests.get(
            f"{self.base_url}/documents/count",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# Usage example
client = HybridRetrievalClient("your-api-key")

# Add a simple document
doc_result = client.add_document(
    content="Artificial Intelligence is transforming how we work and live.",
    metadata={
        "title": "AI Overview",
        "category": "technology",
        "tags": ["ai", "overview"]
    }
)
print(f"Document added: {doc_result['document_id']}")
```

### Adding Different Content Types

```python
# Add a local file
file_result = client.add_file(
    Path("research_paper.pdf"),
    metadata={
        "category": "research",
        "subject": "machine_learning",
        "authors": ["Dr. Smith", "Dr. Johnson"]
    }
)

# Add website content
website_result = client.add_website(
    "https://arxiv.org/abs/2301.00001",
    extract_links=True,
    max_depth=1,
    metadata={
        "category": "research_paper",
        "source": "arxiv"
    }
)

# Add GitHub repository
github_result = client.add_github_repo(
    "https://github.com/openai/gpt-3",
    include_code=True,
    include_docs=True,
    file_extensions=[".py", ".md", ".txt"],
    metadata={
        "category": "source_code",
        "project_type": "ai_model"
    }
)

# Search with enhanced context
search_results = client.search(
    "neural network architectures",
    method="hybrid",
    limit=5,
    filters={"category": "research"}
)

for result in search_results["results"]:
    print(f"Score: {result['score']:.3f}")
    print(f"Title: {result['metadata'].get('title', 'Untitled')}")
    print(f"Content: {result['content'][:200]}...")
    print("-" * 50)
```

## Advanced Usage Examples

### Bulk Document Processing

```python
import os
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

def process_directory(client: HybridRetrievalClient, directory: Path, category: str):
    """Process all documents in a directory."""
    
    def process_file(file_path: Path):
        try:
            if file_path.suffix.lower() in ['.txt', '.md', '.py', '.js', '.html']:
                result = client.add_file(
                    file_path,
                    metadata={
                        "category": category,
                        "directory": str(file_path.parent),
                        "processed_at": "2024-06-29T12:00:00Z"
                    }
                )
                return f"✅ Added: {file_path.name}"
            else:
                return f"⏭️ Skipped: {file_path.name} (unsupported format)"
        except Exception as e:
            return f"❌ Error: {file_path.name} - {e}"
    
    # Get all files
    files = [f for f in directory.rglob('*') if f.is_file()]
    
    # Process files in parallel
    with ThreadPoolExecutor(max_workers=5) as executor:
        results = list(executor.map(process_file, files))
    
    for result in results:
        print(result)

# Usage
client = HybridRetrievalClient("your-api-key")
process_directory(client, Path("./documentation"), "docs")
```

### Website Crawling

```python
def crawl_website(client: HybridRetrievalClient, base_url: str, max_pages: int = 50):
    """Crawl a website and add all pages to the retrieval system."""
    
    crawled_urls = set()
    to_crawl = [base_url]
    
    while to_crawl and len(crawled_urls) < max_pages:
        url = to_crawl.pop(0)
        
        if url in crawled_urls:
            continue
        
        try:
            print(f"Crawling: {url}")
            
            result = client.add_website(
                url,
                extract_links=True,
                max_depth=1,
                metadata={
                    "category": "website",
                    "domain": url.split('/')[2],
                    "crawl_session": "2024-06-29"
                }
            )
            
            crawled_urls.add(url)
            
            # Add discovered links to crawl queue
            if 'discovered_links' in result:
                for link in result['discovered_links']:
                    if link.startswith(base_url) and link not in crawled_urls:
                        to_crawl.append(link)
            
            print(f"✅ Added: {result['document_id']}")
            
        except Exception as e:
            print(f"❌ Error crawling {url}: {e}")
    
    print(f"Crawling complete. Processed {len(crawled_urls)} pages.")

# Usage
crawl_website(client, "https://docs.python.org/3/", max_pages=20)
```

### GitHub Organization Processing

```python
def process_github_organization(client: HybridRetrievalClient, org_name: str, repo_filter: str = None):
    """Process all repositories in a GitHub organization."""
    
    import requests
    
    # Get all repositories in the organization
    github_api_url = f"https://api.github.com/orgs/{org_name}/repos"
    response = requests.get(github_api_url)
    repos = response.json()
    
    for repo in repos:
        repo_name = repo['name']
        repo_url = repo['html_url']
        
        # Apply filter if specified
        if repo_filter and repo_filter.lower() not in repo_name.lower():
            continue
        
        try:
            print(f"Processing repository: {repo_name}")
            
            result = client.add_github_repo(
                repo_url,
                include_code=True,
                include_docs=True,
                file_extensions=[".py", ".md", ".txt", ".js", ".ts"],
                metadata={
                    "category": "source_code",
                    "organization": org_name,
                    "repository": repo_name,
                    "language": repo.get('language', 'unknown'),
                    "stars": repo.get('stargazers_count', 0),
                    "description": repo.get('description', '')
                }
            )
            
            print(f"✅ Added repository: {repo_name}")
            
        except Exception as e:
            print(f"❌ Error processing {repo_name}: {e}")

# Usage
process_github_organization("openai", repo_filter="gpt")
```

## Integration with Chat Completions

### Enhanced Context Retrieval

```python
def chat_with_context(client: HybridRetrievalClient, query: str, context_limit: int = 5):
    """Chat with enhanced context from the retrieval system."""
    
    # Search for relevant context
    search_results = client.search(
        query,
        method="hybrid",
        limit=context_limit
    )
    
    # Build context from search results
    context_parts = []
    for result in search_results["results"]:
        title = result['metadata'].get('title', 'Document')
        content = result['content'][:500]  # Limit content length
        context_parts.append(f"**{title}**:\n{content}")
    
    context = "\n\n".join(context_parts)
    
    # Create enhanced prompt
    enhanced_prompt = f"""Based on the following context, please answer the question:

CONTEXT:
{context}

QUESTION: {query}

Please provide a comprehensive answer based on the context provided above."""
    
    # Send to chat completion
    import openai
    openai.api_base = "http://localhost:8080/v1"
    openai.api_key = client.api_key
    
    response = openai.ChatCompletion.create(
        model="gemma3n:e2b",
        messages=[
            {"role": "user", "content": enhanced_prompt}
        ],
        temperature=0.7,
        max_tokens=1000
    )
    
    return {
        "answer": response.choices[0].message.content,
        "context_sources": [r['metadata'].get('title', 'Unknown') for r in search_results["results"]],
        "context_count": len(search_results["results"])
    }

# Usage
result = chat_with_context(client, "How do neural networks learn?")
print("Answer:", result["answer"])
print("Sources:", result["context_sources"])
```

## Configuration and Optimization

### Retrieval System Settings

```python
# Configure retrieval parameters
retrieval_config = {
    "chunk_size": 1000,           # Size of text chunks
    "chunk_overlap": 200,         # Overlap between chunks
    "embedding_model": "mxbai-embed-large",
    "vector_dimensions": 1024,
    "similarity_threshold": 0.7,
    "max_results": 50,
    "enable_reranking": True,
    "rerank_model": "cross-encoder/ms-marco-MiniLM-L-6-v2"
}

# Update configuration
response = requests.post(
    f"{client.base_url}/documents/configure",
    headers=client.headers,
    json=retrieval_config
)
```

### Performance Monitoring

```python
def monitor_retrieval_performance(client: HybridRetrievalClient):
    """Monitor retrieval system performance."""
    
    # Get document statistics
    stats = client.get_document_count()
    print(f"Total documents: {stats['total_documents']}")
    print(f"Indexed documents: {stats['indexed_documents']}")
    
    # Test search performance
    import time
    
    test_queries = [
        "machine learning algorithms",
        "neural network architecture",
        "data preprocessing techniques",
        "model evaluation metrics"
    ]
    
    for query in test_queries:
        start_time = time.time()
        results = client.search(query, limit=10)
        duration = time.time() - start_time
        
        print(f"Query: '{query}'")
        print(f"Results: {len(results['results'])}")
        print(f"Duration: {duration:.3f}s")
        print("-" * 40)

# Usage
monitor_retrieval_performance(client)
```

## Best Practices

### Document Organization

1. **Use Consistent Metadata**: Establish a metadata schema for your organization
2. **Categorize Content**: Use categories to organize different types of content
3. **Tag Appropriately**: Use tags for cross-cutting concerns and topics
4. **Version Control**: Include version information in metadata
5. **Source Tracking**: Always include source information for traceability

### Performance Optimization

1. **Batch Processing**: Process multiple documents in batches when possible
2. **Parallel Processing**: Use threading for I/O-bound operations
3. **Chunking Strategy**: Optimize chunk size based on your content type
4. **Index Management**: Regularly monitor and optimize your vector indices
5. **Cache Results**: Cache frequently accessed search results

### Security Considerations

1. **API Key Management**: Secure your API keys and rotate them regularly
2. **Content Filtering**: Validate and sanitize content before ingestion
3. **Access Control**: Implement proper access controls for sensitive documents
4. **Audit Logging**: Log all document operations for security auditing
5. **Data Privacy**: Ensure compliance with data privacy regulations

## Troubleshooting

### Common Issues

**1. Document Not Found After Adding**
```python
# Check document status
response = requests.get(
    f"{client.base_url}/documents/{document_id}/status",
    headers=client.headers
)
print(response.json())
```

**2. Poor Search Results**
```python
# Try different search methods
methods = ["vector", "keyword", "hybrid"]
for method in methods:
    results = client.search("your query", method=method)
    print(f"{method}: {len(results['results'])} results")
```

**3. GitHub Repository Access Issues**
```python
# Add GitHub token for private repositories
github_result = client.add_github_repo(
    "https://github.com/user/private-repo",
    github_token="your-github-token",
    include_code=True
)
```

For more troubleshooting help, see the [main troubleshooting guide](deployment_guide.md#troubleshooting).
