"""
Hybrid retriever that combines FAISS for fast retrieval with LightRAG for reranking.
This provides a good balance between speed and accuracy.
"""

import logging
from typing import List, Dict, Any, Optional, Union
import numpy as np
import torch
import time
from lightrag import LightRAG
from vector_store import TorchVectorStore
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Union, Tuple

# Configure logger
logger = logging.getLogger(__name__)

@dataclass
class RetrievalResult:
    """Container for retrieval results."""
    id: str
    text: str
    score: float
    metadata: Dict[str, Any] = None

class HybridRetriever:
    """
    A hybrid retriever that combines:
    1. First-stage: Fast vector similarity search (FAISS or TorchVectorStore)
    2. Second-stage: LightRAG for reranking and contextual understanding
    
    This provides better accuracy than either approach alone while maintaining good performance.
    """
    
    def __init__(self, 
                 vector_db: str = "faiss",
                 use_gpu: bool = True,
                 first_stage_k: int = 100,
                 rerank_k: int = 10,
                 model_name: str = "BAAI/bge-small-en-v1.5"):
        """Initialize the hybrid retriever.
        
        Args:
            vector_db: The vector database to use ("faiss" or "torch")
            use_gpu: Whether to use GPU acceleration if available
            first_stage_k: Number of candidates to retrieve in the first stage
            rerank_k: Number of results to return after reranking
            model_name: Name of the embedding model to use
        """
        self.vector_db = vector_db
        self.use_gpu = use_gpu and torch.cuda.is_available()
        self.first_stage_k = first_stage_k
        self.rerank_k = rerank_k
        self.model_name = model_name
        
        # Initialize components
        self._init_vector_store()
        self._init_lightrag()
        
        # Document store
        self.documents = {}
        self.doc_ids = []
        self.embeddings = None
        
    def _init_vector_store(self):
        """Initialize the vector store for first-stage retrieval."""
        if self.vector_db == "torch" or not self._is_faiss_available():
            logger.info("Using TorchVectorStore for first-stage retrieval")
            self.vector_store = TorchVectorStore(use_gpu=self.use_gpu)
        else:
            logger.info("Using FAISS for first-stage retrieval")
            self.vector_store = self._init_faiss()
    
    def _is_faiss_available(self) -> bool:
        """Check if FAISS is available."""
        try:
            import faiss
            return True
        except ImportError:
            logger.warning("FAISS not available. Falling back to TorchVectorStore")
            return False
    
    def _init_faiss(self):
        """Initialize FAISS index."""
        import faiss
        
        # Use GPU if available and requested
        if self.use_gpu and hasattr(faiss, 'StandardGpuResources'):
            res = faiss.StandardGpuResources()
            index = faiss.IndexFlatIP(768)  # Default dimension for BERT-like models
            return faiss.index_cpu_to_gpu(res, 0, index)
        else:
            return faiss.IndexFlatIP(768)
    
    def _init_lightrag(self):
        """Initialize the LightRAG model for reranking."""
        try:
            self.lightrag = LightRAG(
                model_name=self.model_name,
                use_gpu=self.use_gpu
            )
            logger.info(f"Initialized LightRAG with model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize LightRAG: {str(e)}")
            raise
            
    def add_documents(self, documents: List[Dict[str, Any]], embeddings: Optional[np.ndarray] = None):
        """Add documents to the retriever.
        
        Args:
            documents: List of document dictionaries with at least 'id' and 'text' keys
            embeddings: Optional pre-computed embeddings for the documents
        """
        if not documents:
            return
            
        # Store documents
        for doc in documents:
            if 'id' not in doc or 'text' not in doc:
                raise ValueError("Documents must contain 'id' and 'text' fields")
            self.documents[doc['id']] = doc
            if doc['id'] not in self.doc_ids:
                self.doc_ids.append(doc['id'])
        
        # Generate or use provided embeddings
        if embeddings is None:
            texts = [doc['text'] for doc in documents]
            embeddings = self.lightrag.encode(texts, convert_to_numpy=True)
        
        # Store embeddings
        if self.embeddings is None:
            self.embeddings = embeddings
        else:
            self.embeddings = np.vstack([self.embeddings, embeddings])
        
        # Update vector store
        if hasattr(self.vector_store, 'add'):
            # For TorchVectorStore
            self.vector_store.add(
                vectors=embeddings,
                doc_ids=[doc['id'] for doc in documents],
                texts=[doc['text'] for doc in documents]
            )
        else:
            # For FAISS
            if not hasattr(self.vector_store, 'is_trained') or self.vector_store.is_trained:
                self.vector_store.add(embeddings.astype('float32'))
            else:
                # If index needs training, train on a sample
                n_samples = min(10000, len(self.embeddings))
                train_data = self.embeddings[np.random.choice(len(self.embeddings), n_samples, replace=False)]
                self.vector_store.train(train_data.astype('float32'))
                self.vector_store.add(embeddings.astype('float32'))
    
    def search(self, query: str, k: Optional[int] = None, rerank: bool = True) -> List[RetrievalResult]:
        """Perform hybrid search with optional reranking.
        
        Args:
            query: The search query
            k: Number of results to return (overrides rerank_k if provided)
            rerank: Whether to use LightRAG for reranking
            
        Returns:
            List of RetrievalResult objects
        """
        k = k or self.rerank_k
        
        # First stage: Fast vector similarity search
        start_time = time.time()
        first_stage_results = self._first_stage_retrieval(query, self.first_stage_k)
        first_stage_time = time.time() - start_time
        
        if not rerank or len(first_stage_results) == 0:
            return first_stage_results[:k]
        
        # Second stage: Rerank with LightRAG
        start_time = time.time()
        reranked_results = self._rerank_with_lightrag(query, first_stage_results, k)
        rerank_time = time.time() - start_time
        
        logger.debug(f"Retrieval times - First stage: {first_stage_time:.3f}s, Reranking: {rerank_time:.3f}s")
        return reranked_results
    
    def _first_stage_retrieval(self, query: str, k: int) -> List[RetrievalResult]:
        """Perform first-stage retrieval using vector similarity."""
        # Get query embedding
        query_embedding = self.lightrag.encode([query], convert_to_numpy=True)
        
        # Search in vector store
        if hasattr(self.vector_store, 'search'):
            # For TorchVectorStore
            results = self.vector_store.search(query_embedding[0], k=k)
            return [
                RetrievalResult(
                    id=res['id'],
                    text=res['text'],
                    score=float(res['score']),
                    metadata=self.documents.get(res['id'], {})
                )
                for res in results
            ]
        else:
            # For FAISS
            distances, indices = self.vector_store.search(query_embedding.astype('float32'), k)
            results = []
            for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
                if idx < 0 or idx >= len(self.doc_ids):
                    continue
                doc_id = self.doc_ids[idx]
                results.append(RetrievalResult(
                    id=doc_id,
                    text=self.documents[doc_id]['text'],
                    score=float(1.0 / (1.0 + dist)),  # Convert distance to similarity
                    metadata=self.documents[doc_id]
                ))
            return results
    
    def _rerank_with_lightrag(self, 
                            query: str, 
                            candidates: List[RetrievalResult],
                            k: int) -> List[RetrievalResult]:
        """Rerank candidates using LightRAG's cross-encoder."""
        if not candidates:
            return []
            
        # Extract texts and metadata
        texts = [cand.text for cand in candidates]
        
        # Get scores from LightRAG
        scores = self.lightrag.rerank(
            query=query,
            documents=texts,
            top_k=min(len(candidates), k)
        )
        
        # Sort by score (descending)
        scored_candidates = [
            (candidates[i], float(score))
            for i, score in enumerate(scores)
        ]
        scored_candidates.sort(key=lambda x: x[1], reverse=True)
        
        # Update scores and return top-k
        return [
            RetrievalResult(
                id=cand.id,
                text=cand.text,
                score=score,
                metadata=cand.metadata
            )
            for cand, score in scored_candidates[:k]
        ]
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information about the retriever."""
        return {
            "type": "hybrid_retriever",
            "vector_db": self.vector_db,
            "model_name": self.model_name,
            "num_documents": len(self.documents),
            "gpu_enabled": self.use_gpu,
            "gpu_available": torch.cuda.is_available(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
            "first_stage_k": self.first_stage_k,
            "rerank_k": self.rerank_k
        }
