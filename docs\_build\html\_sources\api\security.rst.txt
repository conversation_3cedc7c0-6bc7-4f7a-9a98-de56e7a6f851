Security Module
===============

.. automodule:: security
   :members:
   :undoc-members:
   :show-inheritance:

Overview
--------

The security module provides comprehensive security features including authentication,
authorization, rate limiting, input validation, and CORS configuration.

Key Features:
* API key authentication with Bearer token support
* Rate limiting with configurable limits
* Input sanitization and validation
* IP blocking for failed attempts
* CORS configuration for cross-origin requests
* Security headers for enhanced protection

SecurityManager Class
---------------------

.. autoclass:: security.SecurityManager
   :members:
   :undoc-members:
   :show-inheritance:

The main class for managing security operations.

Initialization
~~~~~~~~~~~~~~

.. automethod:: security.SecurityManager.__init__

Core Methods
~~~~~~~~~~~~

.. automethod:: security.SecurityManager.validate_api_key
.. automethod:: security.SecurityManager.is_ip_blocked
.. automethod:: security.SecurityManager.record_failed_attempt
.. automethod:: security.SecurityManager.sanitize_input

Authentication
--------------

API Key Authentication
~~~~~~~~~~~~~~~~~~~~~~

The system uses API key authentication with <PERSON>er tokens:

.. code-block:: python

   from security import verify_api_key
   from fastapi import Depends
   
   @app.get("/protected")
   async def protected_endpoint(api_key: str = Depends(verify_api_key)):
       return {"message": "Access granted", "api_key": api_key}

Configuration:

* API keys are configured via environment variables
* Keys are validated using constant-time comparison
* Failed attempts are tracked and can trigger IP blocking

Rate Limiting
-------------

RateLimiter Class
~~~~~~~~~~~~~~~~~

.. autoclass:: security.RateLimiter
   :members:
   :undoc-members:
   :show-inheritance:

The rate limiter provides configurable request limiting:

.. code-block:: python

   from security import rate_limiter
   
   # Check if request is allowed
   client_ip = "***********"
   if rate_limiter.is_allowed(client_ip, limit=100, window=60):
       # Process request
       pass
   else:
       # Return rate limit error
       raise HTTPException(status_code=429, detail="Rate limit exceeded")

Configuration:

* ``limit``: Maximum requests per window (default: 100)
* ``window``: Time window in seconds (default: 60)
* ``identifier``: Unique identifier (usually IP address)

Input Validation
----------------

Input Sanitization
~~~~~~~~~~~~~~~~~~

.. autofunction:: security.SecurityManager.sanitize_input

The system provides comprehensive input sanitization:

.. code-block:: python

   from security import security_manager
   
   # Sanitize user input
   try:
       clean_text = security_manager.sanitize_input(
           user_input,
           max_length=10000
       )
   except ValueError as e:
       # Handle validation error
       return {"error": str(e)}

Features:

* Removes null bytes and control characters
* Enforces maximum length limits
* Strips whitespace
* Validates non-empty input after sanitization

Request Size Limits
~~~~~~~~~~~~~~~~~~~

.. autofunction:: security.check_request_size

Prevents large request attacks:

.. code-block:: python

   from security import check_request_size
   from fastapi import Request, Depends
   
   @app.post("/upload")
   async def upload_endpoint(
       request: Request,
       _: None = Depends(check_request_size)
   ):
       # Process upload
       pass

CORS Configuration
------------------

.. autofunction:: security.get_cors_config

Provides secure cross-origin resource sharing:

.. code-block:: python

   from security import get_cors_config
   from fastapi.middleware.cors import CORSMiddleware
   
   # Configure CORS
   cors_config = get_cors_config()
   app.add_middleware(CORSMiddleware, **cors_config)

Features:

* Configurable allowed origins
* Production-safe defaults
* Credential support
* Preflight request caching

Origin Validation
~~~~~~~~~~~~~~~~~

.. autofunction:: security.validate_cors_origin

Validates CORS origins:

.. code-block:: python

   from security import validate_cors_origin
   
   allowed_origins = ["https://example.com", "https://app.example.com"]
   origin = "https://example.com"
   
   if validate_cors_origin(origin, allowed_origins):
       # Allow request
       pass
   else:
       # Reject request
       pass

Security Headers
----------------

.. autofunction:: security.get_security_headers

Provides comprehensive security headers:

.. code-block:: python

   from security import get_security_headers
   from fastapi import Response
   
   @app.get("/")
   async def root(response: Response):
       # Add security headers
       headers = get_security_headers()
       for key, value in headers.items():
           response.headers[key] = value
       
       return {"message": "Hello World"}

Included headers:

* ``X-Content-Type-Options``: Prevents MIME type sniffing
* ``X-Frame-Options``: Prevents clickjacking
* ``X-XSS-Protection``: Enables XSS filtering
* ``Strict-Transport-Security``: Enforces HTTPS
* ``Content-Security-Policy``: Controls resource loading
* ``Referrer-Policy``: Controls referrer information

IP Blocking
-----------

Failed Attempt Tracking
~~~~~~~~~~~~~~~~~~~~~~~~

The system tracks failed authentication attempts:

.. code-block:: python

   from security import security_manager
   
   # Record failed attempt
   client_ip = get_client_ip(request)
   security_manager.record_failed_attempt(client_ip)
   
   # Check if IP is blocked
   if security_manager.is_ip_blocked(client_ip):
       raise HTTPException(
           status_code=403,
           detail="IP address blocked due to repeated failed attempts"
       )

Configuration:

* Maximum failed attempts: 5 (configurable)
* Block duration: 1 hour (configurable)
* Cleanup interval: 15 minutes

Client IP Detection
~~~~~~~~~~~~~~~~~~~

.. autofunction:: security.get_client_ip

Accurately detects client IP addresses:

.. code-block:: python

   from security import get_client_ip
   from fastapi import Request
   
   @app.post("/login")
   async def login(request: Request):
       client_ip = get_client_ip(request)
       # Use IP for rate limiting and blocking
       pass

Supports:

* ``X-Forwarded-For`` header (load balancers)
* ``X-Real-IP`` header (reverse proxies)
* Direct connection IP address

Example Usage
-------------

Complete Security Setup
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from fastapi import FastAPI, Request, Depends, HTTPException
   from fastapi.middleware.cors import CORSMiddleware
   from security import (
       verify_api_key, check_request_size, get_cors_config,
       get_security_headers, security_manager, rate_limiter,
       get_client_ip
   )
   
   app = FastAPI()
   
   # Configure CORS
   cors_config = get_cors_config()
   app.add_middleware(CORSMiddleware, **cors_config)
   
   @app.middleware("http")
   async def security_middleware(request: Request, call_next):
       # Get client IP
       client_ip = get_client_ip(request)
       
       # Check if IP is blocked
       if security_manager.is_ip_blocked(client_ip):
           raise HTTPException(status_code=403, detail="IP blocked")
       
       # Check rate limit
       if not rate_limiter.is_allowed(client_ip):
           raise HTTPException(status_code=429, detail="Rate limit exceeded")
       
       # Process request
       response = await call_next(request)
       
       # Add security headers
       headers = get_security_headers()
       for key, value in headers.items():
           response.headers[key] = value
       
       return response
   
   @app.post("/api/chat")
   async def chat_endpoint(
       request: Request,
       api_key: str = Depends(verify_api_key),
       _: None = Depends(check_request_size)
   ):
       # Secure endpoint implementation
       pass

Error Handling
--------------

The security module provides structured error responses:

.. code-block:: python

   from security import security_manager
   from exceptions import AuthenticationError, RateLimitError
   
   try:
       # Validate API key
       if not security_manager.validate_api_key(api_key):
           raise AuthenticationError("Invalid API key")
       
       # Check rate limit
       if not rate_limiter.is_allowed(client_ip):
           raise RateLimitError("Rate limit exceeded", retry_after=60)
           
   except AuthenticationError as e:
       return JSONResponse(
           status_code=e.status_code,
           content=e.to_dict()
       )

Best Practices
--------------

API Key Management
~~~~~~~~~~~~~~~~~~

* Use strong, randomly generated API keys
* Rotate keys regularly
* Store keys securely (environment variables, secrets manager)
* Never log or expose keys in responses

Rate Limiting
~~~~~~~~~~~~~

* Set appropriate limits based on expected usage
* Use different limits for different endpoints
* Consider burst allowances for legitimate traffic
* Monitor and adjust limits based on usage patterns

Input Validation
~~~~~~~~~~~~~~~~

* Validate all user inputs
* Use whitelist validation when possible
* Sanitize inputs before processing
* Log validation failures for monitoring

CORS Configuration
~~~~~~~~~~~~~~~~~~

* Specify exact allowed origins in production
* Avoid wildcards (*) in production environments
* Regularly review and update allowed origins
* Test CORS configuration thoroughly
