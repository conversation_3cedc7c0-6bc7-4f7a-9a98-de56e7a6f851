

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>API Reference &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=39bd3b11" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=01f34227"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="API Reference" href="modules.html" />
    <link rel="prev" title="Configuration Guide" href="configuration.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="configuration.html">Configuration Guide</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#rest-api-endpoints">REST API Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#base-url">Base URL</a></li>
<li class="toctree-l3"><a class="reference internal" href="#authentication">Authentication</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#chat-completions-api">Chat Completions API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#openai-compatible-endpoint">OpenAI-Compatible Endpoint</a></li>
<li class="toctree-l3"><a class="reference internal" href="#streaming-responses">Streaming Responses</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#system-information-api">System Information API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#system-info">System Info</a></li>
<li class="toctree-l3"><a class="reference internal" href="#health-check">Health Check</a></li>
<li class="toctree-l3"><a class="reference internal" href="#readiness-check">Readiness Check</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#performance-monitoring-api">Performance Monitoring API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#performance-metrics">Performance Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#request-parameters">Request Parameters</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#common-parameters">Common Parameters</a></li>
<li class="toctree-l3"><a class="reference internal" href="#message-format">Message Format</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-responses">Error Responses</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#error-format">Error Format</a></li>
<li class="toctree-l3"><a class="reference internal" href="#error-codes">Error Codes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#rate-limiting">Rate Limiting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#rate-limit-headers">Rate Limit Headers</a></li>
<li class="toctree-l3"><a class="reference internal" href="#default-limits">Default Limits</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#cors-support">CORS Support</a></li>
<li class="toctree-l2"><a class="reference internal" href="#openapi-documentation">OpenAPI Documentation</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">API Reference</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/api_reference.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="api-reference">
<h1>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading"></a></h1>
<p>This section provides comprehensive API documentation for the Neural Symbolic Language Model.</p>
<section id="rest-api-endpoints">
<h2>REST API Endpoints<a class="headerlink" href="#rest-api-endpoints" title="Link to this heading"></a></h2>
<p>The application provides a RESTful API with OpenAI-compatible endpoints.</p>
<section id="base-url">
<h3>Base URL<a class="headerlink" href="#base-url" title="Link to this heading"></a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http://localhost:8000
</pre></div>
</div>
</section>
<section id="authentication">
<h3>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h3>
<p>All API endpoints require authentication using Bearer tokens:</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">Authorization: Bearer your-api-key-here</span>
</pre></div>
</div>
</section>
</section>
<section id="chat-completions-api">
<h2>Chat Completions API<a class="headerlink" href="#chat-completions-api" title="Link to this heading"></a></h2>
<section id="openai-compatible-endpoint">
<h3>OpenAI-Compatible Endpoint<a class="headerlink" href="#openai-compatible-endpoint" title="Link to this heading"></a></h3>
</section>
<section id="streaming-responses">
<h3>Streaming Responses<a class="headerlink" href="#streaming-responses" title="Link to this heading"></a></h3>
<p>Enable streaming by setting <code class="docutils literal notranslate"><span class="pre">&quot;stream&quot;:</span> <span class="pre">true</span></code> in the request:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;model&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;messages&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="err">...</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;stream&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Streaming Response Format:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>data: {&quot;id&quot;:&quot;chatcmpl-123&quot;,&quot;object&quot;:&quot;chat.completion.chunk&quot;,...}
data: {&quot;id&quot;:&quot;chatcmpl-123&quot;,&quot;object&quot;:&quot;chat.completion.chunk&quot;,...}
data: [DONE]
</pre></div>
</div>
</section>
</section>
<section id="system-information-api">
<h2>System Information API<a class="headerlink" href="#system-information-api" title="Link to this heading"></a></h2>
<section id="system-info">
<h3>System Info<a class="headerlink" href="#system-info" title="Link to this heading"></a></h3>
</section>
<section id="health-check">
<h3>Health Check<a class="headerlink" href="#health-check" title="Link to this heading"></a></h3>
</section>
<section id="readiness-check">
<h3>Readiness Check<a class="headerlink" href="#readiness-check" title="Link to this heading"></a></h3>
</section>
</section>
<section id="performance-monitoring-api">
<h2>Performance Monitoring API<a class="headerlink" href="#performance-monitoring-api" title="Link to this heading"></a></h2>
<section id="performance-metrics">
<h3>Performance Metrics<a class="headerlink" href="#performance-metrics" title="Link to this heading"></a></h3>
</section>
</section>
<section id="request-parameters">
<h2>Request Parameters<a class="headerlink" href="#request-parameters" title="Link to this heading"></a></h2>
<section id="common-parameters">
<h3>Common Parameters<a class="headerlink" href="#common-parameters" title="Link to this heading"></a></h3>
<p>All chat completion requests support these parameters:</p>
<table class="docutils align-default">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 60.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Parameter</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">model</span></code></p></td>
<td><p>string</p></td>
<td><p>Model identifier (required)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">messages</span></code></p></td>
<td><p>array</p></td>
<td><p>List of conversation messages (required)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">max_tokens</span></code></p></td>
<td><p>integer</p></td>
<td><p>Maximum tokens in response (optional, default: 150)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">temperature</span></code></p></td>
<td><p>number</p></td>
<td><p>Response randomness 0.0-1.0 (optional, default: 0.7)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">top_p</span></code></p></td>
<td><p>number</p></td>
<td><p>Nucleus sampling parameter (optional, default: 1.0)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">stream</span></code></p></td>
<td><p>boolean</p></td>
<td><p>Enable streaming response (optional, default: false)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">stop</span></code></p></td>
<td><p>array</p></td>
<td><p>Stop sequences (optional)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="message-format">
<h3>Message Format<a class="headerlink" href="#message-format" title="Link to this heading"></a></h3>
<p>Each message in the <code class="docutils literal notranslate"><span class="pre">messages</span></code> array has this format:</p>
<table class="docutils align-default">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 60.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Type</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">role</span></code></p></td>
<td><p>string</p></td>
<td><p>Message role: “user”, “assistant”, or “system”</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">content</span></code></p></td>
<td><p>string</p></td>
<td><p>Message content text</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">name</span></code></p></td>
<td><p>string</p></td>
<td><p>Optional message author name</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="error-responses">
<h2>Error Responses<a class="headerlink" href="#error-responses" title="Link to this heading"></a></h2>
<section id="error-format">
<h3>Error Format<a class="headerlink" href="#error-format" title="Link to this heading"></a></h3>
<p>All errors return a structured response:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ERROR_CODE&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Human-readable error message&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;field&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;additional_context&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="error-codes">
<h3>Error Codes<a class="headerlink" href="#error-codes" title="Link to this heading"></a></h3>
<table class="docutils align-default">
<colgroup>
<col style="width: 30.0%" />
<col style="width: 20.0%" />
<col style="width: 50.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Error Code</p></th>
<th class="head"><p>HTTP Status</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">VALIDATION_ERROR</span></code></p></td>
<td><p>400</p></td>
<td><p>Invalid request format or parameters</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">AUTHENTICATION_ERROR</span></code></p></td>
<td><p>401</p></td>
<td><p>Invalid or missing API key</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">AUTHORIZATION_ERROR</span></code></p></td>
<td><p>403</p></td>
<td><p>Access denied or IP blocked</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">RATE_LIMIT_ERROR</span></code></p></td>
<td><p>429</p></td>
<td><p>Rate limit exceeded</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">REASONING_ERROR</span></code></p></td>
<td><p>500</p></td>
<td><p>Symbolic reasoning operation failed</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">RETRIEVAL_ERROR</span></code></p></td>
<td><p>500</p></td>
<td><p>Vector retrieval operation failed</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">INTERNAL_ERROR</span></code></p></td>
<td><p>500</p></td>
<td><p>Unexpected server error</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="rate-limiting">
<h2>Rate Limiting<a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<section id="rate-limit-headers">
<h3>Rate Limit Headers<a class="headerlink" href="#rate-limit-headers" title="Link to this heading"></a></h3>
<p>Responses include rate limiting information:</p>
<div class="highlight-http notranslate"><div class="highlight"><pre><span></span><span class="err">X-RateLimit-Limit: 100</span>
<span class="err">X-RateLimit-Remaining: 95</span>
<span class="err">X-RateLimit-Reset: 1677652348</span>
</pre></div>
</div>
</section>
<section id="default-limits">
<h3>Default Limits<a class="headerlink" href="#default-limits" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Requests per minute:</strong> 100 (configurable)</p></li>
<li><p><strong>Request size limit:</strong> 10MB (configurable)</p></li>
<li><p><strong>Concurrent requests:</strong> 10 per client</p></li>
</ul>
</section>
</section>
<section id="cors-support">
<h2>CORS Support<a class="headerlink" href="#cors-support" title="Link to this heading"></a></h2>
<p>The API supports Cross-Origin Resource Sharing (CORS) with configurable origins.</p>
<p><strong>Allowed Methods:</strong> GET, POST, PUT, DELETE, OPTIONS
<strong>Allowed Headers:</strong> Authorization, Content-Type, X-API-Key
<strong>Credentials:</strong> Supported</p>
</section>
<section id="openapi-documentation">
<h2>OpenAPI Documentation<a class="headerlink" href="#openapi-documentation" title="Link to this heading"></a></h2>
<p>Interactive API documentation is available at:</p>
<ul class="simple">
<li><p><strong>Swagger UI:</strong> <code class="docutils literal notranslate"><span class="pre">/docs</span></code></p></li>
<li><p><strong>ReDoc:</strong> <code class="docutils literal notranslate"><span class="pre">/redoc</span></code></p></li>
<li><p><strong>OpenAPI JSON:</strong> <code class="docutils literal notranslate"><span class="pre">/openapi.json</span></code></p></li>
</ul>
<p>These endpoints provide interactive documentation where you can test API calls directly.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="configuration.html" class="btn btn-neutral float-left" title="Configuration Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="modules.html" class="btn btn-neutral float-right" title="API Reference" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>