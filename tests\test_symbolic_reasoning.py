"""Unit tests for the symbolic reasoning module."""

import unittest
import torch
from src.symbolic_reasoning import SymbolicReasoner

class TestSymbolicReasoner(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures."""
        self.reasoner = SymbolicReasoner(use_gpu=False)  # Use CPU for testing
        
    def test_initialization(self):
        """Test reasoner initialization."""
        self.assertIsInstance(self.reasoner, SymbolicReasoner)
        self.assertEqual(self.reasoner.engine, "local")
        self.assertEqual(self.reasoner.model, "llama")
        
    def test_process_query(self):
        """Test query processing."""
        query = "What is symbolic reasoning?"
        response = self.reasoner.process_query(query)
        self.assertIsInstance(response, str)
        self.assertTrue(len(response) > 0)
        
    def test_batch_process_queries(self):
        """Test batch query processing."""
        queries = [
            "What is symbolic reasoning?",
            "How does neural-symbolic AI work?",
            "What are the benefits of symbolic AI?"
        ]
        responses = self.reasoner.batch_process_queries(queries)
        self.assertIsInstance(responses, list)
        self.assertEqual(len(responses), len(queries))
        for response in responses:
            self.assertIsInstance(response, str)
            self.assertTrue(len(response) > 0)
            
    def test_system_info(self):
        """Test system information retrieval."""
        info = self.reasoner.get_system_info()
        self.assertIsInstance(info, dict)
        self.assertIn("engine", info)
        self.assertIn("model", info)
        self.assertIn("gpu_enabled", info)
        self.assertIn("gpu_available", info)
        
if __name__ == "__main__":
    unittest.main()
