{"first_query_times": [2.033313592274984, 2.0373446146647134, 2.037968873977661, 2.0383240381876626, 2.0339624087015786], "cached_query_times": [2.047109603881836, 2.0358579953511557, 2.024289608001709, 2.0332020918528237, 2.0312987168629966], "queries": ["What is Neural-Symbolic AI?", "Explain the difference between FAISS and ChromaDB", "How does GPU acceleration improve vector search?", "What are the benefits of symbolic reasoning?", "How to implement RAG in a production system?"], "timestamp": "2025-05-19T02:20:10.075422", "raw_data": [{"query": "What is Neural-Symbolic AI?", "iterations": [{"iteration": 1, "first_query": {"time": 2.031827926635742, "cached": false}, "cached_query": {"time": 2.0434765815734863, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.043184757232666, "cached": true}, "cached_query": {"time": 2.0426747798919678, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.024928092956543, "cached": true}, "cached_query": {"time": 2.0551774501800537, "cached": true}}]}, {"query": "Explain the difference between FAISS and ChromaDB", "iterations": [{"iteration": 1, "first_query": {"time": 2.0346906185150146, "cached": false}, "cached_query": {"time": 2.033762216567993, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.0435309410095215, "cached": true}, "cached_query": {"time": 2.0337460041046143, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.0338122844696045, "cached": true}, "cached_query": {"time": 2.0400657653808594, "cached": true}}]}, {"query": "How does GPU acceleration improve vector search?", "iterations": [{"iteration": 1, "first_query": {"time": 2.0348775386810303, "cached": false}, "cached_query": {"time": 2.0327675342559814, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.0406854152679443, "cached": true}, "cached_query": {"time": 2.016481399536133, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.038343667984009, "cached": true}, "cached_query": {"time": 2.0236198902130127, "cached": true}}]}, {"query": "What are the benefits of symbolic reasoning?", "iterations": [{"iteration": 1, "first_query": {"time": 2.036918878555298, "cached": false}, "cached_query": {"time": 2.0271589756011963, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.042680501937866, "cached": true}, "cached_query": {"time": 2.027829885482788, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.035372734069824, "cached": true}, "cached_query": {"time": 2.0446174144744873, "cached": true}}]}, {"query": "How to implement RAG in a production system?", "iterations": [{"iteration": 1, "first_query": {"time": 2.045360803604126, "cached": false}, "cached_query": {"time": 2.0308852195739746, "cached": true}}, {"iteration": 2, "first_query": {"time": 2.0343573093414307, "cached": true}, "cached_query": {"time": 2.0276806354522705, "cached": true}}, {"iteration": 3, "first_query": {"time": 2.0221691131591797, "cached": true}, "cached_query": {"time": 2.035330295562744, "cached": true}}]}]}