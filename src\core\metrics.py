"""Comprehensive metrics collection and export system.

This module provides advanced metrics collection, aggregation, and export
capabilities for the Neural Symbolic Language Model, including:
- Prometheus-compatible metrics export
- Custom metric types (counters, gauges, histograms)
- Automatic metric collection from various system components
- Time-series data storage and retrieval
"""

import time
import threading
from collections import defaultdict, deque
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta

# Local imports
from .logging_config import get_logging_manager


@dataclass
class MetricPoint:
    """Represents a single metric data point."""
    timestamp: float
    value: Union[int, float]
    labels: Dict[str, str] = field(default_factory=dict)


class Counter:
    """A monotonically increasing counter metric."""
    
    def __init__(self, name: str, description: str = ""):
        """Initialize the counter.
        
        Args:
            name: Metric name
            description: Metric description
        """
        self.name = name
        self.description = description
        self._value = 0.0
        self._lock = threading.Lock()
    
    def inc(self, amount: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """Increment the counter.
        
        Args:
            amount: Amount to increment by
            labels: Optional labels for this increment
        """
        with self._lock:
            self._value += amount
    
    def get_value(self) -> float:
        """Get the current counter value."""
        return self._value
    
    def reset(self):
        """Reset the counter to zero."""
        with self._lock:
            self._value = 0.0


class Gauge:
    """A gauge metric that can go up and down."""
    
    def __init__(self, name: str, description: str = ""):
        """Initialize the gauge.
        
        Args:
            name: Metric name
            description: Metric description
        """
        self.name = name
        self.description = description
        self._value = 0.0
        self._lock = threading.Lock()
    
    def set(self, value: float, labels: Optional[Dict[str, str]] = None):
        """Set the gauge value.
        
        Args:
            value: Value to set
            labels: Optional labels for this value
        """
        with self._lock:
            self._value = value
    
    def inc(self, amount: float = 1.0):
        """Increment the gauge value."""
        with self._lock:
            self._value += amount
    
    def dec(self, amount: float = 1.0):
        """Decrement the gauge value."""
        with self._lock:
            self._value -= amount
    
    def get_value(self) -> float:
        """Get the current gauge value."""
        return self._value


class Histogram:
    """A histogram metric for tracking distributions."""
    
    def __init__(self, name: str, description: str = "", buckets: Optional[List[float]] = None):
        """Initialize the histogram.
        
        Args:
            name: Metric name
            description: Metric description
            buckets: Bucket boundaries for the histogram
        """
        self.name = name
        self.description = description
        self.buckets = buckets or [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        self._bucket_counts = {bucket: 0 for bucket in self.buckets}
        self._bucket_counts[float('inf')] = 0
        self._sum = 0.0
        self._count = 0
        self._lock = threading.Lock()
    
    def observe(self, value: float, labels: Optional[Dict[str, str]] = None):
        """Observe a value in the histogram.
        
        Args:
            value: Value to observe
            labels: Optional labels for this observation
        """
        with self._lock:
            self._sum += value
            self._count += 1
            
            # Update bucket counts
            for bucket in self.buckets:
                if value <= bucket:
                    self._bucket_counts[bucket] += 1
            
            # Always update the +Inf bucket
            self._bucket_counts[float('inf')] += 1
    
    def get_bucket_counts(self) -> Dict[float, int]:
        """Get bucket counts."""
        return self._bucket_counts.copy()
    
    def get_sum(self) -> float:
        """Get the sum of all observed values."""
        return self._sum
    
    def get_count(self) -> int:
        """Get the count of observations."""
        return self._count


class MetricsRegistry:
    """Central registry for all metrics."""
    
    def __init__(self):
        """Initialize the metrics registry."""
        self.metrics: Dict[str, Union[Counter, Gauge, Histogram]] = {}
        self._lock = threading.Lock()
    
    def register_counter(self, name: str, description: str = "") -> Counter:
        """Register a new counter metric.
        
        Args:
            name: Metric name
            description: Metric description
            
        Returns:
            Counter instance
        """
        with self._lock:
            if name in self.metrics:
                if not isinstance(self.metrics[name], Counter):
                    raise ValueError(f"Metric {name} already exists with different type")
                return self.metrics[name]
            
            counter = Counter(name, description)
            self.metrics[name] = counter
            return counter
    
    def register_gauge(self, name: str, description: str = "") -> Gauge:
        """Register a new gauge metric.
        
        Args:
            name: Metric name
            description: Metric description
            
        Returns:
            Gauge instance
        """
        with self._lock:
            if name in self.metrics:
                if not isinstance(self.metrics[name], Gauge):
                    raise ValueError(f"Metric {name} already exists with different type")
                return self.metrics[name]
            
            gauge = Gauge(name, description)
            self.metrics[name] = gauge
            return gauge
    
    def register_histogram(self, name: str, description: str = "", buckets: Optional[List[float]] = None) -> Histogram:
        """Register a new histogram metric.
        
        Args:
            name: Metric name
            description: Metric description
            buckets: Bucket boundaries
            
        Returns:
            Histogram instance
        """
        with self._lock:
            if name in self.metrics:
                if not isinstance(self.metrics[name], Histogram):
                    raise ValueError(f"Metric {name} already exists with different type")
                return self.metrics[name]
            
            histogram = Histogram(name, description, buckets)
            self.metrics[name] = histogram
            return histogram
    
    def get_metric(self, name: str) -> Optional[Union[Counter, Gauge, Histogram]]:
        """Get a metric by name.
        
        Args:
            name: Metric name
            
        Returns:
            Metric instance or None if not found
        """
        return self.metrics.get(name)
    
    def get_all_metrics(self) -> Dict[str, Union[Counter, Gauge, Histogram]]:
        """Get all registered metrics.
        
        Returns:
            Dictionary of all metrics
        """
        return self.metrics.copy()


class MetricsCollector:
    """Collects metrics from various system components."""
    
    def __init__(self, registry: MetricsRegistry):
        """Initialize the metrics collector.
        
        Args:
            registry: Metrics registry to use
        """
        self.registry = registry
        self.running = False
        self.collection_thread: Optional[threading.Thread] = None
        self.collection_interval = 30.0  # seconds
        
        # Register standard metrics
        self._register_standard_metrics()
    
    def _register_standard_metrics(self):
        """Register standard system metrics."""
        # Request metrics
        self.request_total = self.registry.register_counter(
            "http_requests_total", "Total number of HTTP requests"
        )
        self.request_duration = self.registry.register_histogram(
            "http_request_duration_seconds", "HTTP request duration in seconds"
        )
        self.request_errors = self.registry.register_counter(
            "http_request_errors_total", "Total number of HTTP request errors"
        )
        
        # System metrics
        self.cpu_usage = self.registry.register_gauge(
            "system_cpu_usage_percent", "CPU usage percentage"
        )
        self.memory_usage = self.registry.register_gauge(
            "system_memory_usage_bytes", "Memory usage in bytes"
        )
        self.gpu_usage = self.registry.register_gauge(
            "system_gpu_usage_percent", "GPU usage percentage"
        )
        
        # Application metrics
        self.cache_hits = self.registry.register_counter(
            "cache_hits_total", "Total number of cache hits"
        )
        self.cache_misses = self.registry.register_counter(
            "cache_misses_total", "Total number of cache misses"
        )
        self.active_connections = self.registry.register_gauge(
            "active_connections", "Number of active connections"
        )
    
    def start_collection(self):
        """Start automatic metrics collection."""
        if self.running:
            return
        
        self.running = True
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
    
    def stop_collection(self):
        """Stop automatic metrics collection."""
        self.running = False
        if self.collection_thread:
            self.collection_thread.join()
    
    def _collection_loop(self):
        """Main collection loop."""
        while self.running:
            try:
                self._collect_system_metrics()
                self._collect_application_metrics()
            except Exception as e:
                # Log error but continue collection
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error in metrics collection: {e}", exc_info=True)
            
            time.sleep(self.collection_interval)
    
    def _collect_system_metrics(self):
        """Collect system-level metrics."""
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent()
            self.cpu_usage.set(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.memory_usage.set(memory.used)
            
            # GPU usage (if available)
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_util = torch.cuda.utilization()
                    self.gpu_usage.set(gpu_util)
            except ImportError:
                pass
                
        except ImportError:
            # psutil not available
            pass
    
    def _collect_application_metrics(self):
        """Collect application-specific metrics."""
        # Get logging metrics
        logging_manager = get_logging_manager()
        perf_metrics = logging_manager.get_performance_metrics()
        
        # Update request metrics from logging
        if 'request_count' in perf_metrics:
            # Note: This is a simplified approach
            # In practice, you'd want to track incremental changes
            pass


class PrometheusExporter:
    """Exports metrics in Prometheus format."""
    
    def __init__(self, registry: MetricsRegistry):
        """Initialize the Prometheus exporter.
        
        Args:
            registry: Metrics registry to export from
        """
        self.registry = registry
    
    def export_metrics(self) -> str:
        """Export all metrics in Prometheus format.
        
        Returns:
            Prometheus-formatted metrics string
        """
        output = []
        
        for name, metric in self.registry.get_all_metrics().items():
            if isinstance(metric, Counter):
                output.append(f"# HELP {name} {metric.description}")
                output.append(f"# TYPE {name} counter")
                output.append(f"{name} {metric.get_value()}")
            
            elif isinstance(metric, Gauge):
                output.append(f"# HELP {name} {metric.description}")
                output.append(f"# TYPE {name} gauge")
                output.append(f"{name} {metric.get_value()}")
            
            elif isinstance(metric, Histogram):
                output.append(f"# HELP {name} {metric.description}")
                output.append(f"# TYPE {name} histogram")
                
                # Export bucket counts
                for bucket, count in metric.get_bucket_counts().items():
                    bucket_str = "+Inf" if bucket == float('inf') else str(bucket)
                    output.append(f'{name}_bucket{{le="{bucket_str}"}} {count}')
                
                # Export sum and count
                output.append(f"{name}_sum {metric.get_sum()}")
                output.append(f"{name}_count {metric.get_count()}")
            
            output.append("")  # Empty line between metrics
        
        return "\n".join(output)


# Global metrics registry and collector
metrics_registry = MetricsRegistry()
metrics_collector = MetricsCollector(metrics_registry)
prometheus_exporter = PrometheusExporter(metrics_registry)
