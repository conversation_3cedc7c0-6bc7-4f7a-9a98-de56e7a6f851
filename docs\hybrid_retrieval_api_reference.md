# Hybrid Retrieval API Quick Reference

This is a quick reference for the Neural Symbolic Language Model's Hybrid Retrieval API endpoints.

## Authentication

All endpoints require authentication using Bearer tokens:

```http
Authorization: Bearer your-api-key
```

## Core Endpoints

### Add Document

Add text content to the retrieval system.

```http
POST /documents/add
Content-Type: application/json

{
    "content": "Document text content",
    "metadata": {
        "title": "Document Title",
        "category": "documentation",
        "tags": ["tag1", "tag2"],
        "author": "Author Name",
        "created_at": "2024-06-29T12:00:00Z"
    }
}
```

**Response:**
```json
{
    "document_id": "doc_abc123",
    "status": "added",
    "message": "Document successfully added"
}
```

### Add Website Content

Extract and add content from web pages.

```http
POST /documents/add-url
Content-Type: application/json

{
    "url": "https://example.com/page",
    "extract_links": true,
    "max_depth": 2,
    "include_images": false,
    "metadata": {
        "category": "web_content",
        "source": "web_scraping"
    }
}
```

**Response:**
```json
{
    "document_id": "doc_web456",
    "status": "added",
    "extracted_links": ["https://example.com/link1", "https://example.com/link2"],
    "message": "Website content successfully added"
}
```

### Add GitHub Repository

Clone and process GitHub repository content.

```http
POST /documents/add-github
Content-Type: application/json

{
    "repository_url": "https://github.com/user/repo",
    "include_code": true,
    "include_docs": true,
    "include_issues": false,
    "file_extensions": [".py", ".md", ".txt", ".js"],
    "exclude_paths": ["node_modules", ".git", "__pycache__"],
    "github_token": "optional_token_for_private_repos",
    "metadata": {
        "category": "source_code",
        "project_type": "python",
        "language": "python"
    }
}
```

**Response:**
```json
{
    "document_id": "doc_github789",
    "status": "added",
    "files_processed": 42,
    "repository_info": {
        "name": "repo",
        "owner": "user",
        "language": "Python",
        "stars": 1234
    },
    "message": "GitHub repository successfully processed"
}
```

### Search Documents

Search for relevant documents using various methods.

```http
POST /documents/search
Content-Type: application/json

{
    "query": "machine learning algorithms",
    "method": "hybrid",
    "limit": 10,
    "filters": {
        "category": "documentation",
        "tags": ["ai", "ml"],
        "language": "python"
    },
    "include_metadata": true,
    "min_score": 0.5
}
```

**Search Methods:**
- `"vector"` - Semantic similarity using embeddings
- `"keyword"` - Traditional text-based search  
- `"hybrid"` - Combines vector and keyword search (recommended)

**Response:**
```json
{
    "results": [
        {
            "document_id": "doc_abc123",
            "score": 0.95,
            "content": "Relevant document content...",
            "metadata": {
                "title": "Document Title",
                "category": "documentation",
                "tags": ["ai", "ml"]
            }
        }
    ],
    "total_results": 1,
    "search_time_ms": 45
}
```

### Get Document Count

Get statistics about documents in the system.

```http
GET /documents/count
```

**Response:**
```json
{
    "total_documents": 1250,
    "indexed_documents": 1248,
    "pending_documents": 2,
    "categories": {
        "documentation": 500,
        "source_code": 400,
        "web_content": 350
    }
}
```

### Get Document by ID

Retrieve a specific document.

```http
GET /documents/{document_id}
```

**Response:**
```json
{
    "document_id": "doc_abc123",
    "content": "Full document content...",
    "metadata": {
        "title": "Document Title",
        "category": "documentation",
        "created_at": "2024-06-29T12:00:00Z"
    },
    "indexed_at": "2024-06-29T12:01:00Z"
}
```

### Update Document

Update document content or metadata.

```http
PUT /documents/{document_id}
Content-Type: application/json

{
    "content": "Updated document content",
    "metadata": {
        "title": "Updated Title",
        "category": "documentation",
        "updated_at": "2024-06-29T13:00:00Z"
    }
}
```

### Delete Document

Remove a document from the system.

```http
DELETE /documents/{document_id}
```

**Response:**
```json
{
    "document_id": "doc_abc123",
    "status": "deleted",
    "message": "Document successfully removed"
}
```

## Batch Operations

### Bulk Add Documents

Add multiple documents in a single request.

```http
POST /documents/bulk-add
Content-Type: application/json

{
    "documents": [
        {
            "content": "First document content",
            "metadata": {"title": "Doc 1", "category": "test"}
        },
        {
            "content": "Second document content", 
            "metadata": {"title": "Doc 2", "category": "test"}
        }
    ]
}
```

### Bulk Search

Perform multiple searches in one request.

```http
POST /documents/bulk-search
Content-Type: application/json

{
    "queries": [
        {"query": "machine learning", "limit": 5},
        {"query": "neural networks", "limit": 3},
        {"query": "data science", "limit": 7}
    ],
    "method": "hybrid"
}
```

## Configuration

### Update Retrieval Settings

Configure retrieval system parameters.

```http
POST /documents/configure
Content-Type: application/json

{
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "embedding_model": "mxbai-embed-large",
    "similarity_threshold": 0.7,
    "max_results": 50,
    "enable_reranking": true
}
```

### Get Current Configuration

```http
GET /documents/config
```

## Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created (document added) |
| 400 | Bad Request (invalid input) |
| 401 | Unauthorized (invalid API key) |
| 404 | Not Found (document doesn't exist) |
| 429 | Rate Limit Exceeded |
| 500 | Internal Server Error |

## Error Response Format

```json
{
    "error": {
        "type": "invalid_request_error",
        "code": "missing_required_field",
        "message": "Missing required field: content",
        "param": "content"
    }
}
```

## Rate Limits

- **Document Addition**: 100 requests per minute
- **Search Requests**: 1000 requests per minute  
- **Bulk Operations**: 10 requests per minute
- **Configuration**: 5 requests per minute

## Python SDK Example

```python
import requests

class HybridRetrievalClient:
    def __init__(self, api_key, base_url="http://localhost:8080"):
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.base_url = base_url
    
    def add_document(self, content, metadata=None):
        response = requests.post(
            f"{self.base_url}/documents/add",
            headers=self.headers,
            json={"content": content, "metadata": metadata or {}}
        )
        return response.json()
    
    def search(self, query, method="hybrid", limit=10):
        response = requests.post(
            f"{self.base_url}/documents/search",
            headers=self.headers,
            json={"query": query, "method": method, "limit": limit}
        )
        return response.json()

# Usage
client = HybridRetrievalClient("your-api-key")

# Add document
result = client.add_document(
    "AI is transforming technology",
    {"category": "ai", "tags": ["artificial-intelligence"]}
)

# Search
results = client.search("artificial intelligence", limit=5)
```

## JavaScript SDK Example

```javascript
class HybridRetrievalClient {
    constructor(apiKey, baseURL = "http://localhost:8080") {
        this.headers = {
            "Authorization": `Bearer ${apiKey}`,
            "Content-Type": "application/json"
        };
        this.baseURL = baseURL;
    }
    
    async addDocument(content, metadata = {}) {
        const response = await fetch(`${this.baseURL}/documents/add`, {
            method: "POST",
            headers: this.headers,
            body: JSON.stringify({ content, metadata })
        });
        return await response.json();
    }
    
    async search(query, method = "hybrid", limit = 10) {
        const response = await fetch(`${this.baseURL}/documents/search`, {
            method: "POST", 
            headers: this.headers,
            body: JSON.stringify({ query, method, limit })
        });
        return await response.json();
    }
}

// Usage
const client = new HybridRetrievalClient("your-api-key");

// Add document
const result = await client.addDocument(
    "AI is transforming technology",
    { category: "ai", tags: ["artificial-intelligence"] }
);

// Search
const results = await client.search("artificial intelligence", "hybrid", 5);
```

## cURL Examples

```bash
# Add document
curl -X POST "http://localhost:8080/documents/add" \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "content": "AI is transforming technology",
       "metadata": {"category": "ai", "tags": ["artificial-intelligence"]}
     }'

# Search documents  
curl -X POST "http://localhost:8080/documents/search" \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "query": "artificial intelligence",
       "method": "hybrid",
       "limit": 5
     }'

# Add website
curl -X POST "http://localhost:8080/documents/add-url" \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "url": "https://example.com/ai-article",
       "metadata": {"category": "web_content"}
     }'

# Get document count
curl -H "Authorization: Bearer your-api-key" \
     "http://localhost:8080/documents/count"
```

For complete documentation and examples, see the [Hybrid Retrieval Guide](hybrid_retrieval_guide.md).
