from importlib.metadata import distributions
import os

print("Listing all discoverable Python distributions and their paths:\n")
found_problematic = False
for dist in distributions():
    dist_location = "N/A"
    # The ._path attribute is not officially public but often holds the location
    # For new versions, dist.path or similar might be available, or files()
    if hasattr(dist, '_path') and dist._path:
        dist_location = str(dist._path)
    elif dist.files:
        # Get the top-level directory from the first file path
        try:
            first_file = next(iter(dist.files))
            # Example: first_file.locate() might be C:\path\to\site-packages\package_name\__init__.py
            # We want C:\path\to\site-packages\package_name
            # This can be tricky as dist.files gives PathDistribution._file_to_path objects
            # A common pattern is that the top-level dir is part of the path
            dist_location = str(first_file.locate().parent)
            if not os.path.basename(dist_location).replace(".dist-info", "").replace(".egg-info","") == dist.metadata['Name'].replace("-","_").lower():
                 # If parent isn't the package name, try grandparent for .dist-info cases
                 grandparent = first_file.locate().parent.parent
                 if os.path.basename(grandparent).replace(".dist-info", "").replace(".egg-info","") == dist.metadata['Name'].replace("-","_").lower():
                     dist_location = str(grandparent)
                 else: # Fallback for complex layouts, show the direct parent of the file
                     dist_location = str(first_file.locate().parent)

        except StopIteration:
            dist_location = "No files listed"
        except Exception as e:
            dist_location = f"Error getting path: {e}"
    
    print(f"Distribution: {dist.name} ({dist.version})")
    print(f"  Location: {dist_location}")

    # Heuristic check for problematic names based on pip's warning behavior
    if dist_location != "N/A" and dist_location != "No files listed" and not dist_location.startswith("Error"):
        dir_name = os.path.basename(dist_location)
        if dir_name.startswith('~') or dir_name.startswith('-') or not dir_name:
            print(f"  *** POTENTIALLY PROBLEMATIC DIRECTORY NAME: {dir_name} ***")
            found_problematic = True
    print("---")

if not found_problematic:
    print("\nNo obviously problematic directory names found by this script's heuristics.")
    print("Please still manually check your site-packages for empty or strangely named folders.")
    print(f"Site-packages is likely: {os.path.dirname(dist_location)}") # Assumes last dist is in site-packages
