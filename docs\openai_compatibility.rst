OpenAI API Compatibility
========================

The Neural Symbolic Language Model provides full compatibility with OpenAI's API format,
allowing it to work as a drop-in replacement for OpenAI's services while providing
enhanced symbolic reasoning capabilities through the gemma3n:e2b model.

Compatibility Overview
----------------------

**Supported Endpoints:**

* ``/v1/chat/completions`` - Chat completion with streaming support
* ``/v1/models`` - List available models
* ``/health`` - Health check endpoint (extension)
* ``/performance`` - Performance metrics (extension)

**Compatible Libraries:**

* OpenAI Python SDK
* OpenAI Node.js SDK
* LangChain
* LlamaIndex
* Any OpenAI-compatible client

API Endpoint Compatibility
---------------------------

Chat Completions
~~~~~~~~~~~~~~~~

**Endpoint:** ``POST /v1/chat/completions``

**Full OpenAI Parameter Support:**

.. list-table::
   :header-rows: 1
   :widths: 20 15 15 50

   * - Parameter
     - Type
     - Required
     - Description
   * - ``model``
     - string
     - Yes
     - Model identifier (use "gemma3n:e2b")
   * - ``messages``
     - array
     - Yes
     - Array of message objects
   * - ``temperature``
     - number
     - No
     - Sampling temperature (0.0 to 2.0)
   * - ``top_p``
     - number
     - No
     - Nucleus sampling parameter
   * - ``max_tokens``
     - integer
     - No
     - Maximum tokens to generate
   * - ``stream``
     - boolean
     - No
     - Enable streaming responses
   * - ``stop``
     - array/string
     - No
     - Stop sequences
   * - ``presence_penalty``
     - number
     - No
     - Presence penalty (-2.0 to 2.0)
   * - ``frequency_penalty``
     - number
     - No
     - Frequency penalty (-2.0 to 2.0)
   * - ``user``
     - string
     - No
     - User identifier

**Response Format:**

Identical to OpenAI's response format with additional performance metrics:

.. code-block:: json

   {
     "id": "chatcmpl-abc123",
     "object": "chat.completion",
     "created": 1677652288,
     "model": "gemma3n:e2b",
     "choices": [
       {
         "index": 0,
         "message": {
           "role": "assistant",
           "content": "Response content here..."
         },
         "finish_reason": "stop"
       }
     ],
     "usage": {
       "prompt_tokens": 45,
       "completion_tokens": 78,
       "total_tokens": 123
     }
   }

Models Endpoint
~~~~~~~~~~~~~~~

**Endpoint:** ``GET /v1/models``

Lists available models in OpenAI-compatible format:

.. code-block:: json

   {
     "object": "list",
     "data": [
       {
         "id": "gemma3n:e2b",
         "object": "model",
         "created": 1677652288,
         "owned_by": "ollama",
         "permission": [],
         "root": "gemma3n:e2b",
         "parent": null
       }
     ]
   }

SDK Integration Examples
------------------------

Python OpenAI SDK
~~~~~~~~~~~~~~~~~~

**Installation:**

.. code-block:: bash

   pip install openai

**Basic Usage:**

.. code-block:: python

   import openai
   
   # Configure for local Neural Symbolic Language Model
   openai.api_base = "http://localhost:8080/v1"
   openai.api_key = "your-api-key"
   
   # Chat completion
   response = openai.ChatCompletion.create(
       model="gemma3n:e2b",
       messages=[
           {"role": "system", "content": "You are an expert in symbolic reasoning."},
           {"role": "user", "content": "Explain the difference between deductive and inductive reasoning."}
       ],
       temperature=0.7,
       max_tokens=1000
   )
   
   print(response.choices[0].message.content)

**Streaming Example:**

.. code-block:: python

   import openai
   
   openai.api_base = "http://localhost:8080/v1"
   openai.api_key = "your-api-key"
   
   # Streaming chat completion
   response = openai.ChatCompletion.create(
       model="gemma3n:e2b",
       messages=[
           {"role": "user", "content": "Explain machine learning step by step."}
       ],
       stream=True
   )
   
   for chunk in response:
       if chunk.choices[0].delta.get("content"):
           print(chunk.choices[0].delta.content, end="")

**Async Example:**

.. code-block:: python

   import asyncio
   import openai
   
   openai.api_base = "http://localhost:8080/v1"
   openai.api_key = "your-api-key"
   
   async def async_chat():
       response = await openai.ChatCompletion.acreate(
           model="gemma3n:e2b",
           messages=[
               {"role": "user", "content": "What is artificial intelligence?"}
           ]
       )
       return response.choices[0].message.content
   
   # Run async function
   result = asyncio.run(async_chat())
   print(result)

Node.js OpenAI SDK
~~~~~~~~~~~~~~~~~~

**Installation:**

.. code-block:: bash

   npm install openai

**Basic Usage:**

.. code-block:: javascript

   const { Configuration, OpenAIApi } = require("openai");
   
   const configuration = new Configuration({
     apiKey: "your-api-key",
     basePath: "http://localhost:8080/v1"
   });
   
   const openai = new OpenAIApi(configuration);
   
   async function chatCompletion() {
     try {
       const response = await openai.createChatCompletion({
         model: "gemma3n:e2b",
         messages: [
           {
             role: "user",
             content: "Explain the concept of neural networks."
           }
         ],
         temperature: 0.7,
         max_tokens: 1000
       });
       
       console.log(response.data.choices[0].message.content);
     } catch (error) {
       console.error("Error:", error.response?.data || error.message);
     }
   }
   
   chatCompletion();

**Streaming Example:**

.. code-block:: javascript

   const { Configuration, OpenAIApi } = require("openai");
   
   const configuration = new Configuration({
     apiKey: "your-api-key",
     basePath: "http://localhost:8080/v1"
   });
   
   const openai = new OpenAIApi(configuration);
   
   async function streamingChat() {
     try {
       const response = await openai.createChatCompletion({
         model: "gemma3n:e2b",
         messages: [
           { role: "user", content: "Tell me about quantum computing." }
         ],
         stream: true
       });
       
       response.data.on('data', (chunk) => {
         const lines = chunk.toString().split('\n').filter(line => line.trim() !== '');
         
         for (const line of lines) {
           const message = line.replace(/^data: /, '');
           if (message === '[DONE]') return;
           
           try {
             const parsed = JSON.parse(message);
             const content = parsed.choices[0]?.delta?.content;
             if (content) {
               process.stdout.write(content);
             }
           } catch (error) {
             // Skip invalid JSON
           }
         }
       });
     } catch (error) {
       console.error("Error:", error);
     }
   }
   
   streamingChat();

Framework Integration
---------------------

LangChain Integration
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from langchain.llms import OpenAI
   from langchain.chat_models import ChatOpenAI
   from langchain.schema import HumanMessage, SystemMessage
   
   # Configure LangChain to use Neural Symbolic Language Model
   chat = ChatOpenAI(
       model_name="gemma3n:e2b",
       openai_api_base="http://localhost:8080/v1",
       openai_api_key="your-api-key",
       temperature=0.7
   )
   
   # Use with LangChain
   messages = [
       SystemMessage(content="You are an expert in symbolic reasoning."),
       HumanMessage(content="Solve this logic puzzle: If all cats are animals and some animals are pets, what can we conclude about cats and pets?")
   ]
   
   response = chat(messages)
   print(response.content)

LlamaIndex Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from llama_index import ServiceContext, LLMPredictor
   from llama_index.llms import OpenAI
   
   # Configure LlamaIndex
   llm = OpenAI(
       model="gemma3n:e2b",
       api_base="http://localhost:8080/v1",
       api_key="your-api-key",
       temperature=0.7
   )
   
   service_context = ServiceContext.from_defaults(
       llm_predictor=LLMPredictor(llm=llm)
   )
   
   # Use with LlamaIndex for document processing
   from llama_index import VectorStoreIndex, SimpleDirectoryReader
   
   documents = SimpleDirectoryReader("./documents").load_data()
   index = VectorStoreIndex.from_documents(
       documents, 
       service_context=service_context
   )
   
   query_engine = index.as_query_engine()
   response = query_engine.query("What are the key concepts in machine learning?")
   print(response)

Differences from OpenAI
-----------------------

Enhanced Features
~~~~~~~~~~~~~~~~~

**1. Multi-Modal Support:**
   - Extended content types for code, math, and structured data
   - Enhanced reasoning capabilities with gemma3n:e2b

**2. Symbolic Reasoning:**
   - Advanced logical reasoning capabilities
   - Formal logic processing
   - Mathematical proof generation

**3. Performance Metrics:**
   - Additional response fields with performance data
   - Cache hit information
   - Reasoning step tracking

**4. Local Processing:**
   - No data sent to external services
   - Full privacy and control
   - Customizable model parameters

Model Mapping
~~~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 30 30 40

   * - OpenAI Model
     - Neural Symbolic Model
     - Notes
   * - ``gpt-3.5-turbo``
     - ``gemma3n:e2b``
     - Enhanced reasoning capabilities
   * - ``gpt-4``
     - ``gemma3n:e2b``
     - Comparable performance with local processing
   * - ``text-davinci-003``
     - ``gemma3n:e2b``
     - Chat format recommended

Migration Guide
---------------

From OpenAI to Neural Symbolic LM
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Step 1: Update Base URL**

.. code-block:: python

   # Before (OpenAI)
   openai.api_base = "https://api.openai.com/v1"
   
   # After (Neural Symbolic LM)
   openai.api_base = "http://localhost:8080/v1"

**Step 2: Update Model Names**

.. code-block:: python

   # Before
   model="gpt-3.5-turbo"
   
   # After
   model="gemma3n:e2b"

**Step 3: Update API Keys**

.. code-block:: python

   # Before
   openai.api_key = "sk-..."
   
   # After
   openai.api_key = "your-local-api-key"

**Step 4: Test Compatibility**

.. code-block:: python

   # Test basic functionality
   response = openai.ChatCompletion.create(
       model="gemma3n:e2b",
       messages=[{"role": "user", "content": "Hello, world!"}]
   )
   
   assert response.choices[0].message.content
   print("Migration successful!")

Error Handling
--------------

OpenAI-Compatible Errors
~~~~~~~~~~~~~~~~~~~~~~~~~

The system returns errors in OpenAI-compatible format:

.. code-block:: json

   {
     "error": {
       "message": "Invalid request: missing required parameter 'model'",
       "type": "invalid_request_error",
       "param": "model",
       "code": "missing_required_parameter"
     }
   }

**Common Error Codes:**

* ``invalid_request_error`` - Malformed request
* ``authentication_error`` - Invalid API key
* ``rate_limit_error`` - Rate limit exceeded
* ``server_error`` - Internal server error

Best Practices
--------------

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Use Appropriate Models**: gemma3n:e2b for reasoning tasks
2. **Optimize Parameters**: Lower temperature for logical tasks
3. **Batch Requests**: Group related requests when possible
4. **Cache Results**: Implement client-side caching for repeated queries
5. **Monitor Usage**: Track token usage and response times

Security Considerations
~~~~~~~~~~~~~~~~~~~~~~~

1. **API Key Management**: Secure your API keys
2. **Rate Limiting**: Implement client-side rate limiting
3. **Input Validation**: Validate inputs before sending
4. **Error Handling**: Implement proper error handling
5. **Logging**: Log requests for debugging and monitoring

For more detailed information, see the :doc:`api_reference` and :doc:`client_integration_guide` sections.
