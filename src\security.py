"""
Security module for the Neural Symbolic Language Model.

This module provides authentication, authorization, rate limiting, and other
security features for the FastAPI application.

Author: AI Assistant
Date: 2025-06-29
"""

import hmac
import os
import time
from typing import Dict, Optional, List, Any
import logging

from fastapi import HTTPException, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import secrets

# Configure logger
logger = logging.getLogger(__name__)

# Security configuration
API_KEY_HEADER = "X-API-Key"
DEFAULT_RATE_LIMIT = "100/minute"
MAX_REQUEST_SIZE = 10 * 1024 * 1024  # 10MB

class SecurityManager:
    """Manages security features for the application."""

    def __init__(self, api_keys: Optional[Dict[str, str]] = None):
        """Initialize the security manager.

        Args:
            api_keys: Dictionary mapping API key names to their values
        """
        self.api_keys = api_keys or {}
        self.failed_attempts: Dict[str, list] = {}
        self.blocked_ips: Dict[str, float] = {}

        # Generate a default API key if none provided
        if not self.api_keys:
            default_key = secrets.token_urlsafe(32)
            self.api_keys["default"] = default_key
            # SECURITY: Never log the actual API key
            logger.warning(
                "No API keys configured. Generated temporary API key. "
                "Set API_KEYS_JSON environment variable for production use."
            )
            # Only show the key in development mode
            if os.getenv('APP_ENVIRONMENT', 'development') == 'development':
                logger.warning(f"Development API key: {default_key}")
            else:
                logger.warning("API key generated but not displayed in production mode")

    def validate_api_key(self, api_key: str) -> bool:
        """Validate an API key.

        Args:
            api_key: The API key to validate

        Returns:
            True if valid, False otherwise
        """
        if not api_key:
            return False

        # Use constant-time comparison to prevent timing attacks
        for stored_key in self.api_keys.values():
            if hmac.compare_digest(api_key, stored_key):
                return True
        return False

    def is_ip_blocked(self, ip_address: str) -> bool:
        """Check if an IP address is blocked.

        Args:
            ip_address: The IP address to check

        Returns:
            True if blocked, False otherwise
        """
        if ip_address in self.blocked_ips:
            block_time = self.blocked_ips[ip_address]
            if time.time() - block_time < 3600:  # 1 hour block
                return True
            else:
                # Unblock after timeout
                del self.blocked_ips[ip_address]
        return False

    def record_failed_attempt(self, ip_address: str) -> None:
        """Record a failed authentication attempt.

        Args:
            ip_address: The IP address that failed authentication
        """
        current_time = time.time()

        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = []

        # Clean old attempts (older than 15 minutes)
        self.failed_attempts[ip_address] = [
            attempt_time for attempt_time in self.failed_attempts[ip_address]
            if current_time - attempt_time < 900
        ]

        # Add current attempt
        self.failed_attempts[ip_address].append(current_time)

        # Block IP if too many failed attempts
        if len(self.failed_attempts[ip_address]) >= 5:
            self.blocked_ips[ip_address] = current_time
            logger.warning(f"Blocked IP {ip_address} due to repeated failed attempts")

    def sanitize_input(self, text: str, max_length: int = 10000) -> str:
        """Sanitize user input text.

        Args:
            text: The input text to sanitize
            max_length: Maximum allowed length

        Returns:
            Sanitized text

        Raises:
            ValueError: If input is invalid
        """
        if not isinstance(text, str):
            raise ValueError("Input must be a string")

        if len(text) > max_length:
            raise ValueError(f"Input too long (max {max_length} characters)")

        # Remove null bytes and control characters
        sanitized = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')

        # Strip whitespace
        sanitized = sanitized.strip()

        if not sanitized:
            raise ValueError("Input cannot be empty after sanitization")

        return sanitized

# Global security manager instance
security_manager = SecurityManager()

# HTTP Bearer token scheme
security_scheme = HTTPBearer()

async def verify_api_key(credentials: HTTPAuthorizationCredentials = security_scheme) -> str:
    """Verify API key from Authorization header.

    Args:
        credentials: HTTP authorization credentials

    Returns:
        The validated API key

    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    api_key = credentials.credentials
    if not security_manager.validate_api_key(api_key):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return api_key

async def check_request_size(request: Request) -> None:
    """Check if request size is within limits.

    Args:
        request: The FastAPI request object

    Raises:
        HTTPException: If request is too large
    """
    content_length = request.headers.get("content-length")
    if content_length and int(content_length) > MAX_REQUEST_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"Request too large (max {MAX_REQUEST_SIZE} bytes)"
        )

def get_security_headers() -> Dict[str, str]:
    """Get security headers to add to responses.

    Returns:
        Dictionary of security headers
    """
    return {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }

class RateLimiter:
    """Simple in-memory rate limiter."""

    def __init__(self):
        """Initialize the rate limiter."""
        self.requests: Dict[str, list] = {}

    def is_allowed(self, identifier: str, limit: int = 100, window: int = 60) -> bool:
        """Check if a request is allowed based on rate limits.

        Args:
            identifier: Unique identifier (e.g., IP address)
            limit: Maximum number of requests allowed
            window: Time window in seconds

        Returns:
            True if request is allowed, False otherwise
        """
        current_time = time.time()

        if identifier not in self.requests:
            self.requests[identifier] = []

        # Clean old requests outside the window
        self.requests[identifier] = [
            req_time for req_time in self.requests[identifier]
            if current_time - req_time < window
        ]

        # Check if limit exceeded
        if len(self.requests[identifier]) >= limit:
            return False

        # Add current request
        self.requests[identifier].append(current_time)
        return True

# Global rate limiter instance
rate_limiter = RateLimiter()

def get_client_ip(request: Request) -> str:
    """Get client IP address from request.

    Args:
        request: The FastAPI request object

    Returns:
        Client IP address
    """
    # Check for forwarded headers first
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # Fall back to direct connection
    if hasattr(request.client, 'host'):
        return request.client.host

    return "unknown"


def get_cors_config() -> Dict[str, Any]:
    """Get CORS (Cross-Origin Resource Sharing) configuration.

    This function provides CORS configuration for the FastAPI application,
    allowing controlled cross-origin requests while maintaining security.

    Returns:
        Dictionary containing CORS configuration parameters

    Example:
        >>> cors_config = get_cors_config()
        >>> print(cors_config['allow_origins'])
        ['http://localhost:3000', 'https://yourdomain.com']
    """
    try:
        from core.config import get_settings
        settings = get_settings()
        cors_origins = getattr(settings.security, 'cors_origins', ['http://localhost:3000'])

        # Ensure we don't allow all origins in production
        if hasattr(settings, 'environment') and settings.environment == 'production' and '*' in cors_origins:
            logger.warning("CORS configured to allow all origins in production - security risk!")
            cors_origins = [origin for origin in cors_origins if origin != '*']
            if not cors_origins:
                cors_origins = ['https://yourdomain.com']
    except ImportError:
        # Fallback if config is not available
        cors_origins = ['http://localhost:3000', 'http://127.0.0.1:3000']

    return {
        "allow_origins": cors_origins,
        "allow_credentials": True,
        "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": [
            "Accept",
            "Accept-Language",
            "Content-Language",
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "X-API-Key"
        ],
        "expose_headers": [
            "X-Request-ID",
            "X-Response-Time"
        ],
        "max_age": 600  # Cache preflight requests for 10 minutes
    }


def validate_cors_origin(origin: str, allowed_origins: List[str]) -> bool:
    """Validate if an origin is allowed for CORS requests.

    Args:
        origin: The origin to validate
        allowed_origins: List of allowed origins

    Returns:
        True if origin is allowed, False otherwise

    Example:
        >>> allowed = ['https://example.com', 'https://app.example.com']
        >>> validate_cors_origin('https://example.com', allowed)
        True
        >>> validate_cors_origin('https://malicious.com', allowed)
        False
    """
    if not origin:
        return False

    # Check for exact match
    if origin in allowed_origins:
        return True

    def sanitize_input(self, input_data: str, max_length: int = 10000) -> str:
        """Sanitize user input to prevent injection attacks.

        This method removes potentially dangerous characters and normalizes
        input to prevent various types of injection attacks including XSS,
        SQL injection, and command injection.

        Args:
            input_data: The input string to sanitize
            max_length: Maximum allowed length for the input

        Returns:
            Sanitized input string

        Raises:
            ValueError: If input is invalid or exceeds length limits

        Example:
            >>> manager = SecurityManager()
            >>> clean_input = manager.sanitize_input("<script>alert('xss')</script>")
            >>> print(clean_input)  # "scriptalert('xss')/script"
        """
        if not isinstance(input_data, str):
            raise ValueError("Input must be a string")

        if len(input_data) > max_length:
            raise ValueError(f"Input too long (max {max_length} characters)")

        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\r', '\n\r']
        sanitized = input_data

        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')

        # Normalize whitespace
        sanitized = ' '.join(sanitized.split())

        return sanitized

    def validate_content_type(self, content_type: str) -> bool:
        """Validate that the content type is allowed.

        Args:
            content_type: The content type to validate

        Returns:
            True if content type is allowed, False otherwise
        """
        allowed_types = [
            'application/json',
            'text/plain',
            'multipart/form-data',
            'application/x-www-form-urlencoded'
        ]

        # Extract base content type (ignore charset, etc.)
        base_type = content_type.split(';')[0].strip().lower()
        return base_type in allowed_types

    def check_request_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Check and sanitize request headers.

        Args:
            headers: Dictionary of request headers

        Returns:
            Dictionary of security warnings (empty if all good)
        """
        warnings = {}

        # Check for suspicious headers
        suspicious_headers = [
            'x-forwarded-for',
            'x-real-ip',
            'x-cluster-client-ip'
        ]

        for header in suspicious_headers:
            if header in headers:
                warnings[header] = "Potentially spoofed header detected"

        # Check User-Agent
        user_agent = headers.get('user-agent', '').lower()
        if not user_agent:
            warnings['user-agent'] = "Missing User-Agent header"
        elif any(bot in user_agent for bot in ['bot', 'crawler', 'spider']):
            warnings['user-agent'] = "Bot detected in User-Agent"

        return warnings

    # Check for wildcard
    if '*' in allowed_origins:
        return True

    # Check for subdomain patterns (if implemented)
    for allowed in allowed_origins:
        if allowed.startswith('*.') and origin.endswith(allowed[1:]):
            return True

    return False