#!/usr/bin/env python3
"""
Simple test to verify Ollama integration with gemma3n:e2b model.
"""

import asyncio
import time

try:
    import ollama
    print("✅ Ollama module imported successfully")
except ImportError as e:
    print(f"❌ Failed to import ollama: {e}")
    exit(1)


async def test_ollama_basic():
    """Test basic Ollama functionality."""
    print("\n🔍 Testing basic Ollama functionality...")
    
    try:
        # Test synchronous client
        client = ollama.Client()
        models = client.list()
        print(f"✅ Found {len(models.get('models', []))} models")
        
        # Check for gemma3n:e2b
        model_names = [model['name'] for model in models.get('models', [])]
        print(f"📋 Available models: {model_names}")
        
        if 'gemma3n:e2b' in model_names:
            print("✅ gemma3n:e2b model is available")
            return True
        else:
            print("❌ gemma3n:e2b model not found")
            return False
            
    except Exception as e:
        print(f"❌ Ollama test failed: {e}")
        return False


async def test_gemma_query():
    """Test a simple query with gemma3n:e2b."""
    print("\n🧠 Testing query with gemma3n:e2b...")
    
    try:
        client = ollama.AsyncClient()
        
        query = "What is 2 + 2? Answer briefly."
        print(f"📝 Query: {query}")
        
        start_time = time.time()
        
        response = await client.chat(
            model='gemma3n:e2b',
            messages=[
                {'role': 'user', 'content': query}
            ]
        )
        
        duration = time.time() - start_time
        
        if response and 'message' in response:
            content = response['message']['content']
            print(f"✅ Response ({duration:.2f}s): {content}")
            return True
        else:
            print(f"❌ Invalid response format: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Query test failed: {e}")
        return False


async def test_multiple_queries():
    """Test multiple queries to check consistency."""
    print("\n⚡ Testing multiple queries...")
    
    queries = [
        "What is artificial intelligence?",
        "Explain machine learning in one sentence.",
        "What is 5 * 7?"
    ]
    
    client = ollama.AsyncClient()
    results = []
    
    for i, query in enumerate(queries, 1):
        print(f"📝 Query {i}: {query}")
        
        try:
            start_time = time.time()
            
            response = await client.chat(
                model='gemma3n:e2b',
                messages=[
                    {'role': 'user', 'content': query}
                ]
            )
            
            duration = time.time() - start_time
            
            if response and 'message' in response:
                content = response['message']['content']
                print(f"✅ Response {i} ({duration:.2f}s): {content[:100]}...")
                results.append(True)
            else:
                print(f"❌ Query {i} failed: Invalid response")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Query {i} failed: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results)
    print(f"📊 Success rate: {success_rate:.1%} ({sum(results)}/{len(results)})")
    
    return success_rate >= 0.8  # 80% success rate


async def main():
    """Run all tests."""
    print("🚀 Starting Simple Ollama Tests with gemma3n:e2b")
    print("=" * 50)
    
    tests = [
        ("Basic Ollama", test_ollama_basic),
        ("Single Query", test_gemma_query),
        ("Multiple Queries", test_multiple_queries)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Ollama with gemma3n:e2b is working!")
        return True
    else:
        print("⚠️  Some tests failed.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
