"""PyTorch-based vector store implementation for GPU-accelerated vector search.

This module provides a high-performance vector store implementation using PyTorch
tensors with CUDA acceleration. It serves as a fallback when FAISS-GPU is not
available but CUDA hardware is present, ensuring optimal performance for
vector similarity search operations.

The implementation supports:
    - GPU-accelerated vector operations using CUDA
    - Efficient similarity search with cosine similarity
    - Dynamic vector addition and storage management
    - Automatic memory management and optimization
    - Fallback to CPU when GPU is not available

Key Features:
    - High-performance vector similarity search
    - CUDA acceleration for large-scale operations
    - Memory-efficient tensor operations
    - Automatic device management (CPU/GPU)
    - Thread-safe operations for concurrent access

Example:
    Basic usage of the vector store::

        import numpy as np
        from vector_store import TorchVectorStore

        # Initialize vector store
        store = TorchVectorStore(dimension=768, use_gpu=True)

        # Add vectors
        vectors = np.random.random((100, 768)).astype('float32')
        doc_ids = [f"doc_{i}" for i in range(100)]
        texts = [f"Document {i} content" for i in range(100)]

        store.add(vectors, doc_ids, texts)

        # Search for similar vectors
        query_vector = np.random.random((1, 768)).astype('float32')
        results = store.search(query_vector, k=5)

Note:
    This implementation is optimized for scenarios where FAISS is not available
    but PyTorch with CUDA support is present. For production deployments with
    FAISS available, prefer FAISS-based implementations for better performance.

Author:
    AI Assistant

Date:
    2025-06-29

Version:
    0.1.0
"""

# Standard library imports
import logging
from typing import Any, Dict, List, Optional, Tuple, Union

# Third-party imports
import numpy as np
import torch

logger = logging.getLogger(__name__)

class TorchVectorStore:
    """High-performance vector store implementation using PyTorch CUDA capabilities.

    This class provides a GPU-accelerated vector store for similarity search
    operations using PyTorch tensors. It automatically handles device management,
    memory optimization, and provides efficient similarity search capabilities.

    The vector store maintains vectors in GPU memory when available, enabling
    fast similarity computations for large-scale retrieval operations. It
    supports dynamic addition of vectors and provides cosine similarity search.

    Attributes:
        dimension (int): The dimensionality of stored vectors
        use_gpu (bool): Whether GPU acceleration is enabled
        vectors (torch.Tensor): Stored vectors as a PyTorch tensor
        documents (Dict[str, str]): Mapping of document IDs to text content
        doc_ids (List[str]): Ordered list of document IDs

    Example:
        Initialize and use the vector store::

            # Create vector store with GPU acceleration
            store = TorchVectorStore(dimension=768, use_gpu=True)

            # Add vectors to the store
            vectors = np.random.random((1000, 768)).astype('float32')
            doc_ids = [f"doc_{i}" for i in range(1000)]
            texts = [f"Content for document {i}" for i in range(1000)]

            count = store.add(vectors, doc_ids, texts)
            print(f"Added {count} vectors to the store")

            # Search for similar vectors
            query = np.random.random((1, 768)).astype('float32')
            results = store.search(query, k=5)

            for result in results:
                print(f"ID: {result['id']}, Score: {result['score']:.3f}")

    Note:
        This implementation is optimized for CUDA-enabled GPUs. When GPU is not
        available, operations fall back to CPU with reduced performance. For
        maximum performance with large datasets, ensure sufficient GPU memory.
    """
    
    def __init__(self, dimension: int = 768, use_gpu: bool = True):
        """Initialize the vector store.
        
        Args:
            dimension (int): Dimension of the vectors
            use_gpu (bool): Whether to use GPU if available
        """
        self.dimension = dimension
        self.use_gpu = use_gpu and torch.cuda.is_available()
        
        # Initialize empty storage
        self.vectors = None  # Will be a torch tensor
        self.documents = {}  # Will store document metadata
        self.doc_ids = []    # Will store document IDs in order
        
        if self.use_gpu:
            logger.info(f"TorchVectorStore using GPU: {torch.cuda.get_device_name(0)}")
        else:
            logger.info("TorchVectorStore using CPU")
            
    def add(self, vectors: np.ndarray, doc_ids: List[str], texts: List[str]):
        """Add vectors and associated documents to the store.
        
        Args:
            vectors: Numpy array of shape (n, dimension)
            doc_ids: List of document IDs
            texts: List of document texts
        """
        if len(vectors) != len(doc_ids) or len(vectors) != len(texts):
            raise ValueError("Length of vectors, doc_ids, and texts must match")
            
        # Convert to torch tensor
        vectors_tensor = torch.from_numpy(vectors.astype('float32'))
        
        # Move to GPU if needed
        if self.use_gpu:
            vectors_tensor = vectors_tensor.cuda()
            
        # Initialize or append to existing storage
        if self.vectors is None:
            self.vectors = vectors_tensor
        else:
            current_device = self.vectors.device
            vectors_tensor = vectors_tensor.to(current_device)
            self.vectors = torch.cat([self.vectors, vectors_tensor], dim=0)
            
        # Store document metadata
        for doc_id, text in zip(doc_ids, texts):
            self.documents[doc_id] = text
            self.doc_ids.append(doc_id)
            
        return len(doc_ids)
            
    def search(self, query_vector: np.ndarray, k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar vectors.
        
        Args:
            query_vector: Query vector of shape (dimension,) or (1, dimension)
            k: Number of results to return
            
        Returns:
            List of dictionaries with document ID, text, and score
        """
        if self.vectors is None or len(self.doc_ids) == 0:
            return []
            
        # Ensure query vector is correct shape
        if len(query_vector.shape) == 1:
            query_vector = query_vector.reshape(1, -1)
            
        # Convert to torch tensor
        query_tensor = torch.from_numpy(query_vector.astype('float32'))
        
        # Move to same device as vectors
        if self.vectors is not None:
            query_tensor = query_tensor.to(self.vectors.device)
        elif self.use_gpu:
            query_tensor = query_tensor.cuda()
            
        # Compute L2 distances (or other similarity)
        with torch.no_grad():
            # Normalize vectors for cosine similarity
            query_norm = torch.nn.functional.normalize(query_tensor, p=2, dim=1)
            vectors_norm = torch.nn.functional.normalize(self.vectors, p=2, dim=1)
            
            # Compute cosine similarity (dot product of normalized vectors)
            similarities = torch.mm(query_norm, vectors_norm.t())[0]
            
            # Get top-k results
            if k > similarities.shape[0]:
                k = similarities.shape[0]
                
            scores, indices = torch.topk(similarities, k)
            
        # Gather results
        results = []
        for score, idx in zip(scores.cpu().numpy(), indices.cpu().numpy()):
            doc_id = self.doc_ids[idx]
            results.append({
                "id": doc_id,
                "text": self.documents[doc_id],
                "score": float(score)
            })
            
        return results
        
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information about the vector store.
        
        Returns:
            Dictionary with system information
        """
        return {
            "type": "torch_vector_store",
            "dimension": self.dimension,
            "num_vectors": 0 if self.vectors is None else self.vectors.shape[0],
            "num_documents": len(self.documents),
            "gpu_enabled": self.use_gpu,
            "gpu_available": torch.cuda.is_available(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
            "backend": "pytorch"
        }
