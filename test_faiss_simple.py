"""
Simple FAISS-GPU test script
"""
import sys
import os
import time

print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")

try:
    import faiss
    print(f"FAISS version: {faiss.__version__}")
    print(f"FAISS GPU support: {hasattr(faiss, 'StandardGpuResources')}")
    
    # Test CPU index
    dimension = 64
    nb = 10000
    nq = 1000  # Changed variable name from np to nq to avoid conflict with numpy import
    
    print("\nCreating test data...")
    import numpy as np  # Import numpy before using it
    xb = np.random.random((nb, dimension)).astype('float32')
    xq = np.random.random((nq, dimension)).astype('float32')
    
    print("Testing CPU index...")
    t0 = time.time()
    index = faiss.IndexFlatL2(dimension)
    index.add(xb)
    D1, I1 = index.search(xq, 5)
    t1 = time.time()
    print(f"CPU search time: {t1-t0:.4f} seconds")
    
    # Test GPU index if available
    if hasattr(faiss, 'StandardGpuResources'):
        try:
            print("\nTesting GPU index...")
            res = faiss.StandardGpuResources()
            gpu_index = faiss.index_cpu_to_gpu(res, 0, faiss.IndexFlatL2(dimension))
            gpu_index.add(xb)
            
            t0 = time.time()
            D2, I2 = gpu_index.search(xq, 5)
            t1 = time.time()
            print(f"GPU search time: {t1-t0:.4f} seconds")
            
            # Compare results
            if np.allclose(D1, D2):
                print("\n✅ CPU and GPU results match")
            else:
                print("\n⚠️ CPU and GPU results differ slightly (may be due to floating point precision)")
            
            print("\n✅ FAISS-GPU IS WORKING CORRECTLY!")
        except Exception as e:
            print(f"\n❌ GPU test failed: {e}")
    else:
        print("\n❌ FAISS does not have GPU support")
    
except ImportError as e:
    print(f"❌ Failed to import FAISS: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
