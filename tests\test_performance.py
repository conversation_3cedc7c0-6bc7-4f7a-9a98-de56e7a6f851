"""Performance tests for the Neural Symbolic Language Model.

This test suite provides performance benchmarks and load testing for
critical components of the system.
"""

import asyncio
import time
import unittest
import statistics
from concurrent.futures import ThreadPoolExecutor
from fastapi.testclient import TestClient

# Import the modules under test
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from symbolic_reasoning import SymbolicReasoner
from retrieval import Retriever
from main import app


class TestPerformance(unittest.TestCase):
    """Performance test suite."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.reasoner = SymbolicReasoner(engine="local", use_gpu=False)
        self.retriever = Retriever(use_gpu=False, embedding_backend="random")
        self.client = TestClient(app)
    
    def test_symbolic_reasoning_performance(self):
        """Test symbolic reasoning performance."""
        query = "What is the relationship between artificial intelligence and machine learning?"
        
        # Measure multiple runs
        times = []
        for _ in range(10):
            start_time = time.time()
            
            # Run the async function in a sync context for testing
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self.reasoner.process_query(query))
                end_time = time.time()
                
                # Verify result is valid
                self.assertIsInstance(result, str)
                self.assertGreater(len(result), 0)
                
                times.append(end_time - start_time)
            finally:
                loop.close()
        
        # Performance assertions
        avg_time = statistics.mean(times)
        max_time = max(times)
        
        print(f"Symbolic Reasoning Performance:")
        print(f"  Average time: {avg_time:.3f}s")
        print(f"  Max time: {max_time:.3f}s")
        print(f"  Min time: {min(times):.3f}s")
        
        # Performance requirements
        self.assertLess(avg_time, 2.0, "Average reasoning time should be under 2 seconds")
        self.assertLess(max_time, 5.0, "Max reasoning time should be under 5 seconds")
    
    def test_retrieval_performance(self):
        """Test document retrieval performance."""
        # Add some test documents
        test_docs = [
            "Artificial intelligence is a field of computer science.",
            "Machine learning is a subset of artificial intelligence.",
            "Neural networks are inspired by biological neural networks.",
            "Deep learning uses multiple layers of neural networks.",
            "Natural language processing deals with human language."
        ]
        
        for i, doc in enumerate(test_docs):
            self.retriever.add_document(doc, f"doc_{i}")
        
        query = "What is machine learning?"
        
        # Measure multiple runs
        times = []
        for _ in range(20):
            start_time = time.time()
            
            # Run the async function in a sync context for testing
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                results = loop.run_until_complete(self.retriever.search(query, k=3))
                end_time = time.time()
                
                # Verify results are valid
                self.assertIsInstance(results, list)
                self.assertGreater(len(results), 0)
                
                times.append(end_time - start_time)
            finally:
                loop.close()
        
        # Performance assertions
        avg_time = statistics.mean(times)
        max_time = max(times)
        
        print(f"Retrieval Performance:")
        print(f"  Average time: {avg_time:.3f}s")
        print(f"  Max time: {max_time:.3f}s")
        print(f"  Min time: {min(times):.3f}s")
        
        # Performance requirements
        self.assertLess(avg_time, 0.5, "Average retrieval time should be under 0.5 seconds")
        self.assertLess(max_time, 1.0, "Max retrieval time should be under 1 second")
    
    def test_api_endpoint_performance(self):
        """Test API endpoint performance."""
        payload = {
            "model": "local",
            "messages": [
                {"role": "user", "content": "What is symbolic reasoning?"}
            ]
        }
        
        # Measure multiple API calls
        times = []
        for _ in range(5):  # Fewer iterations for API tests
            start_time = time.time()
            
            response = self.client.post("/v1/chat/completions", json=payload)
            end_time = time.time()
            
            # Verify response is valid
            self.assertEqual(response.status_code, 200)
            data = response.json()
            self.assertIn("choices", data)
            
            times.append(end_time - start_time)
        
        # Performance assertions
        avg_time = statistics.mean(times)
        max_time = max(times)
        
        print(f"API Endpoint Performance:")
        print(f"  Average time: {avg_time:.3f}s")
        print(f"  Max time: {max_time:.3f}s")
        print(f"  Min time: {min(times):.3f}s")
        
        # Performance requirements
        self.assertLess(avg_time, 5.0, "Average API response time should be under 5 seconds")
        self.assertLess(max_time, 10.0, "Max API response time should be under 10 seconds")
    
    def test_concurrent_requests_performance(self):
        """Test performance under concurrent load."""
        payload = {
            "model": "local",
            "messages": [
                {"role": "user", "content": "Test concurrent request"}
            ]
        }
        
        def make_request():
            """Make a single API request."""
            start_time = time.time()
            response = self.client.post("/v1/chat/completions", json=payload)
            end_time = time.time()
            
            return {
                "status_code": response.status_code,
                "duration": end_time - start_time,
                "success": response.status_code == 200
            }
        
        # Test with multiple concurrent requests
        num_concurrent = 5
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [executor.submit(make_request) for _ in range(num_concurrent)]
            results = [future.result() for future in futures]
        end_time = time.time()
        
        total_time = end_time - start_time
        
        # Analyze results
        successful_requests = sum(1 for r in results if r["success"])
        avg_duration = statistics.mean([r["duration"] for r in results])
        max_duration = max([r["duration"] for r in results])
        
        print(f"Concurrent Requests Performance:")
        print(f"  Total time for {num_concurrent} requests: {total_time:.3f}s")
        print(f"  Successful requests: {successful_requests}/{num_concurrent}")
        print(f"  Average request duration: {avg_duration:.3f}s")
        print(f"  Max request duration: {max_duration:.3f}s")
        
        # Performance assertions
        self.assertEqual(successful_requests, num_concurrent, "All concurrent requests should succeed")
        self.assertLess(total_time, 30.0, "Concurrent requests should complete within 30 seconds")
        self.assertLess(avg_duration, 10.0, "Average concurrent request time should be under 10 seconds")
    
    def test_memory_usage_stability(self):
        """Test memory usage stability over multiple operations."""
        import psutil
        import gc
        
        process = psutil.Process()
        
        # Get initial memory usage
        gc.collect()  # Force garbage collection
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform multiple operations
        for i in range(20):
            query = f"Test query number {i} for memory stability testing"
            
            # Run reasoning
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self.reasoner.process_query(query))
                self.assertIsInstance(result, str)
            finally:
                loop.close()
            
            # Add and search documents
            self.retriever.add_document(f"Document {i} content", f"doc_{i}")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                search_results = loop.run_until_complete(self.retriever.search(query, k=2))
                self.assertIsInstance(search_results, list)
            finally:
                loop.close()
        
        # Get final memory usage
        gc.collect()  # Force garbage collection
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_increase = final_memory - initial_memory
        
        print(f"Memory Usage Stability:")
        print(f"  Initial memory: {initial_memory:.2f} MB")
        print(f"  Final memory: {final_memory:.2f} MB")
        print(f"  Memory increase: {memory_increase:.2f} MB")
        
        # Memory stability assertion
        self.assertLess(memory_increase, 100.0, "Memory increase should be less than 100 MB")
    
    def test_cache_performance(self):
        """Test caching performance improvement."""
        payload = {
            "model": "local",
            "messages": [
                {"role": "user", "content": "What is caching performance?"}
            ]
        }
        
        # First request (no cache)
        start_time = time.time()
        response1 = self.client.post("/v1/chat/completions", json=payload)
        first_request_time = time.time() - start_time
        
        self.assertEqual(response1.status_code, 200)
        
        # Second request (should be cached)
        start_time = time.time()
        response2 = self.client.post("/v1/chat/completions", json=payload)
        second_request_time = time.time() - start_time
        
        self.assertEqual(response2.status_code, 200)
        
        print(f"Cache Performance:")
        print(f"  First request time: {first_request_time:.3f}s")
        print(f"  Second request time: {second_request_time:.3f}s")
        print(f"  Speed improvement: {first_request_time / second_request_time:.2f}x")
        
        # Cache should improve performance
        self.assertLess(second_request_time, first_request_time, 
                       "Cached request should be faster than first request")


if __name__ == '__main__':
    unittest.main(verbosity=2)
