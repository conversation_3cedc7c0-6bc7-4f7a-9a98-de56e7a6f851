

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Security Module &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Data Models Module" href="models.html" />
    <link rel="prev" title="Vector Retrieval Module" href="retrieval.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../modules.html">API Reference</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="../modules.html#core-modules">Core Modules</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="main.html">Main Application Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">Security Module</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#security.SecurityManager"><code class="docutils literal notranslate"><span class="pre">SecurityManager</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#security.verify_api_key"><code class="docutils literal notranslate"><span class="pre">verify_api_key()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#security.check_request_size"><code class="docutils literal notranslate"><span class="pre">check_request_size()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#security.get_security_headers"><code class="docutils literal notranslate"><span class="pre">get_security_headers()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#security.RateLimiter"><code class="docutils literal notranslate"><span class="pre">RateLimiter</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#security.get_client_ip"><code class="docutils literal notranslate"><span class="pre">get_client_ip()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#security.get_cors_config"><code class="docutils literal notranslate"><span class="pre">get_cors_config()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#security.validate_cors_origin"><code class="docutils literal notranslate"><span class="pre">validate_cors_origin()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="#securitymanager-class">SecurityManager Class</a></li>
<li class="toctree-l4"><a class="reference internal" href="#authentication">Authentication</a></li>
<li class="toctree-l4"><a class="reference internal" href="#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l4"><a class="reference internal" href="#input-validation">Input Validation</a></li>
<li class="toctree-l4"><a class="reference internal" href="#cors-configuration">CORS Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#security-headers">Security Headers</a></li>
<li class="toctree-l4"><a class="reference internal" href="#ip-blocking">IP Blocking</a></li>
<li class="toctree-l4"><a class="reference internal" href="#example-usage">Example Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l4"><a class="reference internal" href="#best-practices">Best Practices</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="models.html">Data Models Module</a></li>
<li class="toctree-l3"><a class="reference internal" href="../modules.html#module-main">Core Components</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#utility-modules">Utility Modules</a></li>
<li class="toctree-l2"><a class="reference internal" href="../modules.html#api-routes">API Routes</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Security Module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#security.SecurityManager"><code class="docutils literal notranslate"><span class="pre">SecurityManager</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#security.SecurityManager.__init__"><code class="docutils literal notranslate"><span class="pre">SecurityManager.__init__()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#security.SecurityManager.validate_api_key"><code class="docutils literal notranslate"><span class="pre">SecurityManager.validate_api_key()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#security.SecurityManager.is_ip_blocked"><code class="docutils literal notranslate"><span class="pre">SecurityManager.is_ip_blocked()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#security.SecurityManager.record_failed_attempt"><code class="docutils literal notranslate"><span class="pre">SecurityManager.record_failed_attempt()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#security.SecurityManager.sanitize_input"><code class="docutils literal notranslate"><span class="pre">SecurityManager.sanitize_input()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security.verify_api_key"><code class="docutils literal notranslate"><span class="pre">verify_api_key()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#security.check_request_size"><code class="docutils literal notranslate"><span class="pre">check_request_size()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#security.get_security_headers"><code class="docutils literal notranslate"><span class="pre">get_security_headers()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#security.RateLimiter"><code class="docutils literal notranslate"><span class="pre">RateLimiter</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="#security.RateLimiter.__init__"><code class="docutils literal notranslate"><span class="pre">RateLimiter.__init__()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#security.RateLimiter.is_allowed"><code class="docutils literal notranslate"><span class="pre">RateLimiter.is_allowed()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security.get_client_ip"><code class="docutils literal notranslate"><span class="pre">get_client_ip()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#security.get_cors_config"><code class="docutils literal notranslate"><span class="pre">get_cors_config()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#security.validate_cors_origin"><code class="docutils literal notranslate"><span class="pre">validate_cors_origin()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#securitymanager-class">SecurityManager Class</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id0"><code class="docutils literal notranslate"><span class="pre">SecurityManager</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id1"><code class="docutils literal notranslate"><span class="pre">SecurityManager.__init__()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2"><code class="docutils literal notranslate"><span class="pre">SecurityManager.validate_api_key()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3"><code class="docutils literal notranslate"><span class="pre">SecurityManager.is_ip_blocked()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4"><code class="docutils literal notranslate"><span class="pre">SecurityManager.record_failed_attempt()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5"><code class="docutils literal notranslate"><span class="pre">SecurityManager.sanitize_input()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#initialization">Initialization</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id6"><code class="docutils literal notranslate"><span class="pre">SecurityManager.__init__()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#core-methods">Core Methods</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id7"><code class="docutils literal notranslate"><span class="pre">SecurityManager.validate_api_key()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id8"><code class="docutils literal notranslate"><span class="pre">SecurityManager.is_ip_blocked()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id9"><code class="docutils literal notranslate"><span class="pre">SecurityManager.record_failed_attempt()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#id10"><code class="docutils literal notranslate"><span class="pre">SecurityManager.sanitize_input()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#authentication">Authentication</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#api-key-authentication">API Key Authentication</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#rate-limiting">Rate Limiting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#ratelimiter-class">RateLimiter Class</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id11"><code class="docutils literal notranslate"><span class="pre">RateLimiter</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#input-validation">Input Validation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#input-sanitization">Input Sanitization</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id14"><code class="docutils literal notranslate"><span class="pre">sanitize_input()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#request-size-limits">Request Size Limits</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id15"><code class="docutils literal notranslate"><span class="pre">check_request_size()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#cors-configuration">CORS Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id16"><code class="docutils literal notranslate"><span class="pre">get_cors_config()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="#origin-validation">Origin Validation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id17"><code class="docutils literal notranslate"><span class="pre">validate_cors_origin()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security-headers">Security Headers</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id18"><code class="docutils literal notranslate"><span class="pre">get_security_headers()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#ip-blocking">IP Blocking</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#failed-attempt-tracking">Failed Attempt Tracking</a></li>
<li class="toctree-l3"><a class="reference internal" href="#client-ip-detection">Client IP Detection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id19"><code class="docutils literal notranslate"><span class="pre">get_client_ip()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#complete-security-setup">Complete Security Setup</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#api-key-management">API Key Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id20">Rate Limiting</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id21">Input Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id22">CORS Configuration</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../modules.html">API Reference</a></li>
      <li class="breadcrumb-item active">Security Module</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api/security.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="module-security">
<span id="security-module"></span><h1>Security Module<a class="headerlink" href="#module-security" title="Link to this heading"></a></h1>
<p>Security module for the Neural Symbolic Language Model.</p>
<p>This module provides authentication, authorization, rate limiting, and other
security features for the FastAPI application.</p>
<p>Author: AI Assistant
Date: 2025-06-29</p>
<dl class="py class">
<dt class="sig sig-object py" id="security.SecurityManager">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">SecurityManager</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">api_keys</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/security.html#SecurityManager"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.SecurityManager" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Manages security features for the application.</p>
<dl class="py method">
<dt class="sig sig-object py" id="security.SecurityManager.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">api_keys</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/security.html#SecurityManager.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.SecurityManager.__init__" title="Link to this definition"></a></dt>
<dd><p>Initialize the security manager.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>api_keys</strong> – Dictionary mapping API key names to their values</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="security.SecurityManager.validate_api_key">
<span class="sig-name descname"><span class="pre">validate_api_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">api_key</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.validate_api_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.SecurityManager.validate_api_key" title="Link to this definition"></a></dt>
<dd><p>Validate an API key.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>api_key</strong> – The API key to validate</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if valid, False otherwise</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="security.SecurityManager.is_ip_blocked">
<span class="sig-name descname"><span class="pre">is_ip_blocked</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ip_address</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.is_ip_blocked"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.SecurityManager.is_ip_blocked" title="Link to this definition"></a></dt>
<dd><p>Check if an IP address is blocked.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>ip_address</strong> – The IP address to check</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if blocked, False otherwise</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="security.SecurityManager.record_failed_attempt">
<span class="sig-name descname"><span class="pre">record_failed_attempt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ip_address</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.record_failed_attempt"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.SecurityManager.record_failed_attempt" title="Link to this definition"></a></dt>
<dd><p>Record a failed authentication attempt.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>ip_address</strong> – The IP address that failed authentication</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="security.SecurityManager.sanitize_input">
<span class="sig-name descname"><span class="pre">sanitize_input</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_length</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">10000</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.sanitize_input"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.SecurityManager.sanitize_input" title="Link to this definition"></a></dt>
<dd><p>Sanitize user input text.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>text</strong> – The input text to sanitize</p></li>
<li><p><strong>max_length</strong> – Maximum allowed length</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Sanitized text</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – If input is invalid</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="security.verify_api_key">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">verify_api_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">credentials:</span> <span class="pre">~fastapi.security.http.HTTPAuthorizationCredentials</span> <span class="pre">=</span> <span class="pre">&lt;fastapi.security.http.HTTPBearer</span> <span class="pre">object&gt;</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/security.html#verify_api_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.verify_api_key" title="Link to this definition"></a></dt>
<dd><p>Verify API key from Authorization header.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>credentials</strong> – HTTP authorization credentials</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The validated API key</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>HTTPException</strong> – If authentication fails</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="security.check_request_size">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">check_request_size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><a class="reference internal" href="../_modules/security.html#check_request_size"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.check_request_size" title="Link to this definition"></a></dt>
<dd><p>Check if request size is within limits.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>request</strong> – The FastAPI request object</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>HTTPException</strong> – If request is too large</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="security.get_security_headers">
<span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">get_security_headers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/security.html#get_security_headers"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.get_security_headers" title="Link to this definition"></a></dt>
<dd><p>Get security headers to add to responses.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Dictionary of security headers</p>
</dd>
</dl>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="security.RateLimiter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">RateLimiter</span></span><a class="reference internal" href="../_modules/security.html#RateLimiter"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.RateLimiter" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Simple in-memory rate limiter.</p>
<dl class="py method">
<dt class="sig sig-object py" id="security.RateLimiter.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/security.html#RateLimiter.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.RateLimiter.__init__" title="Link to this definition"></a></dt>
<dd><p>Initialize the rate limiter.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="security.RateLimiter.is_allowed">
<span class="sig-name descname"><span class="pre">is_allowed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">identifier</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">window</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">60</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#RateLimiter.is_allowed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.RateLimiter.is_allowed" title="Link to this definition"></a></dt>
<dd><p>Check if a request is allowed based on rate limits.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>identifier</strong> – Unique identifier (e.g., IP address)</p></li>
<li><p><strong>limit</strong> – Maximum number of requests allowed</p></li>
<li><p><strong>window</strong> – Time window in seconds</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if request is allowed, False otherwise</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="security.get_client_ip">
<span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">get_client_ip</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/security.html#get_client_ip"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.get_client_ip" title="Link to this definition"></a></dt>
<dd><p>Get client IP address from request.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>request</strong> – The FastAPI request object</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Client IP address</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="security.get_cors_config">
<span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">get_cors_config</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><span class="pre">Any</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/security.html#get_cors_config"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.get_cors_config" title="Link to this definition"></a></dt>
<dd><p>Get CORS (Cross-Origin Resource Sharing) configuration.</p>
<p>This function provides CORS configuration for the FastAPI application,
allowing controlled cross-origin requests while maintaining security.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Dictionary containing CORS configuration parameters</p>
</dd>
</dl>
<p class="rubric">Example</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">cors_config</span> <span class="o">=</span> <span class="n">get_cors_config</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">cors_config</span><span class="p">[</span><span class="s1">&#39;allow_origins&#39;</span><span class="p">])</span>
<span class="go">[&#39;http://localhost:3000&#39;, &#39;https://yourdomain.com&#39;]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="security.validate_cors_origin">
<span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">validate_cors_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">allowed_origins</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#validate_cors_origin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#security.validate_cors_origin" title="Link to this definition"></a></dt>
<dd><p>Validate if an origin is allowed for CORS requests.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>origin</strong> – The origin to validate</p></li>
<li><p><strong>allowed_origins</strong> – List of allowed origins</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if origin is allowed, False otherwise</p>
</dd>
</dl>
<p class="rubric">Example</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">allowed</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;https://example.com&#39;</span><span class="p">,</span> <span class="s1">&#39;https://app.example.com&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">validate_cors_origin</span><span class="p">(</span><span class="s1">&#39;https://example.com&#39;</span><span class="p">,</span> <span class="n">allowed</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">validate_cors_origin</span><span class="p">(</span><span class="s1">&#39;https://malicious.com&#39;</span><span class="p">,</span> <span class="n">allowed</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
</dd></dl>

<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The security module provides comprehensive security features including authentication,
authorization, rate limiting, input validation, and CORS configuration.</p>
<p>Key Features:
* API key authentication with Bearer token support
* Rate limiting with configurable limits
* Input sanitization and validation
* IP blocking for failed attempts
* CORS configuration for cross-origin requests
* Security headers for enhanced protection</p>
</section>
<section id="securitymanager-class">
<h2>SecurityManager Class<a class="headerlink" href="#securitymanager-class" title="Link to this heading"></a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="id0">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">SecurityManager</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">api_keys</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/security.html#SecurityManager"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id0" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Manages security features for the application.</p>
<dl class="py method">
<dt class="sig sig-object py" id="id1">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">api_keys</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/security.html#SecurityManager.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id1" title="Link to this definition"></a></dt>
<dd><p>Initialize the security manager.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>api_keys</strong> – Dictionary mapping API key names to their values</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id2">
<span class="sig-name descname"><span class="pre">validate_api_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">api_key</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.validate_api_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id2" title="Link to this definition"></a></dt>
<dd><p>Validate an API key.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>api_key</strong> – The API key to validate</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if valid, False otherwise</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id3">
<span class="sig-name descname"><span class="pre">is_ip_blocked</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ip_address</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.is_ip_blocked"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id3" title="Link to this definition"></a></dt>
<dd><p>Check if an IP address is blocked.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>ip_address</strong> – The IP address to check</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if blocked, False otherwise</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id4">
<span class="sig-name descname"><span class="pre">record_failed_attempt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ip_address</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.record_failed_attempt"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id4" title="Link to this definition"></a></dt>
<dd><p>Record a failed authentication attempt.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>ip_address</strong> – The IP address that failed authentication</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id5">
<span class="sig-name descname"><span class="pre">sanitize_input</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_length</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">10000</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.sanitize_input"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id5" title="Link to this definition"></a></dt>
<dd><p>Sanitize user input text.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>text</strong> – The input text to sanitize</p></li>
<li><p><strong>max_length</strong> – Maximum allowed length</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Sanitized text</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – If input is invalid</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<p>The main class for managing security operations.</p>
<section id="initialization">
<h3>Initialization<a class="headerlink" href="#initialization" title="Link to this heading"></a></h3>
<dl class="py method">
<dt class="sig sig-object py" id="id6">
<span class="sig-prename descclassname"><span class="pre">SecurityManager.</span></span><span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">api_keys</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/security.html#SecurityManager.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id6" title="Link to this definition"></a></dt>
<dd><p>Initialize the security manager.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>api_keys</strong> – Dictionary mapping API key names to their values</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="core-methods">
<h3>Core Methods<a class="headerlink" href="#core-methods" title="Link to this heading"></a></h3>
<dl class="py method">
<dt class="sig sig-object py" id="id7">
<span class="sig-prename descclassname"><span class="pre">SecurityManager.</span></span><span class="sig-name descname"><span class="pre">validate_api_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">api_key</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.validate_api_key"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id7" title="Link to this definition"></a></dt>
<dd><p>Validate an API key.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>api_key</strong> – The API key to validate</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if valid, False otherwise</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id8">
<span class="sig-prename descclassname"><span class="pre">SecurityManager.</span></span><span class="sig-name descname"><span class="pre">is_ip_blocked</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ip_address</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.is_ip_blocked"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id8" title="Link to this definition"></a></dt>
<dd><p>Check if an IP address is blocked.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>ip_address</strong> – The IP address to check</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if blocked, False otherwise</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id9">
<span class="sig-prename descclassname"><span class="pre">SecurityManager.</span></span><span class="sig-name descname"><span class="pre">record_failed_attempt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ip_address</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.record_failed_attempt"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id9" title="Link to this definition"></a></dt>
<dd><p>Record a failed authentication attempt.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>ip_address</strong> – The IP address that failed authentication</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id10">
<span class="sig-prename descclassname"><span class="pre">SecurityManager.</span></span><span class="sig-name descname"><span class="pre">sanitize_input</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_length</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">10000</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/security.html#SecurityManager.sanitize_input"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id10" title="Link to this definition"></a></dt>
<dd><p>Sanitize user input text.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>text</strong> – The input text to sanitize</p></li>
<li><p><strong>max_length</strong> – Maximum allowed length</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Sanitized text</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – If input is invalid</p>
</dd>
</dl>
</dd></dl>

</section>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<section id="api-key-authentication">
<h3>API Key Authentication<a class="headerlink" href="#api-key-authentication" title="Link to this heading"></a></h3>
<p>The system uses API key authentication with Bearer tokens:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">verify_api_key</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">Depends</span>

<span class="nd">@app</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/protected&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">protected_endpoint</span><span class="p">(</span><span class="n">api_key</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">verify_api_key</span><span class="p">)):</span>
    <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;Access granted&quot;</span><span class="p">,</span> <span class="s2">&quot;api_key&quot;</span><span class="p">:</span> <span class="n">api_key</span><span class="p">}</span>
</pre></div>
</div>
<p>Configuration:</p>
<ul class="simple">
<li><p>API keys are configured via environment variables</p></li>
<li><p>Keys are validated using constant-time comparison</p></li>
<li><p>Failed attempts are tracked and can trigger IP blocking</p></li>
</ul>
</section>
</section>
<section id="rate-limiting">
<h2>Rate Limiting<a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<section id="ratelimiter-class">
<h3>RateLimiter Class<a class="headerlink" href="#ratelimiter-class" title="Link to this heading"></a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="id11">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">RateLimiter</span></span><a class="reference internal" href="../_modules/security.html#RateLimiter"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id11" title="Link to this definition"></a></dt>
<dd><p>Bases: <a class="reference external" href="https://docs.python.org/3/library/functions.html#object" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></p>
<p>Simple in-memory rate limiter.</p>
<dl class="py method">
<dt class="sig sig-object py" id="id12">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/security.html#RateLimiter.__init__"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id12" title="Link to this definition"></a></dt>
<dd><p>Initialize the rate limiter.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id13">
<span class="sig-name descname"><span class="pre">is_allowed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">identifier</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">100</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">window</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">60</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#RateLimiter.is_allowed"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id13" title="Link to this definition"></a></dt>
<dd><p>Check if a request is allowed based on rate limits.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>identifier</strong> – Unique identifier (e.g., IP address)</p></li>
<li><p><strong>limit</strong> – Maximum number of requests allowed</p></li>
<li><p><strong>window</strong> – Time window in seconds</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if request is allowed, False otherwise</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

<p>The rate limiter provides configurable request limiting:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">rate_limiter</span>

<span class="c1"># Check if request is allowed</span>
<span class="n">client_ip</span> <span class="o">=</span> <span class="s2">&quot;***********&quot;</span>
<span class="k">if</span> <span class="n">rate_limiter</span><span class="o">.</span><span class="n">is_allowed</span><span class="p">(</span><span class="n">client_ip</span><span class="p">,</span> <span class="n">limit</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">window</span><span class="o">=</span><span class="mi">60</span><span class="p">):</span>
    <span class="c1"># Process request</span>
    <span class="k">pass</span>
<span class="k">else</span><span class="p">:</span>
    <span class="c1"># Return rate limit error</span>
    <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">429</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Rate limit exceeded&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Configuration:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">limit</span></code>: Maximum requests per window (default: 100)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">window</span></code>: Time window in seconds (default: 60)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">identifier</span></code>: Unique identifier (usually IP address)</p></li>
</ul>
</section>
</section>
<section id="input-validation">
<h2>Input Validation<a class="headerlink" href="#input-validation" title="Link to this heading"></a></h2>
<section id="input-sanitization">
<h3>Input Sanitization<a class="headerlink" href="#input-sanitization" title="Link to this heading"></a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="id14">
<span class="sig-prename descclassname"><span class="pre">security.SecurityManager.</span></span><span class="sig-name descname"><span class="pre">sanitize_input</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_length</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">10000</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="headerlink" href="#id14" title="Link to this definition"></a></dt>
<dd><p>Sanitize user input text.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>text</strong> – The input text to sanitize</p></li>
<li><p><strong>max_length</strong> – Maximum allowed length</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Sanitized text</p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><strong>ValueError</strong></a> – If input is invalid</p>
</dd>
</dl>
</dd></dl>

<p>The system provides comprehensive input sanitization:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">security_manager</span>

<span class="c1"># Sanitize user input</span>
<span class="k">try</span><span class="p">:</span>
    <span class="n">clean_text</span> <span class="o">=</span> <span class="n">security_manager</span><span class="o">.</span><span class="n">sanitize_input</span><span class="p">(</span>
        <span class="n">user_input</span><span class="p">,</span>
        <span class="n">max_length</span><span class="o">=</span><span class="mi">10000</span>
    <span class="p">)</span>
<span class="k">except</span> <span class="ne">ValueError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="c1"># Handle validation error</span>
    <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;error&quot;</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)}</span>
</pre></div>
</div>
<p>Features:</p>
<ul class="simple">
<li><p>Removes null bytes and control characters</p></li>
<li><p>Enforces maximum length limits</p></li>
<li><p>Strips whitespace</p></li>
<li><p>Validates non-empty input after sanitization</p></li>
</ul>
</section>
<section id="request-size-limits">
<h3>Request Size Limits<a class="headerlink" href="#request-size-limits" title="Link to this heading"></a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="id15">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">check_request_size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span></span><a class="reference internal" href="../_modules/security.html#check_request_size"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id15" title="Link to this definition"></a></dt>
<dd><p>Check if request size is within limits.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>request</strong> – The FastAPI request object</p>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>HTTPException</strong> – If request is too large</p>
</dd>
</dl>
</dd></dl>

<p>Prevents large request attacks:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">check_request_size</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">Request</span><span class="p">,</span> <span class="n">Depends</span>

<span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/upload&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">upload_endpoint</span><span class="p">(</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span>
    <span class="n">_</span><span class="p">:</span> <span class="kc">None</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">check_request_size</span><span class="p">)</span>
<span class="p">):</span>
    <span class="c1"># Process upload</span>
    <span class="k">pass</span>
</pre></div>
</div>
</section>
</section>
<section id="cors-configuration">
<h2>CORS Configuration<a class="headerlink" href="#cors-configuration" title="Link to this heading"></a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="id16">
<span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">get_cors_config</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Any" title="(in Python v3.13)"><span class="pre">Any</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/security.html#get_cors_config"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id16" title="Link to this definition"></a></dt>
<dd><p>Get CORS (Cross-Origin Resource Sharing) configuration.</p>
<p>This function provides CORS configuration for the FastAPI application,
allowing controlled cross-origin requests while maintaining security.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Dictionary containing CORS configuration parameters</p>
</dd>
</dl>
<p class="rubric">Example</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">cors_config</span> <span class="o">=</span> <span class="n">get_cors_config</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">cors_config</span><span class="p">[</span><span class="s1">&#39;allow_origins&#39;</span><span class="p">])</span>
<span class="go">[&#39;http://localhost:3000&#39;, &#39;https://yourdomain.com&#39;]</span>
</pre></div>
</div>
</dd></dl>

<p>Provides secure cross-origin resource sharing:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_cors_config</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi.middleware.cors</span><span class="w"> </span><span class="kn">import</span> <span class="n">CORSMiddleware</span>

<span class="c1"># Configure CORS</span>
<span class="n">cors_config</span> <span class="o">=</span> <span class="n">get_cors_config</span><span class="p">()</span>
<span class="n">app</span><span class="o">.</span><span class="n">add_middleware</span><span class="p">(</span><span class="n">CORSMiddleware</span><span class="p">,</span> <span class="o">**</span><span class="n">cors_config</span><span class="p">)</span>
</pre></div>
</div>
<p>Features:</p>
<ul class="simple">
<li><p>Configurable allowed origins</p></li>
<li><p>Production-safe defaults</p></li>
<li><p>Credential support</p></li>
<li><p>Preflight request caching</p></li>
</ul>
<section id="origin-validation">
<h3>Origin Validation<a class="headerlink" href="#origin-validation" title="Link to this heading"></a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="id17">
<span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">validate_cors_origin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">origin</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">allowed_origins</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span></span><a class="reference internal" href="../_modules/security.html#validate_cors_origin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id17" title="Link to this definition"></a></dt>
<dd><p>Validate if an origin is allowed for CORS requests.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>origin</strong> – The origin to validate</p></li>
<li><p><strong>allowed_origins</strong> – List of allowed origins</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>True if origin is allowed, False otherwise</p>
</dd>
</dl>
<p class="rubric">Example</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">allowed</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;https://example.com&#39;</span><span class="p">,</span> <span class="s1">&#39;https://app.example.com&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">validate_cors_origin</span><span class="p">(</span><span class="s1">&#39;https://example.com&#39;</span><span class="p">,</span> <span class="n">allowed</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">validate_cors_origin</span><span class="p">(</span><span class="s1">&#39;https://malicious.com&#39;</span><span class="p">,</span> <span class="n">allowed</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
</dd></dl>

<p>Validates CORS origins:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">validate_cors_origin</span>

<span class="n">allowed_origins</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;https://example.com&quot;</span><span class="p">,</span> <span class="s2">&quot;https://app.example.com&quot;</span><span class="p">]</span>
<span class="n">origin</span> <span class="o">=</span> <span class="s2">&quot;https://example.com&quot;</span>

<span class="k">if</span> <span class="n">validate_cors_origin</span><span class="p">(</span><span class="n">origin</span><span class="p">,</span> <span class="n">allowed_origins</span><span class="p">):</span>
    <span class="c1"># Allow request</span>
    <span class="k">pass</span>
<span class="k">else</span><span class="p">:</span>
    <span class="c1"># Reject request</span>
    <span class="k">pass</span>
</pre></div>
</div>
</section>
</section>
<section id="security-headers">
<h2>Security Headers<a class="headerlink" href="#security-headers" title="Link to this heading"></a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="id18">
<span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">get_security_headers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.Dict" title="(in Python v3.13)"><span class="pre">Dict</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/security.html#get_security_headers"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id18" title="Link to this definition"></a></dt>
<dd><p>Get security headers to add to responses.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Dictionary of security headers</p>
</dd>
</dl>
</dd></dl>

<p>Provides comprehensive security headers:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_security_headers</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">Response</span>

<span class="nd">@app</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">root</span><span class="p">(</span><span class="n">response</span><span class="p">:</span> <span class="n">Response</span><span class="p">):</span>
    <span class="c1"># Add security headers</span>
    <span class="n">headers</span> <span class="o">=</span> <span class="n">get_security_headers</span><span class="p">()</span>
    <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">headers</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>

    <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;Hello World&quot;</span><span class="p">}</span>
</pre></div>
</div>
<p>Included headers:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">X-Content-Type-Options</span></code>: Prevents MIME type sniffing</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">X-Frame-Options</span></code>: Prevents clickjacking</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">X-XSS-Protection</span></code>: Enables XSS filtering</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Strict-Transport-Security</span></code>: Enforces HTTPS</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Content-Security-Policy</span></code>: Controls resource loading</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Referrer-Policy</span></code>: Controls referrer information</p></li>
</ul>
</section>
<section id="ip-blocking">
<h2>IP Blocking<a class="headerlink" href="#ip-blocking" title="Link to this heading"></a></h2>
<section id="failed-attempt-tracking">
<h3>Failed Attempt Tracking<a class="headerlink" href="#failed-attempt-tracking" title="Link to this heading"></a></h3>
<p>The system tracks failed authentication attempts:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">security_manager</span>

<span class="c1"># Record failed attempt</span>
<span class="n">client_ip</span> <span class="o">=</span> <span class="n">get_client_ip</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
<span class="n">security_manager</span><span class="o">.</span><span class="n">record_failed_attempt</span><span class="p">(</span><span class="n">client_ip</span><span class="p">)</span>

<span class="c1"># Check if IP is blocked</span>
<span class="k">if</span> <span class="n">security_manager</span><span class="o">.</span><span class="n">is_ip_blocked</span><span class="p">(</span><span class="n">client_ip</span><span class="p">):</span>
    <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="mi">403</span><span class="p">,</span>
        <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;IP address blocked due to repeated failed attempts&quot;</span>
    <span class="p">)</span>
</pre></div>
</div>
<p>Configuration:</p>
<ul class="simple">
<li><p>Maximum failed attempts: 5 (configurable)</p></li>
<li><p>Block duration: 1 hour (configurable)</p></li>
<li><p>Cleanup interval: 15 minutes</p></li>
</ul>
</section>
<section id="client-ip-detection">
<h3>Client IP Detection<a class="headerlink" href="#client-ip-detection" title="Link to this heading"></a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="id19">
<span class="sig-prename descclassname"><span class="pre">security.</span></span><span class="sig-name descname"><span class="pre">get_client_ip</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://fastapi.tiangolo.com/reference/request/#fastapi.Request" title="(in FastAPI v0.0.0)"><span class="pre">Request</span></a></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/security.html#get_client_ip"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id19" title="Link to this definition"></a></dt>
<dd><p>Get client IP address from request.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>request</strong> – The FastAPI request object</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Client IP address</p>
</dd>
</dl>
</dd></dl>

<p>Accurately detects client IP addresses:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_client_ip</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">Request</span>

<span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/login&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">login</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">):</span>
    <span class="n">client_ip</span> <span class="o">=</span> <span class="n">get_client_ip</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
    <span class="c1"># Use IP for rate limiting and blocking</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p>Supports:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">X-Forwarded-For</span></code> header (load balancers)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">X-Real-IP</span></code> header (reverse proxies)</p></li>
<li><p>Direct connection IP address</p></li>
</ul>
</section>
</section>
<section id="example-usage">
<h2>Example Usage<a class="headerlink" href="#example-usage" title="Link to this heading"></a></h2>
<section id="complete-security-setup">
<h3>Complete Security Setup<a class="headerlink" href="#complete-security-setup" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">FastAPI</span><span class="p">,</span> <span class="n">Request</span><span class="p">,</span> <span class="n">Depends</span><span class="p">,</span> <span class="n">HTTPException</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi.middleware.cors</span><span class="w"> </span><span class="kn">import</span> <span class="n">CORSMiddleware</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">verify_api_key</span><span class="p">,</span> <span class="n">check_request_size</span><span class="p">,</span> <span class="n">get_cors_config</span><span class="p">,</span>
    <span class="n">get_security_headers</span><span class="p">,</span> <span class="n">security_manager</span><span class="p">,</span> <span class="n">rate_limiter</span><span class="p">,</span>
    <span class="n">get_client_ip</span>
<span class="p">)</span>

<span class="n">app</span> <span class="o">=</span> <span class="n">FastAPI</span><span class="p">()</span>

<span class="c1"># Configure CORS</span>
<span class="n">cors_config</span> <span class="o">=</span> <span class="n">get_cors_config</span><span class="p">()</span>
<span class="n">app</span><span class="o">.</span><span class="n">add_middleware</span><span class="p">(</span><span class="n">CORSMiddleware</span><span class="p">,</span> <span class="o">**</span><span class="n">cors_config</span><span class="p">)</span>

<span class="nd">@app</span><span class="o">.</span><span class="n">middleware</span><span class="p">(</span><span class="s2">&quot;http&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">security_middleware</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">call_next</span><span class="p">):</span>
    <span class="c1"># Get client IP</span>
    <span class="n">client_ip</span> <span class="o">=</span> <span class="n">get_client_ip</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>

    <span class="c1"># Check if IP is blocked</span>
    <span class="k">if</span> <span class="n">security_manager</span><span class="o">.</span><span class="n">is_ip_blocked</span><span class="p">(</span><span class="n">client_ip</span><span class="p">):</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">403</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;IP blocked&quot;</span><span class="p">)</span>

    <span class="c1"># Check rate limit</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">rate_limiter</span><span class="o">.</span><span class="n">is_allowed</span><span class="p">(</span><span class="n">client_ip</span><span class="p">):</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">429</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Rate limit exceeded&quot;</span><span class="p">)</span>

    <span class="c1"># Process request</span>
    <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">call_next</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>

    <span class="c1"># Add security headers</span>
    <span class="n">headers</span> <span class="o">=</span> <span class="n">get_security_headers</span><span class="p">()</span>
    <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">headers</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="n">response</span><span class="o">.</span><span class="n">headers</span><span class="p">[</span><span class="n">key</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>

    <span class="k">return</span> <span class="n">response</span>

<span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/api/chat&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">chat_endpoint</span><span class="p">(</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span>
    <span class="n">api_key</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">verify_api_key</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="kc">None</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">check_request_size</span><span class="p">)</span>
<span class="p">):</span>
    <span class="c1"># Secure endpoint implementation</span>
    <span class="k">pass</span>
</pre></div>
</div>
</section>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>The security module provides structured error responses:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">security</span><span class="w"> </span><span class="kn">import</span> <span class="n">security_manager</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">exceptions</span><span class="w"> </span><span class="kn">import</span> <span class="n">AuthenticationError</span><span class="p">,</span> <span class="n">RateLimitError</span>

<span class="k">try</span><span class="p">:</span>
    <span class="c1"># Validate API key</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">security_manager</span><span class="o">.</span><span class="n">validate_api_key</span><span class="p">(</span><span class="n">api_key</span><span class="p">):</span>
        <span class="k">raise</span> <span class="n">AuthenticationError</span><span class="p">(</span><span class="s2">&quot;Invalid API key&quot;</span><span class="p">)</span>

    <span class="c1"># Check rate limit</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">rate_limiter</span><span class="o">.</span><span class="n">is_allowed</span><span class="p">(</span><span class="n">client_ip</span><span class="p">):</span>
        <span class="k">raise</span> <span class="n">RateLimitError</span><span class="p">(</span><span class="s2">&quot;Rate limit exceeded&quot;</span><span class="p">,</span> <span class="n">retry_after</span><span class="o">=</span><span class="mi">60</span><span class="p">)</span>

<span class="k">except</span> <span class="n">AuthenticationError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="n">e</span><span class="o">.</span><span class="n">status_code</span><span class="p">,</span>
        <span class="n">content</span><span class="o">=</span><span class="n">e</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
    <span class="p">)</span>
</pre></div>
</div>
</section>
<section id="best-practices">
<h2>Best Practices<a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="api-key-management">
<h3>API Key Management<a class="headerlink" href="#api-key-management" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Use strong, randomly generated API keys</p></li>
<li><p>Rotate keys regularly</p></li>
<li><p>Store keys securely (environment variables, secrets manager)</p></li>
<li><p>Never log or expose keys in responses</p></li>
</ul>
</section>
<section id="id20">
<h3>Rate Limiting<a class="headerlink" href="#id20" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Set appropriate limits based on expected usage</p></li>
<li><p>Use different limits for different endpoints</p></li>
<li><p>Consider burst allowances for legitimate traffic</p></li>
<li><p>Monitor and adjust limits based on usage patterns</p></li>
</ul>
</section>
<section id="id21">
<h3>Input Validation<a class="headerlink" href="#id21" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Validate all user inputs</p></li>
<li><p>Use whitelist validation when possible</p></li>
<li><p>Sanitize inputs before processing</p></li>
<li><p>Log validation failures for monitoring</p></li>
</ul>
</section>
<section id="id22">
<h3>CORS Configuration<a class="headerlink" href="#id22" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Specify exact allowed origins in production</p></li>
<li><p>Avoid wildcards (*) in production environments</p></li>
<li><p>Regularly review and update allowed origins</p></li>
<li><p>Test CORS configuration thoroughly</p></li>
</ul>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="retrieval.html" class="btn btn-neutral float-left" title="Vector Retrieval Module" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="models.html" class="btn btn-neutral float-right" title="Data Models Module" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>