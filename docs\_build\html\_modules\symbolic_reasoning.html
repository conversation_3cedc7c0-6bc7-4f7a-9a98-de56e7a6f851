

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>symbolic_reasoning &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Module code</a></li>
      <li class="breadcrumb-item active">symbolic_reasoning</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for symbolic_reasoning</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Symbolic reasoning module for the Neural Symbolic Language Model.</span>

<span class="sd">This module provides symbolic reasoning capabilities with proper error handling,</span>
<span class="sd">logging, and performance monitoring.</span>

<span class="sd">Author: AI Assistant</span>
<span class="sd">Date: 2025-06-29</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">torch</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Any</span><span class="p">,</span> <span class="n">List</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">exceptions</span><span class="w"> </span><span class="kn">import</span> <span class="n">ReasoningError</span><span class="p">,</span> <span class="n">ConfigurationError</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>


<div class="viewcode-block" id="SymbolicReasoner">
<a class="viewcode-back" href="../modules.html#symbolic_reasoning.SymbolicReasoner">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">SymbolicReasoner</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Symbolic reasoning engine for neural-symbolic AI processing.</span>

<span class="sd">    This class provides symbolic reasoning capabilities that can be combined</span>
<span class="sd">    with neural networks for enhanced AI reasoning. It supports multiple</span>
<span class="sd">    reasoning engines and GPU acceleration for optimal performance.</span>

<span class="sd">    The symbolic reasoner processes queries using logical rules and symbolic</span>
<span class="sd">    manipulation, providing explainable AI capabilities that complement</span>
<span class="sd">    neural network predictions.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        use_gpu (bool): Whether GPU acceleration is enabled</span>
<span class="sd">        engine (str): The reasoning engine being used</span>
<span class="sd">        model (str): The specific model within the engine</span>

<span class="sd">    Example:</span>
<span class="sd">        &gt;&gt;&gt; reasoner = SymbolicReasoner(engine=&quot;local&quot;, use_gpu=True)</span>
<span class="sd">        &gt;&gt;&gt; response = reasoner.process_query(&quot;What is symbolic reasoning?&quot;)</span>
<span class="sd">        &gt;&gt;&gt; print(response)</span>
<span class="sd">        &quot;Symbolic reasoning involves manipulating symbols according to logical rules...&quot;</span>

<span class="sd">    Note:</span>
<span class="sd">        This implementation provides a foundation for symbolic reasoning.</span>
<span class="sd">        In production, this would integrate with more sophisticated</span>
<span class="sd">        symbolic AI frameworks and knowledge bases.</span>
<span class="sd">    &quot;&quot;&quot;</span>
<div class="viewcode-block" id="SymbolicReasoner.__init__">
<a class="viewcode-back" href="../modules.html#symbolic_reasoning.SymbolicReasoner.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">engine</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;local&quot;</span><span class="p">,</span> <span class="n">model</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;llama&quot;</span><span class="p">,</span> <span class="n">use_gpu</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the symbolic reasoning engine.</span>

<span class="sd">        Sets up the symbolic reasoning engine with the specified configuration.</span>
<span class="sd">        Automatically detects GPU availability and configures the engine accordingly.</span>

<span class="sd">        :param engine: The reasoning engine to use. Supported engines include:</span>
<span class="sd">                      - &quot;local&quot;: Local symbolic reasoning engine</span>
<span class="sd">                      - &quot;openai&quot;: OpenAI-based reasoning (requires API key)</span>
<span class="sd">                      - &quot;anthropic&quot;: Anthropic-based reasoning (requires API key)</span>
<span class="sd">        :type engine: str</span>
<span class="sd">        :param model: The specific model to use within the chosen engine.</span>
<span class="sd">                     For local engine, supports &quot;llama&quot;, &quot;gpt&quot;, etc.</span>
<span class="sd">        :type model: str</span>
<span class="sd">        :param use_gpu: Whether to enable GPU acceleration if available.</span>
<span class="sd">                       GPU acceleration significantly improves performance</span>
<span class="sd">                       for large-scale reasoning tasks.</span>
<span class="sd">        :type use_gpu: bool</span>

<span class="sd">        :raises ConfigurationError: If the engine configuration is invalid</span>
<span class="sd">                                   or required dependencies are missing</span>

<span class="sd">        :example:</span>

<span class="sd">        &gt;&gt;&gt; # Initialize with default local engine</span>
<span class="sd">        &gt;&gt;&gt; reasoner = SymbolicReasoner()</span>
<span class="sd">        &gt;&gt;&gt;</span>
<span class="sd">        &gt;&gt;&gt; # Initialize with specific configuration</span>
<span class="sd">        &gt;&gt;&gt; reasoner = SymbolicReasoner(</span>
<span class="sd">        ...     engine=&quot;local&quot;,</span>
<span class="sd">        ...     model=&quot;llama&quot;,</span>
<span class="sd">        ...     use_gpu=True</span>
<span class="sd">        ... )</span>
<span class="sd">        &gt;&gt;&gt;</span>
<span class="sd">        &gt;&gt;&gt; # Check if GPU is being used</span>
<span class="sd">        &gt;&gt;&gt; print(f&quot;GPU enabled: {reasoner.use_gpu}&quot;)</span>

<span class="sd">        .. note::</span>
<span class="sd">           GPU acceleration requires CUDA-compatible hardware and</span>
<span class="sd">           proper PyTorch installation with CUDA support.</span>

<span class="sd">        .. warning::</span>
<span class="sd">           Remote engines (OpenAI, Anthropic) require valid API keys</span>
<span class="sd">           and internet connectivity.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span> <span class="o">=</span> <span class="n">use_gpu</span> <span class="ow">and</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">()</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">engine</span> <span class="o">=</span> <span class="n">engine</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">model</span> <span class="o">=</span> <span class="n">model</span>

            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;SymbolicReasoner initialized with GPU: </span><span class="si">{</span><span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">get_device_name</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;SymbolicReasoner initialized with CPU&quot;</span><span class="p">)</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">ConfigurationError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Failed to initialize SymbolicReasoner: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>

        
<div class="viewcode-block" id="SymbolicReasoner.process_query">
<a class="viewcode-back" href="../modules.html#symbolic_reasoning.SymbolicReasoner.process_query">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">process_query</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">query</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">context</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Process a query using symbolic reasoning with optional context.</span>

<span class="sd">        This method performs symbolic reasoning on the input query, optionally</span>
<span class="sd">        incorporating additional context to improve reasoning accuracy. The</span>
<span class="sd">        reasoning process involves parsing the query, applying logical rules,</span>
<span class="sd">        and generating an explainable response.</span>

<span class="sd">        :param query: The natural language query to process. Must be a non-empty</span>
<span class="sd">                     string containing the question or statement to reason about.</span>
<span class="sd">                     Maximum length is 10,000 characters.</span>
<span class="sd">        :type query: str</span>
<span class="sd">        :param context: Optional additional context to inform the reasoning process.</span>
<span class="sd">                       This can include relevant facts, background information,</span>
<span class="sd">                       or previous conversation history. If provided, it will be</span>
<span class="sd">                       incorporated into the reasoning process.</span>
<span class="sd">        :type context: Optional[str]</span>

<span class="sd">        :returns: The reasoning engine&#39;s response as a natural language string.</span>
<span class="sd">                 The response includes the reasoning conclusion and may contain</span>
<span class="sd">                 explanations of the logical steps taken.</span>
<span class="sd">        :rtype: str</span>

<span class="sd">        :raises ReasoningError: If the reasoning process fails due to:</span>
<span class="sd">                               - Invalid logical structure in the query</span>
<span class="sd">                               - Engine-specific processing errors</span>
<span class="sd">                               - Resource limitations (memory, GPU)</span>
<span class="sd">        :raises ValidationError: If the input validation fails due to:</span>
<span class="sd">                                - Empty or None query</span>
<span class="sd">                                - Query exceeding maximum length</span>
<span class="sd">                                - Invalid character encoding</span>

<span class="sd">        :example:</span>

<span class="sd">        &gt;&gt;&gt; reasoner = SymbolicReasoner()</span>
<span class="sd">        &gt;&gt;&gt;</span>
<span class="sd">        &gt;&gt;&gt; # Simple reasoning query</span>
<span class="sd">        &gt;&gt;&gt; response = reasoner.process_query(&quot;What is symbolic reasoning?&quot;)</span>
<span class="sd">        &gt;&gt;&gt; print(response)</span>
<span class="sd">        &quot;Symbolic reasoning involves manipulating symbols according to logical rules...&quot;</span>
<span class="sd">        &gt;&gt;&gt;</span>
<span class="sd">        &gt;&gt;&gt; # Query with context</span>
<span class="sd">        &gt;&gt;&gt; context = &quot;We are discussing AI methodologies.&quot;</span>
<span class="sd">        &gt;&gt;&gt; response = reasoner.process_query(</span>
<span class="sd">        ...     &quot;How does it differ from neural networks?&quot;,</span>
<span class="sd">        ...     context=context</span>
<span class="sd">        ... )</span>
<span class="sd">        &gt;&gt;&gt; print(response)</span>
<span class="sd">        &quot;In the context of AI methodologies, symbolic reasoning differs from...&quot;</span>
<span class="sd">        &gt;&gt;&gt;</span>
<span class="sd">        &gt;&gt;&gt; # Logical reasoning</span>
<span class="sd">        &gt;&gt;&gt; response = reasoner.process_query(</span>
<span class="sd">        ...     &quot;If A implies B and B implies C, what can we conclude about A and C?&quot;</span>
<span class="sd">        ... )</span>
<span class="sd">        &gt;&gt;&gt; print(response)</span>
<span class="sd">        &quot;If A implies B and B implies C, then A implies C (transitive property)...&quot;</span>

<span class="sd">        .. note::</span>
<span class="sd">           The reasoning quality depends on the underlying engine and model.</span>
<span class="sd">           Local engines provide faster responses but may have limited</span>
<span class="sd">           reasoning capabilities compared to cloud-based engines.</span>

<span class="sd">        .. seealso::</span>
<span class="sd">           :meth:`get_system_info` for checking engine capabilities</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">query</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">query</span><span class="o">.</span><span class="n">strip</span><span class="p">():</span>
                <span class="k">raise</span> <span class="n">ReasoningError</span><span class="p">(</span><span class="s2">&quot;Query cannot be empty&quot;</span><span class="p">,</span> <span class="n">reasoning_type</span><span class="o">=</span><span class="s2">&quot;validation&quot;</span><span class="p">)</span>

            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Processing query: </span><span class="si">{</span><span class="n">query</span><span class="p">[:</span><span class="mi">100</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">context</span><span class="p">:</span>
                <span class="n">full_query</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">query</span><span class="si">}</span><span class="se">\n\n</span><span class="s2">Additional context: </span><span class="si">{</span><span class="n">context</span><span class="si">}</span><span class="s2">&quot;</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">full_query</span> <span class="o">=</span> <span class="n">query</span>

            <span class="c1"># Basic implementation for testing</span>
            <span class="k">if</span> <span class="s2">&quot;implies&quot;</span> <span class="ow">in</span> <span class="n">full_query</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
                <span class="n">response</span> <span class="o">=</span> <span class="s2">&quot;If A implies B and B implies C, then A implies C (transitive property of implication)&quot;</span>
            <span class="k">elif</span> <span class="s2">&quot;neural&quot;</span> <span class="ow">in</span> <span class="n">full_query</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span> <span class="ow">and</span> <span class="s2">&quot;symbolic&quot;</span> <span class="ow">in</span> <span class="n">full_query</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
                <span class="n">response</span> <span class="o">=</span> <span class="s2">&quot;Neural-symbolic AI combines the learning capabilities of neural networks with the reasoning capabilities of symbolic systems.&quot;</span>
            <span class="k">elif</span> <span class="s2">&quot;reasoning&quot;</span> <span class="ow">in</span> <span class="n">full_query</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
                <span class="n">response</span> <span class="o">=</span> <span class="s2">&quot;Symbolic reasoning involves manipulating symbols according to logical rules to derive conclusions.&quot;</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">response</span> <span class="o">=</span> <span class="s2">&quot;I understand your query. This is a placeholder response from the symbolic reasoning engine.&quot;</span>

            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Generated response: </span><span class="si">{</span><span class="n">response</span><span class="p">[:</span><span class="mi">100</span><span class="p">]</span><span class="si">}</span><span class="s2">...&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">response</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error in symbolic reasoning: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">e</span><span class="p">,</span> <span class="n">ReasoningError</span><span class="p">):</span>
                <span class="k">raise</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">ReasoningError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Symbolic reasoning failed: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">reasoning_type</span><span class="o">=</span><span class="s2">&quot;processing&quot;</span><span class="p">)</span></div>

        
<div class="viewcode-block" id="SymbolicReasoner.get_system_info">
<a class="viewcode-back" href="../modules.html#symbolic_reasoning.SymbolicReasoner.get_system_info">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_system_info</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get comprehensive information about the reasoning system configuration.</span>

<span class="sd">        Retrieves detailed information about the current reasoning engine</span>
<span class="sd">        configuration, including hardware capabilities, engine status,</span>
<span class="sd">        and operational parameters.</span>

<span class="sd">        :returns: A dictionary containing system configuration information with keys:</span>

<span class="sd">                 - **engine** (str): The reasoning engine name</span>
<span class="sd">                 - **model** (str): The model being used</span>
<span class="sd">                 - **gpu_enabled** (bool): Whether GPU acceleration is enabled</span>
<span class="sd">                 - **gpu_available** (bool): Whether GPU hardware is available</span>
<span class="sd">                 - **gpu_name** (str|None): Name of the GPU if available</span>
<span class="sd">                 - **status** (str): Current operational status (&quot;operational&quot;, &quot;error&quot;)</span>
<span class="sd">        :rtype: Dict[str, Any]</span>

<span class="sd">        :raises ReasoningError: If system information cannot be retrieved due to:</span>
<span class="sd">                               - Hardware detection failures</span>
<span class="sd">                               - Engine communication errors</span>
<span class="sd">                               - Permission or access issues</span>

<span class="sd">        :example:</span>

<span class="sd">        &gt;&gt;&gt; reasoner = SymbolicReasoner(use_gpu=True)</span>
<span class="sd">        &gt;&gt;&gt; info = reasoner.get_system_info()</span>
<span class="sd">        &gt;&gt;&gt; print(f&quot;Engine: {info[&#39;engine&#39;]}&quot;)</span>
<span class="sd">        Engine: local</span>
<span class="sd">        &gt;&gt;&gt; print(f&quot;GPU Available: {info[&#39;gpu_available&#39;]}&quot;)</span>
<span class="sd">        GPU Available: True</span>
<span class="sd">        &gt;&gt;&gt; print(f&quot;Status: {info[&#39;status&#39;]}&quot;)</span>
<span class="sd">        Status: operational</span>
<span class="sd">        &gt;&gt;&gt;</span>
<span class="sd">        &gt;&gt;&gt; # Check GPU details if available</span>
<span class="sd">        &gt;&gt;&gt; if info[&#39;gpu_available&#39;]:</span>
<span class="sd">        ...     print(f&quot;GPU: {info[&#39;gpu_name&#39;]}&quot;)</span>
<span class="sd">        GPU: NVIDIA GeForce RTX 4090</span>

<span class="sd">        .. note::</span>
<span class="sd">           GPU information is only available when CUDA is properly installed</span>
<span class="sd">           and compatible hardware is present.</span>

<span class="sd">        .. versionadded:: 0.1.0</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">return</span> <span class="p">{</span>
                <span class="s2">&quot;engine&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">engine</span><span class="p">,</span>
                <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="p">,</span>
                <span class="s2">&quot;gpu_enabled&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="p">,</span>
                <span class="s2">&quot;gpu_available&quot;</span><span class="p">:</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">(),</span>
                <span class="s2">&quot;gpu_name&quot;</span><span class="p">:</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">get_device_name</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span> <span class="k">if</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">()</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
                <span class="s2">&quot;status&quot;</span><span class="p">:</span> <span class="s2">&quot;operational&quot;</span>
            <span class="p">}</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting system info: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="k">raise</span> <span class="n">ReasoningError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Failed to get system info: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">reasoning_type</span><span class="o">=</span><span class="s2">&quot;system_info&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="SymbolicReasoner.batch_process_queries">
<a class="viewcode-back" href="../modules.html#symbolic_reasoning.SymbolicReasoner.batch_process_queries">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">batch_process_queries</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">queries</span><span class="p">,</span> <span class="n">contexts</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Process multiple queries in batch.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            queries (list): List of queries to process</span>
<span class="sd">            contexts (list, optional): List of contexts for each query</span>
<span class="sd">            </span>
<span class="sd">        Returns:</span>
<span class="sd">            list: List of responses for each query</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">responses</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="n">contexts</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">contexts</span> <span class="o">=</span> <span class="p">[</span><span class="kc">None</span><span class="p">]</span> <span class="o">*</span> <span class="nb">len</span><span class="p">(</span><span class="n">queries</span><span class="p">)</span>
        
        <span class="c1"># Process queries in parallel if possible</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span> <span class="ow">and</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">():</span>
            <span class="c1"># Use GPU for parallel processing</span>
            <span class="k">with</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">device</span><span class="p">(</span><span class="mi">0</span><span class="p">):</span>
                <span class="k">for</span> <span class="n">query</span><span class="p">,</span> <span class="n">context</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="n">queries</span><span class="p">,</span> <span class="n">contexts</span><span class="p">):</span>
                    <span class="n">responses</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="n">query</span><span class="p">,</span> <span class="n">context</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Fallback to sequential processing</span>
            <span class="k">for</span> <span class="n">query</span><span class="p">,</span> <span class="n">context</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="n">queries</span><span class="p">,</span> <span class="n">contexts</span><span class="p">):</span>
                <span class="n">responses</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">process_query</span><span class="p">(</span><span class="n">query</span><span class="p">,</span> <span class="n">context</span><span class="p">))</span>
        
        <span class="k">return</span> <span class="n">responses</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>