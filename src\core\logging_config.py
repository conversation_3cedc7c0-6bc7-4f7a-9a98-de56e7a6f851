"""Advanced logging configuration with structured logging and correlation IDs.

This module provides comprehensive logging functionality including:
- Structured JSON logging for production environments
- Correlation ID tracking for request tracing
- Log rotation and retention policies
- Performance metrics integration
- Distributed tracing support
"""

import logging
import logging.handlers
import json
import uuid
import time
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from contextvars import ContextVar

# Third-party imports
try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False

# Local imports
from .config import get_settings

# Context variable for correlation ID tracking
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class CorrelationIDFilter(logging.Filter):
    """Add correlation ID to log records."""
    
    def filter(self, record):
        """Add correlation ID to the log record."""
        record.correlation_id = correlation_id.get() or 'no-correlation-id'
        record.request_id = getattr(record, 'request_id', record.correlation_id)
        return True


class StructuredFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def __init__(self, include_extra: bool = True):
        """Initialize the structured formatter.
        
        Args:
            include_extra: Whether to include extra fields in the log record
        """
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """Format the log record as JSON.
        
        Args:
            record: The log record to format
            
        Returns:
            JSON-formatted log string
        """
        # Base log data
        log_data = {
            'timestamp': datetime.utcfromtimestamp(record.created).isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'correlation_id': getattr(record, 'correlation_id', 'no-correlation-id'),
            'thread_id': record.thread,
            'thread_name': record.threadName,
            'process_id': record.process,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }
        
        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {
                k: v for k, v in record.__dict__.items()
                if k not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'lineno', 'funcName', 'created',
                    'msecs', 'relativeCreated', 'thread', 'threadName',
                    'processName', 'process', 'getMessage', 'exc_info',
                    'exc_text', 'stack_info', 'correlation_id'
                }
            }
            if extra_fields:
                log_data['extra'] = extra_fields
        
        return json.dumps(log_data, default=str, ensure_ascii=False)


class PerformanceMetricsHandler(logging.Handler):
    """Custom handler for collecting performance metrics from logs."""
    
    def __init__(self):
        """Initialize the performance metrics handler."""
        super().__init__()
        self.metrics = {
            'error_count': 0,
            'warning_count': 0,
            'request_count': 0,
            'response_times': [],
            'last_reset': time.time()
        }
        self._lock = threading.Lock()
    
    def emit(self, record: logging.LogRecord):
        """Process log record and update metrics.
        
        Args:
            record: The log record to process
        """
        with self._lock:
            # Count log levels
            if record.levelno >= logging.ERROR:
                self.metrics['error_count'] += 1
            elif record.levelno >= logging.WARNING:
                self.metrics['warning_count'] += 1
            
            # Track request metrics
            if hasattr(record, 'request_duration'):
                self.metrics['request_count'] += 1
                self.metrics['response_times'].append(record.request_duration)
                
                # Keep only last 1000 response times
                if len(self.metrics['response_times']) > 1000:
                    self.metrics['response_times'] = self.metrics['response_times'][-1000:]
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics.
        
        Returns:
            Dictionary containing performance metrics
        """
        with self._lock:
            response_times = self.metrics['response_times']
            
            metrics = {
                'error_count': self.metrics['error_count'],
                'warning_count': self.metrics['warning_count'],
                'request_count': self.metrics['request_count'],
                'uptime_seconds': time.time() - self.metrics['last_reset']
            }
            
            if response_times:
                metrics.update({
                    'avg_response_time': sum(response_times) / len(response_times),
                    'min_response_time': min(response_times),
                    'max_response_time': max(response_times),
                    'p95_response_time': sorted(response_times)[int(len(response_times) * 0.95)]
                })
            
            return metrics
    
    def reset_metrics(self):
        """Reset all metrics counters."""
        with self._lock:
            self.metrics = {
                'error_count': 0,
                'warning_count': 0,
                'request_count': 0,
                'response_times': [],
                'last_reset': time.time()
            }


class LoggingManager:
    """Centralized logging configuration and management."""
    
    def __init__(self):
        """Initialize the logging manager."""
        self.settings = get_settings()
        self.performance_handler = PerformanceMetricsHandler()
        self._configured = False
    
    def configure_logging(self):
        """Configure application logging based on settings."""
        if self._configured:
            return
        
        # Get root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.settings.logging.level.upper()))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Configure console handler
        console_handler = logging.StreamHandler()
        
        if self.settings.logging.structured:
            console_formatter = StructuredFormatter()
        else:
            console_formatter = logging.Formatter(
                fmt=self.settings.logging.format,
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        console_handler.setFormatter(console_formatter)
        console_handler.addFilter(CorrelationIDFilter())
        root_logger.addHandler(console_handler)
        
        # Configure file handler if enabled
        if self.settings.logging.file_enabled:
            self._configure_file_logging()
        
        # Add performance metrics handler
        self.performance_handler.setLevel(logging.INFO)
        root_logger.addHandler(self.performance_handler)
        
        # Configure third-party loggers
        self._configure_third_party_loggers()
        
        self._configured = True
        
        # Log configuration completion
        logger = logging.getLogger(__name__)
        logger.info(
            "Logging configured",
            extra={
                'level': self.settings.logging.level,
                'structured': self.settings.logging.structured,
                'file_enabled': self.settings.logging.file_enabled
            }
        )
    
    def _configure_file_logging(self):
        """Configure file-based logging with rotation."""
        log_dir = Path(self.settings.logging.file_path)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / "neural_symbolic_lm.log"
        
        # Rotating file handler
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=self.settings.logging.max_file_size,
            backupCount=self.settings.logging.backup_count,
            encoding='utf-8'
        )
        
        if self.settings.logging.structured:
            file_formatter = StructuredFormatter()
        else:
            file_formatter = logging.Formatter(
                fmt=self.settings.logging.format,
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        file_handler.setFormatter(file_formatter)
        file_handler.addFilter(CorrelationIDFilter())
        
        # Add to root logger
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
    
    def _configure_third_party_loggers(self):
        """Configure third-party library loggers."""
        # Reduce noise from third-party libraries
        noisy_loggers = [
            'urllib3.connectionpool',
            'requests.packages.urllib3',
            'httpx',
            'httpcore',
            'ollama',
            'transformers',
            'torch'
        ]
        
        for logger_name in noisy_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.WARNING)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics from logging.
        
        Returns:
            Dictionary containing performance metrics
        """
        return self.performance_handler.get_metrics()
    
    def reset_performance_metrics(self):
        """Reset performance metrics counters."""
        self.performance_handler.reset_metrics()


def set_correlation_id(cid: Optional[str] = None) -> str:
    """Set correlation ID for the current context.
    
    Args:
        cid: Correlation ID to set, or None to generate a new one
        
    Returns:
        The correlation ID that was set
    """
    if cid is None:
        cid = str(uuid.uuid4())
    
    correlation_id.set(cid)
    return cid


def get_correlation_id() -> Optional[str]:
    """Get the current correlation ID.
    
    Returns:
        Current correlation ID or None if not set
    """
    return correlation_id.get()


def log_request_start(method: str, path: str, **kwargs):
    """Log the start of a request.
    
    Args:
        method: HTTP method
        path: Request path
        **kwargs: Additional context to log
    """
    logger = logging.getLogger('request')
    logger.info(
        f"Request started: {method} {path}",
        extra={
            'event': 'request_start',
            'method': method,
            'path': path,
            **kwargs
        }
    )


def log_request_end(method: str, path: str, status_code: int, duration: float, **kwargs):
    """Log the end of a request.
    
    Args:
        method: HTTP method
        path: Request path
        status_code: HTTP status code
        duration: Request duration in seconds
        **kwargs: Additional context to log
    """
    logger = logging.getLogger('request')
    logger.info(
        f"Request completed: {method} {path} - {status_code} ({duration:.3f}s)",
        extra={
            'event': 'request_end',
            'method': method,
            'path': path,
            'status_code': status_code,
            'request_duration': duration,
            **kwargs
        }
    )


def log_error_with_context(error: Exception, context: Dict[str, Any] = None):
    """Log an error with additional context.
    
    Args:
        error: The exception to log
        context: Additional context information
    """
    logger = logging.getLogger('error')
    logger.error(
        f"Error occurred: {type(error).__name__}: {error}",
        exc_info=True,
        extra={
            'event': 'error',
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context or {}
        }
    )


# Global logging manager instance
_logging_manager: Optional[LoggingManager] = None


def get_logging_manager() -> LoggingManager:
    """Get the global logging manager instance.
    
    Returns:
        LoggingManager instance
    """
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    return _logging_manager


def configure_logging():
    """Configure application logging."""
    manager = get_logging_manager()
    manager.configure_logging()


# Configure logging on module import
configure_logging()
