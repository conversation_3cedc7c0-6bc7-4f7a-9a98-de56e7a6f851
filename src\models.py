"""
Pydantic v2 models for the Neural Symbolic Language Model.

This module defines all data models using Pydantic v2 with proper validation,
field constraints, and comprehensive documentation.

Author: AI Assistant
Date: 2025-06-29
"""

from datetime import datetime, timezone
from typing import List, Optional, Any
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator
from pydantic.types import PositiveInt, NonNegativeFloat


class ModelRole(str, Enum):
    """Enumeration of valid message roles."""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


class ChatMessage(BaseModel):
    """Represents a single chat message.

    This model validates chat messages with proper role validation
    and content length constraints.

    Attributes:
        role: The role of the message sender
        content: The message content
        timestamp: When the message was created
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid',
        json_schema_extra={
            "example": {
                "role": "user",
                "content": "What is neural-symbolic AI?",
                "timestamp": "2025-06-29T12:00:00Z"
            }
        }
    )

    role: ModelRole = Field(
        ...,
        description="The role of the message sender (system, user, or assistant)"
    )
    content: str = Field(
        ...,
        min_length=1,
        max_length=10000,
        description="The message content"
    )
    timestamp: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When the message was created"
    )

    @field_validator('content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """Validate message content.

        Args:
            v: The content to validate

        Returns:
            Validated content

        Raises:
            ValueError: If content is invalid
        """
        if not v.strip():
            raise ValueError("Message content cannot be empty")
        return v


class ChatRequest(BaseModel):
    """Request model for chat endpoints.

    This model validates chat requests with proper message validation,
    parameter constraints, and security considerations.

    Attributes:
        messages: List of chat messages
        model: Model identifier to use
        temperature: Response randomness (0.0-1.0)
        max_tokens: Maximum tokens in response
        stream: Whether to stream the response
        top_p: Nucleus sampling parameter
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid',
        json_schema_extra={
            "example": {
                "messages": [
                    {"role": "user", "content": "What is symbolic reasoning?"}
                ],
                "model": "local",
                "temperature": 0.7,
                "max_tokens": 1000,
                "stream": False
            }
        }
    )

    messages: List[ChatMessage] = Field(
        ...,
        min_length=1,
        max_length=100,
        description="List of chat messages"
    )
    model: str = Field(
        default="local",
        min_length=1,
        max_length=100,
        description="Model identifier to use for generation"
    )
    temperature: NonNegativeFloat = Field(
        default=0.7,
        ge=0.0,
        le=2.0,
        description="Response randomness (0.0 = deterministic, 2.0 = very random)"
    )
    max_tokens: Optional[PositiveInt] = Field(
        default=None,
        le=4096,
        description="Maximum number of tokens in the response"
    )
    stream: bool = Field(
        default=False,
        description="Whether to stream the response"
    )
    top_p: NonNegativeFloat = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="Nucleus sampling parameter"
    )

    @field_validator('messages')
    @classmethod
    def validate_messages(cls, v: List[ChatMessage]) -> List[ChatMessage]:
        """Validate chat messages.

        Args:
            v: List of messages to validate

        Returns:
            Validated messages

        Raises:
            ValueError: If messages are invalid
        """
        if not v:
            raise ValueError("At least one message is required")

        # Check for at least one user message
        user_messages = [msg for msg in v if msg.role == ModelRole.USER]
        if not user_messages:
            raise ValueError("At least one user message is required")

        return v


class ChatChoice(BaseModel):
    """Represents a single chat completion choice.

    Attributes:
        index: Choice index
        message: The generated message
        finish_reason: Why generation stopped
    """
    model_config = ConfigDict(
        validate_assignment=True,
        extra='forbid'
    )

    index: int = Field(
        ...,
        ge=0,
        description="Index of this choice"
    )
    message: ChatMessage = Field(
        ...,
        description="The generated message"
    )
    finish_reason: str = Field(
        ...,
        description="Reason why generation stopped (stop, length, etc.)"
    )


class TokenUsage(BaseModel):
    """Token usage statistics.

    Attributes:
        prompt_tokens: Tokens in the prompt
        completion_tokens: Tokens in the completion
        total_tokens: Total tokens used
    """
    model_config = ConfigDict(
        validate_assignment=True,
        extra='forbid'
    )

    prompt_tokens: int = Field(
        ...,
        ge=0,
        description="Number of tokens in the prompt"
    )
    completion_tokens: int = Field(
        ...,
        ge=0,
        description="Number of tokens in the completion"
    )
    total_tokens: int = Field(
        ...,
        ge=0,
        description="Total number of tokens used"
    )

    @field_validator('total_tokens')
    @classmethod
    def validate_total_tokens(cls, v: int, info) -> int:
        """Validate that total tokens equals sum of prompt and completion tokens.

        Args:
            v: Total tokens value
            info: Validation info containing other field values

        Returns:
            Validated total tokens

        Raises:
            ValueError: If total doesn't match sum
        """
        if hasattr(info, 'data') and info.data:
            prompt_tokens = info.data.get('prompt_tokens', 0)
            completion_tokens = info.data.get('completion_tokens', 0)
            expected_total = prompt_tokens + completion_tokens
            if v != expected_total:
                raise ValueError(f"Total tokens ({v}) must equal prompt_tokens + completion_tokens ({expected_total})")
        return v


class ChatResponse(BaseModel):
    """Response model for chat completions.

    This model represents the complete response from a chat completion request,
    including all choices, usage statistics, and metadata.

    Attributes:
        id: Unique identifier for the completion
        object: Object type (always "chat.completion")
        created: Unix timestamp of creation
        model: Model used for generation
        choices: List of completion choices
        usage: Token usage statistics
    """
    model_config = ConfigDict(
        validate_assignment=True,
        extra='forbid',
        json_schema_extra={
            "example": {
                "id": "chatcmpl-123",
                "object": "chat.completion",
                "created": 1677652288,
                "model": "local",
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": "Hello! How can I help you today?"
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": 9,
                    "completion_tokens": 12,
                    "total_tokens": 21
                }
            }
        }
    )

    id: str = Field(
        ...,
        min_length=1,
        description="Unique identifier for the completion"
    )
    object: str = Field(
        default="chat.completion",
        description="Object type"
    )
    created: int = Field(
        ...,
        ge=0,
        description="Unix timestamp of when the completion was created"
    )
    model: str = Field(
        ...,
        min_length=1,
        description="Model used for the completion"
    )
    choices: List[ChatChoice] = Field(
        ...,
        min_length=1,
        description="List of completion choices"
    )
    usage: TokenUsage = Field(
        ...,
        description="Token usage statistics"
    )


class DocumentAddRequest(BaseModel):
    """Request model for adding documents to the retrieval system.

    Attributes:
        text: Document content
        metadata: Optional document metadata
        document_id: Optional document identifier
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid',
        json_schema_extra={
            "example": {
                "text": "This is a sample document about neural networks.",
                "metadata": {"source": "research_paper", "author": "Dr. Smith"},
                "document_id": "doc_123"
            }
        }
    )

    text: str = Field(
        ...,
        min_length=1,
        max_length=100000,
        description="Document content"
    )
    metadata: Optional[dict] = Field(
        default=None,
        description="Optional document metadata"
    )
    document_id: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="Optional document identifier"
    )

    @field_validator('text')
    @classmethod
    def validate_text(cls, v: str) -> str:
        """Validate document text.

        Args:
            v: The text to validate

        Returns:
            Validated text

        Raises:
            ValueError: If text is invalid
        """
        if not v.strip():
            raise ValueError("Document text cannot be empty")
        return v


class DocumentAddResponse(BaseModel):
    """Response model for document addition.

    Attributes:
        success: Whether the operation was successful
        message: Human-readable status message
        document_id: ID of the added document
        metadata: Additional response metadata
    """
    model_config = ConfigDict(
        validate_assignment=True,
        extra='forbid'
    )

    success: bool = Field(
        ...,
        description="Whether the operation was successful"
    )
    message: str = Field(
        ...,
        min_length=1,
        description="Human-readable status message"
    )
    document_id: Optional[str] = Field(
        default=None,
        description="ID of the added document"
    )
    metadata: Optional[dict] = Field(
        default=None,
        description="Additional response metadata"
    )


class SystemInfo(BaseModel):
    """System information model.

    Attributes:
        gpu_available: Whether GPU is available
        gpu_name: Name of the GPU
        gpu_optimized: Whether GPU optimizations are enabled
        reasoner_info: Information about the reasoning system
        retriever_info: Information about the retrieval system
    """
    model_config = ConfigDict(
        validate_assignment=True,
        extra='forbid'
    )

    gpu_available: bool = Field(
        ...,
        description="Whether GPU is available"
    )
    gpu_name: Optional[str] = Field(
        default=None,
        description="Name of the GPU if available"
    )
    gpu_optimized: bool = Field(
        ...,
        description="Whether GPU optimizations are enabled"
    )
    reasoner_info: dict = Field(
        ...,
        description="Information about the reasoning system"
    )
    retriever_info: dict = Field(
        ...,
        description="Information about the retrieval system"
    )


class PerformanceMetrics(BaseModel):
    """Performance metrics model.

    Attributes:
        cache: Cache performance metrics
        system: System performance metrics
        requests: Request performance metrics
    """
    model_config = ConfigDict(
        validate_assignment=True,
        extra='forbid'
    )

    cache: dict = Field(
        ...,
        description="Cache performance metrics"
    )
    system: dict = Field(
        ...,
        description="System performance metrics"
    )
    requests: dict = Field(
        ...,
        description="Request performance metrics"
    )


# OpenAI-compatible models for backward compatibility
class OpenAIMessage(BaseModel):
    """OpenAI-compatible message model."""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid'
    )

    role: str = Field(
        ...,
        description="Message role (system, user, assistant)"
    )
    content: str = Field(
        ...,
        min_length=1,
        max_length=10000,
        description="Message content"
    )


class OpenAIChatRequest(BaseModel):
    """OpenAI-compatible chat request model."""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid'
    )

    model: str = Field(
        ...,
        description="Model to use"
    )
    messages: List[OpenAIMessage] = Field(
        ...,
        min_length=1,
        description="List of messages"
    )
    temperature: float = Field(
        default=0.7,
        ge=0.0,
        le=2.0,
        description="Sampling temperature"
    )
    max_tokens: Optional[int] = Field(
        default=None,
        ge=1,
        le=4096,
        description="Maximum tokens to generate"
    )
    stream: bool = Field(
        default=False,
        description="Whether to stream responses"
    )


class OpenAIChatChoice(BaseModel):
    """OpenAI-compatible chat choice model."""
    model_config = ConfigDict(
        validate_assignment=True,
        extra='forbid'
    )

    index: int = Field(
        ...,
        ge=0,
        description="Choice index"
    )
    message: OpenAIMessage = Field(
        ...,
        description="Generated message"
    )
    finish_reason: str = Field(
        ...,
        description="Finish reason"
    )


class OpenAIChatResponse(BaseModel):
    """OpenAI-compatible chat response model."""
    model_config = ConfigDict(
        validate_assignment=True,
        extra='forbid'
    )

    id: str = Field(
        ...,
        description="Completion ID"
    )
    object: str = Field(
        default="chat.completion",
        description="Object type"
    )
    created: int = Field(
        ...,
        ge=0,
        description="Creation timestamp"
    )
    model: str = Field(
        ...,
        description="Model used"
    )
    choices: List[OpenAIChatChoice] = Field(
        ...,
        min_length=1,
        description="List of choices"
    )
    usage: dict = Field(
        ...,
        description="Token usage"
    )