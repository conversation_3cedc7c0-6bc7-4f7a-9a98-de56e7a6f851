# Staging Environment Configuration
# This file contains configuration settings for staging environment

# Application Settings
APP_TITLE="Neural Symbolic Language Model (Staging)"
APP_VERSION="0.1.0-staging"
APP_DEBUG=false
APP_ENVIRONMENT="staging"
APP_HOST="0.0.0.0"
APP_PORT=8080
APP_WORKERS=2
APP_RELOAD=false

# Security Settings
SECURITY_API_KEYS_JSON='{"staging_key": {"key": "staging_api_key_67890", "permissions": ["read", "write"], "rate_limit": 500, "created_at": "2025-06-29T00:00:00"}}'
SECURITY_RATE_LIMIT_REQUESTS=200
SECURITY_RATE_LIMIT_WINDOW=60
SECURITY_MAX_REQUEST_SIZE=5242880
SECURITY_CORS_ORIGINS='["https://staging.example.com", "https://test.example.com"]'
SECURITY_BLOCK_DURATION=1800
SECURITY_MAX_FAILED_ATTEMPTS=7

# Model Settings
MODEL_REASONING_ENGINE="ollama"
MODEL_REASONING_MODEL="llama2"
MODEL_EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
MODEL_EMBEDDING_DIMENSION=384
MODEL_USE_GPU=true
MODEL_GPU_MEMORY_FRACTION=0.6
MODEL_OLLAMA_HOST="http://ollama-staging:11434"
MODEL_OLLAMA_TIMEOUT=45
MODEL_VECTOR_DB_BACKEND="faiss"

# Cache Settings
CACHE_ENABLED=true
CACHE_TTL=1800
CACHE_MAX_SIZE=5000
CACHE_BACKEND="memory"

# Logging Settings
LOG_LEVEL="INFO"
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE_ENABLED=true
LOG_FILE_PATH="/var/log/neural-symbolic-lm-staging"
LOG_MAX_FILE_SIZE=52428800
LOG_BACKUP_COUNT=7
LOG_STRUCTURED=true

# Staging-specific settings
STAGING_ENABLE_TESTING=true
STAGING_ENABLE_PROFILING=false
STAGING_MOCK_EXTERNAL_APIS=false
STAGING_ENABLE_MONITORING=true
