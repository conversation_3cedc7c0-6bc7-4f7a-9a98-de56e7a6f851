"""Unit tests for the retrieval module."""

import unittest
import torch
from src.retrieval import Retriever

class TestRetriever(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures."""
        self.retriever = Retriever(use_gpu=False)  # Use CPU for testing
        
    def test_initialization(self):
        """Test retriever initialization."""
        self.assertIsInstance(self.retriever, Retriever)
        self.assertEqual(self.retriever.vector_db, "faiss")
        
    def test_add_and_query_documents(self):
        """Test document addition and querying."""
        # Test documents with unique IDs
        documents = [
            {"id": "doc1", "text": "Symbolic reasoning is a method of problem-solving using formal logic."},
            {"id": "doc2", "text": "Neural networks learn patterns from data through training."},
            {"id": "doc3", "text": "FAISS is an efficient similarity search library."}
        ]
        
        # Add documents
        self.retriever.add_documents(documents)
        self.assertEqual(len(self.retriever.documents), len(documents))
        
        # Test searching
        query = "What is symbolic reasoning?"
        results = self.retriever.search(query, k=1)
        self.assertIsInstance(results, list)
        self.assertTrue(len(results) > 0)
        self.assertIn('text', results[0])
        self.assertIn('score', results[0])
        
    def test_batch_add_documents(self):
        """Test batch document addition."""
        documents = [{"id": f"doc_{i}", "text": f"Test document {i}"} for i in range(10)]
        self.retriever.batch_add_documents(documents, batch_size=3)
        self.assertEqual(len(self.retriever.documents), len(documents))
        
    def test_system_info(self):
        """Test system information retrieval."""
        info = self.retriever.get_system_info()
        self.assertIsInstance(info, dict)
        self.assertIn("vector_db", info)
        self.assertIn("gpu_enabled", info)
        self.assertIn("gpu_available", info)
        self.assertIn("index_size", info)
        
if __name__ == "__main__":
    unittest.main()
