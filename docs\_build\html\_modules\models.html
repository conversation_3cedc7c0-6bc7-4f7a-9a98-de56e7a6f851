

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>models &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Module code</a></li>
      <li class="breadcrumb-item active">models</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for models</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Pydantic v2 models for the Neural Symbolic Language Model.</span>

<span class="sd">This module defines all data models using Pydantic v2 with proper validation,</span>
<span class="sd">field constraints, and comprehensive documentation.</span>

<span class="sd">Author: AI Assistant</span>
<span class="sd">Date: 2025-06-29</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timezone</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Any</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">enum</span><span class="w"> </span><span class="kn">import</span> <span class="n">Enum</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">pydantic</span><span class="w"> </span><span class="kn">import</span> <span class="n">BaseModel</span><span class="p">,</span> <span class="n">Field</span><span class="p">,</span> <span class="n">ConfigDict</span><span class="p">,</span> <span class="n">field_validator</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pydantic.types</span><span class="w"> </span><span class="kn">import</span> <span class="n">PositiveInt</span><span class="p">,</span> <span class="n">NonNegativeFloat</span>


<div class="viewcode-block" id="ModelRole">
<a class="viewcode-back" href="../modules.html#models.ModelRole">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ModelRole</span><span class="p">(</span><span class="nb">str</span><span class="p">,</span> <span class="n">Enum</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Enumeration of valid message roles.&quot;&quot;&quot;</span>
    <span class="n">SYSTEM</span> <span class="o">=</span> <span class="s2">&quot;system&quot;</span>
    <span class="n">USER</span> <span class="o">=</span> <span class="s2">&quot;user&quot;</span>
    <span class="n">ASSISTANT</span> <span class="o">=</span> <span class="s2">&quot;assistant&quot;</span></div>



<div class="viewcode-block" id="ChatMessage">
<a class="viewcode-back" href="../modules.html#models.ChatMessage">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ChatMessage</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Represents a single chat message.</span>

<span class="sd">    This model validates chat messages with proper role validation</span>
<span class="sd">    and content length constraints.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        role: The role of the message sender</span>
<span class="sd">        content: The message content</span>
<span class="sd">        timestamp: When the message was created</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">str_strip_whitespace</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span><span class="p">,</span>
        <span class="n">json_schema_extra</span><span class="o">=</span><span class="p">{</span>
            <span class="s2">&quot;example&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;user&quot;</span><span class="p">,</span>
                <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;What is neural-symbolic AI?&quot;</span><span class="p">,</span>
                <span class="s2">&quot;timestamp&quot;</span><span class="p">:</span> <span class="s2">&quot;2025-06-29T12:00:00Z&quot;</span>
            <span class="p">}</span>
        <span class="p">}</span>
    <span class="p">)</span>

    <span class="n">role</span><span class="p">:</span> <span class="n">ModelRole</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;The role of the message sender (system, user, or assistant)&quot;</span>
    <span class="p">)</span>
    <span class="n">content</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">max_length</span><span class="o">=</span><span class="mi">10000</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;The message content&quot;</span>
    <span class="p">)</span>
    <span class="n">timestamp</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default_factory</span><span class="o">=</span><span class="k">lambda</span><span class="p">:</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">(</span><span class="n">timezone</span><span class="o">.</span><span class="n">utc</span><span class="p">),</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;When the message was created&quot;</span>
    <span class="p">)</span>

<div class="viewcode-block" id="ChatMessage.validate_content">
<a class="viewcode-back" href="../modules.html#models.ChatMessage.validate_content">[docs]</a>
    <span class="nd">@field_validator</span><span class="p">(</span><span class="s1">&#39;content&#39;</span><span class="p">)</span>
    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">validate_content</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Validate message content.</span>

<span class="sd">        Args:</span>
<span class="sd">            v: The content to validate</span>

<span class="sd">        Returns:</span>
<span class="sd">            Validated content</span>

<span class="sd">        Raises:</span>
<span class="sd">            ValueError: If content is invalid</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">v</span><span class="o">.</span><span class="n">strip</span><span class="p">():</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Message content cannot be empty&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">v</span></div>
</div>



<div class="viewcode-block" id="ChatRequest">
<a class="viewcode-back" href="../modules.html#models.ChatRequest">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ChatRequest</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Request model for chat endpoints.</span>

<span class="sd">    This model validates chat requests with proper message validation,</span>
<span class="sd">    parameter constraints, and security considerations.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        messages: List of chat messages</span>
<span class="sd">        model: Model identifier to use</span>
<span class="sd">        temperature: Response randomness (0.0-1.0)</span>
<span class="sd">        max_tokens: Maximum tokens in response</span>
<span class="sd">        stream: Whether to stream the response</span>
<span class="sd">        top_p: Nucleus sampling parameter</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">str_strip_whitespace</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span><span class="p">,</span>
        <span class="n">json_schema_extra</span><span class="o">=</span><span class="p">{</span>
            <span class="s2">&quot;example&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;messages&quot;</span><span class="p">:</span> <span class="p">[</span>
                    <span class="p">{</span><span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;user&quot;</span><span class="p">,</span> <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;What is symbolic reasoning?&quot;</span><span class="p">}</span>
                <span class="p">],</span>
                <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;local&quot;</span><span class="p">,</span>
                <span class="s2">&quot;temperature&quot;</span><span class="p">:</span> <span class="mf">0.7</span><span class="p">,</span>
                <span class="s2">&quot;max_tokens&quot;</span><span class="p">:</span> <span class="mi">1000</span><span class="p">,</span>
                <span class="s2">&quot;stream&quot;</span><span class="p">:</span> <span class="kc">False</span>
            <span class="p">}</span>
        <span class="p">}</span>
    <span class="p">)</span>

    <span class="n">messages</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ChatMessage</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;List of chat messages&quot;</span>
    <span class="p">)</span>
    <span class="n">model</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="s2">&quot;local&quot;</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Model identifier to use for generation&quot;</span>
    <span class="p">)</span>
    <span class="n">temperature</span><span class="p">:</span> <span class="n">NonNegativeFloat</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="mf">0.7</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mf">0.0</span><span class="p">,</span>
        <span class="n">le</span><span class="o">=</span><span class="mf">2.0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Response randomness (0.0 = deterministic, 2.0 = very random)&quot;</span>
    <span class="p">)</span>
    <span class="n">max_tokens</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">PositiveInt</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">le</span><span class="o">=</span><span class="mi">4096</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Maximum number of tokens in the response&quot;</span>
    <span class="p">)</span>
    <span class="n">stream</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Whether to stream the response&quot;</span>
    <span class="p">)</span>
    <span class="n">top_p</span><span class="p">:</span> <span class="n">NonNegativeFloat</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="mf">1.0</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mf">0.0</span><span class="p">,</span>
        <span class="n">le</span><span class="o">=</span><span class="mf">1.0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Nucleus sampling parameter&quot;</span>
    <span class="p">)</span>

<div class="viewcode-block" id="ChatRequest.validate_messages">
<a class="viewcode-back" href="../modules.html#models.ChatRequest.validate_messages">[docs]</a>
    <span class="nd">@field_validator</span><span class="p">(</span><span class="s1">&#39;messages&#39;</span><span class="p">)</span>
    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">validate_messages</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ChatMessage</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">ChatMessage</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Validate chat messages.</span>

<span class="sd">        Args:</span>
<span class="sd">            v: List of messages to validate</span>

<span class="sd">        Returns:</span>
<span class="sd">            Validated messages</span>

<span class="sd">        Raises:</span>
<span class="sd">            ValueError: If messages are invalid</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">v</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;At least one message is required&quot;</span><span class="p">)</span>

        <span class="c1"># Check for at least one user message</span>
        <span class="n">user_messages</span> <span class="o">=</span> <span class="p">[</span><span class="n">msg</span> <span class="k">for</span> <span class="n">msg</span> <span class="ow">in</span> <span class="n">v</span> <span class="k">if</span> <span class="n">msg</span><span class="o">.</span><span class="n">role</span> <span class="o">==</span> <span class="n">ModelRole</span><span class="o">.</span><span class="n">USER</span><span class="p">]</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">user_messages</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;At least one user message is required&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">v</span></div>
</div>



<div class="viewcode-block" id="ChatChoice">
<a class="viewcode-back" href="../modules.html#models.ChatChoice">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ChatChoice</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Represents a single chat completion choice.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        index: Choice index</span>
<span class="sd">        message: The generated message</span>
<span class="sd">        finish_reason: Why generation stopped</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="n">index</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Index of this choice&quot;</span>
    <span class="p">)</span>
    <span class="n">message</span><span class="p">:</span> <span class="n">ChatMessage</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;The generated message&quot;</span>
    <span class="p">)</span>
    <span class="n">finish_reason</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Reason why generation stopped (stop, length, etc.)&quot;</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="TokenUsage">
<a class="viewcode-back" href="../modules.html#models.TokenUsage">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">TokenUsage</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Token usage statistics.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        prompt_tokens: Tokens in the prompt</span>
<span class="sd">        completion_tokens: Tokens in the completion</span>
<span class="sd">        total_tokens: Total tokens used</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="n">prompt_tokens</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of tokens in the prompt&quot;</span>
    <span class="p">)</span>
    <span class="n">completion_tokens</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of tokens in the completion&quot;</span>
    <span class="p">)</span>
    <span class="n">total_tokens</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Total number of tokens used&quot;</span>
    <span class="p">)</span>

<div class="viewcode-block" id="TokenUsage.validate_total_tokens">
<a class="viewcode-back" href="../modules.html#models.TokenUsage.validate_total_tokens">[docs]</a>
    <span class="nd">@field_validator</span><span class="p">(</span><span class="s1">&#39;total_tokens&#39;</span><span class="p">)</span>
    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">validate_total_tokens</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">info</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Validate that total tokens equals sum of prompt and completion tokens.</span>

<span class="sd">        Args:</span>
<span class="sd">            v: Total tokens value</span>
<span class="sd">            info: Validation info containing other field values</span>

<span class="sd">        Returns:</span>
<span class="sd">            Validated total tokens</span>

<span class="sd">        Raises:</span>
<span class="sd">            ValueError: If total doesn&#39;t match sum</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">info</span><span class="p">,</span> <span class="s1">&#39;data&#39;</span><span class="p">)</span> <span class="ow">and</span> <span class="n">info</span><span class="o">.</span><span class="n">data</span><span class="p">:</span>
            <span class="n">prompt_tokens</span> <span class="o">=</span> <span class="n">info</span><span class="o">.</span><span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;prompt_tokens&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">completion_tokens</span> <span class="o">=</span> <span class="n">info</span><span class="o">.</span><span class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;completion_tokens&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">expected_total</span> <span class="o">=</span> <span class="n">prompt_tokens</span> <span class="o">+</span> <span class="n">completion_tokens</span>
            <span class="k">if</span> <span class="n">v</span> <span class="o">!=</span> <span class="n">expected_total</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Total tokens (</span><span class="si">{</span><span class="n">v</span><span class="si">}</span><span class="s2">) must equal prompt_tokens + completion_tokens (</span><span class="si">{</span><span class="n">expected_total</span><span class="si">}</span><span class="s2">)&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">v</span></div>
</div>



<div class="viewcode-block" id="ChatResponse">
<a class="viewcode-back" href="../modules.html#models.ChatResponse">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">ChatResponse</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Response model for chat completions.</span>

<span class="sd">    This model represents the complete response from a chat completion request,</span>
<span class="sd">    including all choices, usage statistics, and metadata.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        id: Unique identifier for the completion</span>
<span class="sd">        object: Object type (always &quot;chat.completion&quot;)</span>
<span class="sd">        created: Unix timestamp of creation</span>
<span class="sd">        model: Model used for generation</span>
<span class="sd">        choices: List of completion choices</span>
<span class="sd">        usage: Token usage statistics</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span><span class="p">,</span>
        <span class="n">json_schema_extra</span><span class="o">=</span><span class="p">{</span>
            <span class="s2">&quot;example&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;id&quot;</span><span class="p">:</span> <span class="s2">&quot;chatcmpl-123&quot;</span><span class="p">,</span>
                <span class="s2">&quot;object&quot;</span><span class="p">:</span> <span class="s2">&quot;chat.completion&quot;</span><span class="p">,</span>
                <span class="s2">&quot;created&quot;</span><span class="p">:</span> <span class="mi">1677652288</span><span class="p">,</span>
                <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;local&quot;</span><span class="p">,</span>
                <span class="s2">&quot;choices&quot;</span><span class="p">:</span> <span class="p">[</span>
                    <span class="p">{</span>
                        <span class="s2">&quot;index&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                        <span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="p">{</span>
                            <span class="s2">&quot;role&quot;</span><span class="p">:</span> <span class="s2">&quot;assistant&quot;</span><span class="p">,</span>
                            <span class="s2">&quot;content&quot;</span><span class="p">:</span> <span class="s2">&quot;Hello! How can I help you today?&quot;</span>
                        <span class="p">},</span>
                        <span class="s2">&quot;finish_reason&quot;</span><span class="p">:</span> <span class="s2">&quot;stop&quot;</span>
                    <span class="p">}</span>
                <span class="p">],</span>
                <span class="s2">&quot;usage&quot;</span><span class="p">:</span> <span class="p">{</span>
                    <span class="s2">&quot;prompt_tokens&quot;</span><span class="p">:</span> <span class="mi">9</span><span class="p">,</span>
                    <span class="s2">&quot;completion_tokens&quot;</span><span class="p">:</span> <span class="mi">12</span><span class="p">,</span>
                    <span class="s2">&quot;total_tokens&quot;</span><span class="p">:</span> <span class="mi">21</span>
                <span class="p">}</span>
            <span class="p">}</span>
        <span class="p">}</span>
    <span class="p">)</span>

    <span class="nb">id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Unique identifier for the completion&quot;</span>
    <span class="p">)</span>
    <span class="nb">object</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="s2">&quot;chat.completion&quot;</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Object type&quot;</span>
    <span class="p">)</span>
    <span class="n">created</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Unix timestamp of when the completion was created&quot;</span>
    <span class="p">)</span>
    <span class="n">model</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Model used for the completion&quot;</span>
    <span class="p">)</span>
    <span class="n">choices</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ChatChoice</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;List of completion choices&quot;</span>
    <span class="p">)</span>
    <span class="n">usage</span><span class="p">:</span> <span class="n">TokenUsage</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Token usage statistics&quot;</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="DocumentAddRequest">
<a class="viewcode-back" href="../modules.html#models.DocumentAddRequest">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">DocumentAddRequest</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Request model for adding documents to the retrieval system.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        text: Document content</span>
<span class="sd">        metadata: Optional document metadata</span>
<span class="sd">        document_id: Optional document identifier</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">str_strip_whitespace</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span><span class="p">,</span>
        <span class="n">json_schema_extra</span><span class="o">=</span><span class="p">{</span>
            <span class="s2">&quot;example&quot;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s2">&quot;text&quot;</span><span class="p">:</span> <span class="s2">&quot;This is a sample document about neural networks.&quot;</span><span class="p">,</span>
                <span class="s2">&quot;metadata&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;source&quot;</span><span class="p">:</span> <span class="s2">&quot;research_paper&quot;</span><span class="p">,</span> <span class="s2">&quot;author&quot;</span><span class="p">:</span> <span class="s2">&quot;Dr. Smith&quot;</span><span class="p">},</span>
                <span class="s2">&quot;document_id&quot;</span><span class="p">:</span> <span class="s2">&quot;doc_123&quot;</span>
            <span class="p">}</span>
        <span class="p">}</span>
    <span class="p">)</span>

    <span class="n">text</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">max_length</span><span class="o">=</span><span class="mi">100000</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Document content&quot;</span>
    <span class="p">)</span>
    <span class="n">metadata</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">dict</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Optional document metadata&quot;</span>
    <span class="p">)</span>
    <span class="n">document_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Optional document identifier&quot;</span>
    <span class="p">)</span>

<div class="viewcode-block" id="DocumentAddRequest.validate_text">
<a class="viewcode-back" href="../modules.html#models.DocumentAddRequest.validate_text">[docs]</a>
    <span class="nd">@field_validator</span><span class="p">(</span><span class="s1">&#39;text&#39;</span><span class="p">)</span>
    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">validate_text</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Validate document text.</span>

<span class="sd">        Args:</span>
<span class="sd">            v: The text to validate</span>

<span class="sd">        Returns:</span>
<span class="sd">            Validated text</span>

<span class="sd">        Raises:</span>
<span class="sd">            ValueError: If text is invalid</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">v</span><span class="o">.</span><span class="n">strip</span><span class="p">():</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Document text cannot be empty&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">v</span></div>
</div>



<div class="viewcode-block" id="DocumentAddResponse">
<a class="viewcode-back" href="../modules.html#models.DocumentAddResponse">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">DocumentAddResponse</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Response model for document addition.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        success: Whether the operation was successful</span>
<span class="sd">        message: Human-readable status message</span>
<span class="sd">        document_id: ID of the added document</span>
<span class="sd">        metadata: Additional response metadata</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="n">success</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Whether the operation was successful&quot;</span>
    <span class="p">)</span>
    <span class="n">message</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Human-readable status message&quot;</span>
    <span class="p">)</span>
    <span class="n">document_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;ID of the added document&quot;</span>
    <span class="p">)</span>
    <span class="n">metadata</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">dict</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Additional response metadata&quot;</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="SystemInfo">
<a class="viewcode-back" href="../modules.html#models.SystemInfo">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">SystemInfo</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;System information model.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        gpu_available: Whether GPU is available</span>
<span class="sd">        gpu_name: Name of the GPU</span>
<span class="sd">        gpu_optimized: Whether GPU optimizations are enabled</span>
<span class="sd">        reasoner_info: Information about the reasoning system</span>
<span class="sd">        retriever_info: Information about the retrieval system</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="n">gpu_available</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Whether GPU is available&quot;</span>
    <span class="p">)</span>
    <span class="n">gpu_name</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Name of the GPU if available&quot;</span>
    <span class="p">)</span>
    <span class="n">gpu_optimized</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Whether GPU optimizations are enabled&quot;</span>
    <span class="p">)</span>
    <span class="n">reasoner_info</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Information about the reasoning system&quot;</span>
    <span class="p">)</span>
    <span class="n">retriever_info</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Information about the retrieval system&quot;</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="PerformanceMetrics">
<a class="viewcode-back" href="../modules.html#models.PerformanceMetrics">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">PerformanceMetrics</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Performance metrics model.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        cache: Cache performance metrics</span>
<span class="sd">        system: System performance metrics</span>
<span class="sd">        requests: Request performance metrics</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="n">cache</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Cache performance metrics&quot;</span>
    <span class="p">)</span>
    <span class="n">system</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;System performance metrics&quot;</span>
    <span class="p">)</span>
    <span class="n">requests</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Request performance metrics&quot;</span>
    <span class="p">)</span></div>



<span class="c1"># OpenAI-compatible models for backward compatibility</span>
<div class="viewcode-block" id="OpenAIMessage">
<a class="viewcode-back" href="../modules.html#models.OpenAIMessage">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">OpenAIMessage</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;OpenAI-compatible message model.&quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">str_strip_whitespace</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="n">role</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Message role (system, user, assistant)&quot;</span>
    <span class="p">)</span>
    <span class="n">content</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">max_length</span><span class="o">=</span><span class="mi">10000</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Message content&quot;</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="OpenAIChatRequest">
<a class="viewcode-back" href="../modules.html#models.OpenAIChatRequest">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">OpenAIChatRequest</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;OpenAI-compatible chat request model.&quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">str_strip_whitespace</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="n">model</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Model to use&quot;</span>
    <span class="p">)</span>
    <span class="n">messages</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">OpenAIMessage</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;List of messages&quot;</span>
    <span class="p">)</span>
    <span class="n">temperature</span><span class="p">:</span> <span class="nb">float</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="mf">0.7</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mf">0.0</span><span class="p">,</span>
        <span class="n">le</span><span class="o">=</span><span class="mf">2.0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Sampling temperature&quot;</span>
    <span class="p">)</span>
    <span class="n">max_tokens</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">le</span><span class="o">=</span><span class="mi">4096</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Maximum tokens to generate&quot;</span>
    <span class="p">)</span>
    <span class="n">stream</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Whether to stream responses&quot;</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="OpenAIChatChoice">
<a class="viewcode-back" href="../modules.html#models.OpenAIChatChoice">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">OpenAIChatChoice</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;OpenAI-compatible chat choice model.&quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="n">index</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Choice index&quot;</span>
    <span class="p">)</span>
    <span class="n">message</span><span class="p">:</span> <span class="n">OpenAIMessage</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Generated message&quot;</span>
    <span class="p">)</span>
    <span class="n">finish_reason</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Finish reason&quot;</span>
    <span class="p">)</span></div>



<div class="viewcode-block" id="OpenAIChatResponse">
<a class="viewcode-back" href="../modules.html#models.OpenAIChatResponse">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">OpenAIChatResponse</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;OpenAI-compatible chat response model.&quot;&quot;&quot;</span>
    <span class="n">model_config</span> <span class="o">=</span> <span class="n">ConfigDict</span><span class="p">(</span>
        <span class="n">validate_assignment</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="n">extra</span><span class="o">=</span><span class="s1">&#39;forbid&#39;</span>
    <span class="p">)</span>

    <span class="nb">id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Completion ID&quot;</span>
    <span class="p">)</span>
    <span class="nb">object</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="n">default</span><span class="o">=</span><span class="s2">&quot;chat.completion&quot;</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Object type&quot;</span>
    <span class="p">)</span>
    <span class="n">created</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Creation timestamp&quot;</span>
    <span class="p">)</span>
    <span class="n">model</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Model used&quot;</span>
    <span class="p">)</span>
    <span class="n">choices</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">OpenAIChatChoice</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;List of choices&quot;</span>
    <span class="p">)</span>
    <span class="n">usage</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span>
        <span class="o">...</span><span class="p">,</span>
        <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Token usage&quot;</span>
    <span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>