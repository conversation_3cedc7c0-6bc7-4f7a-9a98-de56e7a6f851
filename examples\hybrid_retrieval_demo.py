#!/usr/bin/env python3
"""
Comprehensive demonstration of the Hybrid Retrieval system.

This script shows how to add various types of content to the Neural Symbolic Language Model's
Hybrid Retrieval system, including:
- Documents and files
- Website content
- GitHub repositories
- Structured data
- Search and retrieval examples
"""

import asyncio
import json
import time
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
import tempfile
import os


class HybridRetrievalDemo:
    """Demonstration client for the Hybrid Retrieval system."""
    
    def __init__(self, api_key: str, base_url: str = "http://localhost:8080"):
        """Initialize the demo client.
        
        Args:
            api_key: API key for authentication
            base_url: Base URL for the API
        """
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        self.added_documents = []
    
    def health_check(self) -> bool:
        """Check if the API is healthy and accessible."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"Health check failed: {e}")
            return False
    
    def demo_document_addition(self):
        """Demonstrate adding various types of documents."""
        print("📄 Document Addition Demo")
        print("-" * 50)
        
        # Example 1: Simple text document
        print("1. Adding simple text document...")
        try:
            doc_result = self.add_document(
                content="""
                Neural-symbolic AI represents a paradigm that combines the learning capabilities 
                of neural networks with the reasoning capabilities of symbolic AI systems. 
                This hybrid approach enables systems to both learn from data and perform 
                logical reasoning, making them more interpretable and capable of handling 
                complex tasks that require both pattern recognition and logical inference.
                
                Key advantages include:
                - Interpretability: Symbolic reasoning provides explainable decisions
                - Robustness: Combines statistical learning with logical constraints
                - Efficiency: Can leverage both learned patterns and explicit rules
                - Generalization: Better performance on out-of-distribution data
                """,
                metadata={
                    "title": "Neural-Symbolic AI Overview",
                    "category": "ai_concepts",
                    "tags": ["neural-symbolic", "ai", "reasoning"],
                    "author": "Demo System",
                    "difficulty": "intermediate"
                }
            )
            print(f"   ✅ Document added: {doc_result.get('document_id', 'Unknown ID')}")
            self.added_documents.append(doc_result.get('document_id'))
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Example 2: Technical documentation
        print("\n2. Adding technical documentation...")
        try:
            tech_doc = self.add_document(
                content="""
                # Machine Learning Pipeline Best Practices
                
                ## Data Preprocessing
                1. **Data Cleaning**: Remove duplicates, handle missing values
                2. **Feature Engineering**: Create meaningful features from raw data
                3. **Normalization**: Scale features to similar ranges
                4. **Validation**: Split data into train/validation/test sets
                
                ## Model Training
                - Choose appropriate algorithms based on problem type
                - Use cross-validation for hyperparameter tuning
                - Monitor for overfitting and underfitting
                - Implement early stopping mechanisms
                
                ## Model Evaluation
                - Use appropriate metrics for your problem domain
                - Test on held-out data not seen during training
                - Analyze model performance across different data segments
                - Consider fairness and bias implications
                
                ## Deployment Considerations
                - Model versioning and reproducibility
                - Monitoring model performance in production
                - A/B testing for model updates
                - Rollback strategies for failed deployments
                """,
                metadata={
                    "title": "ML Pipeline Best Practices",
                    "category": "technical_documentation",
                    "tags": ["machine-learning", "pipeline", "best-practices"],
                    "format": "markdown",
                    "level": "advanced"
                }
            )
            print(f"   ✅ Technical doc added: {tech_doc.get('document_id', 'Unknown ID')}")
            self.added_documents.append(tech_doc.get('document_id'))
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Example 3: Code documentation
        print("\n3. Adding code documentation...")
        try:
            code_doc = self.add_document(
                content="""
                ```python
                class NeuralNetwork:
                    def __init__(self, layers):
                        \"\"\"Initialize a neural network with specified layer sizes.
                        
                        Args:
                            layers: List of integers specifying layer sizes
                        \"\"\"
                        self.layers = layers
                        self.weights = self._initialize_weights()
                        self.biases = self._initialize_biases()
                    
                    def forward(self, x):
                        \"\"\"Forward pass through the network.\"\"\"
                        activation = x
                        for i in range(len(self.weights)):
                            z = np.dot(activation, self.weights[i]) + self.biases[i]
                            activation = self.sigmoid(z)
                        return activation
                    
                    def sigmoid(self, z):
                        \"\"\"Sigmoid activation function.\"\"\"
                        return 1 / (1 + np.exp(-np.clip(z, -250, 250)))
                ```
                
                This implementation provides a basic feedforward neural network with:
                - Configurable layer sizes
                - Sigmoid activation functions
                - Forward propagation
                - Numerical stability through clipping
                """,
                metadata={
                    "title": "Basic Neural Network Implementation",
                    "category": "code_examples",
                    "tags": ["python", "neural-networks", "implementation"],
                    "language": "python",
                    "complexity": "beginner"
                }
            )
            print(f"   ✅ Code doc added: {code_doc.get('document_id', 'Unknown ID')}")
            self.added_documents.append(code_doc.get('document_id'))
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    def demo_website_addition(self):
        """Demonstrate adding website content."""
        print("\n🌐 Website Content Addition Demo")
        print("-" * 50)
        
        # Example websites to add (using mock data for demo)
        websites = [
            {
                "url": "https://en.wikipedia.org/wiki/Artificial_intelligence",
                "description": "Wikipedia AI article",
                "category": "reference"
            },
            {
                "url": "https://arxiv.org/abs/2301.00001",
                "description": "Research paper from arXiv",
                "category": "research"
            },
            {
                "url": "https://docs.python.org/3/tutorial/",
                "description": "Python tutorial documentation",
                "category": "documentation"
            }
        ]
        
        for i, site in enumerate(websites, 1):
            print(f"{i}. Adding website: {site['description']}")
            try:
                # For demo purposes, we'll simulate website content
                # In a real implementation, this would scrape the actual website
                simulated_content = f"""
                Content from {site['url']}
                
                This is simulated content that would be extracted from the website.
                In a real implementation, the system would:
                1. Fetch the webpage content
                2. Extract text from HTML
                3. Clean and process the content
                4. Extract metadata like title, description, etc.
                5. Handle different content types (articles, documentation, etc.)
                
                Category: {site['category']}
                Source: {site['url']}
                """
                
                result = self.add_document(
                    content=simulated_content,
                    metadata={
                        "title": site['description'],
                        "category": site['category'],
                        "source": "website",
                        "url": site['url'],
                        "content_type": "web_page",
                        "extracted_at": "2024-06-29T12:00:00Z"
                    }
                )
                print(f"   ✅ Website content added: {result.get('document_id', 'Unknown ID')}")
                self.added_documents.append(result.get('document_id'))
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    def demo_github_addition(self):
        """Demonstrate adding GitHub repository content."""
        print("\n🐙 GitHub Repository Addition Demo")
        print("-" * 50)
        
        # Example repositories to add (using mock data for demo)
        repositories = [
            {
                "url": "https://github.com/openai/gpt-3",
                "description": "OpenAI GPT-3 implementation",
                "language": "python",
                "type": "ai_model"
            },
            {
                "url": "https://github.com/huggingface/transformers",
                "description": "Hugging Face Transformers library",
                "language": "python",
                "type": "ml_library"
            },
            {
                "url": "https://github.com/microsoft/vscode",
                "description": "Visual Studio Code",
                "language": "typescript",
                "type": "editor"
            }
        ]
        
        for i, repo in enumerate(repositories, 1):
            print(f"{i}. Adding repository: {repo['description']}")
            try:
                # For demo purposes, we'll simulate repository content
                # In a real implementation, this would clone and process the actual repository
                simulated_content = f"""
                Repository: {repo['url']}
                Language: {repo['language']}
                Type: {repo['type']}
                
                README.md:
                # {repo['description']}
                
                This repository contains implementation and documentation for {repo['description'].lower()}.
                
                ## Installation
                ```bash
                pip install package-name
                ```
                
                ## Usage
                ```{repo['language']}
                import package
                
                # Example usage
                model = package.Model()
                result = model.process(data)
                ```
                
                ## Features
                - Feature 1: Advanced functionality
                - Feature 2: Easy-to-use API
                - Feature 3: Comprehensive documentation
                - Feature 4: Active community support
                
                ## Contributing
                Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests.
                
                ## License
                This project is licensed under the MIT License - see the LICENSE file for details.
                """
                
                result = self.add_document(
                    content=simulated_content,
                    metadata={
                        "title": repo['description'],
                        "category": "source_code",
                        "source": "github",
                        "repository_url": repo['url'],
                        "language": repo['language'],
                        "project_type": repo['type'],
                        "content_type": "repository",
                        "cloned_at": "2024-06-29T12:00:00Z"
                    }
                )
                print(f"   ✅ Repository content added: {result.get('document_id', 'Unknown ID')}")
                self.added_documents.append(result.get('document_id'))
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    def demo_structured_data_addition(self):
        """Demonstrate adding structured data."""
        print("\n📊 Structured Data Addition Demo")
        print("-" * 50)
        
        # Example 1: API documentation
        print("1. Adding API documentation...")
        try:
            api_doc = {
                "endpoint": "/v1/chat/completions",
                "method": "POST",
                "description": "Create a chat completion",
                "parameters": {
                    "model": {"type": "string", "required": True, "description": "Model to use"},
                    "messages": {"type": "array", "required": True, "description": "List of messages"},
                    "temperature": {"type": "number", "required": False, "description": "Sampling temperature"}
                },
                "example_request": {
                    "model": "gemma3n:e2b",
                    "messages": [{"role": "user", "content": "Hello!"}],
                    "temperature": 0.7
                },
                "example_response": {
                    "choices": [{"message": {"role": "assistant", "content": "Hello! How can I help you?"}}]
                }
            }
            
            result = self.add_document(
                content=json.dumps(api_doc, indent=2),
                metadata={
                    "title": "Chat Completions API",
                    "category": "api_documentation",
                    "tags": ["api", "chat", "completions"],
                    "format": "json",
                    "endpoint": api_doc["endpoint"]
                }
            )
            print(f"   ✅ API doc added: {result.get('document_id', 'Unknown ID')}")
            self.added_documents.append(result.get('document_id'))
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Example 2: Configuration data
        print("\n2. Adding configuration documentation...")
        try:
            config_doc = {
                "configuration": {
                    "model": {
                        "reasoning_engine": "ollama",
                        "reasoning_model": "gemma3n:e2b",
                        "use_gpu": True,
                        "gpu_memory_fraction": 0.8
                    },
                    "security": {
                        "api_keys_required": True,
                        "rate_limit_requests": 100,
                        "rate_limit_window": 60
                    },
                    "performance": {
                        "cache_enabled": True,
                        "cache_max_size": 10000,
                        "max_concurrent_requests": 50
                    }
                },
                "description": "Configuration options for Neural Symbolic Language Model",
                "examples": {
                    "development": {"model.use_gpu": False, "security.rate_limit_requests": 1000},
                    "production": {"model.use_gpu": True, "security.rate_limit_requests": 100}
                }
            }
            
            result = self.add_document(
                content=json.dumps(config_doc, indent=2),
                metadata={
                    "title": "Configuration Reference",
                    "category": "configuration",
                    "tags": ["config", "settings", "reference"],
                    "format": "json",
                    "type": "reference"
                }
            )
            print(f"   ✅ Config doc added: {result.get('document_id', 'Unknown ID')}")
            self.added_documents.append(result.get('document_id'))
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    def demo_search_and_retrieval(self):
        """Demonstrate search and retrieval capabilities."""
        print("\n🔍 Search and Retrieval Demo")
        print("-" * 50)
        
        # Wait a moment for documents to be indexed
        print("Waiting for documents to be indexed...")
        time.sleep(2)
        
        # Test different search queries
        search_queries = [
            {
                "query": "neural networks and machine learning",
                "description": "General AI/ML query"
            },
            {
                "query": "API documentation and endpoints",
                "description": "API-related query"
            },
            {
                "query": "Python code examples",
                "description": "Code-specific query"
            },
            {
                "query": "configuration and settings",
                "description": "Configuration query"
            }
        ]
        
        for i, search in enumerate(search_queries, 1):
            print(f"\n{i}. Searching: {search['description']}")
            print(f"   Query: '{search['query']}'")
            
            try:
                results = self.search_documents(
                    query=search['query'],
                    limit=3
                )
                
                if results and 'results' in results:
                    print(f"   Found {len(results['results'])} results:")
                    
                    for j, result in enumerate(results['results'], 1):
                        title = result.get('metadata', {}).get('title', 'Untitled')
                        score = result.get('score', 0)
                        content_preview = result.get('content', '')[:100] + "..."
                        
                        print(f"     {j}. {title} (Score: {score:.3f})")
                        print(f"        {content_preview}")
                else:
                    print("   No results found")
                    
            except Exception as e:
                print(f"   ❌ Search error: {e}")
    
    def demo_enhanced_chat(self):
        """Demonstrate chat with enhanced context from retrieval."""
        print("\n💬 Enhanced Chat Demo")
        print("-" * 50)
        
        # Test query that should benefit from retrieved context
        query = "How do neural networks learn and what are the best practices for training them?"
        
        print(f"Query: {query}")
        print("\nStep 1: Searching for relevant context...")
        
        try:
            # Search for relevant context
            search_results = self.search_documents(
                query=query,
                limit=3
            )
            
            if search_results and 'results' in search_results:
                print(f"Found {len(search_results['results'])} relevant documents")
                
                # Build context from search results
                context_parts = []
                for result in search_results['results']:
                    title = result.get('metadata', {}).get('title', 'Document')
                    content = result.get('content', '')[:500]  # Limit content length
                    context_parts.append(f"**{title}**:\n{content}")
                
                context = "\n\n".join(context_parts)
                
                print("\nStep 2: Sending enhanced query to chat completion...")
                
                # Create enhanced prompt
                enhanced_prompt = f"""Based on the following context from my knowledge base, please answer the question:

CONTEXT:
{context}

QUESTION: {query}

Please provide a comprehensive answer based on the context provided above, and feel free to add your own knowledge to supplement the information."""
                
                # Simulate chat completion (in real implementation, this would call the actual API)
                print("\n📝 Enhanced Response (simulated):")
                print("Based on the retrieved context about neural networks and machine learning best practices,")
                print("I can provide you with a comprehensive answer...")
                print("\nThe context shows that neural networks learn through:")
                print("1. Forward propagation of data through layers")
                print("2. Backpropagation to adjust weights based on errors")
                print("3. Iterative optimization using gradient descent")
                print("\nBest practices include:")
                print("- Proper data preprocessing and normalization")
                print("- Cross-validation for hyperparameter tuning")
                print("- Monitoring for overfitting and implementing early stopping")
                print("- Using appropriate evaluation metrics")
                
                print(f"\n📚 Sources used: {[r.get('metadata', {}).get('title', 'Unknown') for r in search_results['results']]}")
            else:
                print("No relevant context found, proceeding with general knowledge...")
                
        except Exception as e:
            print(f"❌ Enhanced chat error: {e}")
    
    def add_document(self, content: str, metadata: dict = None) -> dict:
        """Add a document to the retrieval system."""
        payload = {
            "content": content,
            "metadata": metadata or {}
        }
        
        response = requests.post(
            f"{self.base_url}/documents/add",
            headers=self.headers,
            json=payload,
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    
    def search_documents(self, query: str, limit: int = 10, method: str = "hybrid") -> dict:
        """Search documents in the retrieval system."""
        payload = {
            "query": query,
            "method": method,
            "limit": limit
        }
        
        response = requests.post(
            f"{self.base_url}/documents/search",
            headers=self.headers,
            json=payload,
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    
    def get_document_count(self) -> dict:
        """Get the total number of documents."""
        response = requests.get(
            f"{self.base_url}/documents/count",
            headers=self.headers,
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    
    def cleanup_demo_documents(self):
        """Clean up documents added during the demo."""
        print("\n🧹 Cleaning up demo documents...")
        
        for doc_id in self.added_documents:
            try:
                response = requests.delete(
                    f"{self.base_url}/documents/{doc_id}",
                    headers=self.headers,
                    timeout=10
                )
                if response.status_code == 200:
                    print(f"   ✅ Deleted: {doc_id}")
                else:
                    print(f"   ⚠️ Could not delete: {doc_id}")
            except Exception as e:
                print(f"   ❌ Error deleting {doc_id}: {e}")


def main():
    """Main demonstration function."""
    print("🚀 Neural Symbolic Language Model - Hybrid Retrieval Demo")
    print("=" * 70)
    
    # Configuration
    API_KEY = "your-api-key"  # Replace with your actual API key
    BASE_URL = "http://localhost:8080"
    
    # Initialize demo client
    demo = HybridRetrievalDemo(API_KEY, BASE_URL)
    
    # Health check
    print("🔍 Checking API health...")
    if not demo.health_check():
        print("❌ API is not accessible. Please ensure:")
        print("   1. Neural Symbolic Language Model is running: python src/main.py")
        print("   2. API key is configured correctly")
        print("   3. Port 8080 is accessible")
        return
    
    print("✅ API is healthy and accessible")
    
    try:
        # Get initial document count
        initial_count = demo.get_document_count()
        print(f"📊 Initial document count: {initial_count.get('total_documents', 0)}")
        
        # Run demonstrations
        demo.demo_document_addition()
        demo.demo_website_addition()
        demo.demo_github_addition()
        demo.demo_structured_data_addition()
        demo.demo_search_and_retrieval()
        demo.demo_enhanced_chat()
        
        # Get final document count
        final_count = demo.get_document_count()
        added_count = final_count.get('total_documents', 0) - initial_count.get('total_documents', 0)
        
        print("\n" + "=" * 70)
        print("🎉 Hybrid Retrieval Demo Completed Successfully!")
        print(f"📊 Added {added_count} documents during demo")
        print(f"📚 Total documents in system: {final_count.get('total_documents', 0)}")
        
        print("\n📝 Next Steps:")
        print("1. Replace 'your-api-key' with your actual API key")
        print("2. Try adding your own documents, websites, and repositories")
        print("3. Experiment with different search queries and methods")
        print("4. Integrate retrieval with your chat applications")
        print("5. Explore the comprehensive guide: docs/hybrid_retrieval_guide.md")
        
        # Ask if user wants to clean up
        cleanup = input("\n🧹 Clean up demo documents? (y/n): ").lower().strip()
        if cleanup == 'y':
            demo.cleanup_demo_documents()
        
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":
    main()
