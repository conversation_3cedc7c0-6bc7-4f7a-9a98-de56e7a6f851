

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>retrieval &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Module code</a></li>
      <li class="breadcrumb-item active">retrieval</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for retrieval</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">LightRAG-based retrieval module for the Neural Symbolic Language Model.</span>
<span class="sd">This module provides a wrapper around LightRAG for efficient retrieval-augmented generation.</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">torch</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Union</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">numpy</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">np</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">vector_store</span><span class="w"> </span><span class="kn">import</span> <span class="n">TorchVectorStore</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">exceptions</span><span class="w"> </span><span class="kn">import</span> <span class="n">RetrievalError</span><span class="p">,</span> <span class="n">ConfigurationError</span><span class="p">,</span> <span class="n">VectorStoreError</span>

<span class="c1"># Configure logger</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="c1"># Try to import FAISS with graceful fallback</span>
<span class="k">try</span><span class="p">:</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">faiss</span>
    <span class="n">FAISS_AVAILABLE</span> <span class="o">=</span> <span class="kc">True</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Imported FAISS successfully&quot;</span><span class="p">)</span>
    
    <span class="c1"># Check if GPU FAISS is available</span>
    <span class="n">GPU_FAISS_AVAILABLE</span> <span class="o">=</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">faiss</span><span class="p">,</span> <span class="s1">&#39;StandardGpuResources&#39;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">GPU_FAISS_AVAILABLE</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;GPU FAISS is available&quot;</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Using CPU version of FAISS&quot;</span><span class="p">)</span>
        
<span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;FAISS not available. Using fallback vector storage.&quot;</span><span class="p">)</span>
    <span class="n">FAISS_AVAILABLE</span> <span class="o">=</span> <span class="kc">False</span>
    <span class="n">GPU_FAISS_AVAILABLE</span> <span class="o">=</span> <span class="kc">False</span>

<div class="viewcode-block" id="Retriever">
<a class="viewcode-back" href="../modules.html#retrieval.Retriever">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">Retriever</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Hybrid retrieval system for neural-symbolic language models.</span>

<span class="sd">    This class implements a sophisticated retrieval system that combines</span>
<span class="sd">    vector similarity search with symbolic reasoning for enhanced document</span>
<span class="sd">    retrieval and context generation. It supports multiple vector database</span>
<span class="sd">    backends and GPU acceleration for optimal performance.</span>

<span class="sd">    The retriever uses dense vector embeddings for fast similarity search</span>
<span class="sd">    and can be extended with sparse retrieval methods and reranking for</span>
<span class="sd">    improved accuracy in domain-specific applications.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        use_gpu (bool): Whether GPU acceleration is enabled</span>
<span class="sd">        vector_db (str): The vector database backend being used</span>
<span class="sd">        index: The vector index for similarity search</span>
<span class="sd">        documents (dict): Storage for document content and metadata</span>
<span class="sd">        dimension (int): Vector embedding dimension</span>
<span class="sd">        using_torch_fallback (bool): Whether using PyTorch fallback implementation</span>

<span class="sd">    Example:</span>
<span class="sd">        &gt;&gt;&gt; retriever = Retriever(vector_db=&quot;faiss&quot;, use_gpu=True)</span>
<span class="sd">        &gt;&gt;&gt; retriever.add_document(&quot;Neural networks are powerful.&quot;, &quot;doc1&quot;)</span>
<span class="sd">        &gt;&gt;&gt; results = retriever.search(&quot;What are neural networks?&quot;, k=3)</span>
<span class="sd">        &gt;&gt;&gt; for result in results:</span>
<span class="sd">        ...     print(f&quot;Score: {result[&#39;score&#39;]:.3f}, Text: {result[&#39;text&#39;][:50]}...&quot;)</span>

<span class="sd">    Note:</span>
<span class="sd">        This implementation provides a foundation for hybrid retrieval.</span>
<span class="sd">        In production, this would integrate with more sophisticated</span>
<span class="sd">        embedding models and reranking systems.</span>
<span class="sd">    &quot;&quot;&quot;</span>
<div class="viewcode-block" id="Retriever.__init__">
<a class="viewcode-back" href="../modules.html#retrieval.Retriever.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">vector_db</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;faiss&quot;</span><span class="p">,</span> <span class="n">use_gpu</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the retrieval system.</span>

<span class="sd">        Args:</span>
<span class="sd">            vector_db: The vector database to use (e.g., &quot;faiss&quot;, &quot;chromadb&quot;)</span>
<span class="sd">            use_gpu: Whether to use GPU acceleration if available</span>

<span class="sd">        Raises:</span>
<span class="sd">            ConfigurationError: If initialization fails</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Check GPU availability</span>
            <span class="n">gpu_available</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">()</span>
            <span class="k">if</span> <span class="n">use_gpu</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">gpu_available</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;GPU requested but not available. Falling back to CPU.&quot;</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span> <span class="o">=</span> <span class="n">use_gpu</span> <span class="ow">and</span> <span class="n">gpu_available</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">vector_db</span> <span class="o">=</span> <span class="n">vector_db</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">index</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">documents</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">setup_vector_db</span><span class="p">()</span>

            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Retriever initialized with </span><span class="si">{</span><span class="n">vector_db</span><span class="si">}</span><span class="s2"> backend, GPU: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Failed to initialize retriever: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="k">raise</span> <span class="n">ConfigurationError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Retriever initialization failed: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="Retriever.setup_vector_db">
<a class="viewcode-back" href="../modules.html#retrieval.Retriever.setup_vector_db">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">setup_vector_db</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Set up the vector database based on the selected backend.&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dimension</span> <span class="o">=</span> <span class="mi">768</span>  <span class="c1"># Default BERT embedding dimension</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">using_torch_fallback</span> <span class="o">=</span> <span class="kc">False</span>
        
        <span class="c1"># Check if FAISS is available</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">FAISS_AVAILABLE</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;FAISS not available. Using PyTorch vector store fallback.&quot;</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">index</span> <span class="o">=</span> <span class="n">TorchVectorStore</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">dimension</span><span class="p">,</span> <span class="n">use_gpu</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">using_torch_fallback</span> <span class="o">=</span> <span class="kc">True</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span> <span class="ow">and</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">():</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Using PyTorch GPU vector store on: </span><span class="si">{</span><span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">get_device_name</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">return</span>
            
        <span class="c1"># FAISS is available</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">vector_db</span> <span class="o">==</span> <span class="s2">&quot;faiss&quot;</span><span class="p">:</span>
            <span class="c1"># Create the base index</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">index</span> <span class="o">=</span> <span class="n">faiss</span><span class="o">.</span><span class="n">IndexFlatL2</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">dimension</span><span class="p">)</span>
            
            <span class="c1"># Try to use GPU FAISS if available and requested</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span> <span class="ow">and</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">():</span>
                <span class="k">if</span> <span class="n">GPU_FAISS_AVAILABLE</span><span class="p">:</span>
                    <span class="k">try</span><span class="p">:</span>
                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Setting up GPU-optimized FAISS index&quot;</span><span class="p">)</span>
                        <span class="c1"># Get GPU resources</span>
                        <span class="n">res</span> <span class="o">=</span> <span class="n">faiss</span><span class="o">.</span><span class="n">StandardGpuResources</span><span class="p">()</span>
                        
                        <span class="c1"># Move the index to GPU</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">index</span> <span class="o">=</span> <span class="n">faiss</span><span class="o">.</span><span class="n">index_cpu_to_gpu</span><span class="p">(</span><span class="n">res</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="p">)</span>
                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;FAISS index using GPU: </span><span class="si">{</span><span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">get_device_name</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error setting up GPU FAISS: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Falling back to PyTorch GPU vector store&quot;</span><span class="p">)</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">index</span> <span class="o">=</span> <span class="n">TorchVectorStore</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">dimension</span><span class="p">,</span> <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">using_torch_fallback</span> <span class="o">=</span> <span class="kc">True</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="c1"># No GPU FAISS but GPU is available, use PyTorch fallback</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;GPU FAISS not available but GPU detected. Using PyTorch GPU vector store.&quot;</span><span class="p">)</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">index</span> <span class="o">=</span> <span class="n">TorchVectorStore</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">dimension</span><span class="p">,</span> <span class="n">use_gpu</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">using_torch_fallback</span> <span class="o">=</span> <span class="kc">True</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># CPU mode</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="s2">&quot;Using CPU FAISS&quot;</span><span class="p">)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Unsupported vector database: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">vector_db</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="Retriever.add_documents">
<a class="viewcode-back" href="../modules.html#retrieval.Retriever.add_documents">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">add_documents</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">documents</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]],</span> <span class="n">embeddings</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Add documents to the retrieval system.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            documents: List of document dictionaries with &#39;id&#39; and &#39;text&#39; keys</span>
<span class="sd">            embeddings: Optional pre-computed embeddings for the documents</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">documents</span><span class="p">:</span>
            <span class="k">return</span>
            
        <span class="k">if</span> <span class="n">embeddings</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># TODO: Implement document embedding using a language model</span>
            <span class="c1"># For now, use random embeddings for testing</span>
            <span class="n">embeddings</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">randn</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">documents</span><span class="p">),</span> <span class="bp">self</span><span class="o">.</span><span class="n">dimension</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;float32&#39;</span><span class="p">)</span>
        
        <span class="c1"># Add documents to the index based on the backend type</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">using_torch_fallback</span><span class="p">:</span>
            <span class="c1"># Extract document IDs and texts</span>
            <span class="n">doc_ids</span> <span class="o">=</span> <span class="p">[</span><span class="n">doc</span><span class="p">[</span><span class="s1">&#39;id&#39;</span><span class="p">]</span> <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">documents</span><span class="p">]</span>
            <span class="n">texts</span> <span class="o">=</span> <span class="p">[</span><span class="n">doc</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span> <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">documents</span><span class="p">]</span>
            
            <span class="c1"># Use the TorchVectorStore add method</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">embeddings</span><span class="p">,</span> <span class="n">doc_ids</span><span class="p">,</span> <span class="n">texts</span><span class="p">)</span>
            
            <span class="c1"># Store document metadata in our local dictionary as well</span>
            <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">documents</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="p">[</span><span class="n">doc</span><span class="p">[</span><span class="s1">&#39;id&#39;</span><span class="p">]]</span> <span class="o">=</span> <span class="n">doc</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Using FAISS</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="c1"># Add embeddings to FAISS index</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">embeddings</span><span class="p">)</span>
                
                <span class="c1"># Store document metadata</span>
                <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">documents</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="p">[</span><span class="n">doc</span><span class="p">[</span><span class="s1">&#39;id&#39;</span><span class="p">]]</span> <span class="o">=</span> <span class="n">doc</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span>
            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error adding documents to FAISS index: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                <span class="k">return</span></div>

    
<div class="viewcode-block" id="Retriever.search">
<a class="viewcode-back" href="../modules.html#retrieval.Retriever.search">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">search</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">query</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">k</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">5</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Union</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">float</span><span class="p">]]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Search for relevant documents using hybrid retrieval methods.</span>

<span class="sd">        This method performs a two-stage search process: first using dense vector</span>
<span class="sd">        similarity search for fast retrieval, then optionally applying reranking</span>
<span class="sd">        for improved accuracy. The search supports both FAISS and PyTorch backends</span>
<span class="sd">        with automatic fallback handling.</span>

<span class="sd">        :param query: The search query text. Must be a non-empty string containing</span>
<span class="sd">                     the question or topic to search for. Maximum length is 10,000</span>
<span class="sd">                     characters. The query will be embedded into a dense vector</span>
<span class="sd">                     for similarity comparison.</span>
<span class="sd">        :type query: str</span>
<span class="sd">        :param k: Number of top results to return. Must be a positive integer.</span>
<span class="sd">                 Higher values return more results but may include less relevant</span>
<span class="sd">                 documents. Typical values range from 3-10 for most applications.</span>
<span class="sd">        :type k: int</span>

<span class="sd">        :returns: List of dictionaries containing search results, sorted by</span>
<span class="sd">                 relevance score in descending order. Each dictionary contains:</span>

<span class="sd">                 - **id** (str): Unique document identifier</span>
<span class="sd">                 - **text** (str): Full document content</span>
<span class="sd">                 - **score** (float): Relevance score between 0.0 and 1.0,</span>
<span class="sd">                   where 1.0 indicates perfect relevance</span>
<span class="sd">        :rtype: List[Dict[str, Union[str, float]]]</span>

<span class="sd">        :raises RetrievalError: If the search operation fails due to:</span>
<span class="sd">                               - Empty or invalid query</span>
<span class="sd">                               - Vector database errors</span>
<span class="sd">                               - Embedding generation failures</span>
<span class="sd">        :raises ValidationError: If input validation fails due to:</span>
<span class="sd">                                - Non-positive k value</span>
<span class="sd">                                - Query exceeding maximum length</span>
<span class="sd">                                - Invalid query format</span>

<span class="sd">        :example:</span>

<span class="sd">        &gt;&gt;&gt; retriever = Retriever()</span>
<span class="sd">        &gt;&gt;&gt; retriever.add_document(&quot;Neural networks learn patterns.&quot;, &quot;doc1&quot;)</span>
<span class="sd">        &gt;&gt;&gt; retriever.add_document(&quot;Symbolic AI uses logical rules.&quot;, &quot;doc2&quot;)</span>
<span class="sd">        &gt;&gt;&gt;</span>
<span class="sd">        &gt;&gt;&gt; # Basic search</span>
<span class="sd">        &gt;&gt;&gt; results = retriever.search(&quot;machine learning&quot;, k=2)</span>
<span class="sd">        &gt;&gt;&gt; for result in results:</span>
<span class="sd">        ...     print(f&quot;Score: {result[&#39;score&#39;]:.3f}&quot;)</span>
<span class="sd">        ...     print(f&quot;Text: {result[&#39;text&#39;][:50]}...&quot;)</span>
<span class="sd">        Score: 0.856</span>
<span class="sd">        Text: Neural networks learn patterns...</span>
<span class="sd">        Score: 0.234</span>
<span class="sd">        Text: Symbolic AI uses logical rules...</span>
<span class="sd">        &gt;&gt;&gt;</span>
<span class="sd">        &gt;&gt;&gt; # Search with specific number of results</span>
<span class="sd">        &gt;&gt;&gt; results = retriever.search(&quot;neural networks&quot;, k=1)</span>
<span class="sd">        &gt;&gt;&gt; print(f&quot;Found {len(results)} result(s)&quot;)</span>
<span class="sd">        Found 1 result(s)</span>

<span class="sd">        .. note::</span>
<span class="sd">           Search performance depends on the vector database backend and</span>
<span class="sd">           the quality of the embedding model. GPU acceleration significantly</span>
<span class="sd">           improves search speed for large document collections.</span>

<span class="sd">        .. warning::</span>
<span class="sd">           This implementation uses random vectors for testing. In production,</span>
<span class="sd">           replace with proper embedding models like BERT, Sentence-BERT,</span>
<span class="sd">           or domain-specific embeddings.</span>

<span class="sd">        .. seealso::</span>
<span class="sd">           :meth:`add_document` for adding documents to search</span>
<span class="sd">           :meth:`get_system_info` for checking backend capabilities</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">query</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">query</span><span class="o">.</span><span class="n">strip</span><span class="p">():</span>
                <span class="k">raise</span> <span class="n">RetrievalError</span><span class="p">(</span><span class="s2">&quot;Query cannot be empty&quot;</span><span class="p">,</span> <span class="n">operation</span><span class="o">=</span><span class="s2">&quot;search&quot;</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">k</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">RetrievalError</span><span class="p">(</span><span class="s2">&quot;k must be positive&quot;</span><span class="p">,</span> <span class="n">operation</span><span class="o">=</span><span class="s2">&quot;search&quot;</span><span class="p">)</span>

            <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Searching for: </span><span class="si">{</span><span class="n">query</span><span class="p">[:</span><span class="mi">100</span><span class="p">]</span><span class="si">}</span><span class="s2">... (k=</span><span class="si">{</span><span class="n">k</span><span class="si">}</span><span class="s2">)&quot;</span><span class="p">)</span>

            <span class="c1"># TODO: Implement query embedding using a language model</span>
            <span class="c1"># For now, use a random vector for testing</span>
            <span class="n">query_vector</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">randn</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">dimension</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;float32&#39;</span><span class="p">)</span>

            <span class="c1"># Handle search differently based on the backend</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">using_torch_fallback</span><span class="p">:</span>
                <span class="c1"># For TorchVectorStore</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="n">results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">query_vector</span><span class="p">,</span> <span class="n">k</span><span class="p">)</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;TorchVectorStore returned </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">)</span><span class="si">}</span><span class="s2"> results&quot;</span><span class="p">)</span>
                    <span class="k">return</span> <span class="n">results</span>
                <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error searching with TorchVectorStore: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
                    <span class="k">raise</span> <span class="n">VectorStoreError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;TorchVectorStore search failed: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">operation</span><span class="o">=</span><span class="s2">&quot;search&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># For FAISS</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="c1"># If the index is not initialized or empty</span>
                    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">index</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                        <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;No documents available for search&quot;</span><span class="p">)</span>
                        <span class="k">return</span> <span class="p">[]</span>

                    <span class="c1"># Perform the search</span>
                    <span class="n">distances</span><span class="p">,</span> <span class="n">indices</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">query_vector</span><span class="p">,</span> <span class="n">k</span><span class="p">)</span>

                    <span class="c1"># Format results</span>
                    <span class="n">results</span> <span class="o">=</span> <span class="p">[]</span>
                    <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">dist</span><span class="p">,</span> <span class="n">idx</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="nb">zip</span><span class="p">(</span><span class="n">distances</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">indices</span><span class="p">[</span><span class="mi">0</span><span class="p">])):</span>
                        <span class="k">if</span> <span class="n">idx</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="ow">or</span> <span class="n">idx</span> <span class="o">&gt;=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="p">):</span>
                            <span class="k">continue</span>
                        <span class="n">doc_id</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="o">.</span><span class="n">keys</span><span class="p">())[</span><span class="n">idx</span><span class="p">]</span>
                        <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">({</span>
                            <span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="n">doc_id</span><span class="p">,</span>
                            <span class="s1">&#39;text&#39;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="p">[</span><span class="n">doc_id</span><span class="p">],</span>
                            <span class="s1">&#39;score&#39;</span><span class="p">:</span> <span class="nb">float</span><span class="p">(</span><span class="mf">1.0</span> <span class="o">/</span> <span class="p">(</span><span class="mf">1.0</span> <span class="o">+</span> <span class="n">dist</span><span class="p">))</span>  <span class="c1"># Convert distance to similarity score</span>
                        <span class="p">})</span>

                    <span class="n">logger</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;FAISS returned </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">results</span><span class="p">)</span><span class="si">}</span><span class="s2"> results&quot;</span><span class="p">)</span>
                    <span class="k">return</span> <span class="n">results</span>
                <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error searching with FAISS: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
                    <span class="k">raise</span> <span class="n">VectorStoreError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;FAISS search failed: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">operation</span><span class="o">=</span><span class="s2">&quot;search&quot;</span><span class="p">)</span>

        <span class="k">except</span> <span class="p">(</span><span class="n">RetrievalError</span><span class="p">,</span> <span class="n">VectorStoreError</span><span class="p">):</span>
            <span class="k">raise</span>
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Unexpected error in search: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="k">raise</span> <span class="n">RetrievalError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Search operation failed: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">operation</span><span class="o">=</span><span class="s2">&quot;search&quot;</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="Retriever.get_system_info">
<a class="viewcode-back" href="../modules.html#retrieval.Retriever.get_system_info">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_system_info</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Union</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">bool</span><span class="p">]]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get information about the system configuration.</span>
<span class="sd">        </span>
<span class="sd">        Returns:</span>
<span class="sd">            dict: System configuration information</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="p">{</span>
            <span class="s1">&#39;vector_db&#39;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">vector_db</span><span class="p">,</span>
            <span class="s1">&#39;gpu_enabled&#39;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="p">,</span>
            <span class="s1">&#39;gpu_available&#39;</span><span class="p">:</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">(),</span>
            <span class="s2">&quot;gpu_name&quot;</span><span class="p">:</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">get_device_name</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span> <span class="k">if</span> <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">is_available</span><span class="p">()</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="s2">&quot;index_size&quot;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="p">)</span>
        <span class="p">}</span></div>


<div class="viewcode-block" id="Retriever.optimize_index">
<a class="viewcode-back" href="../modules.html#retrieval.Retriever.optimize_index">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">optimize_index</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Optimize the FAISS index for better performance.</span>
<span class="sd">        </span>
<span class="sd">        This should be called after adding a significant number of documents.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">True</span>
            
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Clear any unused memory</span>
            <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">empty_cache</span><span class="p">()</span>
            
            <span class="c1"># If index is not trained (for IVF indexes), train it</span>
            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="p">,</span> <span class="s1">&#39;is_trained&#39;</span><span class="p">)</span> <span class="ow">and</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">is_trained</span><span class="p">:</span>
                <span class="c1"># Generate training data</span>
                <span class="n">train_size</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="p">),</span> <span class="mi">100000</span><span class="p">)</span>
                <span class="n">train_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">randn</span><span class="p">(</span><span class="n">train_size</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">dimension</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;float32&#39;</span><span class="p">)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">train</span><span class="p">(</span><span class="n">train_data</span><span class="p">)</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Trained IVF index&quot;</span><span class="p">)</span>
            
            <span class="c1"># If using GPU, ensure we&#39;re using float16 for better performance</span>
            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="p">,</span> <span class="s1">&#39;getNumProbes&#39;</span><span class="p">):</span>
                <span class="c1"># For IVF indexes, set number of probes for better recall</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">nprobe</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="mi">32</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">nlist</span><span class="p">)</span>
            
            <span class="k">return</span> <span class="kc">True</span>
            
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error optimizing index: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">False</span></div>


<div class="viewcode-block" id="Retriever.batch_add_documents">
<a class="viewcode-back" href="../modules.html#retrieval.Retriever.batch_add_documents">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">batch_add_documents</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">documents</span><span class="p">,</span> <span class="n">batch_size</span><span class="o">=</span><span class="mi">32</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Add multiple documents in batches for better performance.</span>
<span class="sd">        </span>
<span class="sd">        Args:</span>
<span class="sd">            documents (list): List of documents to add</span>
<span class="sd">            batch_size (int): Size of each batch</span>
<span class="sd">            </span>
<span class="sd">        Returns:</span>
<span class="sd">            bool: True if successful</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Pre-allocate GPU memory for better performance</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="p">:</span>
                <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">empty_cache</span><span class="p">()</span>
                <span class="c1"># Reserve some GPU memory</span>
                <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">set_per_process_memory_fraction</span><span class="p">(</span><span class="mf">0.8</span><span class="p">)</span>
            
            <span class="c1"># Process in batches</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">documents</span><span class="p">),</span> <span class="n">batch_size</span><span class="p">):</span>
                <span class="n">batch</span> <span class="o">=</span> <span class="n">documents</span><span class="p">[</span><span class="n">i</span><span class="p">:</span><span class="n">i</span> <span class="o">+</span> <span class="n">batch_size</span><span class="p">]</span>
                
                <span class="c1"># Generate embeddings for the batch</span>
                <span class="c1"># TODO: Replace with actual embedding model</span>
                <span class="n">embeddings</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">randn</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">batch</span><span class="p">),</span> <span class="bp">self</span><span class="o">.</span><span class="n">dimension</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;float32&#39;</span><span class="p">)</span>
                
                <span class="c1"># Extract document IDs and texts</span>
                <span class="n">doc_ids</span> <span class="o">=</span> <span class="p">[</span><span class="n">doc</span><span class="p">[</span><span class="s1">&#39;id&#39;</span><span class="p">]</span> <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">batch</span><span class="p">]</span>
                <span class="n">texts</span> <span class="o">=</span> <span class="p">[</span><span class="n">doc</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span> <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">batch</span><span class="p">]</span>
                
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">using_torch_fallback</span><span class="p">:</span>
                    <span class="c1"># Use TorchVectorStore&#39;s add method</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">embeddings</span><span class="p">,</span> <span class="n">doc_ids</span><span class="p">,</span> <span class="n">texts</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="c1"># Using FAISS</span>
                    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="p">:</span>
                        <span class="c1"># Move embeddings to GPU if needed</span>
                        <span class="n">gpu_embeddings</span> <span class="o">=</span> <span class="n">torch</span><span class="o">.</span><span class="n">from_numpy</span><span class="p">(</span><span class="n">embeddings</span><span class="p">)</span><span class="o">.</span><span class="n">cuda</span><span class="p">()</span>
                        <span class="n">embeddings</span> <span class="o">=</span> <span class="n">gpu_embeddings</span><span class="o">.</span><span class="n">cpu</span><span class="p">()</span><span class="o">.</span><span class="n">numpy</span><span class="p">()</span>
                    
                    <span class="c1"># Add to FAISS index</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">index</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">embeddings</span><span class="p">)</span>
                
                <span class="c1"># Store document metadata in our local dictionary</span>
                <span class="k">for</span> <span class="n">doc</span> <span class="ow">in</span> <span class="n">batch</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">documents</span><span class="p">[</span><span class="n">doc</span><span class="p">[</span><span class="s1">&#39;id&#39;</span><span class="p">]]</span> <span class="o">=</span> <span class="n">doc</span><span class="p">[</span><span class="s1">&#39;text&#39;</span><span class="p">]</span>
                
                <span class="c1"># Optional: Print progress</span>
                <span class="n">progress</span> <span class="o">=</span> <span class="p">(</span><span class="n">i</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">batch</span><span class="p">))</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">documents</span><span class="p">)</span> <span class="o">*</span> <span class="mi">100</span>
                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Progress: </span><span class="si">{</span><span class="n">progress</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">% (</span><span class="si">{</span><span class="n">i</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nb">len</span><span class="p">(</span><span class="n">batch</span><span class="p">)</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">documents</span><span class="p">)</span><span class="si">}</span><span class="s2"> documents)&quot;</span><span class="p">)</span>
            
            <span class="c1"># Final optimization</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">use_gpu</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">optimize_index</span><span class="p">()</span>
                <span class="n">torch</span><span class="o">.</span><span class="n">cuda</span><span class="o">.</span><span class="n">empty_cache</span><span class="p">()</span>
            
            <span class="k">return</span> <span class="kc">True</span>
            
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error in batch_add_documents: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">False</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>