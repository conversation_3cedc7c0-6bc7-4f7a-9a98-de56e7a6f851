2025-05-20 00:32:59,080 - root - INFO - Logging system initialized
2025-05-20 00:32:59,118 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:32:59,183 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:32:59,243 - __mp_main__ - INFO - Initializing components...
2025-05-20 00:32:59,284 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:32:59,325 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:32:59,364 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:32:59,403 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:32:59,446 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 00:32:59,508 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:32:59,597 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 00:32:59,929 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 00:33:00,132 - root - INFO - Logging system initialized
2025-05-20 00:33:00,138 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:33:00,149 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:33:00,178 - main - INFO - Initializing components...
2025-05-20 00:33:00,184 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:33:00,191 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:33:00,197 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:33:00,204 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:33:00,211 - main - INFO - Components initialized successfully
2025-05-20 00:33:00,217 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:33:00,232 - main - WARNING - No documents found in data directory
2025-05-20 00:33:40,741 - main - INFO - Application shutting down
2025-05-20 00:34:00,153 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:34:03,709 - root - INFO - Logging system initialized
2025-05-20 00:34:03,720 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:34:03,749 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:34:03,755 - __mp_main__ - INFO - Initializing components...
2025-05-20 00:34:03,779 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:34:03,785 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:34:03,791 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:34:03,799 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:34:03,806 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 00:34:03,814 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:34:03,854 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 00:34:04,146 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 00:34:04,379 - root - INFO - Logging system initialized
2025-05-20 00:34:04,385 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:34:04,395 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:34:04,420 - main - INFO - Initializing components...
2025-05-20 00:34:04,426 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:34:04,431 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:34:04,437 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:34:04,445 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:34:04,450 - main - INFO - Components initialized successfully
2025-05-20 00:34:04,457 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:34:04,479 - main - WARNING - No documents found in data directory
2025-05-20 00:34:56,877 - main - INFO - Application shutting down
2025-05-20 00:35:04,409 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:35:07,853 - root - INFO - Logging system initialized
2025-05-20 00:35:07,859 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:35:07,884 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:35:07,893 - __mp_main__ - INFO - Initializing components...
2025-05-20 00:35:07,917 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:35:07,921 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:35:07,930 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:35:07,936 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:35:07,941 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 00:35:07,948 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:35:07,968 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 00:35:08,221 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 00:35:08,461 - root - INFO - Logging system initialized
2025-05-20 00:35:08,502 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:35:08,550 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:35:08,579 - main - INFO - Initializing components...
2025-05-20 00:35:08,586 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:35:08,592 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:35:08,600 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:35:08,608 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:35:08,613 - main - INFO - Components initialized successfully
2025-05-20 00:35:08,620 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:35:08,639 - main - WARNING - No documents found in data directory
2025-05-20 00:37:26,694 - main - INFO - Application shutting down
2025-05-20 00:38:08,573 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:38:13,148 - root - INFO - Logging system initialized
2025-05-20 00:38:13,156 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:38:13,189 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:38:13,224 - __mp_main__ - INFO - Initializing components...
2025-05-20 00:38:13,239 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:38:13,247 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:38:13,252 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:38:13,260 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:38:13,265 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 00:38:13,271 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:38:13,307 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 00:38:13,598 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 00:38:13,831 - root - INFO - Logging system initialized
2025-05-20 00:38:13,841 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:38:13,862 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:38:13,968 - main - INFO - Initializing components...
2025-05-20 00:38:14,006 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:38:14,026 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:38:14,041 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:38:14,056 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:38:14,072 - main - INFO - Components initialized successfully
2025-05-20 00:38:14,080 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:38:14,097 - main - WARNING - No documents found in data directory
2025-05-20 00:41:18,237 - main - INFO - Application shutting down
2025-05-20 00:41:48,628 - root - INFO - Logging system initialized
2025-05-20 00:41:48,656 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:41:48,680 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:41:48,738 - main - INFO - Initializing components...
2025-05-20 00:41:48,745 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:41:48,752 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:41:48,758 - main - INFO - Components initialized successfully
2025-05-20 00:41:48,766 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-20 00:41:48,794 - main - WARNING - No documents found in data directory
2025-05-20 00:41:49,364 - src.retrieval - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 00:41:49,796 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 422 Unprocessable Entity"
2025-05-20 00:41:49,806 - main - INFO - Received chat request for model: local
2025-05-20 00:41:49,806 - main - WARNING - No user message found in request
2025-05-20 00:41:49,817 - monitoring - WARNING - Request chat-2e32344d-2395-4d23-877f-a95a09efb5da failed after 0.01s: No user message found
2025-05-20 00:41:49,822 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 00:41:49,831 - main - INFO - Received chat request for model: local
2025-05-20 00:41:49,842 - main - WARNING - No user message found in request
2025-05-20 00:41:49,847 - monitoring - WARNING - Request chat-48dcbdea-97b7-471f-8b13-0d0afbbdc60a failed after 0.02s: No user message found
2025-05-20 00:41:49,856 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 00:41:49,867 - main - INFO - Received chat request for model: local
2025-05-20 00:41:49,874 - main - INFO - Performing retrieval operation
2025-05-20 00:41:49,879 - main - ERROR - Error processing request: 'MockRetriever' object has no attribute 'query'
Traceback (most recent call last):
  File "\\WDMyCloud\Marshall\Symbolic Language Model\src\main.py", line 699, in openai_chat
    retrieved_info = await asyncio.to_thread(retriever.query, last_message)
AttributeError: 'MockRetriever' object has no attribute 'query'
2025-05-20 00:41:49,899 - monitoring - WARNING - Request chat-2e5901e4-c92b-4149-a5b6-4820f5a9f013 failed after 0.03s: 'MockRetriever' object has no attribute 'query'
2025-05-20 00:41:49,905 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 500 Internal Server Error"
2025-05-20 00:41:49,994 - main - INFO - Retrieving performance statistics
2025-05-20 00:41:50,001 - main - INFO - Successfully retrieved performance statistics
2025-05-20 00:41:50,006 - httpx - INFO - HTTP Request: GET http://testserver/performance "HTTP/1.1 200 OK"
2025-05-20 00:41:50,020 - main - INFO - Received chat request for model: local
2025-05-20 00:41:50,027 - main - INFO - Request requires streaming, forwarding to streaming endpoint
2025-05-20 00:41:50,030 - monitoring - INFO - Request chat-ed50ed71-1dc9-4074-b5ee-2bac4a8543c7 completed in 0.01s
2025-05-20 00:41:50,034 - main - INFO - Received streaming chat request for model: local
2025-05-20 00:41:50,034 - main - INFO - Performing retrieval operation for streaming request
2025-05-20 00:41:50,047 - main - ERROR - Error processing streaming request: 'MockRetriever' object has no attribute 'query'
Traceback (most recent call last):
  File "\\WDMyCloud\Marshall\Symbolic Language Model\src\main.py", line 551, in openai_chat_stream
    retrieved_info = await asyncio.to_thread(retriever.query, last_message)
AttributeError: 'MockRetriever' object has no attribute 'query'
2025-05-20 00:41:50,073 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 500 Internal Server Error"
2025-05-20 00:41:50,084 - main - INFO - Retrieving system configuration information
2025-05-20 00:41:50,094 - main - INFO - Successfully retrieved system configuration
2025-05-20 00:41:50,097 - httpx - INFO - HTTP Request: GET http://testserver/system/info "HTTP/1.1 200 OK"
2025-05-20 00:41:50,115 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:41:50,120 - monitoring - INFO - Request test-456 completed in 0.00s
2025-05-20 00:41:51,119 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:41:51,130 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:41:51,138 - monitoring - WARNING - Request test-789 failed after 0.00s: Test error
2025-05-20 00:41:51,144 - monitoring - INFO - Request test-790 completed in 0.00s
2025-05-20 00:41:52,144 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:41:52,207 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:41:52,248 - monitoring - INFO - Request recent completed in 0.00s
2025-05-20 00:41:53,216 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:41:53,277 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:41:53,322 - monitoring - INFO - Request test-123 completed in 0.00s
2025-05-20 00:41:54,287 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:41:54,358 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:41:57,378 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:41:57,559 - src.retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:41:57,596 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:41:57,916 - src.retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:41:57,955 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:41:58,096 - src.retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:41:58,136 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:41:58,185 - src.retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:41:58,232 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:42:13,904 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:42:19,380 - root - INFO - Logging system initialized
2025-05-20 00:42:19,383 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:42:19,394 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:42:19,417 - __mp_main__ - INFO - Initializing components...
2025-05-20 00:42:19,435 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:42:19,446 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:42:19,453 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:42:19,455 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:42:19,465 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 00:42:19,471 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:42:19,493 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 00:42:19,765 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 00:42:20,022 - root - INFO - Logging system initialized
2025-05-20 00:42:20,031 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:42:20,045 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:42:20,074 - main - INFO - Initializing components...
2025-05-20 00:42:20,080 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:42:20,091 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:42:20,099 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:42:20,107 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:42:20,112 - main - INFO - Components initialized successfully
2025-05-20 00:42:20,119 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:42:20,138 - main - WARNING - No documents found in data directory
2025-05-20 00:58:51,444 - main - INFO - Application shutting down
2025-05-20 00:59:20,234 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 00:59:24,012 - root - INFO - Logging system initialized
2025-05-20 00:59:24,018 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:59:24,041 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:59:24,051 - __mp_main__ - INFO - Initializing components...
2025-05-20 00:59:24,071 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:59:24,076 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:59:24,082 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:59:24,088 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:59:24,095 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 00:59:24,100 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:59:24,107 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 00:59:24,403 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 00:59:24,885 - root - INFO - Logging system initialized
2025-05-20 00:59:25,217 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 00:59:25,847 - monitoring - INFO - Performance monitoring initialized
2025-05-20 00:59:29,504 - main - INFO - Initializing components...
2025-05-20 00:59:31,041 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 00:59:31,169 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 00:59:31,391 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 00:59:31,521 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 00:59:31,560 - main - INFO - Components initialized successfully
2025-05-20 00:59:31,636 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 00:59:31,751 - main - WARNING - No documents found in data directory
2025-05-20 00:59:31,867 - main - INFO - Application shutting down
2025-05-20 01:00:25,858 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 01:00:29,316 - root - INFO - Logging system initialized
2025-05-20 01:00:29,322 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 01:00:29,344 - monitoring - INFO - Performance monitoring initialized
2025-05-20 01:00:29,350 - __mp_main__ - INFO - Initializing components...
2025-05-20 01:00:29,371 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 01:00:29,378 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 01:00:29,386 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 01:00:29,393 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 01:00:29,400 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 01:00:29,406 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 01:00:29,413 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 01:00:29,704 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 01:00:29,950 - root - INFO - Logging system initialized
2025-05-20 01:00:29,965 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 01:00:30,006 - monitoring - INFO - Performance monitoring initialized
2025-05-20 01:00:30,031 - main - INFO - Initializing components...
2025-05-20 01:00:30,032 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 01:00:30,044 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 01:00:30,050 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 01:00:30,057 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 01:00:30,064 - main - INFO - Components initialized successfully
2025-05-20 01:00:30,070 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 01:00:30,092 - main - WARNING - No documents found in data directory
2025-05-20 01:00:34,622 - main - INFO - Application shutting down
2025-05-20 01:01:30,013 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 01:01:34,446 - root - INFO - Logging system initialized
2025-05-20 01:01:34,451 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 01:01:34,466 - monitoring - INFO - Performance monitoring initialized
2025-05-20 01:01:34,481 - __mp_main__ - INFO - Initializing components...
2025-05-20 01:01:34,505 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 01:01:34,505 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 01:01:34,517 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 01:01:34,525 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 01:01:34,531 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 01:01:34,537 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 01:01:34,571 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 01:01:34,863 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 01:01:35,132 - root - INFO - Logging system initialized
2025-05-20 01:01:35,138 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 01:01:35,152 - monitoring - INFO - Performance monitoring initialized
2025-05-20 01:01:35,180 - main - INFO - Initializing components...
2025-05-20 01:01:35,186 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 01:01:35,194 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 01:01:35,202 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 01:01:35,216 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 01:01:35,222 - main - INFO - Components initialized successfully
2025-05-20 01:01:35,231 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 01:01:35,254 - main - WARNING - No documents found in data directory
2025-05-20 04:01:51,639 - main - INFO - Application shutting down
2025-05-20 04:02:37,261 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 04:02:41,203 - root - INFO - Logging system initialized
2025-05-20 04:02:41,211 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:02:41,238 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:02:41,268 - __mp_main__ - INFO - Initializing components...
2025-05-20 04:02:41,275 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:02:41,283 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:02:41,291 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:02:41,299 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:02:41,305 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 04:02:41,314 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:02:41,343 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 04:02:41,620 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 04:02:41,841 - root - INFO - Logging system initialized
2025-05-20 04:02:41,846 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:02:41,860 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:02:41,886 - main - INFO - Initializing components...
2025-05-20 04:02:41,912 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:02:41,958 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:02:41,989 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:02:42,002 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:02:42,024 - main - INFO - Components initialized successfully
2025-05-20 04:02:42,038 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:02:42,062 - main - WARNING - No documents found in data directory
2025-05-20 04:06:07,848 - root - INFO - Logging system initialized
2025-05-20 04:06:07,914 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:06:07,930 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:06:07,930 - main - INFO - Initializing components...
2025-05-20 04:06:07,959 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:06:07,965 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:06:07,967 - main - INFO - Components initialized successfully
2025-05-20 04:06:07,976 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-20 04:06:07,979 - main - WARNING - No documents found in data directory
2025-05-20 04:06:08,060 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 422 Unprocessable Entity"
2025-05-20 04:06:08,067 - main - INFO - Received chat request for model: local
2025-05-20 04:06:08,073 - main - WARNING - No user message found in request
2025-05-20 04:06:08,080 - monitoring - WARNING - Request chat-041b716f-f310-434f-aa67-b710f3cfc6fc failed after 0.01s: No user message found
2025-05-20 04:06:08,080 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:06:08,096 - main - INFO - Received chat request for model: local
2025-05-20 04:06:08,106 - main - WARNING - No user message found in request
2025-05-20 04:06:08,112 - monitoring - WARNING - Request chat-219dd981-5c34-4593-9673-e945fd2e2079 failed after 0.02s: No user message found
2025-05-20 04:06:08,119 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:06:08,132 - main - INFO - Received chat request for model: local
2025-05-20 04:06:08,139 - main - INFO - Performing retrieval operation
2025-05-20 04:06:08,151 - main - ERROR - Error processing request: 'MockRetriever' object has no attribute 'query'
Traceback (most recent call last):
  File "\\WDMyCloud\Marshall\Symbolic Language Model\src\main.py", line 699, in openai_chat
    retrieved_info = await asyncio.to_thread(retriever.query, last_message)
AttributeError: 'MockRetriever' object has no attribute 'query'
2025-05-20 04:06:08,199 - monitoring - WARNING - Request chat-7e74b092-4152-483e-a9a5-97d9561e9492 failed after 0.07s: 'MockRetriever' object has no attribute 'query'
2025-05-20 04:06:08,206 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 500 Internal Server Error"
2025-05-20 04:06:08,223 - main - INFO - Retrieving performance statistics
2025-05-20 04:06:08,229 - main - INFO - Successfully retrieved performance statistics
2025-05-20 04:06:08,230 - httpx - INFO - HTTP Request: GET http://testserver/performance "HTTP/1.1 200 OK"
2025-05-20 04:06:08,246 - main - INFO - Received chat request for model: local
2025-05-20 04:06:08,255 - main - INFO - Request requires streaming, forwarding to streaming endpoint
2025-05-20 04:06:08,259 - monitoring - INFO - Request chat-2408756f-c6cf-481f-8235-495559a6392f completed in 0.01s
2025-05-20 04:06:08,266 - main - INFO - Received streaming chat request for model: local
2025-05-20 04:06:08,270 - main - INFO - Performing retrieval operation for streaming request
2025-05-20 04:06:08,279 - main - ERROR - Error processing streaming request: 'MockRetriever' object has no attribute 'query'
Traceback (most recent call last):
  File "\\WDMyCloud\Marshall\Symbolic Language Model\src\main.py", line 551, in openai_chat_stream
    retrieved_info = await asyncio.to_thread(retriever.query, last_message)
AttributeError: 'MockRetriever' object has no attribute 'query'
2025-05-20 04:06:08,296 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 500 Internal Server Error"
2025-05-20 04:06:08,309 - main - INFO - Retrieving system configuration information
2025-05-20 04:06:08,317 - main - INFO - Successfully retrieved system configuration
2025-05-20 04:06:08,322 - httpx - INFO - HTTP Request: GET http://testserver/system/info "HTTP/1.1 200 OK"
2025-05-20 04:06:42,259 - main - INFO - Application shutting down
2025-05-20 04:06:54,123 - root - INFO - Logging system initialized
2025-05-20 04:06:54,128 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:06:54,159 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:06:54,168 - main - INFO - Initializing components...
2025-05-20 04:06:54,190 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:06:54,190 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:06:54,199 - main - INFO - Components initialized successfully
2025-05-20 04:06:54,207 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-20 04:06:54,215 - main - WARNING - No documents found in data directory
2025-05-20 04:06:54,298 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 422 Unprocessable Entity"
2025-05-20 04:06:54,306 - main - INFO - Received chat request for model: local
2025-05-20 04:06:54,311 - main - WARNING - No user message found in request
2025-05-20 04:06:54,318 - monitoring - WARNING - Request chat-8173f46d-3d07-465b-bb2a-a6f8fb0ecae3 failed after 0.01s: No user message found
2025-05-20 04:06:54,327 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:06:54,334 - main - INFO - Received chat request for model: local
2025-05-20 04:06:54,339 - main - WARNING - No user message found in request
2025-05-20 04:06:54,347 - monitoring - WARNING - Request chat-80d15943-282e-4065-b24c-2506b4d17e72 failed after 0.01s: No user message found
2025-05-20 04:06:54,353 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:06:54,371 - main - INFO - Received chat request for model: local
2025-05-20 04:06:54,379 - main - INFO - Performing retrieval operation
2025-05-20 04:06:54,387 - main - INFO - Retrieved relevant context
2025-05-20 04:06:54,392 - main - INFO - Processing with symbolic reasoning
2025-05-20 04:06:54,400 - main - INFO - Symbolic reasoning completed
2025-05-20 04:06:54,406 - main - INFO - Successfully generated response
2025-05-20 04:06:54,413 - monitoring - INFO - Request chat-73d18e4e-e0be-4da4-9228-737c382f6e45 completed in 0.04s
2025-05-20 04:06:54,422 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:06:54,430 - main - INFO - Received chat request for model: local
2025-05-20 04:06:54,434 - main - INFO - Found response in cache
2025-05-20 04:06:54,441 - monitoring - INFO - Request chat-e83794c1-e508-4a74-8532-750a2840100b completed in 0.01s
2025-05-20 04:06:54,450 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:06:54,459 - main - INFO - Retrieving performance statistics
2025-05-20 04:06:54,464 - main - INFO - Successfully retrieved performance statistics
2025-05-20 04:06:54,473 - httpx - INFO - HTTP Request: GET http://testserver/performance "HTTP/1.1 200 OK"
2025-05-20 04:06:54,480 - main - INFO - Received chat request for model: local
2025-05-20 04:06:54,485 - main - INFO - Request requires streaming, forwarding to streaming endpoint
2025-05-20 04:06:54,492 - monitoring - INFO - Request chat-c9b0c618-9564-496a-9e8f-5c882adcbf33 completed in 0.01s
2025-05-20 04:06:54,500 - main - INFO - Received streaming chat request for model: local
2025-05-20 04:06:54,508 - main - INFO - Found response in cache for streaming request
2025-05-20 04:06:54,515 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:06:54,530 - main - INFO - Retrieving system configuration information
2025-05-20 04:06:54,534 - main - INFO - Successfully retrieved system configuration
2025-05-20 04:06:54,541 - httpx - INFO - HTTP Request: GET http://testserver/system/info "HTTP/1.1 200 OK"
2025-05-20 04:07:41,921 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 04:07:45,956 - root - INFO - Logging system initialized
2025-05-20 04:07:45,965 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:07:45,995 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:07:46,025 - __mp_main__ - INFO - Initializing components...
2025-05-20 04:07:46,033 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:07:46,039 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:07:46,045 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:07:46,051 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:07:46,058 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 04:07:46,064 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:07:46,106 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 04:07:46,367 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 04:07:46,613 - root - INFO - Logging system initialized
2025-05-20 04:07:46,642 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:07:46,677 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:07:46,706 - main - INFO - Initializing components...
2025-05-20 04:07:46,711 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:07:46,717 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:07:46,723 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:07:46,728 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:07:46,734 - main - INFO - Components initialized successfully
2025-05-20 04:07:46,740 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:07:46,757 - main - WARNING - No documents found in data directory
2025-05-20 04:08:55,921 - root - INFO - Logging system initialized
2025-05-20 04:08:55,956 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:08:55,979 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:08:56,006 - main - INFO - Initializing components...
2025-05-20 04:08:56,017 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:08:56,022 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:08:56,030 - main - INFO - Components initialized successfully
2025-05-20 04:08:56,037 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-20 04:08:56,065 - main - WARNING - No documents found in data directory
2025-05-20 04:08:56,152 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 422 Unprocessable Entity"
2025-05-20 04:08:56,164 - main - INFO - Received chat request for model: local
2025-05-20 04:08:56,170 - main - WARNING - No user message found in request
2025-05-20 04:08:56,177 - monitoring - WARNING - Request chat-0d046153-5367-444c-be49-40fae83ca271 failed after 0.01s: No user message found
2025-05-20 04:08:56,185 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:08:56,195 - main - INFO - Received chat request for model: local
2025-05-20 04:08:56,200 - main - WARNING - No user message found in request
2025-05-20 04:08:56,205 - monitoring - WARNING - Request chat-001879de-b434-4e6d-95d1-93a9749c726a failed after 0.01s: No user message found
2025-05-20 04:08:56,213 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:08:56,229 - main - INFO - Received chat request for model: local
2025-05-20 04:08:56,231 - main - INFO - Performing retrieval operation
2025-05-20 04:08:56,242 - main - INFO - Retrieved relevant context
2025-05-20 04:08:56,250 - main - INFO - Processing with symbolic reasoning
2025-05-20 04:08:56,278 - main - INFO - Symbolic reasoning completed
2025-05-20 04:08:56,283 - main - INFO - Successfully generated response
2025-05-20 04:08:56,298 - monitoring - INFO - Request chat-65f7a9e1-ef81-4438-9924-0c7ea81cf7ab completed in 0.07s
2025-05-20 04:08:56,305 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:08:56,317 - main - INFO - Received chat request for model: local
2025-05-20 04:08:56,330 - main - INFO - Found response in cache
2025-05-20 04:08:56,336 - monitoring - INFO - Request chat-b59d5dde-d945-4fa4-b9db-cd0a04662c17 completed in 0.02s
2025-05-20 04:08:56,338 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:08:56,352 - main - INFO - Retrieving performance statistics
2025-05-20 04:08:56,355 - main - INFO - Successfully retrieved performance statistics
2025-05-20 04:08:56,368 - httpx - INFO - HTTP Request: GET http://testserver/performance "HTTP/1.1 200 OK"
2025-05-20 04:08:56,379 - main - INFO - Received chat request for model: local
2025-05-20 04:08:56,384 - main - INFO - Request requires streaming, forwarding to streaming endpoint
2025-05-20 04:08:56,384 - monitoring - INFO - Request chat-1ce833d2-d6d7-4ef5-9d0f-37808d53b83b completed in 0.00s
2025-05-20 04:08:56,399 - main - INFO - Received streaming chat request for model: local
2025-05-20 04:08:56,405 - main - INFO - Found response in cache for streaming request
2025-05-20 04:08:56,406 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:08:56,425 - main - INFO - Retrieving system configuration information
2025-05-20 04:08:56,430 - main - INFO - Successfully retrieved system configuration
2025-05-20 04:08:56,430 - httpx - INFO - HTTP Request: GET http://testserver/system/info "HTTP/1.1 200 OK"
2025-05-20 04:09:14,422 - main - INFO - Application shutting down
2025-05-20 04:09:28,611 - root - INFO - Logging system initialized
2025-05-20 04:09:28,617 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:09:28,641 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:09:28,648 - main - INFO - Initializing components...
2025-05-20 04:09:28,669 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:09:28,676 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:09:28,681 - main - INFO - Components initialized successfully
2025-05-20 04:09:28,689 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-20 04:09:28,697 - main - WARNING - No documents found in data directory
2025-05-20 04:09:28,786 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 422 Unprocessable Entity"
2025-05-20 04:09:28,801 - main - INFO - Received chat request for model: local
2025-05-20 04:09:28,806 - main - WARNING - No user message found in request
2025-05-20 04:09:28,814 - monitoring - WARNING - Request chat-0e8ee981-09c7-4a24-bc6c-6daa803f978d failed after 0.01s: No user message found
2025-05-20 04:09:28,819 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:09:28,829 - main - INFO - Received chat request for model: local
2025-05-20 04:09:28,835 - main - WARNING - No user message found in request
2025-05-20 04:09:28,840 - monitoring - WARNING - Request chat-59767779-0e93-451f-bac9-53fe327bf082 failed after 0.01s: No user message found
2025-05-20 04:09:28,846 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:09:28,863 - main - INFO - Received chat request for model: local
2025-05-20 04:09:28,870 - main - INFO - Performing retrieval operation
2025-05-20 04:09:28,877 - main - INFO - Retrieved relevant context
2025-05-20 04:09:28,880 - main - INFO - Processing with symbolic reasoning
2025-05-20 04:09:28,890 - main - INFO - Symbolic reasoning completed
2025-05-20 04:09:28,894 - main - INFO - Successfully generated response
2025-05-20 04:09:28,897 - monitoring - INFO - Request chat-321755c7-23d6-4a50-8c3c-b0801b5aa25f completed in 0.03s
2025-05-20 04:09:28,906 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:09:28,917 - main - INFO - Received chat request for model: local
2025-05-20 04:09:28,922 - main - INFO - Found response in cache
2025-05-20 04:09:28,930 - monitoring - INFO - Request chat-9a3d873c-9f5e-4f45-a84d-f4b01e5ca1b7 completed in 0.01s
2025-05-20 04:09:28,938 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:09:28,946 - main - INFO - Retrieving performance statistics
2025-05-20 04:09:28,956 - main - INFO - Successfully retrieved performance statistics
2025-05-20 04:09:28,966 - httpx - INFO - HTTP Request: GET http://testserver/performance "HTTP/1.1 200 OK"
2025-05-20 04:09:28,974 - main - INFO - Received chat request for model: local
2025-05-20 04:09:28,980 - main - INFO - Request requires streaming, forwarding to streaming endpoint
2025-05-20 04:09:28,985 - monitoring - INFO - Request chat-05a9c433-df02-494f-9fb8-1b50d49d8d90 completed in 0.01s
2025-05-20 04:09:28,994 - main - INFO - Received streaming chat request for model: local
2025-05-20 04:09:28,999 - main - INFO - Found response in cache for streaming request
2025-05-20 04:09:29,006 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:09:29,022 - main - INFO - Retrieving system configuration information
2025-05-20 04:09:29,030 - main - INFO - Successfully retrieved system configuration
2025-05-20 04:09:29,033 - httpx - INFO - HTTP Request: GET http://testserver/system/info "HTTP/1.1 200 OK"
2025-05-20 04:09:46,705 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 04:09:52,610 - root - INFO - Logging system initialized
2025-05-20 04:09:52,618 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:09:52,640 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:09:52,663 - __mp_main__ - INFO - Initializing components...
2025-05-20 04:09:52,672 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:09:52,677 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:09:52,683 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:09:52,691 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:09:52,699 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 04:09:52,704 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:09:52,728 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 04:09:52,985 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 04:09:53,204 - root - INFO - Logging system initialized
2025-05-20 04:09:53,279 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:09:53,338 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:09:53,367 - main - INFO - Initializing components...
2025-05-20 04:09:53,373 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:09:53,379 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:09:53,385 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:09:53,392 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:09:53,399 - main - INFO - Components initialized successfully
2025-05-20 04:09:53,404 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:09:53,424 - main - WARNING - No documents found in data directory
2025-05-20 04:09:58,270 - main - INFO - Application shutting down
2025-05-20 04:10:10,037 - root - INFO - Logging system initialized
2025-05-20 04:10:10,038 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:10:10,071 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:10:10,076 - main - INFO - Initializing components...
2025-05-20 04:10:10,099 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:10:10,106 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:10:10,113 - main - INFO - Components initialized successfully
2025-05-20 04:10:10,118 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-20 04:10:10,125 - main - WARNING - No documents found in data directory
2025-05-20 04:10:10,201 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 422 Unprocessable Entity"
2025-05-20 04:10:10,212 - main - INFO - Received chat request for model: local
2025-05-20 04:10:10,217 - main - WARNING - No user message found in request
2025-05-20 04:10:10,223 - monitoring - WARNING - Request chat-72e2117d-818e-4947-8695-37205ce03954 failed after 0.01s: No user message found
2025-05-20 04:10:10,231 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:10:10,240 - main - INFO - Received chat request for model: local
2025-05-20 04:10:10,245 - main - WARNING - No user message found in request
2025-05-20 04:10:10,252 - monitoring - WARNING - Request chat-11b681b0-0c66-4304-8c68-691bc52a27c6 failed after 0.01s: No user message found
2025-05-20 04:10:10,261 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-20 04:10:10,273 - main - INFO - Received chat request for model: local
2025-05-20 04:10:10,279 - main - INFO - Performing retrieval operation
2025-05-20 04:10:10,286 - main - INFO - Retrieved relevant context
2025-05-20 04:10:10,291 - main - INFO - Processing with symbolic reasoning
2025-05-20 04:10:10,298 - main - INFO - Symbolic reasoning completed
2025-05-20 04:10:10,305 - main - INFO - Successfully generated response
2025-05-20 04:10:10,312 - monitoring - INFO - Request chat-9fdbc5a2-e917-4c68-887c-135f32ad439e completed in 0.04s
2025-05-20 04:10:10,322 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:10:10,327 - main - INFO - Received chat request for model: local
2025-05-20 04:10:10,332 - main - INFO - Found response in cache
2025-05-20 04:10:10,339 - monitoring - INFO - Request chat-288af20a-6770-47c7-940e-0cbdb45dc773 completed in 0.01s
2025-05-20 04:10:10,347 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:10:10,356 - main - INFO - Retrieving performance statistics
2025-05-20 04:10:10,360 - main - INFO - Successfully retrieved performance statistics
2025-05-20 04:10:10,368 - httpx - INFO - HTTP Request: GET http://testserver/performance "HTTP/1.1 200 OK"
2025-05-20 04:10:10,377 - main - INFO - Received chat request for model: local
2025-05-20 04:10:10,383 - main - INFO - Request requires streaming, forwarding to streaming endpoint
2025-05-20 04:10:10,390 - monitoring - INFO - Request chat-2e21b8c8-3c5e-4331-8383-cafa090b3a7a completed in 0.01s
2025-05-20 04:10:10,398 - main - INFO - Received streaming chat request for model: local
2025-05-20 04:10:10,404 - main - INFO - Found response in cache for streaming request
2025-05-20 04:10:10,414 - httpx - INFO - HTTP Request: POST http://testserver/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-20 04:10:10,422 - main - INFO - Retrieving system configuration information
2025-05-20 04:10:10,433 - main - INFO - Successfully retrieved system configuration
2025-05-20 04:10:10,439 - httpx - INFO - HTTP Request: GET http://testserver/system/info "HTTP/1.1 200 OK"
2025-05-20 04:10:27,325 - root - INFO - Logging system initialized
2025-05-20 04:10:27,341 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:10:27,367 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:10:27,397 - main - INFO - Initializing components...
2025-05-20 04:10:27,456 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:10:27,469 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:10:27,477 - main - INFO - Components initialized successfully
2025-05-20 04:10:27,479 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-20 04:10:27,488 - main - WARNING - No documents found in data directory
2025-05-20 04:10:39,754 - root - INFO - Logging system initialized
2025-05-20 04:10:39,797 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:10:39,822 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:10:39,830 - main - INFO - Initializing components...
2025-05-20 04:10:39,847 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:10:39,856 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:10:39,864 - main - INFO - Components initialized successfully
2025-05-20 04:10:39,873 - main - INFO - Loading documents from \\WDMyCloud\Marshall\Symbolic Language Model\src\..\data
2025-05-20 04:10:39,879 - main - WARNING - No documents found in data directory
2025-05-20 04:10:53,341 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 04:10:56,356 - root - INFO - Logging system initialized
2025-05-20 04:10:56,361 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:10:56,384 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:10:56,390 - __mp_main__ - INFO - Initializing components...
2025-05-20 04:10:56,408 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:10:56,415 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:10:56,423 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:10:56,431 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:10:56,437 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 04:10:56,445 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:10:56,451 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 04:10:56,703 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 04:10:56,925 - root - INFO - Logging system initialized
2025-05-20 04:10:56,954 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:10:56,993 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:10:57,020 - main - INFO - Initializing components...
2025-05-20 04:10:57,026 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:10:57,031 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:10:57,037 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:10:57,041 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:10:57,047 - main - INFO - Components initialized successfully
2025-05-20 04:10:57,054 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:10:57,073 - main - WARNING - No documents found in data directory
2025-05-20 04:11:13,686 - main - INFO - Application shutting down
2025-05-20 04:11:56,995 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 04:12:00,693 - root - INFO - Logging system initialized
2025-05-20 04:12:00,699 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:12:00,729 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:12:00,758 - __mp_main__ - INFO - Initializing components...
2025-05-20 04:12:00,766 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:12:00,771 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:12:00,777 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:12:00,784 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:12:00,791 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 04:12:00,797 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:12:00,823 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 04:12:01,090 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 04:12:01,348 - root - INFO - Logging system initialized
2025-05-20 04:12:01,354 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 04:12:01,397 - monitoring - INFO - Performance monitoring initialized
2025-05-20 04:12:01,425 - main - INFO - Initializing components...
2025-05-20 04:12:01,432 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 04:12:01,438 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 04:12:01,444 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 04:12:01,450 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 04:12:01,458 - main - INFO - Components initialized successfully
2025-05-20 04:12:01,466 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 04:12:01,493 - main - WARNING - No documents found in data directory
2025-05-20 19:33:56,263 - main - INFO - Application shutting down
2025-05-20 19:34:12,627 - monitoring - INFO - Performance monitoring shutdown
2025-05-20 19:34:19,443 - root - INFO - Logging system initialized
2025-05-20 19:34:19,458 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 19:34:19,492 - monitoring - INFO - Performance monitoring initialized
2025-05-20 19:34:19,501 - __mp_main__ - INFO - Initializing components...
2025-05-20 19:34:19,525 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 19:34:19,532 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 19:34:19,539 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 19:34:19,545 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 19:34:19,553 - __mp_main__ - INFO - Components initialized successfully
2025-05-20 19:34:19,560 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 19:34:19,588 - __mp_main__ - WARNING - No documents found in data directory
2025-05-20 19:34:19,944 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-20 19:34:20,311 - root - INFO - Logging system initialized
2025-05-20 19:34:20,317 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-20 19:34:20,332 - monitoring - INFO - Performance monitoring initialized
2025-05-20 19:34:20,358 - main - INFO - Initializing components...
2025-05-20 19:34:20,364 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-20 19:34:20,371 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-20 19:34:20,382 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-20 19:34:20,389 - vector_store - INFO - TorchVectorStore using CPU
2025-05-20 19:34:20,394 - main - INFO - Components initialized successfully
2025-05-20 19:34:20,400 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-20 19:34:20,425 - main - WARNING - No documents found in data directory
2025-05-21 04:35:47,640 - main - INFO - Application shutting down
2025-05-21 04:36:27,239 - monitoring - INFO - Performance monitoring shutdown
