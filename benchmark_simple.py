"""
Simple FAISS CPU vs GPU Benchmark

A minimal script to benchmark FAISS performance on CPU vs GPU.
"""

import time
import sys
import numpy as np

# Print Python version and executable
print(f"Python: {sys.version}")
print(f"Executable: {sys.executable}")

# Try importing FAISS
try:
    import faiss
    print(f"FAISS version: {faiss.__version__}")
    print(f"FAISS GPU support: {hasattr(faiss, 'StandardGpuResources')}")
except ImportError as e:
    print(f"Error importing FAISS: {e}")
    sys.exit(1)

# Try importing PyTorch for GPU info
try:
    import torch
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
except ImportError:
    print("PyTorch not available")

print("\n" + "=" * 50)
print("FAISS CPU vs GPU Benchmark".center(50))
print("=" * 50)

# Create test data
dimensions = [64, 128, 256]  # Dimensions to test
sizes = [10000, 50000, 100000]  # Number of vectors

for dim in dimensions:
    print(f"\nDimension: {dim}")
    print("-" * 30)
    
    for size in sizes:
        print(f"\nDataset size: {size} vectors")
        
        # Create dataset
        np.random.seed(42)  # For reproducibility
        vectors = np.random.random((size, dim)).astype('float32')
        queries = np.random.random((1000, dim)).astype('float32')
        
        # CPU benchmark
        print("\nCPU:")
        cpu_index = faiss.IndexFlatL2(dim)
        
        # Build index
        start = time.time()
        cpu_index.add(vectors)
        cpu_build_time = time.time() - start
        print(f"  Build time: {cpu_build_time:.4f}s")
        
        # Search
        start = time.time()
        D1, I1 = cpu_index.search(queries, 10)  # Find 10 nearest neighbors
        cpu_search_time = time.time() - start
        print(f"  Search time: {cpu_search_time:.4f}s for 1000 queries")
        
        # GPU benchmark (if available)
        if hasattr(faiss, 'StandardGpuResources'):
            try:
                print("\nGPU:")
                res = faiss.StandardGpuResources()
                gpu_index = faiss.index_cpu_to_gpu(res, 0, faiss.IndexFlatL2(dim))
                
                # Build index
                start = time.time()
                gpu_index.add(vectors)
                gpu_build_time = time.time() - start
                print(f"  Build time: {gpu_build_time:.4f}s")
                
                # Search
                start = time.time()
                D2, I2 = gpu_index.search(queries, 10)
                gpu_search_time = time.time() - start
                print(f"  Search time: {gpu_search_time:.4f}s for 1000 queries")
                
                # Calculate speedup
                build_speedup = cpu_build_time / gpu_build_time
                search_speedup = cpu_search_time / gpu_search_time
                print(f"\nSpeedup:")
                print(f"  Build: {build_speedup:.2f}x faster on GPU")
                print(f"  Search: {search_speedup:.2f}x faster on GPU")
                
                # Verify results match
                match = np.allclose(D1, D2, rtol=1e-5, atol=1e-5)
                print(f"  Results match: {match}")
                
            except Exception as e:
                print(f"GPU benchmark failed: {e}")
        else:
            print("\nGPU: Not available")

print("\n" + "=" * 50)
print("Benchmark Complete".center(50))
print("=" * 50)
