# Production Dependencies for Neural Symbolic Language Model
# Pinned versions for production stability and security

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
httpx==0.25.2
requests==2.31.0

# Async Support
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# AI/ML Libraries
ollama==0.1.7
transformers==4.35.2
torch==2.1.1
numpy==1.24.4
scikit-learn==1.3.2

# Vector Database
faiss-cpu==1.7.4
# faiss-gpu==1.7.4  # Uncomment for GPU support

# Data Processing
pandas==2.1.3
pyarrow==14.0.1

# Configuration Management
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# Logging and Monitoring
structlog==23.2.0
psutil==5.9.6

# Security
cryptography==41.0.7
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Database (if needed)
sqlalchemy==2.0.23
alembic==1.12.1

# Caching
redis==5.0.1

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0

# Production Server
gunicorn==21.2.0

# Health Checks
healthcheck==1.3.3

# Environment Detection
python-multipart==0.0.6
