2025-06-29 09:42:40,269 - logging_config - INFO - 7125a5cd-721a-4bd2-aab5-1159d96e3f5c - Logging configured
2025-06-29 09:42:40,355 - monitoring - INFO - 156ec295-1388-4a76-8ed4-9009127c3379 - Performance monitoring initialized
2025-06-29 09:42:40,509 - main - INFO - 11fa0bf6-6837-4716-bdcf-f545975a8c6d - Initializing components...
2025-06-29 09:42:48,854 - main - INFO - 2c98941f-6ccd-4c3c-b3b9-c6c4ab66e2f7 - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 09:42:52,871 - main - WARNING - 95211f64-2613-4f36-a31b-ab5d39c26bb2 - GPU FAISS not available. Will use CPU version of FAISS.
2025-06-29 09:42:53,482 - main - INFO - cf00a2f5-4515-4583-8ab7-96a54e2602bc - Components initialized successfully
2025-06-29 09:42:53,745 - main - INFO - 43a38115-c8ec-4b1a-8157-40741cc222d0 - Loading documents from Z:\Symbolic Neural Language Model\src\..\data
2025-06-29 09:42:53,923 - main - WARNING - 39870cf0-db81-4b76-b99d-7e825f978858 - No documents found in data directory
2025-06-29 09:42:58,090 - src.retrieval - INFO - c604d5c0-926d-4956-a26b-320dc60bf39b - Imported FAISS successfully
2025-06-29 09:42:58,170 - src.retrieval - INFO - 1ed78865-55f6-4cda-92a7-6384e0935ad6 - Using CPU version of FAISS
2025-06-29 09:43:03,477 - main - INFO - bb119f62-db2c-4309-83f0-76467588b595 - Received chat request for model: local
2025-06-29 09:43:03,588 - main - INFO - 8c9537b9-8901-4648-ba62-e38733219316 - Performing retrieval operation
2025-06-29 09:43:03,647 - main - INFO - b5697234-4d4f-4a3d-be63-894c6d99f1bb - Retrieved relevant context
2025-06-29 09:43:03,690 - main - INFO - 837c9e12-f83e-462b-be1b-9878dca36f44 - Processing with symbolic reasoning
2025-06-29 09:43:03,737 - main - INFO - 93db3c6f-78e4-4c9c-a404-d324dce1af14 - Symbolic reasoning completed
2025-06-29 09:43:03,792 - main - INFO - 75e14d5b-5609-4912-a0ca-77b8d9d27797 - Successfully generated response
2025-06-29 09:43:03,856 - monitoring - INFO - 68d04689-643f-4066-97d2-dc90ac03ec06 - Request chat-c9a41e61-b577-407c-9b14-a24837ea5d5f completed in 0.38s
2025-06-29 09:43:03,942 - main - INFO - 8a8f0dc8-e585-4935-bb6a-ddf40afad78e - Received chat request for model: local
2025-06-29 09:43:04,011 - main - INFO - d9ec5ba9-78f3-4572-abc6-2e7f0130dc9f - Found response in cache
2025-06-29 09:43:05,716 - monitoring - INFO - 19aef984-d81c-4a42-9c39-2bf72dd10ff8 - Request chat-a7685695-465e-4caa-9d54-c733d0c502ed completed in 1.77s
2025-06-29 09:43:05,945 - main - INFO - 23706e12-00b6-48f2-bf41-b55013fbd090 - Retrieving performance statistics
2025-06-29 09:43:06,111 - main - INFO - 791b37a1-f878-406b-bbbe-9f9d06d522b2 - Successfully retrieved performance statistics
2025-06-29 09:43:06,219 - main - INFO - 0a92063c-1b6d-4253-8631-cea9e1de6b6e - Received chat request for model: local
2025-06-29 09:43:06,345 - main - INFO - 21a5f79b-3ac9-4781-98ca-33eb64c63aca - Request requires streaming, forwarding to streaming endpoint
2025-06-29 09:43:06,426 - monitoring - INFO - 58b22713-1b5b-471e-977d-64b674896bb1 - Request chat-40861127-10b5-49f6-b93b-7d16bef8af3d completed in 0.21s
2025-06-29 09:43:06,494 - main - INFO - d1c94259-e813-4ffd-98cf-9c3f862c5ac3 - Received streaming chat request for model: local
2025-06-29 09:43:06,609 - main - INFO - cdfdc4c3-2184-427f-b7c8-1779b27af928 - Found response in cache for streaming request
2025-06-29 09:43:06,742 - main - INFO - f7a5a837-6b9d-4443-aae2-31f5135d3cf1 - Retrieving system configuration information
2025-06-29 09:43:06,793 - main - INFO - cb706cf8-9f06-4d09-b694-dab26dfb150d - Successfully retrieved system configuration
2025-06-29 09:43:06,942 - monitoring - INFO - 74398434-ff5e-4df8-972f-651705248a30 - Performance monitoring initialized
2025-06-29 09:43:06,994 - monitoring - INFO - 6f1dd519-ee0c-4b6a-80d4-05f1a4041396 - Request test-456 completed in 0.00s
2025-06-29 09:43:07,949 - monitoring - INFO - 11aa4139-2b9a-4f5e-a8d1-b1112d54f4a0 - Performance monitoring shutdown
2025-06-29 09:43:08,487 - monitoring - INFO - 35d93b3d-c803-440a-a2ae-0abb2040c669 - Performance monitoring initialized
2025-06-29 09:43:08,589 - monitoring - WARNING - 5444ae5d-a1dd-4f47-840a-fc6fc0718e21 - Request test-789 failed after 0.00s: Test error
2025-06-29 09:43:08,718 - monitoring - INFO - 8f5e88d3-a38c-40cb-bedb-a824b2de5c74 - Request test-790 completed in 0.00s
2025-06-29 09:43:09,493 - monitoring - INFO - c745dbbf-afb5-433b-b9f7-dc761cc8f493 - Performance monitoring shutdown
2025-06-29 09:43:09,594 - monitoring - INFO - 3c87d033-9808-48ce-b106-a5001860750f - Performance monitoring initialized
2025-06-29 09:43:09,622 - monitoring - INFO - bce07dad-aa99-430a-a1d9-a48a55dc8e89 - Request recent completed in 0.00s
2025-06-29 09:43:10,600 - monitoring - INFO - 36e386a8-eefc-4d1d-9262-7502a25ba1d9 - Performance monitoring shutdown
2025-06-29 09:43:10,613 - monitoring - INFO - 186191da-da55-4007-9be0-eb45a226d6c4 - Performance monitoring initialized
2025-06-29 09:43:10,711 - monitoring - INFO - 323aff7a-790d-40b9-8c32-bd18963c1090 - Request test-123 completed in 0.00s
2025-06-29 09:43:11,615 - monitoring - INFO - 38b09ebc-915b-47a4-9f47-1ad017d2edd6 - Performance monitoring shutdown
2025-06-29 09:43:11,650 - monitoring - INFO - 9de08b9e-ca90-46f3-98f6-57fae34ff027 - Performance monitoring initialized
2025-06-29 09:43:14,670 - monitoring - INFO - 753968dd-efc8-4774-b58d-5225e33a18ae - Performance monitoring shutdown
2025-06-29 09:43:14,950 - src.symbolic_reasoning - INFO - 013a384b-fb37-4218-8f1f-a0920be7c272 - SymbolicReasoner initialized with GPU: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 09:43:16,439 - src.retrieval - WARNING - 272ee59e-b434-423b-9ae2-20b786c4af59 - Ollama not available, falling back to random embeddings
2025-06-29 09:43:16,449 - src.retrieval - INFO - d19fb995-5238-49ca-8ac3-f9de9ba912d4 - GPU FAISS not available but GPU detected. Using PyTorch GPU vector store.
2025-06-29 09:43:16,469 - vector_store - INFO - e83d16f6-4d04-4c18-8956-9c6cab024749 - TorchVectorStore using GPU: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 09:43:16,496 - src.retrieval - INFO - dce47850-3a79-4837-b23b-17b4621ab9ba - Retriever initialized with faiss backend, GPU: True
2025-06-29 09:43:16,668 - src.retrieval - WARNING - e1747c41-5902-47b3-baad-a9746f808323 - Ollama not available, falling back to random embeddings
2025-06-29 09:43:16,702 - src.retrieval - INFO - 79f5660c-62d6-482b-bf43-f7ed323e4b1d - GPU FAISS not available but GPU detected. Using PyTorch GPU vector store.
2025-06-29 09:43:16,732 - vector_store - INFO - a68d623d-754b-4845-a9e6-a18251296036 - TorchVectorStore using GPU: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 09:43:16,767 - src.retrieval - INFO - 39c90de2-26c3-4e39-90db-9379c0177297 - Retriever initialized with faiss backend, GPU: True
2025-06-29 09:43:16,971 - src.symbolic_reasoning - INFO - aea05539-4948-41fb-a2c7-1bf290dfe927 - SymbolicReasoner initialized with GPU: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 09:43:16,995 - src.retrieval - INFO - df219d3d-6676-40f1-85bf-c3015d7bb93e - GPU FAISS not available but GPU detected. Using PyTorch GPU vector store.
2025-06-29 09:43:17,015 - vector_store - INFO - 1d8c8e53-95b7-4ca2-86e2-6167bc0c3f19 - TorchVectorStore using GPU: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 09:43:17,038 - src.retrieval - INFO - f7aa0699-bc5c-46b7-b4cf-6bcb93347ddb - Retriever initialized with faiss backend, GPU: True
2025-06-29 09:43:20,355 - src.main - INFO - ff4bafbd-1bc7-434a-85ef-aa2e398fa08e - Imported FAISS successfully
2025-06-29 09:43:20,398 - src.main - INFO - 6f3223e7-6638-40f4-be58-cf8b15b0e1c9 - Using CPU version of FAISS
2025-06-29 09:43:20,577 - logging_config - INFO - 40ae493a-f0e0-4552-b54c-649d391e8d8a - Logging configured
2025-06-29 09:43:20,592 - monitoring - INFO - 32ee64bc-7f8b-4f39-9377-73f664ad5350 - Performance monitoring initialized
2025-06-29 09:43:20,613 - src.main - INFO - d96a04ea-3e64-4998-9a32-5daf33481052 - Initializing components...
2025-06-29 09:43:20,620 - src.main - INFO - 3aa1562e-fce4-4611-9836-b6cb3407367c - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 09:43:20,625 - src.main - WARNING - 81ecd490-f023-49ad-86a1-06d1d0cb8c70 - GPU FAISS not available. Will use CPU version of FAISS.
2025-06-29 09:43:20,635 - src.main - INFO - ffcfc477-35ae-4a68-a84a-863efbabe6f5 - Components initialized successfully
2025-06-29 09:43:20,640 - src.main - INFO - ecf37173-58cd-46c5-a4f7-eb61f8fa3911 - Loading documents from Z:\Symbolic Neural Language Model\src\..\data
2025-06-29 09:43:20,703 - src.main - WARNING - 21f82cf0-0be3-4fe7-90e1-9ec1c0935dee - No documents found in data directory
2025-06-29 09:43:20,802 - src.main - ERROR - 86d045eb-0bfb-4624-be8e-898246e8f57e - Unhandled exception: `BaseSettings` has been moved to the `pydantic-settings` package. See https://docs.pydantic.dev/2.11/migration/#basesettings-has-moved-to-pydantic-settings for more details.

For further information visit https://errors.pydantic.dev/2.11/u/import-error
  + Exception Group Traceback (most recent call last):
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_utils.py", line 76, in collapse_excgroups
  |     yield
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 178, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\anyio\_backends\_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  | exceptiongroup.ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    |     await self.app(scope, receive, _send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |   File "Z:\Symbolic Neural Language Model\src\main.py", line 160, in rate_limit_middleware
    |     response = await call_next(request)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    |     raise app_exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |   File "Z:\Symbolic Neural Language Model\src\main.py", line 131, in check_request_size_middleware
    |     response = await call_next(request)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    |     raise app_exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |   File "Z:\Symbolic Neural Language Model\src\main.py", line 116, in add_security_headers
    |     response = await call_next(request)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    |     raise app_exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 715, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 735, in app
    |     await route.handle(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 288, in handle
    |     await self.app(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 76, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 73, in app
    |     response = await f(request)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\fastapi\routing.py", line 301, in app
    |     raw_response = await run_endpoint_function(
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    |     return await dependant.call(**values)
    |   File "Z:\Symbolic Neural Language Model\src\main.py", line 938, in health_check
    |     from src.core.config import get_settings
    |   File "Z:\Symbolic Neural Language Model\src\core\config.py", line 15, in <module>
    |     from pydantic import BaseSettings, Field, validator
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\__init__.py", line 426, in __getattr__
    |     return _getattr_migration(attr_name)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\_migration.py", line 296, in wrapper
    |     raise PydanticImportError(
    | pydantic.errors.PydanticImportError: `BaseSettings` has been moved to the `pydantic-settings` package. See https://docs.pydantic.dev/2.11/migration/#basesettings-has-moved-to-pydantic-settings for more details.
    | 
    | For further information visit https://errors.pydantic.dev/2.11/u/import-error
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
  File "Z:\Symbolic Neural Language Model\src\main.py", line 160, in rate_limit_middleware
    response = await call_next(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
  File "Z:\Symbolic Neural Language Model\src\main.py", line 131, in check_request_size_middleware
    response = await call_next(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
  File "Z:\Symbolic Neural Language Model\src\main.py", line 116, in add_security_headers
    response = await call_next(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 154, in call_next
    raise app_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
  File "Z:\Symbolic Neural Language Model\src\main.py", line 938, in health_check
    from src.core.config import get_settings
  File "Z:\Symbolic Neural Language Model\src\core\config.py", line 15, in <module>
    from pydantic import BaseSettings, Field, validator
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\__init__.py", line 426, in __getattr__
    return _getattr_migration(attr_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\_migration.py", line 296, in wrapper
    raise PydanticImportError(
pydantic.errors.PydanticImportError: `BaseSettings` has been moved to the `pydantic-settings` package. See https://docs.pydantic.dev/2.11/migration/#basesettings-has-moved-to-pydantic-settings for more details.

For further information visit https://errors.pydantic.dev/2.11/u/import-error
2025-06-29 09:43:22,138 - src.retrieval - INFO - d22a0fcd-03a8-4b29-aa8a-713675389e7d - Using CPU FAISS
2025-06-29 09:43:22,157 - src.retrieval - INFO - c90f6f06-36dc-460a-a060-27dbde22bee2 - Retriever initialized with faiss backend, GPU: False
2025-06-29 11:41:12,969 - logging_config - INFO - a1a14cd8-710b-4130-9edb-c41bfc20eff9 - Logging configured
2025-06-29 11:41:13,095 - monitoring - INFO - 776a438d-ab15-4a29-829c-52484ed1995d - Performance monitoring initialized
2025-06-29 11:41:14,257 - __main__ - INFO - c5db0d28-0c2d-4bed-97c6-792cbde5106f - Validating security configuration...
2025-06-29 11:41:14,340 - __main__ - INFO - e066fba5-4c51-4446-b499-feee20954839 - Running in test mode
2025-06-29 11:41:14,347 - __main__ - INFO - ea2fb49d-d4bc-4464-bc0e-7e9b74d6ed73 - Initializing components...
2025-06-29 11:41:14,403 - __main__ - INFO - 3371f48a-8a39-4692-b40b-5037b8a7db36 - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 11:41:14,409 - __main__ - WARNING - dabe914f-cca6-4c48-af68-b551e1ec51bd - GPU FAISS not available. Will use CPU version of FAISS.
2025-06-29 11:41:14,410 - symbolic_reasoning - INFO - 2e0bfe06-e34e-4de7-9678-faf4c93ca0df - SymbolicReasoner initialized with GPU: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 11:41:14,444 - retrieval - INFO - b33a16bf-942b-477a-9194-e992af27216a - Using CPU FAISS
2025-06-29 11:41:14,451 - retrieval - INFO - 028a9d3f-fc52-4047-b9aa-7059fec595d0 - Retriever initialized with faiss backend, GPU: False
2025-06-29 11:41:14,501 - __main__ - INFO - 9ce4bf0e-c244-4420-ae3d-8ca13e0cdd9e - Components initialized successfully
2025-06-29 11:41:14,506 - __main__ - INFO - f778a503-6152-4631-8b0f-74645a91aaff - Loading documents from Z:\Symbolic Neural Language Model\src\..\data
2025-06-29 11:41:14,528 - __main__ - WARNING - 353a12e8-0878-4e0b-ba90-a33e2244d1a2 - No documents found in data directory
2025-06-29 11:41:21,749 - logging_config - INFO - e7c9cdb3-59fd-4111-a6e6-d59629f5a9c0 - Logging configured
2025-06-29 11:41:21,847 - monitoring - INFO - 2b02fcd9-4fcc-4f40-9bea-cb43fa37092e - Performance monitoring initialized
2025-06-29 11:41:21,868 - __mp_main__ - INFO - 8322a5b4-31bb-461e-8555-dcd6b5803e95 - Validating security configuration...
2025-06-29 11:41:21,875 - __mp_main__ - INFO - 2b867b6a-c649-4cd9-9c1b-1d0daf916a38 - Running in test mode
2025-06-29 11:41:21,882 - __mp_main__ - INFO - 3cdfca15-4a0a-4c20-9490-7b3ecbde7e2f - Initializing components...
2025-06-29 11:41:21,889 - __mp_main__ - INFO - 5362515b-07ef-4e39-83bf-637993a8050a - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 11:41:21,896 - __mp_main__ - WARNING - 2cf4cc46-6d44-48c9-bcbd-d7e792f0e42d - GPU FAISS not available. Will use CPU version of FAISS.
2025-06-29 11:41:21,903 - symbolic_reasoning - INFO - 674bb9c5-baf0-4fbc-9979-ff89456b5666 - SymbolicReasoner initialized with GPU: NVIDIA GeForce RTX 4070 Laptop GPU
2025-06-29 11:41:21,903 - retrieval - INFO - 3784351b-79aa-4c1c-b129-af600a0b91d4 - Using CPU FAISS
2025-06-29 11:41:21,910 - retrieval - INFO - 8e6f83cb-1482-434b-b046-51b793828699 - Retriever initialized with faiss backend, GPU: False
2025-06-29 11:41:21,917 - __mp_main__ - INFO - 418a4f3c-0dc0-4f76-96b8-c6244fed4c8a - Components initialized successfully
2025-06-29 11:41:21,924 - __mp_main__ - INFO - 9f621260-89b9-4b23-b7bb-e78a8c85c4f8 - Loading documents from Z:\Symbolic Neural Language Model\src\..\data
2025-06-29 11:41:21,930 - __mp_main__ - WARNING - 096e1fb1-8030-48f0-8442-74fe6cedcb1d - No documents found in data directory
2025-06-29 11:41:22,518 - watchfiles.main - INFO - 029f2a94-338a-4ad5-a9d7-f6f76c4ae09b - 1 change detected
2025-06-29 11:41:23,490 - main - INFO - d97c3de1-d003-419d-8265-bcabb4ed8eb1 - Imported FAISS successfully
2025-06-29 11:41:23,578 - main - INFO - 516616f1-c9bc-46d8-a0d7-03a9a64d4a2c - Using CPU version of FAISS
2025-06-29 11:41:23,663 - logging_config - INFO - cfaf48b6-1bb7-4a57-8fd4-770f102e573d - Logging configured
2025-06-29 11:41:23,686 - monitoring - INFO - 9dc04ffb-258c-4afd-b4a6-bebcd658a101 - Performance monitoring initialized
2025-06-29 11:41:23,701 - watchfiles.main - INFO - ef2c117c-f247-4c3f-8eb3-e12568d0e3de - 3 changes detected
