"""
Example usage of the HybridRetriever class.
This script demonstrates how to use the hybrid retriever with both FAISS and LightRAG.
"""

import logging
import numpy as np
from pathlib import Path
import sys

# Add project root to path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from src.hybrid_retriever import HybridRetriever

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    # Initialize the hybrid retriever
    retriever = HybridRetriever(
        vector_db="faiss",  # Use "torch" for pure PyTorch implementation
        use_gpu=True,       # Use GPU if available
        first_stage_k=100,  # Number of candidates in first stage
        rerank_k=10,        # Number of results after reranking
        model_name="BAAI/bge-small-en-v1.5"  # Embedding model
    )
    
    # Example documents
    documents = [
        {"id": "doc1", "text": "The quick brown fox jumps over the lazy dog.", "category": "animals"},
        {"id": "doc2", "text": "Machine learning is a subset of artificial intelligence.", "category": "ai"},
        {"id": "doc3", "text": "The capital of France is Paris.", "category": "geography"},
        {"id": "doc4", "text": "Python is a popular programming language for data science.", "category": "programming"},
        {"id": "doc5", "text": "The Great Wall of China is visible from space.", "category": "geography"},
        {"id": "doc6", "text": "Deep learning models require large amounts of data.", "category": "ai"},
        {"id": "doc7", "text": "The Eiffel Tower is located in Paris, France.", "category": "landmarks"},
        {"id": "doc8", "text": "JavaScript is commonly used for web development.", "category": "programming"},
    ]
    
    # Add documents to the retriever
    logger.info("Adding documents to the retriever...")
    retriever.add_documents(documents)
    
    # Example queries
    queries = [
        "What is machine learning?",
        "Tell me about Paris",
        "Programming languages for data science"
    ]
    
    # Perform searches
    for query in queries:
        print(f"\nQuery: {query}")
        print("-" * 50)
        
        # Search with reranking
        results = retriever.search(query, rerank=True)
        
        print("With Reranking:")
        for i, result in enumerate(results, 1):
            print(f"{i}. [{result.metadata.get('category', 'N/A')}] {result.text} (Score: {result.score:.4f})")
        
        # Search without reranking (for comparison)
        if len(queries) <= 3:  # Only show for a few queries to avoid too much output
            results = retriever.search(query, rerank=False)
            print("\nWithout Reranking:")
            for i, result in enumerate(results, 1):
                print(f"{i}. [{result.metadata.get('category', 'N/A')}] {result.text} (Score: {result.score:.4f})")
        
        print("\n" + "="*80 + "\n")
    
    # Print system information
    print("\nSystem Information:")
    print("-" * 50)
    for key, value in retriever.get_system_info().items():
        print(f"{key}: {value}")

def demo_advanced_content_addition():
    """Demonstrate adding various types of content to the retrieval system."""
    print("\n" + "="*60)
    print("🚀 ADVANCED CONTENT ADDITION DEMO")
    print("="*60)

    # Initialize retriever
    retriever = HybridRetriever(
        vector_db="faiss",
        use_gpu=True,
        first_stage_k=50,
        rerank_k=5
    )

    # Example 1: Technical documentation
    print("\n📚 Adding Technical Documentation...")
    tech_docs = [
        {
            "id": "neural_networks_guide",
            "text": """
            Neural Networks: A Comprehensive Guide

            Neural networks are computational models inspired by biological neural networks.
            They consist of interconnected nodes (neurons) organized in layers:

            1. Input Layer: Receives input data
            2. Hidden Layers: Process information through weighted connections
            3. Output Layer: Produces final predictions

            Key concepts:
            - Activation functions (ReLU, Sigmoid, Tanh)
            - Backpropagation for training
            - Gradient descent optimization
            - Regularization techniques (Dropout, L1/L2)

            Applications:
            - Image recognition and computer vision
            - Natural language processing
            - Speech recognition
            - Autonomous vehicles
            """,
            "metadata": {
                "category": "technical_documentation",
                "topic": "neural_networks",
                "difficulty": "intermediate",
                "tags": ["ai", "machine-learning", "deep-learning"]
            }
        },
        {
            "id": "api_documentation",
            "text": """
            REST API Best Practices

            When designing RESTful APIs, follow these principles:

            1. Use HTTP methods correctly:
               - GET: Retrieve data
               - POST: Create new resources
               - PUT: Update entire resources
               - PATCH: Partial updates
               - DELETE: Remove resources

            2. Design intuitive URLs:
               - Use nouns, not verbs: /users not /getUsers
               - Use plural nouns: /users/123
               - Maintain consistency

            3. Handle errors properly:
               - Use appropriate HTTP status codes
               - Provide meaningful error messages
               - Include error codes for programmatic handling

            4. Implement proper authentication:
               - Use HTTPS for all endpoints
               - Implement rate limiting
               - Use API keys or OAuth tokens
            """,
            "metadata": {
                "category": "api_documentation",
                "topic": "rest_api",
                "difficulty": "beginner",
                "tags": ["api", "rest", "web-development"]
            }
        }
    ]

    retriever.add_documents(tech_docs)
    print(f"✅ Added {len(tech_docs)} technical documents")

    # Example 2: Code examples
    print("\n💻 Adding Code Examples...")
    code_examples = [
        {
            "id": "python_ml_example",
            "text": """
            # Machine Learning with Python - Complete Example

            import numpy as np
            import pandas as pd
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.metrics import accuracy_score, classification_report

            # Load and prepare data
            def load_data(file_path):
                \"\"\"Load dataset from CSV file.\"\"\"
                data = pd.read_csv(file_path)
                return data

            # Feature engineering
            def preprocess_data(data):
                \"\"\"Clean and preprocess the dataset.\"\"\"
                # Handle missing values
                data = data.dropna()

                # Encode categorical variables
                categorical_columns = data.select_dtypes(include=['object']).columns
                data = pd.get_dummies(data, columns=categorical_columns)

                return data

            # Train model
            def train_model(X_train, y_train):
                \"\"\"Train a Random Forest classifier.\"\"\"
                model = RandomForestClassifier(
                    n_estimators=100,
                    random_state=42,
                    max_depth=10
                )
                model.fit(X_train, y_train)
                return model

            # Main execution
            if __name__ == "__main__":
                # Load and preprocess data
                data = load_data("dataset.csv")
                data = preprocess_data(data)

                # Split features and target
                X = data.drop('target', axis=1)
                y = data['target']

                # Train-test split
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=42
                )

                # Train and evaluate model
                model = train_model(X_train, y_train)
                predictions = model.predict(X_test)

                # Print results
                accuracy = accuracy_score(y_test, predictions)
                print(f"Accuracy: {accuracy:.3f}")
                print(classification_report(y_test, predictions))
            """,
            "metadata": {
                "category": "code_examples",
                "language": "python",
                "topic": "machine_learning",
                "difficulty": "intermediate",
                "tags": ["python", "scikit-learn", "machine-learning", "classification"]
            }
        },
        {
            "id": "javascript_api_example",
            "text": """
            // JavaScript API Integration Example

            class APIClient {
                constructor(baseURL, apiKey) {
                    this.baseURL = baseURL;
                    this.apiKey = apiKey;
                    this.headers = {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    };
                }

                async makeRequest(endpoint, method = 'GET', data = null) {
                    const url = `${this.baseURL}${endpoint}`;
                    const config = {
                        method,
                        headers: this.headers
                    };

                    if (data && method !== 'GET') {
                        config.body = JSON.stringify(data);
                    }

                    try {
                        const response = await fetch(url, config);

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        return await response.json();
                    } catch (error) {
                        console.error('API request failed:', error);
                        throw error;
                    }
                }

                // Chat completion method
                async chatCompletion(messages, options = {}) {
                    const payload = {
                        model: 'gemma3n:e2b',
                        messages: messages,
                        temperature: options.temperature || 0.7,
                        max_tokens: options.maxTokens || 1000
                    };

                    return await this.makeRequest('/v1/chat/completions', 'POST', payload);
                }

                // Document management
                async addDocument(content, metadata = {}) {
                    const payload = { content, metadata };
                    return await this.makeRequest('/documents/add', 'POST', payload);
                }

                async searchDocuments(query, limit = 10) {
                    const payload = { query, limit };
                    return await this.makeRequest('/documents/search', 'POST', payload);
                }
            }

            // Usage example
            const client = new APIClient('http://localhost:8080', 'your-api-key');

            // Add a document
            client.addDocument(
                'This is a sample document about AI.',
                { category: 'ai', tags: ['artificial-intelligence'] }
            ).then(result => {
                console.log('Document added:', result.document_id);
            });

            // Search documents
            client.searchDocuments('artificial intelligence').then(results => {
                console.log('Search results:', results);
            });

            // Chat with context
            client.chatCompletion([
                { role: 'user', content: 'Explain machine learning' }
            ]).then(response => {
                console.log('AI Response:', response.choices[0].message.content);
            });
            """,
            "metadata": {
                "category": "code_examples",
                "language": "javascript",
                "topic": "api_integration",
                "difficulty": "intermediate",
                "tags": ["javascript", "api", "fetch", "async-await"]
            }
        }
    ]

    retriever.add_documents(code_examples)
    print(f"✅ Added {len(code_examples)} code examples")

    # Example 3: Research papers and articles
    print("\n📄 Adding Research Content...")
    research_content = [
        {
            "id": "transformer_architecture",
            "text": """
            Attention Is All You Need: The Transformer Architecture

            Abstract:
            The Transformer architecture, introduced in "Attention Is All You Need" (Vaswani et al., 2017),
            revolutionized natural language processing by relying entirely on attention mechanisms,
            dispensing with recurrence and convolutions entirely.

            Key Innovations:
            1. Self-Attention Mechanism:
               - Allows the model to weigh the importance of different words in a sequence
               - Enables parallel processing unlike RNNs
               - Captures long-range dependencies effectively

            2. Multi-Head Attention:
               - Multiple attention heads capture different types of relationships
               - Each head learns different aspects of the input
               - Concatenated and linearly transformed for final output

            3. Positional Encoding:
               - Since there's no recurrence, position information is added explicitly
               - Uses sinusoidal functions to encode position
               - Allows the model to understand sequence order

            4. Feed-Forward Networks:
               - Applied to each position separately and identically
               - Consists of two linear transformations with ReLU activation
               - Provides non-linearity to the model

            Impact:
            - Foundation for BERT, GPT, T5, and other large language models
            - Enabled the development of models with billions of parameters
            - Transformed machine translation, text summarization, and question answering
            - Led to the current era of large language models and generative AI
            """,
            "metadata": {
                "category": "research_papers",
                "topic": "transformer_architecture",
                "authors": ["Vaswani", "Shazeer", "Parmar"],
                "year": 2017,
                "venue": "NIPS",
                "tags": ["transformer", "attention", "nlp", "deep-learning"]
            }
        }
    ]

    retriever.add_documents(research_content)
    print(f"✅ Added {len(research_content)} research documents")

    # Demonstrate advanced search
    print("\n🔍 Advanced Search Demonstrations...")

    search_queries = [
        {
            "query": "neural network training and optimization",
            "description": "Technical ML query"
        },
        {
            "query": "REST API authentication and security",
            "description": "API development query"
        },
        {
            "query": "Python machine learning code examples",
            "description": "Code-specific query"
        },
        {
            "query": "transformer attention mechanism research",
            "description": "Research-focused query"
        }
    ]

    for search in search_queries:
        print(f"\n🎯 {search['description']}: '{search['query']}'")
        results = retriever.search(search['query'], k=3)

        for i, result in enumerate(results, 1):
            doc_id = result.get('id', 'unknown')
            score = result.get('score', 0)
            category = result.get('metadata', {}).get('category', 'unknown')

            print(f"   {i}. {doc_id} (Score: {score:.3f}, Category: {category})")

            # Show content preview
            content = result.get('text', '')
            preview = content.replace('\n', ' ').strip()[:100] + "..."
            print(f"      Preview: {preview}")


if __name__ == "__main__":
    # Run original demo
    main()

    # Run advanced content demo
    demo_advanced_content_addition()
