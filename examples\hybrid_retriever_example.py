"""
Example usage of the HybridRetriever class.
This script demonstrates how to use the hybrid retriever with both FAISS and LightRAG.
"""

import logging
import numpy as np
from pathlib import Path
import sys

# Add project root to path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from src.hybrid_retriever import HybridRetriever

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    # Initialize the hybrid retriever
    retriever = HybridRetriever(
        vector_db="faiss",  # Use "torch" for pure PyTorch implementation
        use_gpu=True,       # Use GPU if available
        first_stage_k=100,  # Number of candidates in first stage
        rerank_k=10,        # Number of results after reranking
        model_name="BAAI/bge-small-en-v1.5"  # Embedding model
    )
    
    # Example documents
    documents = [
        {"id": "doc1", "text": "The quick brown fox jumps over the lazy dog.", "category": "animals"},
        {"id": "doc2", "text": "Machine learning is a subset of artificial intelligence.", "category": "ai"},
        {"id": "doc3", "text": "The capital of France is Paris.", "category": "geography"},
        {"id": "doc4", "text": "Python is a popular programming language for data science.", "category": "programming"},
        {"id": "doc5", "text": "The Great Wall of China is visible from space.", "category": "geography"},
        {"id": "doc6", "text": "Deep learning models require large amounts of data.", "category": "ai"},
        {"id": "doc7", "text": "The Eiffel Tower is located in Paris, France.", "category": "landmarks"},
        {"id": "doc8", "text": "JavaScript is commonly used for web development.", "category": "programming"},
    ]
    
    # Add documents to the retriever
    logger.info("Adding documents to the retriever...")
    retriever.add_documents(documents)
    
    # Example queries
    queries = [
        "What is machine learning?",
        "Tell me about Paris",
        "Programming languages for data science"
    ]
    
    # Perform searches
    for query in queries:
        print(f"\nQuery: {query}")
        print("-" * 50)
        
        # Search with reranking
        results = retriever.search(query, rerank=True)
        
        print("With Reranking:")
        for i, result in enumerate(results, 1):
            print(f"{i}. [{result.metadata.get('category', 'N/A')}] {result.text} (Score: {result.score:.4f})")
        
        # Search without reranking (for comparison)
        if len(queries) <= 3:  # Only show for a few queries to avoid too much output
            results = retriever.search(query, rerank=False)
            print("\nWithout Reranking:")
            for i, result in enumerate(results, 1):
                print(f"{i}. [{result.metadata.get('category', 'N/A')}] {result.text} (Score: {result.score:.4f})")
        
        print("\n" + "="*80 + "\n")
    
    # Print system information
    print("\nSystem Information:")
    print("-" * 50)
    for key, value in retriever.get_system_info().items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    main()
