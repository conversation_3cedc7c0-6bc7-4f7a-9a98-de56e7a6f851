# Simple script to check FAISS installation
import sys
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")

try:
    import faiss
    print(f"FAISS imported successfully")
    print(f"FAISS version: {faiss.__version__}")
    print(f"FAISS build with GPU support: {hasattr(faiss, 'StandardGpuResources')}")
except ImportError as e:
    print(f"Error importing FAISS: {e}")

# Try to import faiss.swigfaiss_avx2 which has been problematic
try:
    import faiss.swigfaiss_avx2
    print("Successfully imported faiss.swigfaiss_avx2")
except ImportError as e:
    print(f"Error importing faiss.swigfaiss_avx2: {e}")
