/* Custom CSS for Neural Symbolic Language Model Documentation */

/* Brand colors */
:root {
    --primary-color: #2980B9;
    --secondary-color: #3498DB;
    --accent-color: #E74C3C;
    --success-color: #27AE60;
    --warning-color: #F39C12;
    --dark-color: #2C3E50;
    --light-color: #ECF0F1;
}

/* Header customization */
.wy-nav-top {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.wy-nav-top a {
    color: white;
    font-weight: bold;
}

/* Sidebar customization */
.wy-nav-side {
    background: var(--dark-color);
}

.wy-menu-vertical a {
    color: var(--light-color);
}

.wy-menu-vertical a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Content area */
.wy-nav-content {
    background: white;
}

/* Code blocks */
.highlight {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin: 1em 0;
}

.highlight pre {
    background: transparent;
    border: none;
    margin: 0;
    padding: 1em;
}

/* API documentation styling */
.py.class, .py.function, .py.method {
    border-left: 4px solid var(--primary-color);
    padding-left: 1em;
    margin: 1em 0;
    background: #f8f9fa;
}

.py.class > dt, .py.function > dt, .py.method > dt {
    background: var(--primary-color);
    color: white;
    padding: 0.5em;
    margin: -1em -1em 0.5em -1em;
    border-radius: 4px 4px 0 0;
}

/* Admonitions */
.admonition {
    border-radius: 4px;
    margin: 1em 0;
    padding: 0;
    overflow: hidden;
}

.admonition.note {
    border-left: 4px solid var(--primary-color);
}

.admonition.warning {
    border-left: 4px solid var(--warning-color);
}

.admonition.danger {
    border-left: 4px solid var(--accent-color);
}

.admonition.tip {
    border-left: 4px solid var(--success-color);
}

.admonition-title {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.5em 1em;
    margin: 0;
    font-weight: bold;
}

.admonition p {
    padding: 0 1em 1em 1em;
    margin: 0;
}

/* Tables */
.wy-table-responsive table td, .wy-table-responsive table th {
    white-space: normal;
}

.wy-table thead th {
    background: var(--primary-color);
    color: white;
}

.wy-table-striped tr:nth-child(2n-1) td {
    background-color: #f8f9fa;
}

/* Buttons and links */
.btn {
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Search box */
.wy-side-nav-search {
    background: var(--dark-color);
}

.wy-side-nav-search input[type=text] {
    border-radius: 4px;
    border: 1px solid var(--primary-color);
}

/* Version selector */
.wy-side-nav-search .version {
    color: var(--light-color);
}

/* Footer */
.rst-footer-buttons {
    margin-top: 2em;
    padding-top: 1em;
    border-top: 1px solid #e9ecef;
}

/* Mobile responsiveness */
@media screen and (max-width: 768px) {
    .wy-nav-content {
        margin-left: 0;
    }
    
    .wy-nav-side {
        left: -300px;
    }
    
    .wy-nav-side.shift {
        left: 0;
    }
}

/* Custom badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.5em;
    font-size: 0.75em;
    font-weight: bold;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    margin: 0 0.25em;
}

.badge-primary {
    color: white;
    background-color: var(--primary-color);
}

.badge-success {
    color: white;
    background-color: var(--success-color);
}

.badge-warning {
    color: white;
    background-color: var(--warning-color);
}

.badge-danger {
    color: white;
    background-color: var(--accent-color);
}

/* API endpoint styling */
.http-method {
    display: inline-block;
    padding: 0.2em 0.5em;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
    border-radius: 3px;
    margin-right: 0.5em;
}

.http-get {
    background-color: var(--success-color);
    color: white;
}

.http-post {
    background-color: var(--primary-color);
    color: white;
}

.http-put {
    background-color: var(--warning-color);
    color: white;
}

.http-delete {
    background-color: var(--accent-color);
    color: white;
}

/* Code syntax highlighting improvements */
.highlight .k { color: #0000ff; } /* Keyword */
.highlight .s { color: #008000; } /* String */
.highlight .c { color: #808080; font-style: italic; } /* Comment */
.highlight .n { color: #000000; } /* Name */
.highlight .o { color: #666666; } /* Operator */

/* Responsive images */
img {
    max-width: 100%;
    height: auto;
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none;
    }
    
    .wy-nav-content {
        margin-left: 0;
    }
}
