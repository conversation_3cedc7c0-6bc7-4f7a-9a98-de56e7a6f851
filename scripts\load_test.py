"""
Load Test Script for Symbolic Language Model API

This script performs a load test on the API to check for memory leaks
and performance under sustained load.
"""

import time
import requests
import threading
import argparse
import json
import matplotlib.pyplot as plt
import numpy as np
from concurrent.futures import ThreadPoolExecutor

# Default settings
DEFAULT_URL = "http://localhost:8080/chat"
DEFAULT_DURATION = 60  # seconds
DEFAULT_THREADS = 4
DEFAULT_INTERVAL = 0.5  # seconds between requests

def send_request(url, request_id):
    """Send a test request to the API."""
    try:
        start_time = time.time()
        
        # Alternate between different test queries
        queries = [
            "What is Neural-Symbolic AI?",
            "Explain the difference between FAISS and ChromaDB",
            "How does GPU acceleration improve vector search?",
            "What are the benefits of symbolic reasoning?"
        ]
        
        query = queries[request_id % len(queries)]
        
        response = requests.post(
            url,
            json={"text": query},
            timeout=30
        )
        
        duration = time.time() - start_time
        status_code = response.status_code
        
        # Extract response length for metrics
        if status_code == 200:
            response_length = len(response.text)
            is_cached = response.json().get("cached", False)
        else:
            response_length = 0
            is_cached = False
        
        return {
            "request_id": request_id,
            "status_code": status_code,
            "duration": duration,
            "timestamp": time.time(),
            "response_length": response_length,
            "is_cached": is_cached
        }
    
    except Exception as e:
        return {
            "request_id": request_id,
            "status_code": 0,
            "duration": time.time() - start_time,
            "timestamp": time.time(),
            "error": str(e),
            "response_length": 0,
            "is_cached": False
        }

def check_monitoring(base_url):
    """Check the monitoring endpoint for server metrics."""
    try:
        monitoring_url = base_url.replace("/chat", "/monitoring")
        response = requests.get(monitoring_url)
        return response.json()
    except Exception as e:
        print(f"Error checking monitoring endpoint: {e}")
        return None

def worker(url, request_id, results, interval):
    """Worker function for sending requests in a thread."""
    result = send_request(url, request_id)
    results.append(result)
    
    # Log request result
    status = "✓" if result["status_code"] == 200 else "✗"
    cached = " (cached)" if result.get("is_cached", False) else ""
    print(f"Request {request_id}: {status} {result['duration']:.2f}s{cached}")
    
    # Delay between requests
    time.sleep(interval)

def run_load_test(url, duration, num_threads, interval):
    """Run the load test."""
    print(f"Starting load test on {url}")
    print(f"Duration: {duration}s, Threads: {num_threads}, Interval: {interval}s")
    print("=" * 80)
    
    # Check initial monitoring state
    base_url = url.rsplit("/", 1)[0]
    initial_metrics = check_monitoring(base_url)
    if initial_metrics:
        print("Initial server metrics:")
        print(f"  CPU: {initial_metrics.get('system', {}).get('cpu_percent', 'N/A')}%")
        print(f"  Memory: {initial_metrics.get('system', {}).get('memory_percent', 'N/A')}%")
        print(f"  Active requests: {initial_metrics.get('system', {}).get('active_requests', 'N/A')}")
    
    # Initialize results and start time
    results = []
    start_time = time.time()
    request_id = 0
    
    # Create thread pool
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        
        # Keep submitting tasks until duration is reached
        while time.time() - start_time < duration:
            # Submit tasks up to thread count
            while len(futures) < num_threads and time.time() - start_time < duration:
                future = executor.submit(worker, url, request_id, results, interval)
                futures.append((future, request_id))
                request_id += 1
            
            # Process completed futures
            remaining_futures = []
            for future, req_id in futures:
                if future.done():
                    try:
                        future.result()  # Get result to catch any exceptions
                    except Exception as e:
                        print(f"Error in request {req_id}: {e}")
                else:
                    remaining_futures.append((future, req_id))
            
            futures = remaining_futures
            time.sleep(0.1)  # Small sleep to prevent CPU hogging
    
    # Calculate test duration
    actual_duration = time.time() - start_time
    
    # Check final monitoring state
    final_metrics = check_monitoring(base_url)
    
    # Print summary
    print("=" * 80)
    print(f"Load test completed in {actual_duration:.2f}s")
    print(f"Total requests: {len(results)}")
    
    # Calculate statistics
    if results:
        success_count = sum(1 for r in results if r["status_code"] == 200)
        success_rate = success_count / len(results) * 100
        durations = [r["duration"] for r in results if r["status_code"] == 200]
        
        if durations:
            avg_duration = sum(durations) / len(durations)
            min_duration = min(durations)
            max_duration = max(durations)
            
            print(f"Success rate: {success_rate:.1f}%")
            print(f"Average response time: {avg_duration:.2f}s")
            print(f"Min response time: {min_duration:.2f}s")
            print(f"Max response time: {max_duration:.2f}s")
            
            # Count cached responses
            cached_count = sum(1 for r in results if r.get("is_cached", False))
            print(f"Cached responses: {cached_count} ({cached_count/len(results)*100:.1f}%)")
        else:
            print("No successful requests to calculate statistics")
    
    # Print memory usage change
    if initial_metrics and final_metrics:
        print("\nServer metrics comparison:")
        initial_mem = initial_metrics.get('system', {}).get('memory_percent', 0)
        final_mem = final_metrics.get('system', {}).get('memory_percent', 0)
        mem_change = final_mem - initial_mem
        
        print(f"  Memory usage: {initial_mem:.1f}% → {final_mem:.1f}% ({mem_change:+.1f}%)")
        
        if mem_change > 5:
            print("⚠️ WARNING: Significant memory increase detected, possible memory leak")
        
        # Check active requests (should be 0 after test)
        active_reqs = final_metrics.get('system', {}).get('active_requests', 0)
        if active_reqs > 0:
            print(f"⚠️ WARNING: {active_reqs} active requests still reported after test completion")
    
    # Visualize results
    visualize_results(results, actual_duration)
    
    return results

def visualize_results(results, duration):
    """Visualize test results."""
    if not results:
        print("No results to visualize")
        return
    
    plt.figure(figsize=(12, 8))
    
    # Extract data
    timestamps = [r["timestamp"] for r in results]
    start_time = min(timestamps)
    relative_times = [(t - start_time) for t in timestamps]
    durations = [r["duration"] for r in results]
    status_codes = [r["status_code"] for r in results]
    
    # Plot response times
    plt.subplot(2, 1, 1)
    success_times = [(t, d) for t, d, s in zip(relative_times, durations, status_codes) if s == 200]
    error_times = [(t, d) for t, d, s in zip(relative_times, durations, status_codes) if s != 200]
    
    if success_times:
        success_x, success_y = zip(*success_times)
        plt.scatter(success_x, success_y, color='green', alpha=0.7, label='Success')
    
    if error_times:
        error_x, error_y = zip(*error_times)
        plt.scatter(error_x, error_y, color='red', alpha=0.7, label='Error')
    
    plt.xlabel('Time (seconds)')
    plt.ylabel('Response Time (seconds)')
    plt.title('Response Times During Load Test')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot request rate
    plt.subplot(2, 1, 2)
    bin_width = max(1, int(duration / 20))  # Divide duration into ~20 bins
    bins = list(range(0, int(duration) + bin_width, bin_width))
    plt.hist(relative_times, bins=bins, color='blue', alpha=0.7)
    plt.xlabel('Time (seconds)')
    plt.ylabel('Number of Requests')
    plt.title('Request Rate')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f"load_test_results_{time.strftime('%Y%m%d_%H%M%S')}.png")
    print(f"Results visualization saved to load_test_results_{time.strftime('%Y%m%d_%H%M%S')}.png")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Load test for Symbolic Language Model API')
    parser.add_argument('--url', default=DEFAULT_URL, help=f'API URL (default: {DEFAULT_URL})')
    parser.add_argument('--duration', type=int, default=DEFAULT_DURATION, help=f'Test duration in seconds (default: {DEFAULT_DURATION})')
    parser.add_argument('--threads', type=int, default=DEFAULT_THREADS, help=f'Number of concurrent threads (default: {DEFAULT_THREADS})')
    parser.add_argument('--interval', type=float, default=DEFAULT_INTERVAL, help=f'Interval between requests in seconds (default: {DEFAULT_INTERVAL})')
    return parser.parse_args()

if __name__ == '__main__':
    args = parse_args()
    results = run_load_test(args.url, args.duration, args.threads, args.interval)
    
    # Save results to file
    with open(f"load_test_results_{time.strftime('%Y%m%d_%H%M%S')}.json", 'w') as f:
        json.dump(results, f, indent=2)
