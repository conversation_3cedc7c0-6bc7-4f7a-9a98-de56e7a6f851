"""
Test client for the Neural Symbolic Language Model API.
This script tests the chat and performance endpoints.
"""

import requests
import time
import json

BASE_URL = "http://localhost:8080"  # Updated to match our current port

def test_chat(query):
    """Test the chat endpoint.
    
    Args:
        query (str): The query to send
        
    Returns:
        dict: The response from the API
    """
    start_time = time.time()
    response = requests.post(f"{BASE_URL}/chat", json={"text": query})
    end_time = time.time()
    
    print(f"Query: {query}")
    print(f"Response time: {end_time - start_time:.2f}s")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 80)
    
    return response.json()

def test_performance():
    """Test the performance endpoint.
    
    Returns:
        dict: Performance statistics
    """
    response = requests.get(f"{BASE_URL}/performance")
    print("Performance Stats:")
    print(json.dumps(response.json(), indent=2))
    print("-" * 80)
    
    return response.json()

if __name__ == "__main__":
    print("Starting API Tests...")
    print("=" * 80)
    
    try:
        # Test performance endpoint
        print("\nTesting Performance Endpoint:")
        test_performance()
        
        # Test chat endpoint with various queries
        print("\nTesting Chat Endpoint:")
        test_queries = [
            "What is Neural-Symbolic AI?",
            "Explain the difference between FAISS and ChromaDB",
            "How does GPU acceleration improve vector search?",
            "What are the benefits of symbolic reasoning?",
            "How to implement RAG in a production system?"
        ]
        
        for query in test_queries:
            test_chat(query)
            time.sleep(1)  # Pause between requests
        
        # Test caching by repeating a query
        print("\nTesting Caching with Repeated Query:")
        test_chat(test_queries[0])
        
        print("\nAll tests completed successfully!")
        
    except requests.exceptions.ConnectionError:
        print("\nError: Could not connect to the API server.")
        print("Please make sure the server is running on", BASE_URL)
    except Exception as e:
        print("\nError during testing:", str(e))
