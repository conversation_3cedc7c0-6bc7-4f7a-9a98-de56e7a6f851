"""
System Python to Conda FAISS-GPU Bridge

This script shows how to use your Conda FAISS-GPU installation with the system Python.
It modifies sys.path to include the Conda environment's site-packages directory.
"""

import os
import sys
import site

def add_conda_to_path(conda_env_name=None, conda_prefix=None):
    """
    Add Conda environment's site-packages to Python path.
    
    Args:
        conda_env_name: Name of the Conda environment (if conda_prefix not provided)
        conda_prefix: Full path to Conda environment (overrides conda_env_name)
    
    Returns:
        bool: True if successful, False otherwise
    """
    # If conda_prefix is not provided, try to find it from environment name
    if not conda_prefix and conda_env_name:
        # Try common Conda installation paths
        user_home = os.path.expanduser("~")
        possible_conda_base_dirs = [
            os.path.join(user_home, "miniconda3"),
            os.path.join(user_home, "anaconda3"),
            os.path.join(user_home, "miniconda"),
            os.path.join(user_home, "anaconda"),
            "C:\\ProgramData\\miniconda3",
            "C:\\ProgramData\\Anaconda3"
        ]
        
        for base_dir in possible_conda_base_dirs:
            if os.path.exists(base_dir):
                env_path = os.path.join(base_dir, "envs", conda_env_name)
                if os.path.exists(env_path):
                    conda_prefix = env_path
                    break
    
    if not conda_prefix or not os.path.exists(conda_prefix):
        print(f"Could not find Conda environment path: {conda_prefix}")
        return False
    
    # Find site-packages directory
    if os.name == 'nt':  # Windows
        site_packages = os.path.join(conda_prefix, "Lib", "site-packages")
    else:  # Linux/Mac
        python_version = f"python{sys.version_info.major}.{sys.version_info.minor}"
        site_packages = os.path.join(conda_prefix, "lib", python_version, "site-packages")
    
    if not os.path.exists(site_packages):
        print(f"Could not find site-packages directory: {site_packages}")
        return False
    
    # Add to sys.path if not already there
    if site_packages not in sys.path:
        sys.path.insert(0, site_packages)
        print(f"Added Conda site-packages to sys.path: {site_packages}")
        
        # Also add to Python's site directories
        site.addsitedir(site_packages)
        print("Added to site directories as well")
        
        return True
    else:
        print(f"Conda site-packages already in sys.path: {site_packages}")
        return True

# This is how you would use this in your main.py or other scripts:
# -------------------------------------------------------------------
# import sys
# import os
# # Add this at the top of your script
# sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
# from use_conda_faiss import add_conda_to_path
# 
# # Call this before importing faiss
# add_conda_to_path(conda_env_name="your_env_name")  # Replace with your env name
# 
# # Now you can import faiss with GPU support
# import faiss
# -------------------------------------------------------------------

if __name__ == "__main__":
    print("This script demonstrates how to use Conda's FAISS-GPU with system Python")
    print("Current Python executable:", sys.executable)
    
    # Check if we're running in Conda already
    conda_prefix = os.environ.get('CONDA_PREFIX')
    if conda_prefix:
        print(f"Already running in Conda environment: {os.path.basename(conda_prefix)}")
        print(f"Path: {conda_prefix}")
        
        try:
            import faiss
            has_gpu = hasattr(faiss, 'StandardGpuResources')
            print(f"FAISS version: {faiss.__version__}")
            print(f"FAISS has GPU support: {has_gpu}")
        except ImportError as e:
            print(f"Error importing FAISS: {e}")
    else:
        print("Not running in a Conda environment")
        print("Please provide your Conda environment name or path:")
        print("Example usage:")
        print("  python use_conda_faiss.py")
        print("  >>> add_conda_to_path(conda_env_name='your_env_name')")
        print("  or")
        print("  >>> add_conda_to_path(conda_prefix='C:\\path\\to\\conda\\envs\\your_env')")
        
        # Interactive prompt
        while True:
            choice = input("\nDo you want to provide a Conda environment? (y/n): ").strip().lower()
            if choice == 'y':
                env_input = input("Enter Conda environment name or full path: ").strip()
                
                # Check if input is a path or name
                if os.path.exists(env_input):
                    success = add_conda_to_path(conda_prefix=env_input)
                else:
                    success = add_conda_to_path(conda_env_name=env_input)
                
                if success:
                    # Try importing FAISS
                    try:
                        import faiss
                        has_gpu = hasattr(faiss, 'StandardGpuResources')
                        print(f"FAISS version: {faiss.__version__}")
                        print(f"FAISS has GPU support: {has_gpu}")
                    except ImportError as e:
                        print(f"Error importing FAISS: {e}")
                break
            elif choice == 'n':
                print("Exiting without modifying Python path")
                break
            else:
                print("Please enter 'y' or 'n'")
