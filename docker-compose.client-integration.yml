# Docker Compose configuration for Neural Symbolic Language Model with client applications
# This setup demonstrates how to deploy the Neural Symbolic Language Model alongside
# popular client applications like Open WebUI, LibreChat, and others.

version: '3.8'

services:
  # Ollama service for running the gemma3n:e2b model
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-neural-symbolic
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Neural Symbolic Language Model API
  neural-symbolic-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: neural-symbolic-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Core configuration
      - APP_ENVIRONMENT=production
      - APP_HOST=0.0.0.0
      - APP_PORT=8080
      
      # Model configuration
      - MODEL_REASONING_ENGINE=ollama
      - MODEL_REASONING_MODEL=gemma3n:e2b
      - MODEL_OLLAMA_HOST=http://ollama:11434
      - MODEL_USE_GPU=true
      
      # Security configuration
      - SECURITY_API_KEYS_JSON={"webui": {"key": "webui-secure-key-2024", "permissions": ["read", "write"], "rate_limit": 1000}, "librechat": {"key": "librechat-secure-key-2024", "permissions": ["read", "write"], "rate_limit": 1000}, "general": {"key": "general-secure-key-2024", "permissions": ["read", "write"], "rate_limit": 500}}
      
      # Performance configuration
      - CACHE_ENABLED=true
      - CACHE_MAX_SIZE=10000
      - PERFORMANCE_MAX_CONCURRENT_REQUESTS=50
      
      # Logging configuration
      - LOG_LEVEL=INFO
      - LOG_STRUCTURED=true
    volumes:
      - neural_symbolic_data:/app/data
      - neural_symbolic_logs:/app/logs
    depends_on:
      ollama:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Open WebUI - ChatGPT-like interface
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui-neural-symbolic
    restart: unless-stopped
    ports:
      - "3000:8080"
    environment:
      # API configuration
      - OPENAI_API_BASE_URL=http://neural-symbolic-api:8080/v1
      - OPENAI_API_KEY=webui-secure-key-2024
      - DEFAULT_MODELS=gemma3n:e2b
      
      # UI configuration
      - WEBUI_NAME=Neural Symbolic Chat
      - WEBUI_URL=http://localhost:3000
      - WEBUI_SECRET_KEY=neural-symbolic-webui-secret-2024
      
      # Features
      - ENABLE_SIGNUP=true
      - ENABLE_LOGIN_FORM=true
      - DEFAULT_USER_ROLE=user
      
      # Advanced features
      - ENABLE_RAG_HYBRID_SEARCH=true
      - ENABLE_RAG_WEB_LOADER=true
      - RAG_EMBEDDING_ENGINE=ollama
      - RAG_EMBEDDING_MODEL=mxbai-embed-large
    volumes:
      - open_webui_data:/app/backend/data
    depends_on:
      neural-symbolic-api:
        condition: service_healthy

  # LibreChat - Enhanced ChatGPT clone
  librechat:
    image: ghcr.io/danny-avila/librechat:latest
    container_name: librechat-neural-symbolic
    restart: unless-stopped
    ports:
      - "3001:3080"
    environment:
      # Database
      - MONGO_URI=mongodb://librechat-mongo:27017/LibreChat
      
      # API configuration
      - CUSTOM_CONFIG_PATH=/app/librechat.yaml
      
      # Security
      - JWT_SECRET=librechat-neural-symbolic-jwt-secret-2024
      - JWT_REFRESH_SECRET=librechat-neural-symbolic-refresh-secret-2024
      
      # Features
      - ALLOW_EMAIL_LOGIN=true
      - ALLOW_REGISTRATION=true
      - ALLOW_SOCIAL_LOGIN=false
      
      # Search
      - SEARCH=true
      - MEILI_HOST=http://librechat-meilisearch:7700
      - MEILI_MASTER_KEY=librechat-meili-master-key-2024
    volumes:
      - ./librechat.yaml:/app/librechat.yaml:ro
      - librechat_data:/app/client/public/images
      - librechat_logs:/app/api/logs
    depends_on:
      - neural-symbolic-api
      - librechat-mongo
      - librechat-meilisearch

  # MongoDB for LibreChat
  librechat-mongo:
    image: mongo:6-jammy
    container_name: librechat-mongo
    restart: unless-stopped
    volumes:
      - librechat_mongo_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=librechat-mongo-password-2024

  # Meilisearch for LibreChat search functionality
  librechat-meilisearch:
    image: getmeili/meilisearch:v1.5
    container_name: librechat-meilisearch
    restart: unless-stopped
    ports:
      - "7700:7700"
    environment:
      - MEILI_HOST=http://localhost:7700
      - MEILI_MASTER_KEY=librechat-meili-master-key-2024
      - MEILI_NO_ANALYTICS=true
    volumes:
      - librechat_meilisearch_data:/meili_data

  # Nginx reverse proxy for production deployment
  nginx:
    image: nginx:alpine
    container_name: nginx-neural-symbolic
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - neural-symbolic-api
      - open-webui
      - librechat

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-neural-symbolic
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    depends_on:
      - neural-symbolic-api

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-neural-symbolic
    restart: unless-stopped
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=grafana-neural-symbolic-2024
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus

volumes:
  # Ollama data
  ollama_data:
    driver: local

  # Neural Symbolic Language Model data
  neural_symbolic_data:
    driver: local
  neural_symbolic_logs:
    driver: local

  # Open WebUI data
  open_webui_data:
    driver: local

  # LibreChat data
  librechat_data:
    driver: local
  librechat_logs:
    driver: local
  librechat_mongo_data:
    driver: local
  librechat_meilisearch_data:
    driver: local

  # Nginx logs
  nginx_logs:
    driver: local

  # Monitoring data
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  default:
    name: neural-symbolic-network
    driver: bridge
