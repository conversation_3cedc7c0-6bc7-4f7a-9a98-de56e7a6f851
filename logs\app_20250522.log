2025-05-22 00:55:10,322 - root - INFO - Logging system initialized
2025-05-22 00:55:10,402 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-22 00:55:10,452 - monitoring - INFO - Performance monitoring initialized
2025-05-22 00:55:10,526 - __mp_main__ - INFO - Initializing components...
2025-05-22 00:55:10,566 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-22 00:55:10,609 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-22 00:55:10,649 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-22 00:55:10,688 - vector_store - INFO - TorchVectorStore using CPU
2025-05-22 00:55:10,728 - __mp_main__ - INFO - Components initialized successfully
2025-05-22 00:55:10,809 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-22 00:55:10,878 - __mp_main__ - WARNING - No documents found in data directory
2025-05-22 00:55:11,163 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-22 00:55:11,375 - root - INFO - Logging system initialized
2025-05-22 00:55:11,382 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-22 00:55:11,402 - monitoring - INFO - Performance monitoring initialized
2025-05-22 00:55:11,482 - main - INFO - Initializing components...
2025-05-22 00:55:11,509 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-22 00:55:11,522 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-22 00:55:11,534 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-22 00:55:11,547 - vector_store - INFO - TorchVectorStore using CPU
2025-05-22 00:55:11,560 - main - INFO - Components initialized successfully
2025-05-22 00:55:11,576 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-22 00:55:11,596 - main - WARNING - No documents found in data directory
2025-05-22 05:06:56,637 - main - INFO - Application shutting down
2025-05-22 05:07:14,568 - monitoring - INFO - Performance monitoring shutdown
2025-05-22 05:07:19,204 - root - INFO - Logging system initialized
2025-05-22 05:07:19,210 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-22 05:07:19,240 - monitoring - INFO - Performance monitoring initialized
2025-05-22 05:07:19,268 - __mp_main__ - INFO - Initializing components...
2025-05-22 05:07:19,274 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-22 05:07:19,280 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-22 05:07:19,284 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-22 05:07:19,294 - vector_store - INFO - TorchVectorStore using CPU
2025-05-22 05:07:19,301 - __mp_main__ - INFO - Components initialized successfully
2025-05-22 05:07:19,307 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-22 05:07:19,334 - __mp_main__ - WARNING - No documents found in data directory
2025-05-22 05:07:19,610 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-22 05:07:19,839 - root - INFO - Logging system initialized
2025-05-22 05:07:19,854 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-22 05:07:19,867 - monitoring - INFO - Performance monitoring initialized
2025-05-22 05:07:19,886 - main - INFO - Initializing components...
2025-05-22 05:07:19,898 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-22 05:07:19,907 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-22 05:07:19,912 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-22 05:07:19,919 - vector_store - INFO - TorchVectorStore using CPU
2025-05-22 05:07:19,924 - main - INFO - Components initialized successfully
2025-05-22 05:07:19,930 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-22 05:07:19,952 - main - WARNING - No documents found in data directory
2025-05-22 05:09:57,288 - main - INFO - Application shutting down
2025-05-22 05:10:19,914 - monitoring - INFO - Performance monitoring shutdown
2025-05-22 05:10:23,990 - root - INFO - Logging system initialized
2025-05-22 05:10:23,990 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-22 05:10:24,031 - monitoring - INFO - Performance monitoring initialized
2025-05-22 05:10:24,060 - __mp_main__ - INFO - Initializing components...
2025-05-22 05:10:24,068 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-22 05:10:24,077 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-22 05:10:24,084 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-22 05:10:24,084 - vector_store - INFO - TorchVectorStore using CPU
2025-05-22 05:10:24,095 - __mp_main__ - INFO - Components initialized successfully
2025-05-22 05:10:24,102 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-22 05:10:24,128 - __mp_main__ - WARNING - No documents found in data directory
2025-05-22 05:10:24,444 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-22 05:10:24,666 - root - INFO - Logging system initialized
2025-05-22 05:10:24,674 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-22 05:10:24,685 - monitoring - INFO - Performance monitoring initialized
2025-05-22 05:10:24,713 - main - INFO - Initializing components...
2025-05-22 05:10:24,718 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-22 05:10:24,726 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-22 05:10:24,732 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-22 05:10:24,738 - vector_store - INFO - TorchVectorStore using CPU
2025-05-22 05:10:24,746 - main - INFO - Components initialized successfully
2025-05-22 05:10:24,753 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-22 05:10:24,774 - main - WARNING - No documents found in data directory
2025-05-22 05:12:07,840 - main - INFO - Application shutting down
2025-05-22 05:12:24,693 - monitoring - INFO - Performance monitoring shutdown
2025-05-22 05:12:28,781 - root - INFO - Logging system initialized
2025-05-22 05:12:28,789 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-22 05:12:28,816 - monitoring - INFO - Performance monitoring initialized
2025-05-22 05:12:28,842 - __mp_main__ - INFO - Initializing components...
2025-05-22 05:12:28,849 - __mp_main__ - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-22 05:12:28,856 - __mp_main__ - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-22 05:12:28,863 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-22 05:12:28,868 - vector_store - INFO - TorchVectorStore using CPU
2025-05-22 05:12:28,873 - __mp_main__ - INFO - Components initialized successfully
2025-05-22 05:12:28,880 - __mp_main__ - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-22 05:12:28,913 - __mp_main__ - WARNING - No documents found in data directory
2025-05-22 05:12:29,207 - main - WARNING - FAISS not available. Using fallback vector storage.
2025-05-22 05:12:29,972 - root - INFO - Logging system initialized
2025-05-22 05:12:29,979 - root - INFO - Log files will be stored in: Z:\Symbolic Language Model\logs
2025-05-22 05:12:29,992 - monitoring - INFO - Performance monitoring initialized
2025-05-22 05:12:30,138 - main - INFO - Initializing components...
2025-05-22 05:12:30,280 - main - INFO - GPU available: NVIDIA GeForce RTX 4070 Laptop GPU
2025-05-22 05:12:30,487 - main - WARNING - GPU FAISS not available. Will use CPU version of FAISS.
2025-05-22 05:12:30,501 - retrieval - WARNING - FAISS not available. Using PyTorch vector store fallback.
2025-05-22 05:12:30,585 - vector_store - INFO - TorchVectorStore using CPU
2025-05-22 05:12:30,663 - main - INFO - Components initialized successfully
2025-05-22 05:12:30,678 - main - INFO - Loading documents from Z:\Symbolic Language Model\src\..\data
2025-05-22 05:12:30,828 - main - WARNING - No documents found in data directory
