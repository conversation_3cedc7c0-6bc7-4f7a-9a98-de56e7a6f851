# Windows FAISS-GPU Setup Guide

This guide provides detailed instructions for setting up FAISS-GPU on Windows for the Symbolic Language Model project.

## Table of Contents

- [Quick Start](#quick-start)
- [Manual Setup](#manual-setup)
- [Verification](#verification)
- [Troubleshooting](#troubleshooting)
- [Advanced Configuration](#advanced-configuration)
- [FAQ](#faq)

## Quick Start

For a fully automated setup, follow these steps:

1. Right-click on `setup_windows.bat` in the project root and select "Run as administrator"
2. The script will:
   - Check for Miniconda installation
   - Verify Microsoft Visual C++ Redistributable is installed
   - Create a Conda environment named `symblang` with Python 3.10
   - Install PyTorch 2.4.0 with CUDA support
   - Install FAISS-GPU from conda-forge
   - Install all other project dependencies
   - Verify the installation

3. After setup completes, activate the environment:
   ```
   conda activate symblang
   ```

4. Verify the installation:
   ```
   python scripts/verify_faiss_gpu.py
   ```

## Manual Setup

If you prefer to set up the environment manually or the automated script doesn't work for your system:

### Prerequisites

1. **Install Miniconda**: Download and install from [https://docs.conda.io/en/latest/miniconda.html](https://docs.conda.io/en/latest/miniconda.html)

2. **Install Microsoft Visual C++ Redistributable**: Download and install from [https://aka.ms/vs/17/release/vc_redist.x64.exe](https://aka.ms/vs/17/release/vc_redist.x64.exe)

### Environment Setup

1. Create a new Conda environment:
   ```
   conda create -n symblang python=3.10
   conda activate symblang
   ```

2. Install PyTorch with CUDA support:
   ```
   conda install -c pytorch -c nvidia pytorch=2.4.0 torchvision torchaudio cudatoolkit
   ```

3. Install FAISS-GPU from conda-forge:
   ```
   conda install -c conda-forge faiss-gpu
   ```

4. Install remaining dependencies:
   ```
   pip install -r requirements.txt
   ```

## Verification

To verify that FAISS-GPU is properly installed and working:

1. Activate your Conda environment:
   ```
   conda activate symblang
   ```

2. Run the verification script:
   ```
   python scripts/verify_faiss_gpu.py
   ```

The script will:
- Check if FAISS can be imported
- Verify CUDA availability
- Test basic FAISS-GPU functionality
- Benchmark search performance (CPU vs GPU)
- Check your environment for potential issues

A successful verification will show:
```
SUMMARY
=================================================
FAISS Import: ✅
CUDA Available: ✅
Environment Check: ✅
FAISS-GPU Functionality: ✅

Overall result: ✅ PASS

Congratulations! FAISS-GPU is correctly installed and working.
```

## Troubleshooting

If you encounter issues with FAISS-GPU, use the diagnostic tool:

```
python scripts/fix_faiss_gpu.py
```

This will identify common problems without making changes. To automatically fix detected issues:

```
python scripts/fix_faiss_gpu.py --fix
```

### Common Issues and Solutions

#### ImportError: No module named 'faiss'

**Problem**: You might be running with the system Python while FAISS-GPU is installed in a Conda environment.

**Solution**: 
- Ensure you've activated the correct Conda environment: `conda activate symblang`
- Verify the Python path is from your Conda environment: `python -c "import sys; print(sys.executable)"`

#### ModuleNotFoundError: No module named 'faiss.swigfaiss_avx2'

**Problem**: Missing Microsoft Visual C++ Redistributables, which are required runtime libraries for FAISS's compiled C++ components.

**Solution**:
- Download and install the latest Visual C++ Redistributable: https://aka.ms/vs/17/release/vc_redist.x64.exe
- Reinstall FAISS-GPU through Conda: `conda install -c conda-forge faiss-gpu`

#### Dependency Conflicts

**Problem**: Packages like vllm 0.6.3 may require specific PyTorch versions (e.g., torch==2.4.0).

**Solution**:
- Use the recommended Conda setup with the specified PyTorch version: `conda install -c pytorch pytorch=2.4.0`
- If needed, create a clean environment: `conda create -n symblang_clean python=3.10`

#### Problematic FAISS Installations

**Problem**: Corrupt or incomplete FAISS installations can prevent proper functioning.

**Solution**:
- Check for problematic directories like '~faiss_cpu-*.dist-info' in your site-packages
- Remove these directories if found (the fix script can do this automatically)
- Reinstall FAISS-GPU through Conda

## Advanced Configuration

### Using Multiple CUDA GPUs

If you have multiple GPUs, FAISS can distribute operations across them. Modify your code to use multiple resources:

```python
import faiss

# Create resources for each GPU
gpu_resources = []
for i in range(torch.cuda.device_count()):
    res = faiss.StandardGpuResources()
    gpu_resources.append(res)

# Create a configuration for using multiple GPUs
config = faiss.GpuMultipleClonerOptions()
config.shard = True  # Shard index across GPUs

# Create the index on multiple GPUs
cpu_index = faiss.IndexFlatL2(dimension)
gpu_index = faiss.index_cpu_to_all_gpus(cpu_index, co=config)
```

### Memory Management

FAISS-GPU can consume significant GPU memory. To limit memory usage:

```python
import faiss

# Create resources with memory limits
res = faiss.StandardGpuResources()
res.setTempMemory(1024 * 1024 * 1024)  # Limit to 1GB

# Create the index on GPU
cpu_index = faiss.IndexFlatL2(dimension)
gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)
```

## FAQ

### Q: Do I need to install CUDA separately?

A: No, when you install PyTorch using the Conda commands in this guide, it includes the necessary CUDA components.

### Q: Can I use pip to install FAISS-GPU on Windows?

A: It's not recommended. On Windows, installing FAISS-GPU via Conda is much more reliable due to complex dependencies and binary compatibility issues.

### Q: How do I know if my GPU is being used?

A: Run `python scripts/verify_faiss_gpu.py` which will test GPU functionality and measure performance. If you see a speedup compared to CPU, your GPU is working.

### Q: I have an older NVIDIA GPU. Will this work?

A: FAISS-GPU requires a CUDA-compatible GPU. For best performance, you should have a GPU with Compute Capability 3.5 or higher. You can check your GPU's compute capability at [NVIDIA's CUDA GPUs page](https://developer.nvidia.com/cuda-gpus).

### Q: How can I switch between CPU and GPU versions at runtime?

A: You can create both CPU and GPU versions of the same index and choose which to use:

```python
import faiss

# Create CPU index
cpu_index = faiss.IndexFlatL2(dimension)

# Create GPU resources
res = faiss.StandardGpuResources()

# Create GPU index from CPU index
gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)

# Use GPU or CPU index based on conditions
if use_gpu and torch.cuda.is_available():
    index = gpu_index
else:
    index = cpu_index
```
