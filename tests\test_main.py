"""Unit tests for the main API module."""

import unittest
import json
from fastapi.testclient import TestClient
from main import app

class TestAPI(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
        
    def test_openai_chat_endpoint(self):
        """Test the OpenAI-compatible chat endpoint."""
        response = self.client.post(
            "/v1/chat/completions",
            json={
                "model": "local",
                "messages": [
                    {"role": "user", "content": "What is symbolic reasoning?"}
                ]
            }
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Check response structure
        self.assertIn("id", data)
        self.assertIn("created", data)
        self.assertIn("model", data)
        self.assertIn("choices", data)
        self.assertIn("usage", data)
        
        # Check choices
        self.assertEqual(len(data["choices"]), 1)
        choice = data["choices"][0]
        self.assertIn("index", choice)
        self.assertIn("message", choice)
        self.assertIn("finish_reason", choice)
        
        # Check message
        message = choice["message"]
        self.assertIn("role", message)
        self.assertIn("content", message)
        self.assertEqual(message["role"], "assistant")
        
        # Test caching
        second_response = self.client.post(
            "/v1/chat/completions",
            json={
                "model": "local",
                "messages": [
                    {"role": "user", "content": "What is symbolic reasoning?"}
                ]
            }
        )
        self.assertEqual(second_response.status_code, 200)
        second_data = second_response.json()
        self.assertEqual(
            data["choices"][0]["message"]["content"],
            second_data["choices"][0]["message"]["content"]
        )
        
    def test_streaming_endpoint(self):
        """Test the streaming chat endpoint."""
        response = self.client.post(
            "/v1/chat/completions",
            json={
                "model": "local",
                "messages": [
                    {"role": "user", "content": "What is symbolic reasoning?"}
                ],
                "stream": True
            }
        )
        self.assertEqual(response.status_code, 200)
        
        # Check streaming response format
        for line in response.iter_lines():
            if line:
                # Handle both string and bytes response content
                if isinstance(line, bytes):
                    line_str = line.decode()
                else:
                    line_str = line
                
                # Skip empty lines
                if not line_str.strip():
                    continue
                    
                # Remove 'data: ' prefix if present
                if line_str.startswith('data: '):
                    line_str = line_str[6:]
                
                # Skip [DONE] message
                if line_str.strip() == '[DONE]':
                    continue
                
                try:
                    chunk = json.loads(line_str)
                    self.assertIn("id", chunk)
                    self.assertIn("object", chunk)
                    self.assertIn("created", chunk)
                    self.assertIn("model", chunk)
                    self.assertIn("choices", chunk)
                    
                    choice = chunk["choices"][0]
                    self.assertIn("index", choice)
                    if "delta" in choice:
                        delta = choice["delta"]
                        if "content" in delta:
                            self.assertIsInstance(delta["content"], str)
                        elif not delta:  # Empty delta means end of stream
                            self.assertEqual(choice["finish_reason"], "stop")
                except json.JSONDecodeError as e:
                    self.fail(f"Failed to parse JSON: {line_str}. Error: {str(e)}")
        
    def test_performance_endpoint(self):
        """Test the performance endpoint."""
        response = self.client.get("/performance")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Check cache info
        self.assertIn("cache", data)
        self.assertIn("size", data["cache"])
        self.assertIn("max_size", data["cache"])
        self.assertIn("hits", data["cache"])
        self.assertIn("misses", data["cache"])
        self.assertIn("hit_rate", data["cache"])
        
        # Check system info
        self.assertIn("system", data)
        system = data["system"]
        self.assertIn("gpu_available", system)
        self.assertIn("gpu_name", system)
        self.assertIn("cpu_percent", system)
        self.assertIn("memory_percent", system)
        self.assertIn("active_requests", system)
        self.assertIn("reasoner", system)
        self.assertIn("retriever", system)
        
        # Check request metrics
        self.assertIn("requests", data)
        requests = data["requests"]
        self.assertIn("total", requests)
        self.assertIn("avg_duration", requests)
        self.assertIn("error_rate", requests)
        self.assertIn("cache_hit_rate", requests)
        
    def test_system_info_endpoint(self):
        """Test the system info endpoint."""
        response = self.client.get("/system/info")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        self.assertIn("reasoner", data)
        self.assertIn("retriever", data)
        self.assertIn("gpu_optimized", data)
        
    def test_invalid_request(self):
        """Test handling of invalid requests."""
        # Test missing messages field
        response = self.client.post("/v1/chat/completions", json={})
        self.assertEqual(response.status_code, 422)
        
        # Test empty messages array
        response = self.client.post(
            "/v1/chat/completions",
            json={"model": "local", "messages": []}
        )
        self.assertEqual(response.status_code, 400)
        
        # Test missing user message
        response = self.client.post(
            "/v1/chat/completions",
            json={
                "model": "local",
                "messages": [{"role": "system", "content": "test"}]
            }
        )
        self.assertEqual(response.status_code, 400)
        
if __name__ == "__main__":
    unittest.main()
