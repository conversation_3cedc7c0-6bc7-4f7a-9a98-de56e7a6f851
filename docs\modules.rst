API Reference
=============

This section provides detailed API documentation for all modules in the
Neural Symbolic Language Model.

Core Modules
------------

.. toctree::
   :maxdepth: 2

   api/main
   api/symbolic_reasoning
   api/retrieval
   api/security
   api/models

Core Components
~~~~~~~~~~~~~~~

.. automodule:: main
   :members:
   :undoc-members:

.. automodule:: symbolic_reasoning
   :members:
   :undoc-members:

.. automodule:: retrieval
   :members:
   :undoc-members:

.. automodule:: security
   :members:
   :undoc-members:

.. automodule:: models
   :members:
   :undoc-members:

Utility Modules
---------------

Configuration Management
~~~~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: core.config
   :members:
   :undoc-members:

.. automodule:: core.cache
   :members:
   :undoc-members:

Monitoring and Logging
~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: monitoring
   :members:
   :undoc-members:

.. automodule:: logging_config
   :members:
   :undoc-members:

Exception <PERSON>
~~~~~~~~~~~~~~~~~~

.. automodule:: exceptions
   :members:
   :undoc-members:

Vector Storage
~~~~~~~~~~~~~~

.. automodule:: vector_store
   :members:
   :undoc-members:

API Routes
----------

Chat Endpoints
~~~~~~~~~~~~~~

.. automodule:: api.routes.chat
   :members:
   :undoc-members:

System Endpoints
~~~~~~~~~~~~~~~~

.. automodule:: api.routes.system
   :members:
   :undoc-members:

Monitoring Endpoints
~~~~~~~~~~~~~~~~~~~~

.. automodule:: api.routes.monitoring
   :members:
   :undoc-members:
