import numpy as np
import time
import faiss

def test_faiss_gpu():
    print("FAISS GPU Test Script")
    print("====================")
    
    # Print FAISS and GPU info
    print(f"FAISS version: {faiss.__version__}")
    print(f"Number of GPUs available: {faiss.get_num_gpus()}")
    
    # Parameters
    dimension = 768  # Dimension of the vectors (e.g., BERT embeddings)
    num_vectors = 10000  # Number of vectors in the database
    query_size = 5  # Number of query vectors
    k = 3  # Number of nearest neighbors to retrieve
    
    print(f"\nGenerating {num_vectors} random vectors of dimension {dimension}...")
    np.random.seed(123)  # For reproducibility
    
    # Generate random database vectors
    database_vectors = np.random.random((num_vectors, dimension)).astype('float32')
    
    # Generate random query vectors
    query_vectors = np.random.random((query_size, dimension)).astype('float32')
    
    print("\n=== CPU Index ===")
    # Create a CPU index
    cpu_index = faiss.IndexFlatL2(dimension)  # L2 distance
    
    # Add vectors to the CPU index
    cpu_index.add(database_vectors)
    
    # Search on CPU
    start_time = time.time()
    cpu_distances, cpu_indices = cpu_index.search(query_vectors, k)
    cpu_time = time.time() - start_time
    
    print(f"CPU search time for {query_size} queries: {cpu_time:.4f} seconds")
    
    print("\n=== GPU Index ===")
    # Create a GPU resource object
    res = faiss.StandardGpuResources()
    
    # Move the index to GPU
    gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)  # 0 is the GPU device ID
    
    # Search on GPU
    start_time = time.time()
    gpu_distances, gpu_indices = gpu_index.search(query_vectors, k)
    gpu_time = time.time() - start_time
    
    print(f"GPU search time for {query_size} queries: {gpu_time:.4f} seconds")
    print(f"Speedup: {cpu_time / gpu_time:.2f}x")
    
    # Verify results are the same (should be very close due to floating-point differences)
    print("\n=== Result Verification ===")
    print("Checking if CPU and GPU results match...")
    
    # Compare the indices (distances might have small floating-point differences)
    indices_match = np.array_equal(cpu_indices, gpu_indices)
    distances_close = np.allclose(cpu_distances, gpu_distances, rtol=1e-4)
    
    print(f"Indices match: {indices_match}")
    print(f"Distances are close: {distances_close}")
    
    if not indices_match or not distances_close:
        print("\nNote: Small differences in distances are normal due to floating-point arithmetic on GPU.")
    
    print("\n=== Example Query Results ===")
    print(f"Query 0 - Top {k} nearest neighbors (indices): {gpu_indices[0]}")
    print(f"Query 0 - Distances: {gpu_distances[0]}")

if __name__ == "__main__":
    test_faiss_gpu()
