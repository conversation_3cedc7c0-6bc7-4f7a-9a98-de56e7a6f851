"""Performance optimization utilities for the Neural Symbolic Language Model.

This module provides various performance optimization features including
connection pooling, request batching, memory optimization, and caching strategies.
"""

# Standard library imports
import asyncio
import time
import threading
from collections import defaultdict, deque
from typing import Any, Dict, List, Optional, Callable, Awaitable
import logging
import weakref
import gc

# Third-party imports
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

logger = logging.getLogger(__name__)


class ConnectionPool:
    """Generic connection pool for managing reusable connections.
    
    This class provides a thread-safe connection pool that can be used
    for database connections, HTTP clients, or any other reusable resources.
    """
    
    def __init__(self, 
                 create_connection: Callable[[], Any],
                 max_connections: int = 10,
                 min_connections: int = 2,
                 connection_timeout: float = 30.0):
        """Initialize the connection pool.
        
        Args:
            create_connection: Function to create new connections
            max_connections: Maximum number of connections in pool
            min_connections: Minimum number of connections to maintain
            connection_timeout: Timeout for connection operations
        """
        self.create_connection = create_connection
        self.max_connections = max_connections
        self.min_connections = min_connections
        self.connection_timeout = connection_timeout
        
        self._pool: deque = deque()
        self._in_use: set = set()
        self._lock = threading.RLock()
        self._created_count = 0
        
        # Pre-create minimum connections
        self._initialize_pool()
        
        logger.info(f"Connection pool initialized: max={max_connections}, min={min_connections}")
    
    def _initialize_pool(self) -> None:
        """Initialize the pool with minimum connections."""
        for _ in range(self.min_connections):
            try:
                conn = self.create_connection()
                self._pool.append(conn)
                self._created_count += 1
            except Exception as e:
                logger.error(f"Failed to create initial connection: {e}")
    
    def get_connection(self) -> Any:
        """Get a connection from the pool.
        
        Returns:
            A connection object
            
        Raises:
            RuntimeError: If unable to get connection within timeout
        """
        with self._lock:
            # Try to get existing connection
            if self._pool:
                conn = self._pool.popleft()
                self._in_use.add(id(conn))
                return conn
            
            # Create new connection if under limit
            if self._created_count < self.max_connections:
                try:
                    conn = self.create_connection()
                    self._created_count += 1
                    self._in_use.add(id(conn))
                    return conn
                except Exception as e:
                    logger.error(f"Failed to create new connection: {e}")
                    raise RuntimeError(f"Failed to create connection: {e}")
            
            # Pool is exhausted
            raise RuntimeError("Connection pool exhausted")
    
    def return_connection(self, conn: Any) -> None:
        """Return a connection to the pool.
        
        Args:
            conn: Connection to return
        """
        with self._lock:
            conn_id = id(conn)
            if conn_id in self._in_use:
                self._in_use.remove(conn_id)
                self._pool.append(conn)
            else:
                logger.warning("Attempted to return connection not from this pool")
    
    def close_all(self) -> None:
        """Close all connections in the pool."""
        with self._lock:
            while self._pool:
                conn = self._pool.popleft()
                try:
                    if hasattr(conn, 'close'):
                        conn.close()
                except Exception as e:
                    logger.error(f"Error closing connection: {e}")
            
            self._created_count = 0
            self._in_use.clear()
        
        logger.info("All connections closed")


class RequestBatcher:
    """Batches requests for efficient processing.
    
    This class collects multiple requests and processes them in batches
    to improve throughput and reduce overhead.
    """
    
    def __init__(self,
                 batch_processor: Callable[[List[Any]], Awaitable[List[Any]]],
                 max_batch_size: int = 10,
                 max_wait_time: float = 0.1):
        """Initialize the request batcher.
        
        Args:
            batch_processor: Async function to process batches
            max_batch_size: Maximum requests per batch
            max_wait_time: Maximum time to wait for batch to fill
        """
        self.batch_processor = batch_processor
        self.max_batch_size = max_batch_size
        self.max_wait_time = max_wait_time
        
        self._pending_requests: List[Dict[str, Any]] = []
        self._lock = asyncio.Lock()
        self._batch_task: Optional[asyncio.Task] = None
        
        logger.info(f"Request batcher initialized: batch_size={max_batch_size}, wait_time={max_wait_time}s")
    
    async def add_request(self, request: Any) -> Any:
        """Add a request to the batch.
        
        Args:
            request: Request to add to batch
            
        Returns:
            Result of processing the request
        """
        future = asyncio.Future()
        
        async with self._lock:
            self._pending_requests.append({
                'request': request,
                'future': future,
                'timestamp': time.time()
            })
            
            # Start batch processing if needed
            if (len(self._pending_requests) >= self.max_batch_size or 
                self._batch_task is None or self._batch_task.done()):
                self._batch_task = asyncio.create_task(self._process_batch())
        
        return await future
    
    async def _process_batch(self) -> None:
        """Process the current batch of requests."""
        # Wait for batch to fill or timeout
        if len(self._pending_requests) < self.max_batch_size:
            await asyncio.sleep(self.max_wait_time)
        
        async with self._lock:
            if not self._pending_requests:
                return
            
            # Extract requests and futures
            batch_items = self._pending_requests[:self.max_batch_size]
            self._pending_requests = self._pending_requests[self.max_batch_size:]
            
            requests = [item['request'] for item in batch_items]
            futures = [item['future'] for item in batch_items]
        
        try:
            # Process the batch
            results = await self.batch_processor(requests)
            
            # Set results for futures
            for future, result in zip(futures, results):
                if not future.done():
                    future.set_result(result)
                    
        except Exception as e:
            # Set exception for all futures
            for future in futures:
                if not future.done():
                    future.set_exception(e)
            
            logger.error(f"Batch processing failed: {e}")


class MemoryOptimizer:
    """Memory optimization utilities.
    
    This class provides utilities for monitoring and optimizing memory usage
    including garbage collection, weak references, and memory profiling.
    """
    
    def __init__(self, gc_threshold: float = 0.8, check_interval: float = 60.0):
        """Initialize the memory optimizer.
        
        Args:
            gc_threshold: Memory usage threshold to trigger GC (0.0-1.0)
            check_interval: Interval between memory checks in seconds
        """
        self.gc_threshold = gc_threshold
        self.check_interval = check_interval
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        
        # Memory statistics
        self.stats = {
            'gc_collections': 0,
            'peak_memory_mb': 0.0,
            'current_memory_mb': 0.0,
            'last_check': 0.0
        }
        
        logger.info(f"Memory optimizer initialized: threshold={gc_threshold}, interval={check_interval}s")
    
    def get_memory_usage(self) -> float:
        """Get current memory usage as a fraction of total memory.
        
        Returns:
            Memory usage fraction (0.0-1.0), or 0.0 if psutil not available
        """
        if not PSUTIL_AVAILABLE:
            return 0.0
        
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            system_memory = psutil.virtual_memory()
            
            usage_fraction = memory_info.rss / system_memory.total
            usage_mb = memory_info.rss / 1024 / 1024
            
            # Update statistics
            self.stats['current_memory_mb'] = usage_mb
            self.stats['peak_memory_mb'] = max(self.stats['peak_memory_mb'], usage_mb)
            self.stats['last_check'] = time.time()
            
            return usage_fraction
            
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return 0.0
    
    def force_garbage_collection(self) -> Dict[str, int]:
        """Force garbage collection and return statistics.
        
        Returns:
            Dictionary with GC statistics
        """
        # Get initial object counts
        initial_objects = len(gc.get_objects())
        
        # Force collection for all generations
        collected = []
        for generation in range(3):
            collected.append(gc.collect(generation))
        
        # Get final object counts
        final_objects = len(gc.get_objects())
        
        self.stats['gc_collections'] += 1
        
        gc_stats = {
            'generation_0': collected[0] if len(collected) > 0 else 0,
            'generation_1': collected[1] if len(collected) > 1 else 0,
            'generation_2': collected[2] if len(collected) > 2 else 0,
            'objects_before': initial_objects,
            'objects_after': final_objects,
            'objects_freed': initial_objects - final_objects
        }
        
        logger.info(f"Forced GC: freed {gc_stats['objects_freed']} objects")
        return gc_stats
    
    async def start_monitoring(self) -> None:
        """Start automatic memory monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_task = asyncio.create_task(self._monitor_memory())
        logger.info("Memory monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop automatic memory monitoring."""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Memory monitoring stopped")
    
    async def _monitor_memory(self) -> None:
        """Monitor memory usage and trigger GC when needed."""
        while self._monitoring:
            try:
                usage = self.get_memory_usage()
                
                if usage > self.gc_threshold:
                    logger.warning(f"Memory usage high ({usage:.1%}), triggering GC")
                    self.force_garbage_collection()
                
                await asyncio.sleep(self.check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                await asyncio.sleep(self.check_interval)


class PerformanceProfiler:
    """Performance profiling utilities.
    
    This class provides utilities for profiling function execution times,
    identifying bottlenecks, and collecting performance metrics.
    """
    
    def __init__(self):
        """Initialize the performance profiler."""
        self.timings: Dict[str, List[float]] = defaultdict(list)
        self.call_counts: Dict[str, int] = defaultdict(int)
        self._lock = threading.Lock()
        
        logger.info("Performance profiler initialized")
    
    def profile(self, name: str):
        """Decorator to profile function execution time.
        
        Args:
            name: Name for the profiling entry
            
        Returns:
            Decorator function
        """
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = await func(*args, **kwargs)
                        return result
                    finally:
                        duration = time.time() - start_time
                        self._record_timing(name, duration)
                return async_wrapper
            else:
                def sync_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = func(*args, **kwargs)
                        return result
                    finally:
                        duration = time.time() - start_time
                        self._record_timing(name, duration)
                return sync_wrapper
        return decorator
    
    def _record_timing(self, name: str, duration: float) -> None:
        """Record a timing measurement.
        
        Args:
            name: Name of the operation
            duration: Duration in seconds
        """
        with self._lock:
            self.timings[name].append(duration)
            self.call_counts[name] += 1
            
            # Keep only recent measurements (last 1000)
            if len(self.timings[name]) > 1000:
                self.timings[name] = self.timings[name][-1000:]
    
    def get_stats(self, name: Optional[str] = None) -> Dict[str, Any]:
        """Get performance statistics.
        
        Args:
            name: Specific operation name, or None for all operations
            
        Returns:
            Dictionary with performance statistics
        """
        with self._lock:
            if name:
                if name not in self.timings:
                    return {}
                
                timings = self.timings[name]
                return {
                    'name': name,
                    'call_count': self.call_counts[name],
                    'avg_time': sum(timings) / len(timings),
                    'min_time': min(timings),
                    'max_time': max(timings),
                    'total_time': sum(timings)
                }
            else:
                stats = {}
                for op_name in self.timings:
                    timings = self.timings[op_name]
                    stats[op_name] = {
                        'call_count': self.call_counts[op_name],
                        'avg_time': sum(timings) / len(timings),
                        'min_time': min(timings),
                        'max_time': max(timings),
                        'total_time': sum(timings)
                    }
                return stats


# Global instances
connection_pool: Optional[ConnectionPool] = None
memory_optimizer = MemoryOptimizer()
performance_profiler = PerformanceProfiler()
