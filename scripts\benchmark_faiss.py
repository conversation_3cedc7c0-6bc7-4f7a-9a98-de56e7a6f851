#!/usr/bin/env python
"""
FAISS CPU vs GPU Benchmark

This script benchmarks FAISS performance comparing CPU and GPU implementations
across different dataset sizes, dimensions, and operations.

Operations tested:
- Index building time
- Search time
- Memory usage

Usage:
    python scripts/benchmark_faiss.py [--dims DIMS] [--max-size MAX_SIZE]

Options:
    --dims DIMS         Comma-separated dimensions to test [default: 64,128,256,512]
    --max-size MAX_SIZE Maximum dataset size (in thousands) [default: 1000]
"""

import os
import gc
import time
import argparse
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from tabulate import tabulate
import psutil
from contextlib import contextmanager

# Import FAISS
try:
    import faiss
    FAISS_AVAILABLE = True
    GPU_AVAILABLE = hasattr(faiss, 'StandardGpuResources')
except ImportError:
    FAISS_AVAILABLE = False
    GPU_AVAILABLE = False
    print("⚠️ FAISS is not available. Please install FAISS to run this benchmark.")
    exit(1)

# Import PyTorch for CUDA status
try:
    import torch
    TORCH_AVAILABLE = True
    CUDA_AVAILABLE = torch.cuda.is_available()
    if CUDA_AVAILABLE:
        CUDA_DEVICE = torch.cuda.get_device_name(0)
        CUDA_VERSION = torch.version.cuda
    else:
        CUDA_DEVICE = "N/A"
        CUDA_VERSION = "N/A"
except ImportError:
    TORCH_AVAILABLE = False
    CUDA_AVAILABLE = False
    CUDA_DEVICE = "N/A"
    CUDA_VERSION = "N/A"

# Initialize GPU resources if available
if GPU_AVAILABLE:
    try:
        GPU_RESOURCES = faiss.StandardGpuResources()
        print(f"✅ FAISS-GPU is available (CUDA device: {CUDA_DEVICE})")
    except Exception as e:
        print(f"⚠️ FAISS-GPU initialization failed: {e}")
        GPU_AVAILABLE = False
else:
    print("⚠️ FAISS-GPU is not available, running CPU-only benchmarks.")

@contextmanager
def timing(operation):
    """Measure and print timing for an operation."""
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        print(f"{operation}: {end_time - start_time:.4f} seconds")

def get_memory_usage():
    """Get current memory usage of the process in MB."""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    return memory_info.rss / (1024 * 1024)  # Convert to MB

def create_dataset(num_vectors, dimension):
    """Create a dataset with the specified number of vectors and dimension."""
    # Use float32 for better compatibility with FAISS
    np.random.seed(42)  # For reproducibility
    return np.random.random((num_vectors, dimension)).astype('float32')

def benchmark_faiss(dimension, dataset_sizes, num_queries=100, k=10):
    """Benchmark FAISS on both CPU and GPU for various dataset sizes."""
    results = []
    
    # Create query vectors (same for all dataset sizes)
    query_vectors = create_dataset(num_queries, dimension)
    
    for size in dataset_sizes:
        size_in_vectors = size * 1000  # Convert from thousands to actual count
        print(f"\n{'='*50}")
        print(f"Benchmarking {size_in_vectors} vectors with dimension {dimension}")
        print(f"{'='*50}")
        
        # Create dataset
        print(f"Creating dataset with {size_in_vectors} vectors...")
        dataset = create_dataset(size_in_vectors, dimension)
        
        # Record initial memory
        initial_memory = get_memory_usage()
        
        # CPU benchmark
        print("\n--- CPU Benchmark ---")
        
        # Create CPU index
        cpu_index = faiss.IndexFlatL2(dimension)
        
        # Measure index build time
        gc.collect()  # Force garbage collection before timing
        with timing("CPU index build time"):
            cpu_index.add(dataset)
        
        # Measure memory usage
        cpu_memory = get_memory_usage() - initial_memory
        print(f"CPU memory usage: {cpu_memory:.2f} MB")
        
        # Measure search time
        gc.collect()
        with timing("CPU search time (100 queries)"):
            cpu_D, cpu_I = cpu_index.search(query_vectors, k)
        
        # Store CPU results
        cpu_result = {
            "dimension": dimension,
            "dataset_size": size_in_vectors,
            "platform": "CPU",
            "index_type": "FlatL2",
            "build_time": 0,  # Will be updated with actual values
            "search_time": 0,  # Will be updated with actual values
            "memory_usage": cpu_memory
        }
        
        # GPU benchmark (if available)
        gpu_result = None
        if GPU_AVAILABLE:
            print("\n--- GPU Benchmark ---")
            
            # Create GPU index
            gpu_index = faiss.index_cpu_to_gpu(GPU_RESOURCES, 0, faiss.IndexFlatL2(dimension))
            
            # Measure index build time
            gc.collect()
            with timing("GPU index build time"):
                gpu_index.add(dataset)
            
            # Memory usage (approximate as we can't directly measure GPU memory here)
            gpu_memory = get_memory_usage() - initial_memory
            print(f"GPU process memory usage: {gpu_memory:.2f} MB")
            
            # Measure search time
            gc.collect()
            with timing("GPU search time (100 queries)"):
                gpu_D, gpu_I = gpu_index.search(query_vectors, k)
            
            # Store GPU results
            gpu_result = {
                "dimension": dimension,
                "dataset_size": size_in_vectors,
                "platform": "GPU",
                "index_type": "FlatL2",
                "build_time": 0,  # Will be updated with actual values
                "search_time": 0,  # Will be updated with actual values
                "memory_usage": gpu_memory
            }
            
            # Verify results
            results_match = np.allclose(cpu_D, gpu_D, rtol=1e-4, atol=1e-4)
            print(f"CPU and GPU results match: {results_match}")
        
        # Free memory explicitly
        del dataset
        del cpu_index
        if GPU_AVAILABLE and 'gpu_index' in locals():
            del gpu_index
        gc.collect()
        
        # Now perform specific timing tests for accurate measurements
        print("\n--- Detailed Timing Tests ---")
        
        # CPU timing
        dataset = create_dataset(size_in_vectors, dimension)
        
        # Index build time
        gc.collect()
        cpu_index = faiss.IndexFlatL2(dimension)
        cpu_build_start = time.time()
        cpu_index.add(dataset)
        cpu_build_end = time.time()
        cpu_build_time = cpu_build_end - cpu_build_start
        print(f"CPU index build time: {cpu_build_time:.4f} seconds")
        
        # Search time
        gc.collect()
        cpu_search_start = time.time()
        cpu_D, cpu_I = cpu_index.search(query_vectors, k)
        cpu_search_end = time.time()
        cpu_search_time = cpu_search_end - cpu_search_start
        print(f"CPU search time: {cpu_search_time:.4f} seconds")
        
        cpu_result["build_time"] = cpu_build_time
        cpu_result["search_time"] = cpu_search_time
        results.append(cpu_result)
        
        # GPU timing
        if GPU_AVAILABLE:
            # Index build time
            gc.collect()
            gpu_index = faiss.index_cpu_to_gpu(GPU_RESOURCES, 0, faiss.IndexFlatL2(dimension))
            gpu_build_start = time.time()
            gpu_index.add(dataset)
            gpu_build_end = time.time()
            gpu_build_time = gpu_build_end - gpu_build_start
            print(f"GPU index build time: {gpu_build_time:.4f} seconds")
            
            # Search time
            gc.collect()
            gpu_search_start = time.time()
            gpu_D, gpu_I = gpu_index.search(query_vectors, k)
            gpu_search_end = time.time()
            gpu_search_time = gpu_search_end - gpu_search_start
            print(f"GPU search time: {gpu_search_time:.4f} seconds")
            
            if gpu_result:
                gpu_result["build_time"] = gpu_build_time
                gpu_result["search_time"] = gpu_search_time
                results.append(gpu_result)
            
            # Calculate speedup
            build_speedup = cpu_build_time / gpu_build_time if gpu_build_time > 0 else float('inf')
            search_speedup = cpu_search_time / gpu_search_time if gpu_search_time > 0 else float('inf')
            print(f"GPU build speedup: {build_speedup:.2f}x")
            print(f"GPU search speedup: {search_speedup:.2f}x")
        
        # Clean up
        del dataset, cpu_index
        if GPU_AVAILABLE and 'gpu_index' in locals():
            del gpu_index
        gc.collect()
    
    return results

def plot_results(results_df, output_dir="results"):
    """Generate plots from benchmark results."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Group by dimension and create plots for each
    for dimension in results_df["dimension"].unique():
        dim_results = results_df[results_df["dimension"] == dimension]
        
        plt.figure(figsize=(12, 8))
        
        # Plot search time
        plt.subplot(2, 2, 1)
        for platform in ["CPU", "GPU"]:
            if platform in dim_results["platform"].values:
                platform_data = dim_results[dim_results["platform"] == platform]
                plt.plot(platform_data["dataset_size"] / 1000, platform_data["search_time"], 
                         marker='o', label=f"{platform}")
        plt.title(f"Search Time (dimension={dimension})")
        plt.xlabel("Dataset Size (thousands)")
        plt.ylabel("Time (seconds)")
        plt.legend()
        plt.grid(True)
        
        # Plot build time
        plt.subplot(2, 2, 2)
        for platform in ["CPU", "GPU"]:
            if platform in dim_results["platform"].values:
                platform_data = dim_results[dim_results["platform"] == platform]
                plt.plot(platform_data["dataset_size"] / 1000, platform_data["build_time"], 
                         marker='o', label=f"{platform}")
        plt.title(f"Index Build Time (dimension={dimension})")
        plt.xlabel("Dataset Size (thousands)")
        plt.ylabel("Time (seconds)")
        plt.legend()
        plt.grid(True)
        
        # Plot memory usage
        plt.subplot(2, 2, 3)
        for platform in ["CPU", "GPU"]:
            if platform in dim_results["platform"].values:
                platform_data = dim_results[dim_results["platform"] == platform]
                plt.plot(platform_data["dataset_size"] / 1000, platform_data["memory_usage"], 
                         marker='o', label=f"{platform}")
        plt.title(f"Memory Usage (dimension={dimension})")
        plt.xlabel("Dataset Size (thousands)")
        plt.ylabel("Memory (MB)")
        plt.legend()
        plt.grid(True)
        
        # Plot speedup if both CPU and GPU results exist
        if "CPU" in dim_results["platform"].values and "GPU" in dim_results["platform"].values:
            plt.subplot(2, 2, 4)
            cpu_data = dim_results[dim_results["platform"] == "CPU"]
            gpu_data = dim_results[dim_results["platform"] == "GPU"]
            
            # Merge on dataset_size
            merged_data = pd.merge(cpu_data, gpu_data, on="dataset_size", suffixes=("_cpu", "_gpu"))
            
            # Calculate speedups
            search_speedup = merged_data["search_time_cpu"] / merged_data["search_time_gpu"]
            build_speedup = merged_data["build_time_cpu"] / merged_data["build_time_gpu"]
            
            plt.plot(merged_data["dataset_size"] / 1000, search_speedup, marker='o', label="Search Speedup")
            plt.plot(merged_data["dataset_size"] / 1000, build_speedup, marker='o', label="Build Speedup")
            plt.title(f"GPU Speedup (dimension={dimension})")
            plt.xlabel("Dataset Size (thousands)")
            plt.ylabel("Speedup (x times)")
            plt.legend()
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f"benchmark_dim{dimension}.png"))
        plt.close()
    
    # Create summary plot across all dimensions
    plt.figure(figsize=(14, 8))
    
    # Group by dimension and platform, then calculate mean speedup
    if "CPU" in results_df["platform"].values and "GPU" in results_df["platform"].values:
        speedups = []
        
        for dimension in results_df["dimension"].unique():
            dim_results = results_df[results_df["dimension"] == dimension]
            cpu_data = dim_results[dim_results["platform"] == "CPU"]
            gpu_data = dim_results[dim_results["platform"] == "GPU"]
            
            if not cpu_data.empty and not gpu_data.empty:
                # Merge on dataset_size
                merged_data = pd.merge(cpu_data, gpu_data, on="dataset_size", suffixes=("_cpu", "_gpu"))
                
                # Calculate average speedups
                avg_search_speedup = (merged_data["search_time_cpu"] / merged_data["search_time_gpu"]).mean()
                avg_build_speedup = (merged_data["build_time_cpu"] / merged_data["build_time_gpu"]).mean()
                
                speedups.append({
                    "dimension": dimension,
                    "search_speedup": avg_search_speedup,
                    "build_speedup": avg_build_speedup
                })
        
        if speedups:
            speedups_df = pd.DataFrame(speedups)
            
            plt.subplot(1, 2, 1)
            plt.bar(speedups_df["dimension"].astype(str), speedups_df["search_speedup"])
            plt.title("Average Search Speedup by Dimension")
            plt.xlabel("Dimension")
            plt.ylabel("Speedup (x times)")
            plt.grid(True, axis='y')
            
            plt.subplot(1, 2, 2)
            plt.bar(speedups_df["dimension"].astype(str), speedups_df["build_speedup"])
            plt.title("Average Index Build Speedup by Dimension")
            plt.xlabel("Dimension")
            plt.ylabel("Speedup (x times)")
            plt.grid(True, axis='y')
            
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, "benchmark_summary.png"))
            plt.close()
    
    return os.path.abspath(output_dir)

def print_system_info():
    """Print system information."""
    print("\n" + "="*50)
    print("SYSTEM INFORMATION")
    print("="*50)
    
    print(f"Python: {os.path.basename(os.path.dirname(os.__file__))}")
    print(f"FAISS version: {faiss.__version__}")
    print(f"FAISS-GPU available: {GPU_AVAILABLE}")
    
    if TORCH_AVAILABLE:
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {CUDA_AVAILABLE}")
        print(f"CUDA version: {CUDA_VERSION}")
        print(f"GPU device: {CUDA_DEVICE}")
    
    print(f"CPU count: {psutil.cpu_count(logical=False)} physical, {psutil.cpu_count()} logical")
    
    memory = psutil.virtual_memory()
    print(f"System memory: {memory.total / (1024**3):.2f} GB total, {memory.available / (1024**3):.2f} GB available")
    
    print("="*50 + "\n")

def main():
    """Main function to run the benchmark."""
    parser = argparse.ArgumentParser(description="Benchmark FAISS CPU vs GPU performance")
    parser.add_argument("--dims", type=str, default="64,128,256,512", 
                        help="Comma-separated dimensions to test")
    parser.add_argument("--max-size", type=int, default=1000, 
                        help="Maximum dataset size (in thousands)")
    args = parser.parse_args()
    
    # Parse dimensions
    dimensions = [int(d) for d in args.dims.split(",")]
    
    # Create dataset sizes (exponential growth)
    dataset_sizes = [10, 50, 100]
    if args.max_size > 100:
        dataset_sizes.extend([250, 500])
    if args.max_size >= 1000:
        dataset_sizes.append(1000)
    
    # Keep only sizes up to max-size
    dataset_sizes = [s for s in dataset_sizes if s <= args.max_size]
    
    print_system_info()
    
    all_results = []
    
    # Run benchmarks for each dimension
    for dimension in dimensions:
        results = benchmark_faiss(dimension, dataset_sizes)
        all_results.extend(results)
    
    # Convert to DataFrame for easier analysis
    results_df = pd.DataFrame(all_results)
    
    # Save results to CSV
    output_dir = "results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    results_df.to_csv(os.path.join(output_dir, "benchmark_results.csv"), index=False)
    
    # Create plots
    plot_dir = plot_results(results_df, output_dir)
    
    # Print summary table
    print("\n" + "="*100)
    print("BENCHMARK SUMMARY")
    print("="*100)
    
    # Calculate speedups for reporting
    summary_rows = []
    
    for dimension in dimensions:
        dim_results = results_df[results_df["dimension"] == dimension]
        
        for size in dim_results["dataset_size"].unique():
            size_results = dim_results[dim_results["dataset_size"] == size]
            
            if "CPU" in size_results["platform"].values and "GPU" in size_results["platform"].values:
                cpu_result = size_results[size_results["platform"] == "CPU"].iloc[0]
                gpu_result = size_results[size_results["platform"] == "GPU"].iloc[0]
                
                search_speedup = cpu_result["search_time"] / gpu_result["search_time"] if gpu_result["search_time"] > 0 else float('inf')
                build_speedup = cpu_result["build_time"] / gpu_result["build_time"] if gpu_result["build_time"] > 0 else float('inf')
                
                summary_rows.append({
                    "Dimension": dimension,
                    "Dataset Size": f"{size/1000:.1f}K",
                    "CPU Search (s)": f"{cpu_result['search_time']:.4f}",
                    "GPU Search (s)": f"{gpu_result['search_time']:.4f}",
                    "Search Speedup": f"{search_speedup:.2f}x",
                    "CPU Build (s)": f"{cpu_result['build_time']:.4f}",
                    "GPU Build (s)": f"{gpu_result['build_time']:.4f}",
                    "Build Speedup": f"{build_speedup:.2f}x"
                })
    
    if summary_rows:
        summary_df = pd.DataFrame(summary_rows)
        print(tabulate(summary_df, headers="keys", tablefmt="grid", showindex=False))
    
    print(f"\nDetailed results and plots saved to: {os.path.abspath(output_dir)}")
    print("\nTo view the plots, open the PNG files in the results directory.")

if __name__ == "__main__":
    main()
