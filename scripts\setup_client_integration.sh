#!/bin/bash
# Setup script for Neural Symbolic Language Model client integration
# This script helps users set up the Neural Symbolic Language Model with popular client applications

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
port_available() {
    ! nc -z localhost "$1" 2>/dev/null
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_status "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Main setup function
main() {
    print_header "🚀 Neural Symbolic Language Model - Client Integration Setup"
    echo "=================================================================="
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    if ! command_exists curl; then
        print_error "curl is not installed. Please install curl first."
        exit 1
    fi
    
    print_status "All prerequisites are available"
    
    # Check if ports are available
    print_status "Checking port availability..."
    
    ports_to_check=(8080 3000 3001 11434)
    for port in "${ports_to_check[@]}"; do
        if ! port_available "$port"; then
            print_warning "Port $port is already in use. Please stop the service using this port or modify the configuration."
        fi
    done
    
    # Ask user which setup they want
    echo ""
    print_header "Setup Options:"
    echo "1. Quick setup (Neural Symbolic LM + Open WebUI)"
    echo "2. Full setup (Neural Symbolic LM + Open WebUI + LibreChat)"
    echo "3. Development setup (Neural Symbolic LM only)"
    echo "4. Custom setup (choose components)"
    echo ""
    
    read -p "Choose setup option (1-4): " setup_option
    
    case $setup_option in
        1)
            setup_quick
            ;;
        2)
            setup_full
            ;;
        3)
            setup_development
            ;;
        4)
            setup_custom
            ;;
        *)
            print_error "Invalid option. Please choose 1-4."
            exit 1
            ;;
    esac
}

# Quick setup function
setup_quick() {
    print_header "🚀 Quick Setup: Neural Symbolic LM + Open WebUI"
    
    # Generate API keys
    generate_api_keys
    
    # Start services
    print_status "Starting Neural Symbolic Language Model and Open WebUI..."
    
    docker-compose -f docker-compose.client-integration.yml up -d ollama neural-symbolic-api open-webui
    
    # Wait for services
    wait_for_service "http://localhost:11434/api/tags" "Ollama"
    wait_for_service "http://localhost:8080/health" "Neural Symbolic API"
    wait_for_service "http://localhost:3000" "Open WebUI"
    
    # Pull the model
    print_status "Pulling gemma3n:e2b model..."
    docker exec ollama-neural-symbolic ollama pull gemma3n:e2b
    
    print_success_quick
}

# Full setup function
setup_full() {
    print_header "🚀 Full Setup: Neural Symbolic LM + Open WebUI + LibreChat"
    
    # Generate API keys
    generate_api_keys
    
    # Start all services
    print_status "Starting all services..."
    
    docker-compose -f docker-compose.client-integration.yml up -d
    
    # Wait for services
    wait_for_service "http://localhost:11434/api/tags" "Ollama"
    wait_for_service "http://localhost:8080/health" "Neural Symbolic API"
    wait_for_service "http://localhost:3000" "Open WebUI"
    wait_for_service "http://localhost:3001" "LibreChat"
    
    # Pull the model
    print_status "Pulling gemma3n:e2b model..."
    docker exec ollama-neural-symbolic ollama pull gemma3n:e2b
    
    print_success_full
}

# Development setup function
setup_development() {
    print_header "🚀 Development Setup: Neural Symbolic LM only"
    
    # Check if Python environment is set up
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt not found. Please run this script from the project root."
        exit 1
    fi
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Start Ollama
    print_status "Starting Ollama..."
    docker-compose -f docker-compose.client-integration.yml up -d ollama
    
    wait_for_service "http://localhost:11434/api/tags" "Ollama"
    
    # Pull the model
    print_status "Pulling gemma3n:e2b model..."
    docker exec ollama-neural-symbolic ollama pull gemma3n:e2b
    
    # Start the API
    print_status "Starting Neural Symbolic Language Model API..."
    print_status "Run: python src/main.py"
    
    print_success_development
}

# Custom setup function
setup_custom() {
    print_header "🚀 Custom Setup: Choose components"
    
    echo "Available components:"
    echo "1. Ollama (required)"
    echo "2. Neural Symbolic API (required)"
    echo "3. Open WebUI"
    echo "4. LibreChat"
    echo "5. Monitoring (Prometheus + Grafana)"
    echo ""
    
    read -p "Enter component numbers to install (e.g., 1,2,3): " components
    
    # Parse components
    IFS=',' read -ra COMP_ARRAY <<< "$components"
    
    services=""
    for comp in "${COMP_ARRAY[@]}"; do
        case $comp in
            1) services="$services ollama" ;;
            2) services="$services neural-symbolic-api" ;;
            3) services="$services open-webui" ;;
            4) services="$services librechat librechat-mongo librechat-meilisearch" ;;
            5) services="$services prometheus grafana" ;;
        esac
    done
    
    if [[ $services == *"neural-symbolic-api"* ]] && [[ $services != *"ollama"* ]]; then
        print_warning "Adding Ollama as it's required for Neural Symbolic API"
        services="ollama $services"
    fi
    
    # Generate API keys
    generate_api_keys
    
    # Start selected services
    print_status "Starting selected services: $services"
    docker-compose -f docker-compose.client-integration.yml up -d $services
    
    # Wait for core services
    if [[ $services == *"ollama"* ]]; then
        wait_for_service "http://localhost:11434/api/tags" "Ollama"
        
        # Pull the model
        print_status "Pulling gemma3n:e2b model..."
        docker exec ollama-neural-symbolic ollama pull gemma3n:e2b
    fi
    
    if [[ $services == *"neural-symbolic-api"* ]]; then
        wait_for_service "http://localhost:8080/health" "Neural Symbolic API"
    fi
    
    print_success_custom "$services"
}

# Function to generate API keys
generate_api_keys() {
    print_status "Generating secure API keys..."
    
    # Generate random API keys
    WEBUI_KEY=$(openssl rand -hex 32)
    LIBRECHAT_KEY=$(openssl rand -hex 32)
    GENERAL_KEY=$(openssl rand -hex 32)
    
    # Create .env file with API keys
    cat > .env.client-integration << EOF
# Generated API keys for client integration
WEBUI_API_KEY=$WEBUI_KEY
LIBRECHAT_API_KEY=$LIBRECHAT_KEY
GENERAL_API_KEY=$GENERAL_KEY

# Security configuration
SECURITY_API_KEYS_JSON={"webui": {"key": "$WEBUI_KEY", "permissions": ["read", "write"], "rate_limit": 1000}, "librechat": {"key": "$LIBRECHAT_KEY", "permissions": ["read", "write"], "rate_limit": 1000}, "general": {"key": "$GENERAL_KEY", "permissions": ["read", "write"], "rate_limit": 500}}
EOF
    
    print_status "API keys saved to .env.client-integration"
}

# Success message functions
print_success_quick() {
    echo ""
    print_header "🎉 Quick Setup Complete!"
    echo "=================================================================="
    print_status "Neural Symbolic Language Model is running with Open WebUI"
    echo ""
    echo "Access points:"
    echo "• Open WebUI: http://localhost:3000"
    echo "• Neural Symbolic API: http://localhost:8080"
    echo "• API Documentation: http://localhost:8080/docs"
    echo ""
    echo "API Key for manual integration: $(grep GENERAL_API_KEY .env.client-integration | cut -d'=' -f2)"
    echo ""
    print_status "Open http://localhost:3000 in your browser to start chatting!"
}

print_success_full() {
    echo ""
    print_header "🎉 Full Setup Complete!"
    echo "=================================================================="
    print_status "All services are running successfully"
    echo ""
    echo "Access points:"
    echo "• Open WebUI: http://localhost:3000"
    echo "• LibreChat: http://localhost:3001"
    echo "• Neural Symbolic API: http://localhost:8080"
    echo "• API Documentation: http://localhost:8080/docs"
    echo "• Prometheus: http://localhost:9090"
    echo "• Grafana: http://localhost:3002 (admin/grafana-neural-symbolic-2024)"
    echo ""
    echo "API Keys:"
    echo "• General: $(grep GENERAL_API_KEY .env.client-integration | cut -d'=' -f2)"
    echo "• Open WebUI: $(grep WEBUI_API_KEY .env.client-integration | cut -d'=' -f2)"
    echo "• LibreChat: $(grep LIBRECHAT_API_KEY .env.client-integration | cut -d'=' -f2)"
    echo ""
    print_status "Choose your preferred interface and start chatting!"
}

print_success_development() {
    echo ""
    print_header "🎉 Development Setup Complete!"
    echo "=================================================================="
    print_status "Ollama is running, ready for development"
    echo ""
    echo "Next steps:"
    echo "1. Start the API: python src/main.py"
    echo "2. Test the API: curl http://localhost:8080/health"
    echo "3. Run the demo: python examples/client_integration_demo.py"
    echo ""
    echo "Access points:"
    echo "• Ollama: http://localhost:11434"
    echo "• Neural Symbolic API: http://localhost:8080 (when started)"
    echo ""
    print_status "Happy coding!"
}

print_success_custom() {
    local services=$1
    echo ""
    print_header "🎉 Custom Setup Complete!"
    echo "=================================================================="
    print_status "Selected services are running: $services"
    echo ""
    echo "Check service status:"
    echo "docker-compose -f docker-compose.client-integration.yml ps"
    echo ""
    print_status "Refer to the documentation for usage instructions."
}

# Cleanup function
cleanup() {
    print_status "Stopping all services..."
    docker-compose -f docker-compose.client-integration.yml down
    print_status "Cleanup complete"
}

# Handle script arguments
case "${1:-}" in
    "cleanup")
        cleanup
        exit 0
        ;;
    "status")
        docker-compose -f docker-compose.client-integration.yml ps
        exit 0
        ;;
    "logs")
        docker-compose -f docker-compose.client-integration.yml logs -f "${2:-}"
        exit 0
        ;;
    "help")
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  (no args)  - Run interactive setup"
        echo "  cleanup    - Stop and remove all services"
        echo "  status     - Show service status"
        echo "  logs       - Show service logs"
        echo "  help       - Show this help"
        exit 0
        ;;
esac

# Run main setup if no arguments
main
