import sys
import os

# Add the project root directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.symbolic_reasoning import <PERSON>ymbolicReasoner

def test_symbolic_reasoning():
    # Initialize the reasoner
    # Initialize with local engine
    reasoner = SymbolicReasoner(engine="local", model="llama", use_gpu=True)
    
    # Print system information
    print("System Configuration:")
    print("-" * 20)
    system_info = reasoner.get_system_info()
    for key, value in system_info.items():
        print(f"{key}: {value}")
    print("-" * 20)
    
    # Test basic reasoning
    test_query = "Solve this logical problem: If A implies B and B implies C, what is the relationship between A and C?"
    print("\nTest Query:", test_query)
    
    response = reasoner.process_query(test_query)
    print("\nResponse:", response)
    
    # Test reasoning with context
    context = "In formal logic, this relationship is known as transitive implication."
    print("\nTesting with additional context...")
    response_with_context = reasoner.process_query(test_query, context)
    print("\nResponse with context:", response_with_context)

if __name__ == "__main__":
    test_symbolic_reasoning()
