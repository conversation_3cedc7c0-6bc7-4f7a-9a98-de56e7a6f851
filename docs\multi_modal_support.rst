Multi-Modal Support with gemma3n:e2b
====================================

The Neural Symbolic Language Model leverages the advanced capabilities of the gemma3n:e2b model
to provide comprehensive multi-modal input processing, including text, code, structured data,
and symbolic reasoning tasks.

Overview
--------

The gemma3n:e2b model provides enhanced capabilities beyond traditional text processing:

* **Code Analysis**: Deep understanding of programming languages and algorithms
* **Structured Data Processing**: JSON, XML, CSV, and other structured formats
* **Mathematical Reasoning**: Complex mathematical and logical problem solving
* **Symbolic Logic**: Formal reasoning with logical operators and rules
* **Multi-Language Support**: Natural language processing in multiple languages
* **Context-Aware Processing**: Understanding relationships between different input types

Supported Input Types
---------------------

Text Content
~~~~~~~~~~~~

Standard natural language processing with enhanced reasoning capabilities.

.. code-block:: json

   {
     "model": "gemma3n:e2b",
     "messages": [
       {
         "role": "user",
         "content": "Explain the philosophical implications of artificial consciousness."
       }
     ]
   }

Code Analysis
~~~~~~~~~~~~~

Deep analysis of source code with understanding of algorithms, patterns, and logic.

.. code-block:: json

   {
     "model": "gemma3n:e2b",
     "messages": [
       {
         "role": "user",
         "content": [
           {
             "type": "text",
             "text": "Analyze this algorithm and explain its time complexity:"
           },
           {
             "type": "code",
             "language": "python",
             "content": "def quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    pivot = arr[len(arr) // 2]\n    left = [x for x in arr if x < pivot]\n    middle = [x for x in arr if x == pivot]\n    right = [x for x in arr if x > pivot]\n    return quicksort(left) + middle + quicksort(right)"
           }
         ]
       }
     ]
   }

**Supported Programming Languages:**

* Python, JavaScript, TypeScript
* Java, C++, C#, Go, Rust
* SQL, HTML, CSS
* Shell scripts, PowerShell
* Configuration files (YAML, JSON, TOML)

Mathematical Expressions
~~~~~~~~~~~~~~~~~~~~~~~~~

Processing of mathematical notation, equations, and symbolic mathematics.

.. code-block:: json

   {
     "model": "gemma3n:e2b",
     "messages": [
       {
         "role": "user",
         "content": [
           {
             "type": "text",
             "text": "Solve this differential equation:"
           },
           {
             "type": "math",
             "notation": "latex",
             "content": "\\frac{dy}{dx} + 2y = e^{-x}"
           }
         ]
       }
     ]
   }

Structured Data
~~~~~~~~~~~~~~~

Analysis of structured data formats with schema understanding.

.. code-block:: json

   {
     "model": "gemma3n:e2b",
     "messages": [
       {
         "role": "user",
         "content": [
           {
             "type": "text",
             "text": "Analyze this API response and suggest improvements:"
           },
           {
             "type": "structured_data",
             "format": "json",
             "content": {
               "users": [
                 {"id": 1, "name": "John", "email": "<EMAIL>"},
                 {"id": 2, "name": "Jane", "email": "<EMAIL>"}
               ],
               "pagination": {"page": 1, "total": 100}
             }
           }
         ]
       }
     ]
   }

Logical Expressions
~~~~~~~~~~~~~~~~~~~

Formal logic processing with symbolic reasoning.

.. code-block:: json

   {
     "model": "gemma3n:e2b",
     "messages": [
       {
         "role": "user",
         "content": [
           {
             "type": "text",
             "text": "Evaluate this logical argument:"
           },
           {
             "type": "logic",
             "notation": "predicate",
             "content": "∀x (P(x) → Q(x)) ∧ P(a) ⊢ Q(a)"
           }
         ]
       }
     ]
   }

Advanced Capabilities
---------------------

Symbolic Reasoning
~~~~~~~~~~~~~~~~~~

The gemma3n:e2b model excels at symbolic reasoning tasks:

.. code-block:: python

   # Example: Logical deduction
   response = requests.post("http://localhost:8080/v1/chat/completions", 
       headers={"Authorization": "Bearer your-api-key"},
       json={
           "model": "gemma3n:e2b",
           "messages": [
               {
                   "role": "system",
                   "content": "You are an expert in formal logic and symbolic reasoning."
               },
               {
                   "role": "user",
                   "content": "Given: All programmers drink coffee. Alice is a programmer. What can we conclude?"
               }
           ],
           "temperature": 0.1  # Lower temperature for logical reasoning
       }
   )

**Reasoning Capabilities:**

* Deductive reasoning (general to specific)
* Inductive reasoning (specific to general)
* Abductive reasoning (best explanation)
* Causal reasoning (cause and effect)
* Analogical reasoning (pattern matching)

Code Generation and Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Advanced code understanding and generation:

.. code-block:: python

   # Example: Code optimization
   response = requests.post("http://localhost:8080/v1/chat/completions",
       headers={"Authorization": "Bearer your-api-key"},
       json={
           "model": "gemma3n:e2b",
           "messages": [
               {
                   "role": "user",
                   "content": [
                       {
                           "type": "text",
                           "text": "Optimize this code for better performance and explain the improvements:"
                       },
                       {
                           "type": "code",
                           "language": "python",
                           "content": "def find_duplicates(lst):\n    duplicates = []\n    for i in range(len(lst)):\n        for j in range(i+1, len(lst)):\n            if lst[i] == lst[j] and lst[i] not in duplicates:\n                duplicates.append(lst[i])\n    return duplicates"
                       }
                   ]
               }
           ]
       }
   )

Mathematical Problem Solving
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Complex mathematical reasoning and problem solving:

.. code-block:: python

   # Example: Mathematical proof
   response = requests.post("http://localhost:8080/v1/chat/completions",
       headers={"Authorization": "Bearer your-api-key"},
       json={
           "model": "gemma3n:e2b",
           "messages": [
               {
                   "role": "user",
                   "content": "Prove that the square root of 2 is irrational using proof by contradiction."
               }
           ],
           "temperature": 0.2
       }
   )

Integration Examples
--------------------

Python SDK with Multi-Modal
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import openai
   
   openai.api_base = "http://localhost:8080/v1"
   openai.api_key = "your-api-key"
   
   # Multi-modal code analysis
   response = openai.ChatCompletion.create(
       model="gemma3n:e2b",
       messages=[
           {
               "role": "user",
               "content": [
                   {
                       "type": "text",
                       "text": "Review this function and suggest improvements:"
                   },
                   {
                       "type": "code",
                       "language": "javascript",
                       "content": "function processData(data) {\n  var result = [];\n  for (var i = 0; i < data.length; i++) {\n    if (data[i].active) {\n      result.push(data[i]);\n    }\n  }\n  return result;\n}"
                   }
               ]
           }
       ],
       temperature=0.3
   )
   
   print(response.choices[0].message.content)

JavaScript Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const response = await fetch('http://localhost:8080/v1/chat/completions', {
       method: 'POST',
       headers: {
           'Authorization': 'Bearer your-api-key',
           'Content-Type': 'application/json'
       },
       body: JSON.stringify({
           model: 'gemma3n:e2b',
           messages: [
               {
                   role: 'user',
                   content: [
                       {
                           type: 'text',
                           text: 'Explain this data structure:'
                       },
                       {
                           type: 'structured_data',
                           format: 'json',
                           content: {
                               type: 'binary_tree',
                               nodes: [
                                   { id: 1, value: 10, left: 2, right: 3 },
                                   { id: 2, value: 5, left: null, right: null },
                                   { id: 3, value: 15, left: null, right: null }
                               ]
                           }
                       }
                   ]
               }
           ]
       })
   });

Performance Optimization
------------------------

Multi-Modal Processing Tips
~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Use Appropriate Temperature**: Lower values (0.1-0.3) for logical reasoning, higher (0.7-0.9) for creative tasks
2. **Structured Input**: Properly format multi-modal content for better understanding
3. **Context Management**: Provide relevant context for complex reasoning tasks
4. **Batch Processing**: Group related multi-modal inputs for efficiency

Model Configuration
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Optimize for multi-modal processing
   MODEL_REASONING_ENGINE=ollama
   MODEL_REASONING_MODEL=gemma3n:e2b
   MODEL_USE_GPU=true
   MODEL_GPU_MEMORY_FRACTION=0.8
   
   # Enhanced context handling
   MODEL_MAX_CONTEXT_LENGTH=4096
   MODEL_ENABLE_REASONING_CACHE=true

Best Practices
--------------

Input Formatting
~~~~~~~~~~~~~~~~~

* **Clear Structure**: Use proper type annotations for different content types
* **Consistent Formatting**: Maintain consistent formatting across similar inputs
* **Context Provision**: Provide sufficient context for complex reasoning tasks
* **Language Specification**: Specify programming languages for code analysis

Error Handling
~~~~~~~~~~~~~~

* **Validation**: Validate multi-modal input structure before sending
* **Fallback**: Implement fallback to text-only mode if multi-modal fails
* **Timeout Handling**: Set appropriate timeouts for complex reasoning tasks
* **Rate Limiting**: Respect rate limits for intensive processing

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**1. Multi-Modal Input Not Recognized**

.. code-block:: json

   {
     "error": {
       "type": "validation_error",
       "message": "Invalid content type in multi-modal input"
     }
   }

*Solution*: Ensure proper content type specification and valid structure.

**2. Code Analysis Timeout**

.. code-block:: json

   {
     "error": {
       "type": "timeout_error", 
       "message": "Code analysis exceeded maximum processing time"
     }
   }

*Solution*: Break down large code blocks or increase timeout settings.

**3. Mathematical Notation Parsing Error**

.. code-block:: json

   {
     "error": {
       "type": "parsing_error",
       "message": "Unable to parse mathematical notation"
     }
   }

*Solution*: Use standard LaTeX notation or provide alternative text representation.

For more information, see the :doc:`api_reference` and :doc:`troubleshooting` sections.
