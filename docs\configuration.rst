Configuration Guide
===================

This guide covers how to configure the Neural Symbolic Language Model for different
environments and use cases.

Overview
--------

The application uses a hierarchical configuration system based on:

* Environment variables
* Configuration files (YAML/JSON)
* Default values with validation
* Environment-specific overrides

Configuration is managed through Pydantic BaseSettings for type safety and validation.

Environment Variables
---------------------

Core Application Settings
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Basic application configuration
   APP_TITLE="Neural Symbolic Language Model API"
   APP_VERSION="0.1.0"
   APP_DEBUG=false
   APP_ENVIRONMENT=development  # development, staging, production
   APP_HOST=0.0.0.0
   APP_PORT=8000
   APP_WORKERS=1
   APP_RELOAD=true

Model Configuration
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Reasoning engine settings
   MODEL_REASONING_ENGINE=local  # local, openai, anthropic, ollama
   MODEL_REASONING_MODEL=llama   # llama, gpt-3.5-turbo, claude-3-sonnet
   MODEL_EMBEDDING_MODEL=BAAI/bge-small-en-v1.5
   MODEL_EMBEDDING_DIMENSION=768
   MODEL_USE_GPU=true
   MODEL_GPU_MEMORY_FRACTION=0.8
   MODEL_OLLAMA_HOST=http://localhost:11434
   MODEL_OLLAMA_TIMEOUT=300
   MODEL_VECTOR_DB_BACKEND=faiss  # faiss, chromadb, pinecone

Ollama Engine Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~

The Ollama engine provides local LLM hosting with automatic model management:

.. code-block:: bash

   # Ollama Configuration
   MODEL_REASONING_ENGINE=ollama
   MODEL_OLLAMA_HOST=http://localhost:11434
   MODEL_OLLAMA_TIMEOUT=300

   # Model Selection
   MODEL_REASONING_MODEL=llama3.2      # For general chat
   MODEL_EMBEDDING_MODEL=mxbai-embed-large  # For embeddings

**Ollama Model Options:**

* **Chat Models**: llama3.2, llama3.2:8b, codellama, mistral
* **Embedding Models**: mxbai-embed-large, nomic-embed-text
* **Specialized**: deepseek-coder, mathstral, llava (vision)

**Benefits of Ollama:**

* **Privacy**: All processing happens locally
* **Cost**: No API fees for inference
* **Performance**: Optimized for local hardware
* **Reliability**: No internet dependency

Security Configuration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # API authentication (JSON format for multiple keys)
   SECURITY_API_KEYS='{"user1": "key1", "user2": "key2"}'
   
   # Rate limiting
   SECURITY_RATE_LIMIT_REQUESTS=100
   SECURITY_RATE_LIMIT_WINDOW=60
   SECURITY_MAX_REQUEST_SIZE=10485760  # 10MB
   
   # CORS settings (JSON array format)
   SECURITY_CORS_ORIGINS='["http://localhost:3000", "https://yourdomain.com"]'
   
   # Security timeouts
   SECURITY_BLOCK_DURATION=3600
   SECURITY_MAX_FAILED_ATTEMPTS=5

Cache Configuration
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Cache settings
   CACHE_MAX_SIZE=1000
   CACHE_TTL_SECONDS=3600
   CACHE_CLEANUP_INTERVAL=300
   
   # Redis configuration (optional)
   CACHE_REDIS_URL=redis://localhost:6379/0

Logging Configuration
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Logging settings
   LOG_LEVEL=INFO
   LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
   LOG_FILE_ENABLED=true
   LOG_FILE_PATH=logs
   LOG_MAX_FILE_SIZE=10485760
   LOG_BACKUP_COUNT=5
   LOG_STRUCTURED_LOGGING=false

Configuration Files
-------------------

Environment-Specific YAML Files
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The application supports YAML configuration files for different environments:

**config/development.yaml**

.. code-block:: yaml

   app:
     title: "Neural Symbolic Language Model API (Development)"
     debug: true
     environment: "development"
     host: "127.0.0.1"
     port: 8000
     reload: true
   
   model:
     reasoning_engine: "local"
     use_gpu: false  # Disabled for development
     vector_db_backend: "faiss"
   
   security:
     api_keys:
       dev: "dev-key-12345"
       test: "test-key-67890"
     rate_limit_requests: 1000  # Higher limit for development
     cors_origins:
       - "http://localhost:3000"
       - "http://127.0.0.1:3000"
   
   cache:
     max_size: 100  # Smaller cache for development
     redis_url: null  # Use in-memory cache
   
   logging:
     level: "DEBUG"
     file_path: "logs/dev"

**config/production.yaml**

.. code-block:: yaml

   app:
     title: "Neural Symbolic Language Model API"
     debug: false  # Always disabled in production
     environment: "production"
     host: "0.0.0.0"
     port: 8000
     workers: 4  # Multiple workers for production
     reload: false
   
   model:
     reasoning_engine: "local"
     use_gpu: true  # Enable GPU for production
     vector_db_backend: "faiss"
   
   security:
     api_keys: {}  # Populated from environment variables
     rate_limit_requests: 100
     cors_origins:
       - "https://yourdomain.com"
       - "https://api.yourdomain.com"
     block_duration: 3600  # 1 hour block
   
   cache:
     max_size: 10000  # Larger cache for production
     redis_url: "redis://redis-server:6379/0"
   
   logging:
     level: "INFO"
     file_path: "/var/log/neural-symbolic"
     structured_logging: true

Loading Configuration Files
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from core.config import load_config_from_file, AppSettings
   
   # Load configuration from file
   config_data = load_config_from_file("config/production.yaml")
   
   # Create settings with file data
   settings = AppSettings(**config_data)

Environment Setup
-----------------

Development Environment
~~~~~~~~~~~~~~~~~~~~~~~

1. **Copy the environment template:**

   .. code-block:: bash

      cp .env.example .env

2. **Edit the .env file:**

   .. code-block:: bash

      # Development settings
      APP_DEBUG=true
      APP_ENVIRONMENT=development
      APP_RELOAD=true
      
      # Use CPU for development
      MODEL_USE_GPU=false
      
      # Development API keys
      SECURITY_API_KEYS='{"dev": "dev-key-12345"}'
      
      # Higher rate limits for development
      SECURITY_RATE_LIMIT_REQUESTS=1000

3. **Start the development server:**

   .. code-block:: bash

      python src/main.py

Production Environment
~~~~~~~~~~~~~~~~~~~~~~

1. **Set production environment variables:**

   .. code-block:: bash

      export APP_ENVIRONMENT=production
      export APP_DEBUG=false
      export APP_WORKERS=4
      
      # Production API keys (use secure key management)
      export SECURITY_API_KEYS='{"prod": "secure-production-key"}'
      
      # Production database
      export CACHE_REDIS_URL=redis://redis-server:6379/0
      
      # Production logging
      export LOG_LEVEL=INFO
      export LOG_STRUCTURED_LOGGING=true

2. **Use production configuration:**

   .. code-block:: bash

      # Load production config
      python -c "
      from core.config import load_config_from_file, AppSettings
      config = load_config_from_file('config/production.yaml')
      settings = AppSettings(**config)
      "

3. **Deploy with production settings:**

   .. code-block:: bash

      uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4

Configuration Validation
-------------------------

Built-in Validation
~~~~~~~~~~~~~~~~~~~

The configuration system includes comprehensive validation:

.. code-block:: python

   from core.config import validate_configuration, get_settings
   
   # Get current settings
   settings = get_settings()
   
   # Validate configuration
   warnings = validate_configuration(settings)
   
   if warnings:
       for warning in warnings:
           print(f"Warning: {warning}")
   else:
       print("Configuration is valid")

Common Validation Checks:

* Production environment safety (debug disabled, secure origins)
* GPU availability when requested
* Database connection validity
* API key security
* Rate limit reasonableness

Custom Validation
~~~~~~~~~~~~~~~~~

You can add custom validation rules:

.. code-block:: python

   def validate_custom_settings(settings):
       warnings = []
       
       # Custom business logic validation
       if settings.model.use_gpu and not torch.cuda.is_available():
           warnings.append("GPU requested but not available")
       
       if settings.environment == 'production' and settings.debug:
           warnings.append("Debug mode enabled in production")
       
       return warnings

Configuration Templates
-----------------------

Generate Environment Template
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from core.config import create_env_template
   
   # Create .env template with all available settings
   create_env_template(".env.template")

This generates a comprehensive template with:

* All available configuration options
* Default values and examples
* Documentation for each setting
* Environment-specific recommendations

Docker Configuration
~~~~~~~~~~~~~~~~~~~~

**Dockerfile environment:**

.. code-block:: dockerfile

   # Set production environment
   ENV APP_ENVIRONMENT=production
   ENV APP_DEBUG=false
   ENV APP_WORKERS=4
   
   # Model configuration
   ENV MODEL_USE_GPU=true
   ENV MODEL_VECTOR_DB_BACKEND=faiss
   
   # Security configuration
   ENV SECURITY_RATE_LIMIT_REQUESTS=100
   ENV SECURITY_CORS_ORIGINS='["https://yourdomain.com"]'

**Docker Compose:**

.. code-block:: yaml

   version: '3.8'
   services:
     neural-symbolic-api:
       build: .
       environment:
         - APP_ENVIRONMENT=production
         - CACHE_REDIS_URL=redis://redis:6379/0
         - LOG_LEVEL=INFO
       depends_on:
         - redis
     
     redis:
       image: redis:alpine
       ports:
         - "6379:6379"

Best Practices
--------------

Security
~~~~~~~~

* Never commit .env files to version control
* Use environment variables for sensitive data
* Rotate API keys regularly
* Use different keys for different environments
* Validate CORS origins in production

Performance
~~~~~~~~~~~

* Enable GPU acceleration in production
* Use Redis for caching in production
* Configure appropriate worker counts
* Set reasonable rate limits
* Monitor and adjust cache sizes

Monitoring
~~~~~~~~~~

* Enable structured logging in production
* Configure appropriate log levels
* Set up log rotation and retention
* Monitor configuration validation warnings
* Track configuration changes

Environment Management
~~~~~~~~~~~~~~~~~~~~~~

* Use separate configurations for each environment
* Validate configurations before deployment
* Document configuration changes
* Use configuration management tools
* Test configuration changes in staging first

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Configuration not loading:**

.. code-block:: bash

   # Check environment variables
   env | grep APP_
   
   # Validate configuration file syntax
   python -c "import yaml; yaml.safe_load(open('config/production.yaml'))"

**GPU not available:**

.. code-block:: bash

   # Check GPU availability
   python -c "import torch; print(torch.cuda.is_available())"
   
   # Disable GPU if not available
   export MODEL_USE_GPU=false

**Redis connection issues:**

.. code-block:: bash

   # Test Redis connection
   redis-cli -u redis://localhost:6379 ping
   
   # Use in-memory cache as fallback
   export CACHE_REDIS_URL=""

**API key authentication failing:**

.. code-block:: bash

   # Check API key format
   echo $SECURITY_API_KEYS | python -m json.tool
   
   # Test API key
   curl -H "Authorization: Bearer your-key" http://localhost:8000/system/info
