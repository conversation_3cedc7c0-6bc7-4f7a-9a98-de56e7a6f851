import numpy as np
import time
import faiss

def create_optimized_index(dimension, nlist=100, use_gpu=True):
    """
    Create an optimized FAISS index with IVFFlat for faster search.
    
    Args:
        dimension: Dimension of the vectors
        nlist: Number of clusters (higher = more accurate but slower)
        use_gpu: Whether to use GPU acceleration
    """
    print(f"\nCreating {'GPU' if use_gpu else 'CPU'} index with {nlist} clusters...")
    
    # Create a quantizer (defines how vectors are compared)
    quantizer = faiss.IndexFlatL2(dimension)
    
    # Create the IVF index
    index = faiss.IndexIVFFlat(quantizer, dimension, nlist)
    
    if use_gpu:
        # Move index to GPU
        res = faiss.StandardGpuResources()
        index = faiss.index_cpu_to_gpu(res, 0, index)
    
    return index

def benchmark_optimized_index(dataset_size=1_000_000, dimension=768, nlist=100, nprobe=10, use_gpu=True):
    """
    Benchmark the optimized FAISS index.
    """
    print(f"\n=== Benchmarking with {dataset_size:,} vectors (dim={dimension}) ===")
    print(f"Using {'GPU' if use_gpu else 'CPU'} with IVF (nlist={nlist}, nprobe={nprobe})")
    
    # Generate random data
    print("Generating random data...")
    np.random.seed(123)
    data = np.random.random((dataset_size, dimension)).astype('float32')
    
    # Create and train the index
    print("Training index...")
    index = create_optimized_index(dimension, nlist, use_gpu)
    
    # Use 10% of data for training (or at most 100K vectors)
    n_train = min(100000, dataset_size // 10)
    train_vectors = data[:n_train]
    index.train(train_vectors)
    
    # Add data to the index
    print("Adding data to index...")
    index.add(data)
    
    # Set nprobe (number of clusters to visit during search)
    if hasattr(index, 'nprobe'):
        index.nprobe = nprobe
    
    # Generate queries
    n_queries = 100
    queries = np.random.random((n_queries, dimension)).astype('float32')
    k = 10  # Number of nearest neighbors to retrieve
    
    # Warm-up
    _ = index.search(queries[:1], k)
    
    # Benchmark search
    print(f"Performing {n_queries} queries...")
    start_time = time.time()
    distances, indices = index.search(queries, k)
    search_time = time.time() - start_time
    
    print(f"Search time for {n_queries} queries: {search_time:.4f} seconds")
    print(f"Time per query: {search_time * 1000 / n_queries:.2f} ms")
    print(f"Throughput: {n_queries / search_time:.2f} queries/second")
    
    # Print some stats
    print("\nResults for first query:")
    print(f"Nearest neighbor indices: {indices[0]}")
    print(f"Distances: {distances[0]}")
    
    return index, data, queries, indices, distances

def compare_with_exact_search(index, data, queries, k=10):
    """
    Compare IVF results with exact search to measure accuracy.
    """
    print("\n=== Comparing with exact search ===")
    
    # Create exact search index
    exact_index = faiss.IndexFlatL2(data.shape[1])
    exact_index.add(data)
    
    # Move to GPU if the original index is on GPU
    if hasattr(index, 'getDevice'):  # This is a GPU index
        res = faiss.StandardGpuResources()
        exact_index = faiss.index_cpu_to_gpu(res, 0, exact_index)
    
    # Perform exact search
    print("Performing exact search...")
    exact_distances, exact_indices = exact_index.search(queries, k)
    
    # Calculate recall@k
    def recall_at_k(approx_indices, exact_indices, k):
        recall_sum = 0
        for i in range(len(approx_indices)):
            approx_set = set(approx_indices[i])
            exact_set = set(exact_indices[i])
            recall_sum += len(approx_set.intersection(exact_set)) / k
        return recall_sum / len(approx_indices)
    
    # Get approximate results
    approx_distances, approx_indices = index.search(queries, k)
    
    # Calculate recall
    recall = recall_at_k(approx_indices, exact_indices, k)
    print(f"Recall@{k}: {recall:.4f} (higher is better, 1.0 = perfect)")
    
    # Print example comparison
    print("\nExample comparison (first query):")
    print(f"Approximate neighbors: {approx_indices[0]}")
    print(f"Exact neighbors:       {exact_indices[0]}")
    print(f"Matching neighbors:    {len(set(approx_indices[0]).intersection(exact_indices[0]))}/{k}")

if __name__ == "__main__":
    # Benchmark parameters
    dataset_size = 1_000_000  # Number of vectors
    dimension = 768           # Dimension of each vector
    nlist = 100               # Number of clusters (higher = more accurate but slower)
    nprobe = 10               # Number of clusters to visit during search (higher = more accurate but slower)
    
    print("FAISS Optimized Index Demo")
    print("===========================")
    print(f"FAISS version: {faiss.__version__}")
    print(f"Number of GPUs available: {faiss.get_num_gpus()}")
    
    # Run the benchmark
    index, data, queries, indices, distances = benchmark_optimized_index(
        dataset_size=dataset_size,
        dimension=dimension,
        nlist=nlist,
        nprobe=nprobe,
        use_gpu=True
    )
    
    # Compare with exact search (for accuracy)
    if dataset_size <= 100_000:  # Only run exact search for smaller datasets
        compare_with_exact_search(index, data, queries)
    else:
        print("\nSkipping exact search comparison for large dataset (would be too slow).")
        print("To measure accuracy, reduce dataset_size to 100,000 or less.")
