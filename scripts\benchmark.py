"""
Benchmarking script for the Neural Symbolic Language Model API.
This script measures and visualizes the performance of the API endpoints.
"""

import time
import requests
import matplotlib.pyplot as plt
import numpy as np
import json
from datetime import datetime

def run_benchmark(queries, iterations=3):
    """Run benchmark tests for the API.
    
    Args:
        queries (list): List of queries to test
        iterations (int): Number of iterations per query
        
    Returns:
        dict: Benchmark results
    """
    results = {
        "first_query_times": [],
        "cached_query_times": [],
        "queries": queries,
        "timestamp": datetime.now().isoformat(),
        "raw_data": []
    }
    
    base_url = "http://localhost:8080"  # Updated to match our current port
    
    for query in queries:
        print(f"\nBenchmarking query: {query}")
        first_times = []
        cached_times = []
        query_data = {"query": query, "iterations": []}
        
        for i in range(iterations):
            print(f"  Iteration {i+1}/{iterations}")
            iteration_data = {"iteration": i+1}
            
            try:
                # First query (uncached)
                start = time.time()
                response = requests.post(f"{base_url}/chat", json={"text": query})
                response.raise_for_status()
                first_time = time.time() - start
                first_times.append(first_time)
                print(f"    First query time: {first_time:.2f}s")
                iteration_data["first_query"] = {
                    "time": first_time,
                    "cached": response.json().get("cached", False)
                }
                
                # Wait briefly
                time.sleep(0.5)
                
                # Second query (should be cached)
                start = time.time()
                response = requests.post(f"{base_url}/chat", json={"text": query})
                response.raise_for_status()
                cached_time = time.time() - start
                cached_times.append(cached_time)
                print(f"    Cached query time: {cached_time:.2f}s")
                iteration_data["cached_query"] = {
                    "time": cached_time,
                    "cached": response.json().get("cached", False)
                }
                
            except requests.exceptions.RequestException as e:
                print(f"    Error during benchmark: {str(e)}")
                iteration_data["error"] = str(e)
            
            query_data["iterations"].append(iteration_data)
            # Wait before next iteration
            time.sleep(1)
        
        # Store average times
        results["first_query_times"].append(np.mean(first_times))
        results["cached_query_times"].append(np.mean(cached_times))
        results["raw_data"].append(query_data)
    
    return results

def visualize_results(results, queries):
    """Visualize benchmark results.
    
    Args:
        results (dict): Benchmark results
        queries (list): List of queries tested
    """
    plt.figure(figsize=(12, 6))
    
    x = np.arange(len(queries))
    width = 0.35
    
    plt.bar(x - width/2, results["first_query_times"], width, label="First Query")
    plt.bar(x + width/2, results["cached_query_times"], width, label="Cached Query")
    
    plt.xlabel("Query")
    plt.ylabel("Response Time (seconds)")
    plt.title("API Response Time Benchmark")
    plt.xticks(x, [f"Q{i+1}" for i in range(len(queries))], rotation=0)
    plt.legend()
    
    # Add value labels on top of bars
    for i in range(len(queries)):
        plt.text(i - width/2, results["first_query_times"][i], 
                f'{results["first_query_times"][i]:.2f}s', 
                ha='center', va='bottom')
        plt.text(i + width/2, results["cached_query_times"][i], 
                f'{results["cached_query_times"][i]:.2f}s', 
                ha='center', va='bottom')
    
    plt.tight_layout()
    
    # Save figure
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plt.savefig(f"benchmark_results_{timestamp}.png")
    print(f"\nBenchmark visualization saved as: benchmark_results_{timestamp}.png")
    
    # Print summary statistics
    print("\nBenchmark Summary:")
    print("-" * 80)
    print("Query Details:")
    for i, query in enumerate(queries):
        print(f"Q{i+1}: {query}")
    print("-" * 80)
    print(f"Average first query time: {np.mean(results['first_query_times']):.2f}s")
    print(f"Average cached query time: {np.mean(results['cached_query_times']):.2f}s")
    speedup = np.mean(results['first_query_times'])/np.mean(results['cached_query_times'])
    print(f"Speed improvement from caching: {speedup:.2f}x")
    print("-" * 80)

if __name__ == "__main__":
    print("Starting API Benchmark...")
    print("=" * 80)
    
    try:
        # First check if the API is available
        base_url = "http://localhost:8080"
        requests.get(f"{base_url}/")
        
        # Sample queries for benchmarking
        test_queries = [
            "What is Neural-Symbolic AI?",
            "Explain the difference between FAISS and ChromaDB",
            "How does GPU acceleration improve vector search?",
            "What are the benefits of symbolic reasoning?",
            "How to implement RAG in a production system?"
        ]
        
        # Run benchmark
        results = run_benchmark(test_queries)
        
        # Visualize results
        visualize_results(results, test_queries)
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"benchmark_results_{timestamp}.json"
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2)
        print(f"\nDetailed benchmark results saved as: {results_file}")
        
    except requests.exceptions.ConnectionError:
        print("\nError: Could not connect to the API server.")
        print("Please make sure the server is running on", base_url)
    except Exception as e:
        print("\nError during benchmark:", str(e))
