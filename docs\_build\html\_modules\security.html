

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>security &mdash; Neural Symbolic Language Model 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=39bd3b11" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=01f34227"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Neural Symbolic Language Model
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Guide</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../modules.html">API Reference</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/main.html">Main Application Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/symbolic_reasoning.html">Symbolic Reasoning Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/retrieval.html">Vector Retrieval Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/security.html">Security Module</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api/models.html">Data Models Module</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Neural Symbolic Language Model</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Module code</a></li>
      <li class="breadcrumb-item active">security</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for security</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Security module for the Neural Symbolic Language Model.</span>

<span class="sd">This module provides authentication, authorization, rate limiting, and other</span>
<span class="sd">security features for the FastAPI application.</span>

<span class="sd">Author: AI Assistant</span>
<span class="sd">Date: 2025-06-29</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">hmac</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Any</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">HTTPException</span><span class="p">,</span> <span class="n">Request</span><span class="p">,</span> <span class="n">status</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi.security</span><span class="w"> </span><span class="kn">import</span> <span class="n">HTTPBearer</span><span class="p">,</span> <span class="n">HTTPAuthorizationCredentials</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">secrets</span>

<span class="c1"># Configure logger</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="c1"># Security configuration</span>
<span class="n">API_KEY_HEADER</span> <span class="o">=</span> <span class="s2">&quot;X-API-Key&quot;</span>
<span class="n">DEFAULT_RATE_LIMIT</span> <span class="o">=</span> <span class="s2">&quot;100/minute&quot;</span>
<span class="n">MAX_REQUEST_SIZE</span> <span class="o">=</span> <span class="mi">10</span> <span class="o">*</span> <span class="mi">1024</span> <span class="o">*</span> <span class="mi">1024</span>  <span class="c1"># 10MB</span>

<div class="viewcode-block" id="SecurityManager">
<a class="viewcode-back" href="../modules.html#security.SecurityManager">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">SecurityManager</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Manages security features for the application.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="SecurityManager.__init__">
<a class="viewcode-back" href="../modules.html#security.SecurityManager.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">api_keys</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the security manager.</span>

<span class="sd">        Args:</span>
<span class="sd">            api_keys: Dictionary mapping API key names to their values</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">api_keys</span> <span class="o">=</span> <span class="n">api_keys</span> <span class="ow">or</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">failed_attempts</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">list</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">blocked_ips</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>

        <span class="c1"># Generate a default API key if none provided</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">api_keys</span><span class="p">:</span>
            <span class="n">default_key</span> <span class="o">=</span> <span class="n">secrets</span><span class="o">.</span><span class="n">token_urlsafe</span><span class="p">(</span><span class="mi">32</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">api_keys</span><span class="p">[</span><span class="s2">&quot;default&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">default_key</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Generated default API key: </span><span class="si">{</span><span class="n">default_key</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="SecurityManager.validate_api_key">
<a class="viewcode-back" href="../modules.html#security.SecurityManager.validate_api_key">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">validate_api_key</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">api_key</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Validate an API key.</span>

<span class="sd">        Args:</span>
<span class="sd">            api_key: The API key to validate</span>

<span class="sd">        Returns:</span>
<span class="sd">            True if valid, False otherwise</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">api_key</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>

        <span class="c1"># Use constant-time comparison to prevent timing attacks</span>
        <span class="k">for</span> <span class="n">stored_key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">api_keys</span><span class="o">.</span><span class="n">values</span><span class="p">():</span>
            <span class="k">if</span> <span class="n">hmac</span><span class="o">.</span><span class="n">compare_digest</span><span class="p">(</span><span class="n">api_key</span><span class="p">,</span> <span class="n">stored_key</span><span class="p">):</span>
                <span class="k">return</span> <span class="kc">True</span>
        <span class="k">return</span> <span class="kc">False</span></div>


<div class="viewcode-block" id="SecurityManager.is_ip_blocked">
<a class="viewcode-back" href="../modules.html#security.SecurityManager.is_ip_blocked">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">is_ip_blocked</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ip_address</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check if an IP address is blocked.</span>

<span class="sd">        Args:</span>
<span class="sd">            ip_address: The IP address to check</span>

<span class="sd">        Returns:</span>
<span class="sd">            True if blocked, False otherwise</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">ip_address</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">blocked_ips</span><span class="p">:</span>
            <span class="n">block_time</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">blocked_ips</span><span class="p">[</span><span class="n">ip_address</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">block_time</span> <span class="o">&lt;</span> <span class="mi">3600</span><span class="p">:</span>  <span class="c1"># 1 hour block</span>
                <span class="k">return</span> <span class="kc">True</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># Unblock after timeout</span>
                <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">blocked_ips</span><span class="p">[</span><span class="n">ip_address</span><span class="p">]</span>
        <span class="k">return</span> <span class="kc">False</span></div>


<div class="viewcode-block" id="SecurityManager.record_failed_attempt">
<a class="viewcode-back" href="../modules.html#security.SecurityManager.record_failed_attempt">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">record_failed_attempt</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">ip_address</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Record a failed authentication attempt.</span>

<span class="sd">        Args:</span>
<span class="sd">            ip_address: The IP address that failed authentication</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">ip_address</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">failed_attempts</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">failed_attempts</span><span class="p">[</span><span class="n">ip_address</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="c1"># Clean old attempts (older than 15 minutes)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">failed_attempts</span><span class="p">[</span><span class="n">ip_address</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">attempt_time</span> <span class="k">for</span> <span class="n">attempt_time</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">failed_attempts</span><span class="p">[</span><span class="n">ip_address</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">current_time</span> <span class="o">-</span> <span class="n">attempt_time</span> <span class="o">&lt;</span> <span class="mi">900</span>
        <span class="p">]</span>

        <span class="c1"># Add current attempt</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">failed_attempts</span><span class="p">[</span><span class="n">ip_address</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">current_time</span><span class="p">)</span>

        <span class="c1"># Block IP if too many failed attempts</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">failed_attempts</span><span class="p">[</span><span class="n">ip_address</span><span class="p">])</span> <span class="o">&gt;=</span> <span class="mi">5</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">blocked_ips</span><span class="p">[</span><span class="n">ip_address</span><span class="p">]</span> <span class="o">=</span> <span class="n">current_time</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Blocked IP </span><span class="si">{</span><span class="n">ip_address</span><span class="si">}</span><span class="s2"> due to repeated failed attempts&quot;</span><span class="p">)</span></div>


<div class="viewcode-block" id="SecurityManager.sanitize_input">
<a class="viewcode-back" href="../modules.html#security.SecurityManager.sanitize_input">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">sanitize_input</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">text</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">max_length</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">10000</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Sanitize user input text.</span>

<span class="sd">        Args:</span>
<span class="sd">            text: The input text to sanitize</span>
<span class="sd">            max_length: Maximum allowed length</span>

<span class="sd">        Returns:</span>
<span class="sd">            Sanitized text</span>

<span class="sd">        Raises:</span>
<span class="sd">            ValueError: If input is invalid</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">text</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Input must be a string&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">text</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">max_length</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Input too long (max </span><span class="si">{</span><span class="n">max_length</span><span class="si">}</span><span class="s2"> characters)&quot;</span><span class="p">)</span>

        <span class="c1"># Remove null bytes and control characters</span>
        <span class="n">sanitized</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">char</span> <span class="k">for</span> <span class="n">char</span> <span class="ow">in</span> <span class="n">text</span> <span class="k">if</span> <span class="nb">ord</span><span class="p">(</span><span class="n">char</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">32</span> <span class="ow">or</span> <span class="n">char</span> <span class="ow">in</span> <span class="s1">&#39;</span><span class="se">\n\r\t</span><span class="s1">&#39;</span><span class="p">)</span>

        <span class="c1"># Strip whitespace</span>
        <span class="n">sanitized</span> <span class="o">=</span> <span class="n">sanitized</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">sanitized</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Input cannot be empty after sanitization&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">sanitized</span></div>
</div>


<span class="c1"># Global security manager instance</span>
<span class="n">security_manager</span> <span class="o">=</span> <span class="n">SecurityManager</span><span class="p">()</span>

<span class="c1"># HTTP Bearer token scheme</span>
<span class="n">security_scheme</span> <span class="o">=</span> <span class="n">HTTPBearer</span><span class="p">()</span>

<div class="viewcode-block" id="verify_api_key">
<a class="viewcode-back" href="../modules.html#security.verify_api_key">[docs]</a>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">verify_api_key</span><span class="p">(</span><span class="n">credentials</span><span class="p">:</span> <span class="n">HTTPAuthorizationCredentials</span> <span class="o">=</span> <span class="n">security_scheme</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Verify API key from Authorization header.</span>

<span class="sd">    Args:</span>
<span class="sd">        credentials: HTTP authorization credentials</span>

<span class="sd">    Returns:</span>
<span class="sd">        The validated API key</span>

<span class="sd">    Raises:</span>
<span class="sd">        HTTPException: If authentication fails</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">credentials</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span>
            <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_401_UNAUTHORIZED</span><span class="p">,</span>
            <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Missing authentication credentials&quot;</span><span class="p">,</span>
            <span class="n">headers</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;WWW-Authenticate&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer&quot;</span><span class="p">},</span>
        <span class="p">)</span>

    <span class="n">api_key</span> <span class="o">=</span> <span class="n">credentials</span><span class="o">.</span><span class="n">credentials</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">security_manager</span><span class="o">.</span><span class="n">validate_api_key</span><span class="p">(</span><span class="n">api_key</span><span class="p">):</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span>
            <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_401_UNAUTHORIZED</span><span class="p">,</span>
            <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Invalid API key&quot;</span><span class="p">,</span>
            <span class="n">headers</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;WWW-Authenticate&quot;</span><span class="p">:</span> <span class="s2">&quot;Bearer&quot;</span><span class="p">},</span>
        <span class="p">)</span>

    <span class="k">return</span> <span class="n">api_key</span></div>


<div class="viewcode-block" id="check_request_size">
<a class="viewcode-back" href="../modules.html#security.check_request_size">[docs]</a>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">check_request_size</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Check if request size is within limits.</span>

<span class="sd">    Args:</span>
<span class="sd">        request: The FastAPI request object</span>

<span class="sd">    Raises:</span>
<span class="sd">        HTTPException: If request is too large</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">content_length</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;content-length&quot;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">content_length</span> <span class="ow">and</span> <span class="nb">int</span><span class="p">(</span><span class="n">content_length</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">MAX_REQUEST_SIZE</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span>
            <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_413_REQUEST_ENTITY_TOO_LARGE</span><span class="p">,</span>
            <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Request too large (max </span><span class="si">{</span><span class="n">MAX_REQUEST_SIZE</span><span class="si">}</span><span class="s2"> bytes)&quot;</span>
        <span class="p">)</span></div>


<div class="viewcode-block" id="get_security_headers">
<a class="viewcode-back" href="../modules.html#security.get_security_headers">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_security_headers</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get security headers to add to responses.</span>

<span class="sd">    Returns:</span>
<span class="sd">        Dictionary of security headers</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="p">{</span>
        <span class="s2">&quot;X-Content-Type-Options&quot;</span><span class="p">:</span> <span class="s2">&quot;nosniff&quot;</span><span class="p">,</span>
        <span class="s2">&quot;X-Frame-Options&quot;</span><span class="p">:</span> <span class="s2">&quot;DENY&quot;</span><span class="p">,</span>
        <span class="s2">&quot;X-XSS-Protection&quot;</span><span class="p">:</span> <span class="s2">&quot;1; mode=block&quot;</span><span class="p">,</span>
        <span class="s2">&quot;Strict-Transport-Security&quot;</span><span class="p">:</span> <span class="s2">&quot;max-age=31536000; includeSubDomains&quot;</span><span class="p">,</span>
        <span class="s2">&quot;Content-Security-Policy&quot;</span><span class="p">:</span> <span class="s2">&quot;default-src &#39;self&#39;&quot;</span><span class="p">,</span>
        <span class="s2">&quot;Referrer-Policy&quot;</span><span class="p">:</span> <span class="s2">&quot;strict-origin-when-cross-origin&quot;</span>
    <span class="p">}</span></div>


<div class="viewcode-block" id="RateLimiter">
<a class="viewcode-back" href="../modules.html#security.RateLimiter">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">RateLimiter</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Simple in-memory rate limiter.&quot;&quot;&quot;</span>

<div class="viewcode-block" id="RateLimiter.__init__">
<a class="viewcode-back" href="../modules.html#security.RateLimiter.__init__">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize the rate limiter.&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">requests</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">list</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span></div>


<div class="viewcode-block" id="RateLimiter.is_allowed">
<a class="viewcode-back" href="../modules.html#security.RateLimiter.is_allowed">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">is_allowed</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">identifier</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">limit</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">100</span><span class="p">,</span> <span class="n">window</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">60</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check if a request is allowed based on rate limits.</span>

<span class="sd">        Args:</span>
<span class="sd">            identifier: Unique identifier (e.g., IP address)</span>
<span class="sd">            limit: Maximum number of requests allowed</span>
<span class="sd">            window: Time window in seconds</span>

<span class="sd">        Returns:</span>
<span class="sd">            True if request is allowed, False otherwise</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">identifier</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">requests</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">requests</span><span class="p">[</span><span class="n">identifier</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="c1"># Clean old requests outside the window</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">requests</span><span class="p">[</span><span class="n">identifier</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">req_time</span> <span class="k">for</span> <span class="n">req_time</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">requests</span><span class="p">[</span><span class="n">identifier</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">current_time</span> <span class="o">-</span> <span class="n">req_time</span> <span class="o">&lt;</span> <span class="n">window</span>
        <span class="p">]</span>

        <span class="c1"># Check if limit exceeded</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">requests</span><span class="p">[</span><span class="n">identifier</span><span class="p">])</span> <span class="o">&gt;=</span> <span class="n">limit</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>

        <span class="c1"># Add current request</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">requests</span><span class="p">[</span><span class="n">identifier</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">current_time</span><span class="p">)</span>
        <span class="k">return</span> <span class="kc">True</span></div>
</div>


<span class="c1"># Global rate limiter instance</span>
<span class="n">rate_limiter</span> <span class="o">=</span> <span class="n">RateLimiter</span><span class="p">()</span>

<div class="viewcode-block" id="get_client_ip">
<a class="viewcode-back" href="../modules.html#security.get_client_ip">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_client_ip</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get client IP address from request.</span>

<span class="sd">    Args:</span>
<span class="sd">        request: The FastAPI request object</span>

<span class="sd">    Returns:</span>
<span class="sd">        Client IP address</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Check for forwarded headers first</span>
    <span class="n">forwarded_for</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;X-Forwarded-For&quot;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">forwarded_for</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">forwarded_for</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s2">&quot;,&quot;</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>

    <span class="n">real_ip</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">headers</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;X-Real-IP&quot;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">real_ip</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">real_ip</span>

    <span class="c1"># Fall back to direct connection</span>
    <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">client</span><span class="p">,</span> <span class="s1">&#39;host&#39;</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">request</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">host</span>

    <span class="k">return</span> <span class="s2">&quot;unknown&quot;</span></div>



<div class="viewcode-block" id="get_cors_config">
<a class="viewcode-back" href="../modules.html#security.get_cors_config">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_cors_config</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get CORS (Cross-Origin Resource Sharing) configuration.</span>

<span class="sd">    This function provides CORS configuration for the FastAPI application,</span>
<span class="sd">    allowing controlled cross-origin requests while maintaining security.</span>

<span class="sd">    Returns:</span>
<span class="sd">        Dictionary containing CORS configuration parameters</span>

<span class="sd">    Example:</span>
<span class="sd">        &gt;&gt;&gt; cors_config = get_cors_config()</span>
<span class="sd">        &gt;&gt;&gt; print(cors_config[&#39;allow_origins&#39;])</span>
<span class="sd">        [&#39;http://localhost:3000&#39;, &#39;https://yourdomain.com&#39;]</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="kn">from</span><span class="w"> </span><span class="nn">core.config</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_settings</span>
        <span class="n">settings</span> <span class="o">=</span> <span class="n">get_settings</span><span class="p">()</span>
        <span class="n">cors_origins</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">settings</span><span class="o">.</span><span class="n">security</span><span class="p">,</span> <span class="s1">&#39;cors_origins&#39;</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;http://localhost:3000&#39;</span><span class="p">])</span>

        <span class="c1"># Ensure we don&#39;t allow all origins in production</span>
        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">settings</span><span class="p">,</span> <span class="s1">&#39;environment&#39;</span><span class="p">)</span> <span class="ow">and</span> <span class="n">settings</span><span class="o">.</span><span class="n">environment</span> <span class="o">==</span> <span class="s1">&#39;production&#39;</span> <span class="ow">and</span> <span class="s1">&#39;*&#39;</span> <span class="ow">in</span> <span class="n">cors_origins</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="s2">&quot;CORS configured to allow all origins in production - security risk!&quot;</span><span class="p">)</span>
            <span class="n">cors_origins</span> <span class="o">=</span> <span class="p">[</span><span class="n">origin</span> <span class="k">for</span> <span class="n">origin</span> <span class="ow">in</span> <span class="n">cors_origins</span> <span class="k">if</span> <span class="n">origin</span> <span class="o">!=</span> <span class="s1">&#39;*&#39;</span><span class="p">]</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">cors_origins</span><span class="p">:</span>
                <span class="n">cors_origins</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;https://yourdomain.com&#39;</span><span class="p">]</span>
    <span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
        <span class="c1"># Fallback if config is not available</span>
        <span class="n">cors_origins</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;http://localhost:3000&#39;</span><span class="p">,</span> <span class="s1">&#39;http://127.0.0.1:3000&#39;</span><span class="p">]</span>

    <span class="k">return</span> <span class="p">{</span>
        <span class="s2">&quot;allow_origins&quot;</span><span class="p">:</span> <span class="n">cors_origins</span><span class="p">,</span>
        <span class="s2">&quot;allow_credentials&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
        <span class="s2">&quot;allow_methods&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;GET&quot;</span><span class="p">,</span> <span class="s2">&quot;POST&quot;</span><span class="p">,</span> <span class="s2">&quot;PUT&quot;</span><span class="p">,</span> <span class="s2">&quot;DELETE&quot;</span><span class="p">,</span> <span class="s2">&quot;OPTIONS&quot;</span><span class="p">],</span>
        <span class="s2">&quot;allow_headers&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="s2">&quot;Accept&quot;</span><span class="p">,</span>
            <span class="s2">&quot;Accept-Language&quot;</span><span class="p">,</span>
            <span class="s2">&quot;Content-Language&quot;</span><span class="p">,</span>
            <span class="s2">&quot;Content-Type&quot;</span><span class="p">,</span>
            <span class="s2">&quot;Authorization&quot;</span><span class="p">,</span>
            <span class="s2">&quot;X-Requested-With&quot;</span><span class="p">,</span>
            <span class="s2">&quot;X-API-Key&quot;</span>
        <span class="p">],</span>
        <span class="s2">&quot;expose_headers&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="s2">&quot;X-Request-ID&quot;</span><span class="p">,</span>
            <span class="s2">&quot;X-Response-Time&quot;</span>
        <span class="p">],</span>
        <span class="s2">&quot;max_age&quot;</span><span class="p">:</span> <span class="mi">600</span>  <span class="c1"># Cache preflight requests for 10 minutes</span>
    <span class="p">}</span></div>



<div class="viewcode-block" id="validate_cors_origin">
<a class="viewcode-back" href="../modules.html#security.validate_cors_origin">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">validate_cors_origin</span><span class="p">(</span><span class="n">origin</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">allowed_origins</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Validate if an origin is allowed for CORS requests.</span>

<span class="sd">    Args:</span>
<span class="sd">        origin: The origin to validate</span>
<span class="sd">        allowed_origins: List of allowed origins</span>

<span class="sd">    Returns:</span>
<span class="sd">        True if origin is allowed, False otherwise</span>

<span class="sd">    Example:</span>
<span class="sd">        &gt;&gt;&gt; allowed = [&#39;https://example.com&#39;, &#39;https://app.example.com&#39;]</span>
<span class="sd">        &gt;&gt;&gt; validate_cors_origin(&#39;https://example.com&#39;, allowed)</span>
<span class="sd">        True</span>
<span class="sd">        &gt;&gt;&gt; validate_cors_origin(&#39;https://malicious.com&#39;, allowed)</span>
<span class="sd">        False</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">origin</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">False</span>

    <span class="c1"># Check for exact match</span>
    <span class="k">if</span> <span class="n">origin</span> <span class="ow">in</span> <span class="n">allowed_origins</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">True</span>

    <span class="c1"># Check for wildcard</span>
    <span class="k">if</span> <span class="s1">&#39;*&#39;</span> <span class="ow">in</span> <span class="n">allowed_origins</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">True</span>

    <span class="c1"># Check for subdomain patterns (if implemented)</span>
    <span class="k">for</span> <span class="n">allowed</span> <span class="ow">in</span> <span class="n">allowed_origins</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">allowed</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;*.&#39;</span><span class="p">)</span> <span class="ow">and</span> <span class="n">origin</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="n">allowed</span><span class="p">[</span><span class="mi">1</span><span class="p">:]):</span>
            <span class="k">return</span> <span class="kc">True</span>

    <span class="k">return</span> <span class="kc">False</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, AI Assistant.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>