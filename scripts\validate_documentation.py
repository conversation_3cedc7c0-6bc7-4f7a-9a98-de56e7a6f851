#!/usr/bin/env python3
"""
Documentation validation script for Neural Symbolic Language Model.

This script validates that the Sphinx documentation accurately reflects all
capabilities of the updated implementation, including:
- Ollama gemma3n:e2b integration
- Multi-modal support
- Hybrid Retrieval system
- Client integration capabilities
- Enhanced monitoring and logging
"""

import os
import re
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
import json


class DocumentationValidator:
    """Validates documentation completeness and accuracy."""
    
    def __init__(self, project_root: Path = None):
        """Initialize the validator.
        
        Args:
            project_root: Root directory of the project
        """
        self.project_root = project_root or Path(__file__).parent.parent
        self.docs_dir = self.project_root / "docs"
        self.src_dir = self.project_root / "src"
        
        self.validation_results = {
            'missing_features': [],
            'outdated_content': [],
            'missing_files': [],
            'api_inconsistencies': [],
            'configuration_gaps': [],
            'example_issues': []
        }
    
    def validate_all(self) -> Dict[str, List[str]]:
        """Run comprehensive documentation validation.
        
        Returns:
            Dictionary containing validation results
        """
        print("🔍 Starting comprehensive documentation validation...")
        
        # Check core capabilities
        self.validate_ollama_integration()
        self.validate_multimodal_support()
        self.validate_hybrid_retrieval()
        self.validate_client_integration()
        self.validate_api_endpoints()
        self.validate_configuration_docs()
        self.validate_examples()
        self.validate_sphinx_structure()
        
        return self.validation_results
    
    def validate_ollama_integration(self):
        """Validate Ollama and gemma3n:e2b documentation."""
        print("\n📋 Validating Ollama integration documentation...")
        
        required_content = [
            "gemma3n:e2b",
            "ollama",
            "MODEL_REASONING_ENGINE=ollama",
            "MODEL_REASONING_MODEL=gemma3n:e2b",
            "ollama pull gemma3n:e2b",
            "http://localhost:11434"
        ]
        
        docs_to_check = [
            "index.rst",
            "configuration.rst",
            "api_reference.rst",
            "ollama_gemma3n_guide.md"
        ]
        
        for doc in docs_to_check:
            doc_path = self.docs_dir / doc
            if not doc_path.exists():
                if doc == "ollama_gemma3n_guide.md":
                    self.validation_results['missing_files'].append(
                        f"Missing Ollama guide: {doc}"
                    )
                continue
            
            content = doc_path.read_text(encoding='utf-8')
            
            for required in required_content:
                if required not in content:
                    self.validation_results['missing_features'].append(
                        f"Missing '{required}' in {doc}"
                    )
        
        # Check if gemma3n:e2b is mentioned as the primary model
        index_path = self.docs_dir / "index.rst"
        if index_path.exists():
            content = index_path.read_text(encoding='utf-8')
            if "gemma3n:e2b" not in content:
                self.validation_results['missing_features'].append(
                    "gemma3n:e2b not prominently featured in main documentation"
                )
    
    def validate_multimodal_support(self):
        """Validate multi-modal support documentation."""
        print("🎯 Validating multi-modal support documentation...")
        
        multimodal_features = [
            "multi-modal",
            "code analysis",
            "structured data",
            "mathematical reasoning",
            "symbolic logic",
            '"type": "code"',
            '"type": "math"',
            '"type": "structured_data"'
        ]
        
        # Check if multi_modal_support.rst exists
        multimodal_doc = self.docs_dir / "multi_modal_support.rst"
        if not multimodal_doc.exists():
            self.validation_results['missing_files'].append(
                "Missing multi_modal_support.rst documentation"
            )
            return
        
        content = multimodal_doc.read_text(encoding='utf-8')
        
        for feature in multimodal_features:
            if feature not in content.lower():
                self.validation_results['missing_features'].append(
                    f"Multi-modal feature '{feature}' not documented"
                )
        
        # Check API reference for multi-modal examples
        api_ref = self.docs_dir / "api_reference.rst"
        if api_ref.exists():
            api_content = api_ref.read_text(encoding='utf-8')
            if '"type": "code"' not in api_content:
                self.validation_results['api_inconsistencies'].append(
                    "Multi-modal API examples missing from api_reference.rst"
                )
    
    def validate_hybrid_retrieval(self):
        """Validate Hybrid Retrieval system documentation."""
        print("📚 Validating Hybrid Retrieval documentation...")
        
        retrieval_endpoints = [
            "/documents/add",
            "/documents/add-url", 
            "/documents/add-github",
            "/documents/search",
            "/documents/count"
        ]
        
        retrieval_features = [
            "hybrid retrieval",
            "vector search",
            "keyword search",
            "github repository",
            "website content",
            "document management"
        ]
        
        # Check if hybrid retrieval guide exists
        retrieval_guide = self.docs_dir / "hybrid_retrieval_guide.md"
        if not retrieval_guide.exists():
            self.validation_results['missing_files'].append(
                "Missing hybrid_retrieval_guide.md"
            )
        
        # Check API reference for retrieval endpoints
        api_ref = self.docs_dir / "api_reference.rst"
        if api_ref.exists():
            api_content = api_ref.read_text(encoding='utf-8')
            
            for endpoint in retrieval_endpoints:
                if endpoint not in api_content:
                    self.validation_results['api_inconsistencies'].append(
                        f"Missing retrieval endpoint: {endpoint}"
                    )
        
        # Check main index for retrieval features
        index_path = self.docs_dir / "index.rst"
        if index_path.exists():
            content = index_path.read_text(encoding='utf-8')
            
            for feature in retrieval_features:
                if feature.lower() not in content.lower():
                    self.validation_results['missing_features'].append(
                        f"Retrieval feature '{feature}' not mentioned in main docs"
                    )
    
    def validate_client_integration(self):
        """Validate client integration documentation."""
        print("🔗 Validating client integration documentation...")
        
        client_apps = [
            "Open WebUI",
            "LibreChat", 
            "Continue.dev",
            "Cursor IDE",
            "Chatbot UI"
        ]
        
        integration_features = [
            "OpenAI-compatible",
            "client integration",
            "docker-compose",
            "setup_client_integration.sh"
        ]
        
        # Check if client integration guide exists
        client_guide = self.docs_dir / "client_integration_guide.md"
        if not client_guide.exists():
            self.validation_results['missing_files'].append(
                "Missing client_integration_guide.md"
            )
        
        # Check if client applications doc exists
        client_apps_doc = self.docs_dir / "client_applications.rst"
        if not client_apps_doc.exists():
            self.validation_results['missing_files'].append(
                "Missing client_applications.rst"
            )
        else:
            content = client_apps_doc.read_text(encoding='utf-8')
            
            for app in client_apps:
                if app not in content:
                    self.validation_results['missing_features'].append(
                        f"Client application '{app}' not documented"
                    )
        
        # Check main documentation for integration features
        index_path = self.docs_dir / "index.rst"
        if index_path.exists():
            content = index_path.read_text(encoding='utf-8')
            
            for feature in integration_features:
                if feature.lower() not in content.lower():
                    self.validation_results['missing_features'].append(
                        f"Integration feature '{feature}' not mentioned"
                    )
    
    def validate_api_endpoints(self):
        """Validate API endpoint documentation."""
        print("🌐 Validating API endpoint documentation...")
        
        # Check if actual API endpoints match documentation
        expected_endpoints = [
            "/v1/chat/completions",
            "/v1/models",
            "/health",
            "/performance",
            "/documents/add",
            "/documents/search"
        ]
        
        api_ref = self.docs_dir / "api_reference.rst"
        if not api_ref.exists():
            self.validation_results['missing_files'].append(
                "Missing api_reference.rst"
            )
            return
        
        content = api_ref.read_text(encoding='utf-8')
        
        for endpoint in expected_endpoints:
            if endpoint not in content:
                self.validation_results['api_inconsistencies'].append(
                    f"Missing API endpoint documentation: {endpoint}"
                )
        
        # Check for OpenAI compatibility documentation
        if "OpenAI-compatible" not in content and "openai compatibility" not in content.lower():
            self.validation_results['api_inconsistencies'].append(
                "OpenAI compatibility not clearly documented in API reference"
            )
    
    def validate_configuration_docs(self):
        """Validate configuration documentation."""
        print("⚙️ Validating configuration documentation...")
        
        required_config_sections = [
            "MODEL_REASONING_ENGINE=ollama",
            "MODEL_REASONING_MODEL=gemma3n:e2b",
            "RETRIEVAL_ENABLED=true",
            "LOG_STRUCTURED=true",
            "SECURITY_API_KEYS_JSON"
        ]
        
        config_doc = self.docs_dir / "configuration.rst"
        if not config_doc.exists():
            self.validation_results['missing_files'].append(
                "Missing configuration.rst"
            )
            return
        
        content = config_doc.read_text(encoding='utf-8')
        
        for config in required_config_sections:
            if config not in content:
                self.validation_results['configuration_gaps'].append(
                    f"Missing configuration option: {config}"
                )
        
        # Check for updated port number (8080 instead of 8000)
        if "APP_PORT=8000" in content and "APP_PORT=8080" not in content:
            self.validation_results['outdated_content'].append(
                "Configuration still shows old port 8000 instead of 8080"
            )
    
    def validate_examples(self):
        """Validate example code and scripts."""
        print("📝 Validating examples and code samples...")
        
        example_files = [
            "examples/hybrid_retrieval_demo.py",
            "examples/client_integration_demo.py",
            "scripts/setup_client_integration.sh",
            "docker-compose.client-integration.yml"
        ]
        
        for example in example_files:
            example_path = self.project_root / example
            if not example_path.exists():
                self.validation_results['example_issues'].append(
                    f"Missing example file: {example}"
                )
        
        # Check if examples are referenced in documentation
        docs_files = list(self.docs_dir.glob("*.rst")) + list(self.docs_dir.glob("*.md"))
        
        example_references = 0
        for doc_file in docs_files:
            content = doc_file.read_text(encoding='utf-8')
            if "examples/" in content or "demo" in content.lower():
                example_references += 1
        
        if example_references < 3:
            self.validation_results['example_issues'].append(
                "Examples not sufficiently referenced in documentation"
            )
    
    def validate_sphinx_structure(self):
        """Validate Sphinx documentation structure."""
        print("📖 Validating Sphinx structure...")
        
        required_files = [
            "conf.py",
            "index.rst",
            "api_reference.rst",
            "configuration.rst"
        ]
        
        for file in required_files:
            file_path = self.docs_dir / file
            if not file_path.exists():
                self.validation_results['missing_files'].append(
                    f"Missing Sphinx file: {file}"
                )
        
        # Check if index.rst includes all new documentation
        index_path = self.docs_dir / "index.rst"
        if index_path.exists():
            content = index_path.read_text(encoding='utf-8')
            
            expected_toctree_entries = [
                "ollama_gemma3n_guide",
                "client_integration_guide", 
                "hybrid_retrieval_guide",
                "multi_modal_support",
                "openai_compatibility"
            ]
            
            for entry in expected_toctree_entries:
                if entry not in content:
                    self.validation_results['missing_features'].append(
                        f"Missing toctree entry: {entry}"
                    )
    
    def generate_report(self) -> str:
        """Generate a comprehensive validation report.
        
        Returns:
            Formatted validation report
        """
        report = []
        report.append("=" * 70)
        report.append("📊 DOCUMENTATION VALIDATION REPORT")
        report.append("=" * 70)
        
        total_issues = sum(len(issues) for issues in self.validation_results.values())
        
        if total_issues == 0:
            report.append("✅ ALL DOCUMENTATION VALIDATION CHECKS PASSED!")
            report.append("\nThe Sphinx documentation accurately reflects all capabilities.")
        else:
            report.append(f"⚠️  Found {total_issues} documentation issues that need attention:")
        
        for category, issues in self.validation_results.items():
            if issues:
                report.append(f"\n🔍 {category.replace('_', ' ').title()}:")
                for issue in issues:
                    report.append(f"   - {issue}")
        
        if total_issues > 0:
            report.append("\n📝 RECOMMENDATIONS:")
            report.append("1. Update missing documentation files")
            report.append("2. Add missing feature descriptions")
            report.append("3. Update outdated configuration examples")
            report.append("4. Ensure API documentation matches implementation")
            report.append("5. Add comprehensive examples for new features")
        
        report.append("\n" + "=" * 70)
        
        return "\n".join(report)
    
    def save_report(self, output_file: Path):
        """Save validation report to file.
        
        Args:
            output_file: Path to save the report
        """
        report = self.generate_report()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Also save JSON results
        json_file = output_file.with_suffix('.json')
        with open(json_file, 'w') as f:
            json.dump(self.validation_results, f, indent=2)


def main():
    """Main validation function."""
    print("🚀 Neural Symbolic Language Model - Documentation Validation")
    print("=" * 70)
    
    # Initialize validator
    validator = DocumentationValidator()
    
    # Run validation
    results = validator.validate_all()
    
    # Generate and display report
    report = validator.generate_report()
    print(report)
    
    # Save report
    output_file = Path("documentation_validation_report.txt")
    validator.save_report(output_file)
    print(f"\n💾 Report saved to: {output_file}")
    
    # Exit with appropriate code
    total_issues = sum(len(issues) for issues in results.values())
    if total_issues > 0:
        print(f"\n❌ Validation failed with {total_issues} issues")
        sys.exit(1)
    else:
        print("\n✅ All documentation validation checks passed!")
        sys.exit(0)


if __name__ == "__main__":
    main()
