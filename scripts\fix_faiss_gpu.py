#!/usr/bin/env python
"""
FAISS-GPU Fix Script for Windows

This script diagnoses and attempts to fix common FAISS-GPU installation issues on Windows:
1. Checks for problematic/corrupt FAISS installations
2. Verifies Python environment consistency
3. Checks for Visual C++ Redistributable
4. Checks for dependency conflicts
5. Provides targeted solutions for common errors

Usage:
    python scripts/fix_faiss_gpu.py [--fix]

Options:
    --fix    Attempt to automatically fix identified issues (use with caution)
"""

import os
import sys
import site
import subprocess
import shutil
import platform
import argparse
import importlib.util
from pathlib import Path

# Define colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Initialize parser
parser = argparse.ArgumentParser(description='Diagnose and fix FAISS-GPU issues on Windows')
parser.add_argument('--fix', action='store_true', help='Attempt to fix identified issues')
args = parser.parse_args()

def print_header(message):
    """Print a formatted header message"""
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'=' * 70}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{message.center(70)}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'=' * 70}{Colors.ENDC}\n")

def print_info(message):
    """Print an info message"""
    print(f"{Colors.BLUE}ℹ️ {message}{Colors.ENDC}")

def print_success(message):
    """Print a success message"""
    print(f"{Colors.GREEN}✅ {message}{Colors.ENDC}")

def print_warning(message):
    """Print a warning message"""
    print(f"{Colors.YELLOW}⚠️ {message}{Colors.ENDC}")

def print_error(message):
    """Print an error message"""
    print(f"{Colors.RED}❌ {message}{Colors.ENDC}")

def print_fix(message):
    """Print a fix message"""
    print(f"{Colors.GREEN}🔧 {message}{Colors.ENDC}")

def run_command(command):
    """Run a command and return its output"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, shell=True)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except Exception as e:
        return "", str(e), 1

def check_system():
    """Check system information"""
    print_header("System Information")
    
    # Check OS
    if platform.system() != "Windows":
        print_warning(f"This script is designed for Windows, but you're running {platform.system()}")
    else:
        print_info(f"OS: {platform.system()} {platform.version()}")
    
    # Check Python
    print_info(f"Python version: {platform.python_version()}")
    print_info(f"Python executable: {sys.executable}")
    
    # Check if in Conda environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print_success(f"Running in Conda environment: {conda_env}")
        
        # Get conda path
        conda_prefix = os.environ.get('CONDA_PREFIX')
        if conda_prefix:
            print_info(f"Conda prefix: {conda_prefix}")
    else:
        print_warning("Not running in a Conda environment - FAISS-GPU on Windows works best with Conda")

def check_vc_redist():
    """Check for Visual C++ Redistributable"""
    print_header("Checking Visual C++ Redistributable")
    
    # Since we can't directly check if VC++ Redist is installed via Python,
    # we'll try to load a DLL that should be present if it's installed
    vc_redist_present = False
    
    # Try to import a package that requires VC++ Redist
    try:
        import numpy
        try:
            # Try to access a function that would fail if VC++ Redist is missing
            numpy.ones(10)
            vc_redist_present = True
        except Exception:
            vc_redist_present = False
    except ImportError:
        print_warning("NumPy not installed, can't reliably check VC++ Redist")
    
    if vc_redist_present:
        print_success("Microsoft Visual C++ Redistributable appears to be installed")
    else:
        print_error("Microsoft Visual C++ Redistributable may not be installed or is not working correctly")
        print_warning("Please download and install from: https://aka.ms/vs/17/release/vc_redist.x64.exe")

def check_faiss_installation():
    """Check for problematic FAISS installations"""
    print_header("Checking FAISS Installation")
    
    site_packages = site.getsitepackages()[0]
    print_info(f"Site-packages directory: {site_packages}")
    
    # Look for problematic FAISS directories
    problematic_dirs = []
    faiss_dirs = []
    
    for item in os.listdir(site_packages):
        item_path = os.path.join(site_packages, item)
        if os.path.isdir(item_path):
            if item.startswith('~faiss'):
                problematic_dirs.append(item_path)
            elif item == 'faiss' or item.startswith('faiss-') or item.startswith('faiss_'):
                faiss_dirs.append(item_path)
    
    # Check for problematic directories
    if problematic_dirs:
        print_error(f"Found {len(problematic_dirs)} problematic FAISS directories:")
        for path in problematic_dirs:
            print(f"  - {os.path.basename(path)}")
        
        if args.fix:
            print_fix("Attempting to remove problematic directories...")
            for path in problematic_dirs:
                try:
                    shutil.rmtree(path)
                    print_success(f"Removed {os.path.basename(path)}")
                except Exception as e:
                    print_error(f"Failed to remove {os.path.basename(path)}: {e}")
        else:
            print_warning("Run with --fix to automatically remove these directories")
    else:
        print_success("No problematic FAISS directories found")
    
    # Check for existing FAISS installations
    if faiss_dirs:
        print_info(f"Found {len(faiss_dirs)} FAISS-related directories:")
        for path in faiss_dirs:
            print(f"  - {os.path.basename(path)}")
    else:
        print_warning("No FAISS installation directories found")
    
    # Try to import FAISS
    try:
        import faiss
        print_success(f"FAISS imported successfully (version: {faiss.__version__})")
        
        # Check for GPU support
        try:
            gpu_available = hasattr(faiss, 'StandardGpuResources')
            if gpu_available:
                print_success("FAISS has GPU support")
                
                # Try to create GPU resources
                try:
                    res = faiss.StandardGpuResources()
                    print_success("Successfully created GPU resources")
                except Exception as e:
                    print_error(f"Failed to create GPU resources: {e}")
                    print_warning("This may indicate missing Visual C++ Redistributable or CUDA issues")
            else:
                print_error("FAISS does not have GPU support - you may have faiss-cpu installed instead of faiss-gpu")
        except Exception as e:
            print_error(f"Error checking GPU support: {e}")
    except ImportError as e:
        print_error(f"Failed to import FAISS: {e}")

def check_dependencies():
    """Check for dependency conflicts"""
    print_header("Checking Dependencies")
    
    # List of packages to check
    packages = {
        'torch': 'PyTorch',
        'vllm': 'vLLM',
        'faiss': 'FAISS',
        'numpy': 'NumPy'
    }
    
    # Check each package
    versions = {}
    for package, name in packages.items():
        try:
            spec = importlib.util.find_spec(package)
            if spec:
                try:
                    mod = importlib.import_module(package)
                    version = getattr(mod, '__version__', 'unknown')
                    versions[package] = version
                    print_success(f"{name} is installed (version: {version})")
                except Exception as e:
                    print_error(f"Error getting {name} version: {e}")
            else:
                print_warning(f"{name} is not installed")
        except Exception as e:
            print_error(f"Error checking {name}: {e}")
    
    # Check for specific version conflicts
    if 'torch' in versions and 'vllm' in versions:
        # vLLM has specific torch version requirements
        torch_version = versions['torch']
        if torch_version.startswith('2.6') and versions['vllm'].startswith('0.6'):
            print_error(f"Version conflict: vLLM 0.6.x requires torch 2.4.0, but torch {torch_version} is installed")
            print_warning("This may cause stability issues")
            
            if args.fix:
                print_fix("Attempting to fix PyTorch version...")
                if 'CONDA_DEFAULT_ENV' in os.environ:
                    print_info("Using Conda to reinstall PyTorch...")
                    cmd = "conda install -c pytorch -c nvidia pytorch=2.4.0 torchvision torchaudio cudatoolkit -y"
                    stdout, stderr, returncode = run_command(cmd)
                    if returncode == 0:
                        print_success("Successfully reinstalled PyTorch 2.4.0")
                    else:
                        print_error(f"Failed to reinstall PyTorch: {stderr}")
                else:
                    print_warning("Not in a Conda environment, skipping fix")
            else:
                print_warning("Run with --fix to attempt to fix this conflict")
    
    # Check CUDA availability
    try:
        import torch
        if torch.cuda.is_available():
            print_success(f"CUDA is available (version: {torch.version.cuda})")
            print_info(f"GPU: {torch.cuda.get_device_name(0)}")
        else:
            print_error("CUDA is not available through PyTorch")
            print_warning("This may be due to missing CUDA drivers or incompatible PyTorch installation")
    except Exception as e:
        print_error(f"Error checking CUDA: {e}")

def fix_environment_paths():
    """Fix environment path issues for FAISS-GPU"""
    print_header("Checking Environment Paths")
    
    # Get the current Python executable directory
    py_dir = os.path.dirname(sys.executable)
    
    # Check if we're in a Conda environment
    if 'CONDA_PREFIX' in os.environ:
        conda_prefix = os.environ['CONDA_PREFIX']
        
        # Check for DLL directories
        dll_dirs = [
            os.path.join(conda_prefix, 'Library', 'bin'),
            os.path.join(conda_prefix, 'bin')
        ]
        
        for dll_dir in dll_dirs:
            if os.path.exists(dll_dir):
                print_info(f"Found DLL directory: {dll_dir}")
                
                # Check if it's in PATH
                paths = os.environ['PATH'].split(os.pathsep)
                if dll_dir not in paths:
                    print_warning(f"{dll_dir} is not in PATH")
                    
                    if args.fix:
                        print_fix(f"Adding {dll_dir} to PATH for this session")
                        os.environ['PATH'] = dll_dir + os.pathsep + os.environ['PATH']
                        print_success("PATH updated for this session")
                        print_warning("To make this permanent, you may need to edit your system environment variables")
                    else:
                        print_warning("Run with --fix to add this directory to PATH for the current session")
                else:
                    print_success(f"{dll_dir} is in PATH")

def recommend_next_steps():
    """Recommend next steps based on the checks"""
    print_header("Recommended Next Steps")
    
    # Try to import FAISS to determine status
    faiss_imported = False
    try:
        import faiss
        faiss_imported = True
    except ImportError:
        pass
    
    if faiss_imported:
        # Check for GPU support
        try:
            gpu_resources_work = False
            try:
                res = faiss.StandardGpuResources()
                gpu_resources_work = True
            except:
                pass
            
            if gpu_resources_work:
                print_success("FAISS-GPU appears to be working correctly!")
                print_info("Next steps:")
                print_info("1. Run 'python scripts/verify_faiss_gpu.py' for a more thorough test")
                print_info("2. Try running your application")
            else:
                print_warning("FAISS is installed but GPU resources couldn't be created")
                print_info("Recommended steps:")
                print_info("1. Make sure Visual C++ Redistributable is installed")
                print_info("2. Check CUDA installation and GPU drivers")
                print_info("3. Reinstall FAISS-GPU using Conda: conda install -c conda-forge faiss-gpu")
        except:
            print_warning("Could not determine if FAISS has GPU support")
    else:
        print_warning("FAISS is not installed or couldn't be imported")
        print_info("Recommended steps:")
        print_info("1. Install FAISS-GPU using Conda:")
        print_info("   conda install -c conda-forge faiss-gpu")
        print_info("2. Make sure you're using the correct Python environment")
        print_info("3. Install Microsoft Visual C++ Redistributable")

    print_info("\nFor a complete environment setup, run:")
    print_info("   scripts/setup_windows_env.ps1")
    
    if args.fix:
        print_warning("\nSome fixes were attempted. You may need to restart your Python environment")
        print_info("for the changes to take effect.")

def main():
    """Main function to run all checks"""
    print_header("FAISS-GPU Fix Script for Windows")
    
    if args.fix:
        print_warning("Running in FIX mode - will attempt to fix identified issues")
    else:
        print_info("Running in CHECK mode - will identify issues but not fix them")
        print_info("Run with --fix to attempt automatic fixes")
    
    # Run all checks
    check_system()
    check_vc_redist()
    check_faiss_installation()
    check_dependencies()
    fix_environment_paths()
    recommend_next_steps()

if __name__ == "__main__":
    main()
