<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neural Symbolic Language Model</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chat-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .user-message {
            background-color: #e3f2fd;
            margin-left: 20%;
        }
        .assistant-message {
            background-color: #f5f5f5;
            margin-right: 20%;
        }
        .input-container {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        textarea {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            min-height: 40px;
            font-family: inherit;
        }
        button {
            padding: 10px 20px;
            background-color: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #1976d2;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            color: #666;
            font-size: 0.9em;
            margin-top: 10px;
        }
        .error {
            color: #d32f2f;
            margin-top: 10px;
        }
        .typing-indicator {
            display: none;
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Neural Symbolic Language Model</h1>
        <p>A local, GPU-accelerated language model combining Neural-Symbolic Reasoning with Retrieval-Augmented Generation</p>
    </div>

    <div class="chat-container">
        <div id="chat-messages"></div>
        <div class="typing-indicator" id="typing-indicator">Assistant is typing...</div>
        <div class="input-container">
            <textarea id="user-input" placeholder="Type your message here..." rows="1"></textarea>
            <button id="send-button">Send</button>
        </div>
        <div id="error-message" class="error"></div>
        <div id="status" class="status"></div>
    </div>

    <script>
        const chatMessages = document.getElementById('chat-messages');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');
        const errorMessage = document.getElementById('error-message');
        const status = document.getElementById('status');

        let isProcessing = false;

        function appendMessage(content, isUser) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
            messageDiv.textContent = content;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function setTypingIndicator(visible) {
            typingIndicator.style.display = visible ? 'block' : 'none';
        }

        function setError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = message ? 'block' : 'none';
        }

        function setStatus(message) {
            status.textContent = message;
        }

        async function sendMessage() {
            const message = userInput.value.trim();
            if (!message || isProcessing) return;

            isProcessing = true;
            sendButton.disabled = true;
            setError('');
            setStatus('');
            userInput.value = '';

            appendMessage(message, true);
            setTypingIndicator(true);

            try {
                const response = await fetch('/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: message })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let assistantResponse = '';

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(5);
                            if (data === '[DONE]') continue;

                            try {
                                const parsed = JSON.parse(data);
                                if (parsed.response) {
                                    assistantResponse = parsed.response;
                                    if (parsed.cached) {
                                        setStatus('(Response retrieved from cache)');
                                    }
                                }
                            } catch (e) {
                                console.error('Error parsing SSE data:', e);
                            }
                        }
                    }
                }

                if (assistantResponse) {
                    appendMessage(assistantResponse, false);
                }
            } catch (error) {
                setError(`Error: ${error.message}`);
                console.error('Error:', error);
            } finally {
                setTypingIndicator(false);
                isProcessing = false;
                sendButton.disabled = false;
            }
        }

        sendButton.addEventListener('click', sendMessage);

        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Auto-resize textarea
        userInput.addEventListener('input', () => {
            userInput.style.height = 'auto';
            userInput.style.height = userInput.scrollHeight + 'px';
        });
    </script>
</body>
</html>
