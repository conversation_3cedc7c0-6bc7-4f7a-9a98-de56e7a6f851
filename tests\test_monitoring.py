"""Unit tests for the monitoring module."""

import unittest
import time
from datetime import datetime, timedelta
from monitoring import PerformanceMonitor, RequestMetrics, SystemMetrics

class TestMonitoring(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures."""
        self.monitor = PerformanceMonitor(collection_interval=1)
    
    def tearDown(self):
        """Clean up test fixtures."""
        self.monitor.shutdown()
    
    def test_request_tracking(self):
        """Test request tracking functionality."""
        request_id = "test-123"
        
        # Start tracking request
        self.monitor.start_request(request_id, "/test")
        self.assertIn(request_id, self.monitor.current_requests)
        
        # Record cache hit
        self.monitor.record_cache_hit(request_id)
        self.assertTrue(self.monitor.current_requests[request_id].cached)
        self.assertEqual(self.monitor.cache_hits, 1)
        
        # Record operation times
        self.monitor.record_retrieval_time(request_id, 0.5)
        self.assertEqual(self.monitor.current_requests[request_id].retrieval_time, 0.5)
        
        self.monitor.record_reasoning_time(request_id, 1.0)
        self.assertEqual(self.monitor.current_requests[request_id].reasoning_time, 1.0)
        
        # Record token count
        self.monitor.record_token_count(request_id, 100)
        self.assertEqual(self.monitor.current_requests[request_id].total_tokens, 100)
        
        # End request
        self.monitor.end_request(request_id)
        self.assertNotIn(request_id, self.monitor.current_requests)
        self.assertEqual(len(self.monitor.request_metrics), 1)
    
    def test_cache_metrics(self):
        """Test cache metrics tracking."""
        # Record cache hits and misses
        request_id = "test-456"
        self.monitor.start_request(request_id, "/test")
        
        self.monitor.record_cache_miss(request_id)
        self.assertEqual(self.monitor.cache_misses, 1)
        
        self.monitor.record_cache_hit(request_id)
        self.assertEqual(self.monitor.cache_hits, 1)
        
        self.monitor.end_request(request_id)
        
        # Get metrics
        metrics = self.monitor.get_recent_metrics()
        self.assertEqual(metrics["cache"]["hits"], 1)
        self.assertEqual(metrics["cache"]["misses"], 1)
        self.assertEqual(metrics["cache"]["hit_rate"], 0.5)
    
    def test_error_tracking(self):
        """Test error tracking functionality."""
        request_id = "test-789"
        error_msg = "Test error"
        
        # Start and end request with error
        self.monitor.start_request(request_id, "/test")
        self.monitor.end_request(request_id, error=error_msg)
        
        # Get metrics
        metrics = self.monitor.get_recent_metrics()
        self.assertEqual(metrics["requests"]["error_rate"], 1.0)
        
        # Add successful request
        request_id = "test-790"
        self.monitor.start_request(request_id, "/test")
        self.monitor.end_request(request_id)
        
        # Check updated error rate
        metrics = self.monitor.get_recent_metrics()
        self.assertEqual(metrics["requests"]["error_rate"], 0.5)
    
    def test_system_metrics_collection(self):
        """Test system metrics collection."""
        # Wait for metrics collection
        time.sleep(2)
        
        # Get metrics
        metrics = self.monitor.get_recent_metrics()
        system = metrics["system"]
        
        # Check system metrics
        self.assertIsInstance(system["cpu_percent"], float)
        self.assertIsInstance(system["memory_percent"], float)
        self.assertIsInstance(system["active_requests"], (int, float))
    
    def test_metrics_filtering(self):
        """Test metrics filtering by time window."""
        # Add old request
        old_metrics = RequestMetrics(
            request_id="old",
            endpoint="/test",
            start_time=time.time() - 600,  # 10 minutes ago
            end_time=time.time() - 590
        )
        self.monitor.request_metrics.append(old_metrics)
        
        # Add recent request
        request_id = "recent"
        self.monitor.start_request(request_id, "/test")
        self.monitor.end_request(request_id)
        
        # Get metrics for last 5 minutes
        metrics = self.monitor.get_recent_metrics(minutes=5)
        self.assertEqual(metrics["requests"]["total"], 1)

if __name__ == "__main__":
    unittest.main()
