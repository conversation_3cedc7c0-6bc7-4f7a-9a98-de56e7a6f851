# Neural Symbolic Language Model Configuration
# Copy this file to .env and customize the values for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Basic application configuration
APP_TITLE=Neural Symbolic Language Model API
APP_VERSION=0.1.0
APP_DEBUG=false
APP_ENVIRONMENT=development  # development, staging, production
APP_HOST=0.0.0.0
APP_PORT=8000
APP_WORKERS=1
APP_RELOAD=true

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================

# Reasoning engine settings
MODEL_REASONING_ENGINE=local  # local, openai, anthropic, ollama
MODEL_REASONING_MODEL=llama   # llama, gpt-3.5-turbo, claude-3-sonnet
MODEL_EMBEDDING_MODEL=BAAI/bge-small-en-v1.5
MODEL_EMBEDDING_DIMENSION=768
MODEL_USE_GPU=true
MODEL_GPU_MEMORY_FRACTION=0.8
MODEL_OLLAMA_HOST=http://localhost:11434
MODEL_OLLAMA_TIMEOUT=300
MODEL_VECTOR_DB_BACKEND=faiss  # faiss, chromadb, pinecone

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# API authentication (JSON format for multiple keys)
# Example: {"user1": "key1", "user2": "key2"}
SECURITY_API_KEYS={}

# Rate limiting
SECURITY_RATE_LIMIT_REQUESTS=100  # requests per window
SECURITY_RATE_LIMIT_WINDOW=60     # window in seconds
SECURITY_MAX_REQUEST_SIZE=10485760  # 10MB in bytes

# CORS settings (JSON array format)
# Example: ["http://localhost:3000", "https://yourdomain.com"]
SECURITY_CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# Security timeouts and limits
SECURITY_BLOCK_DURATION=3600      # IP block duration in seconds
SECURITY_MAX_FAILED_ATTEMPTS=5    # Max failed auth attempts before blocking

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Cache settings
CACHE_MAX_SIZE=1000
CACHE_TTL_SECONDS=3600
CACHE_CLEANUP_INTERVAL=300

# Redis configuration (optional, leave empty for in-memory cache)
CACHE_REDIS_URL=
# Example: redis://localhost:6379/0
# Example with auth: redis://:password@localhost:6379/0

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs
LOG_MAX_FILE_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5
LOG_STRUCTURED_LOGGING=false

# =============================================================================
# DEVELOPMENT SETTINGS (for development environment only)
# =============================================================================

# These settings are automatically disabled in production

# Enable API documentation endpoints
DEV_ENABLE_DOCS=true
DEV_ENABLE_REDOC=true

# Enable detailed error responses
DEV_DETAILED_ERRORS=true

# Enable request/response logging
DEV_LOG_REQUESTS=true

# =============================================================================
# PRODUCTION SETTINGS (for production environment only)
# =============================================================================

# Database connection (use PostgreSQL or MySQL in production)
# PROD_DATABASE_URL=postgresql://user:password@localhost/neural_symbolic
# PROD_DATABASE_POOL_SIZE=20
# PROD_DATABASE_MAX_OVERFLOW=30

# External service URLs
# PROD_REDIS_URL=redis://redis-server:6379/0
# PROD_JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# SSL/TLS settings
# PROD_SSL_KEYFILE=/path/to/keyfile.pem
# PROD_SSL_CERTFILE=/path/to/certfile.pem

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Metrics collection
MONITORING_ENABLE_METRICS=true
MONITORING_METRICS_PORT=9090

# Distributed tracing (optional)
MONITORING_ENABLE_TRACING=false
MONITORING_JAEGER_ENDPOINT=
MONITORING_SERVICE_NAME=neural-symbolic-ai

# Health checks
MONITORING_HEALTH_CHECK_INTERVAL=30

# =============================================================================
# EXTERNAL SERVICES (optional)
# =============================================================================

# OpenAI API (if using OpenAI reasoning engine)
# OPENAI_API_KEY=your-openai-api-key-here
# OPENAI_ORG_ID=your-org-id-here

# Anthropic API (if using Anthropic reasoning engine)
# ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Vector database services
# PINECONE_API_KEY=your-pinecone-api-key-here
# PINECONE_ENVIRONMENT=your-pinecone-environment

# =============================================================================
# NOTES
# =============================================================================

# 1. Copy this file to .env and customize the values
# 2. Never commit .env files to version control
# 3. Use different configurations for different environments
# 4. In production, consider using a secrets management system
# 5. Validate your configuration using the built-in validation functions