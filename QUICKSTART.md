# Quick Start Guide for Symbolic Language Model

This guide provides step-by-step instructions for setting up and running the Symbolic Language Model project with GPU acceleration.

## Prerequisites

- NVIDIA GPU with CUDA support
- Python 3.10 (recommended)
- Microsoft Visual C++ Redistributable for Visual Studio 2015-2022 (Windows only)
- Miniconda or Anaconda

## 1. Environment Setup (Windows)

### Automated Setup (Recommended)

1. Right-click on `setup_windows.bat` in the project root and select "Run as administrator"
2. Follow the on-screen instructions
3. After completion, activate the environment:
   ```
   conda activate symblang
   ```

### Manual Setup

1. Create and activate a Conda environment:
   ```
   conda create -n symblang python=3.10
   conda activate symblang
   ```

2. Install PyTorch and FAISS-GPU:
   ```
   conda install -c pytorch -c nvidia pytorch=2.4.0 torchvision torchaudio cudatoolkit
   conda install -c conda-forge faiss-gpu
   ```

3. Install remaining dependencies:
   ```
   pip install -r requirements.txt
   ```

## 2. Verify Installation

Run the verification script to ensure FAISS-GPU is working correctly:

```
python scripts/verify_faiss_gpu.py
```

You should see "FAISS-GPU IS WORKING CORRECTLY!" in the output.

## 3. Run the API Server

Start the FastAPI server:

```
python -m uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload
```

## 4. Test the API

Make a sample request to the API:

```python
import requests

# Chat endpoint
response = requests.post("http://localhost:8080/chat", 
                        json={"text": "What is Neural-Symbolic AI?"})
print(response.json())

# Performance endpoint
stats = requests.get("http://localhost:8080/performance")
print(stats.json())
```

## 5. Run Tests

Execute the test suite:

```
pytest tests/
```

## 6. Run Benchmarks

Measure GPU acceleration benefits:

```
python scripts/gpu_impact.py
```

## Troubleshooting

If you encounter issues with FAISS-GPU:

1. Run the diagnostic tool:
   ```
   python scripts/fix_faiss_gpu.py
   ```

2. For more advanced troubleshooting, see:
   - `docs/WINDOWS_FAISS_GUIDE.md` for detailed Windows setup instructions
   - `RELEASE_NOTES.md` for known issues

3. Common issues and solutions:
   - **ImportError: No module named 'faiss'**: Ensure you're using the Conda environment's Python
   - **ModuleNotFoundError: No module named 'faiss.swigfaiss_avx2'**: Install Visual C++ Redistributable
   - **GPU not detected**: Check CUDA installation and GPU drivers

## Performance Expectations

With GPU acceleration properly configured, you should see:
- Vector search operations 7.9x-13.5x faster than CPU
- Higher performance gains with larger datasets and dimensions
- No difference in result quality between CPU and GPU

For detailed performance metrics, see the benchmarks in `RELEASE_NOTES.md`.
