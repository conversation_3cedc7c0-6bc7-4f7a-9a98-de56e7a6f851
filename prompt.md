# Prompt: Implement GPT-4o Alternative using SymbolicAI + LightRAG

## Task Description

You will help me implement a local, GPU-accelerated alternative to OpenAI's GPT-4o that combines Neuro-Symbolic Reasoning (via SymbolicAI) with Retrieval-Augmented Generation (via LightRAG). I need you to guide me through the implementation step-by-step, following the detailed checklist in 'ToDo.md' while ensuring we meet all requirements specified in 'PRD.md'.

## Implementation Strategy

Please help me implement this project by:

1. Analyzing the PRD.md and ToDo.md documents to understand the full project scope
2. Working through the steps in the ToDo.md checklist sequentially 
3. Providing complete code for each component with explanations
4. Validating that each component meets the requirements in PRD.md
5. Ensuring code components work together properly as a cohesive system
6. Tracking our progress through the implementation phases
7. Addressing any challenges or roadblocks we encounter during implementation

## How to Respond

For each implementation step:

1. **Identify the current step** from ToDo.md to work on
2. **Explain the purpose and requirements** tied to this step, referencing the relevant sections from PRD.md
3. **Provide complete, runnable code** with detailed comments explaining key functions and logic
4. **Explain implementation decisions** that satisfy specific requirements
5. **Include validation instructions** to verify this component works correctly
6. **Update the checklist status** by checking off completed items
7. **Suggest the next step** to work on 

As we progress, maintain context about what we've already built and how components interconnect. Help me avoid pitfalls by highlighting potential issues before they arise and suggesting best practices throughout the implementation.

## Project Specifications

The project combines:
- Neuro-Symbolic Reasoning via SymbolicAI for structured, explainable decision-making
- Retrieval-Augmented Generation via LightRAG for fact-grounded responses
- GPU-accelerated processing for improved performance
- API compatibility with GPT-4o for easy integration

Key requirements include:
- Fast response times (under 2-3 seconds for most queries)
- Support for consumer-grade GPUs (NVIDIA RTX 3060/4060)
- Docker deployment support
- OpenAI-compatible API format
- Performance monitoring and caching systems

## Implementation Support

I'll need help:
- Understanding the PRD requirements
- Implementing each component in the ToDo checklist
- Debugging code when errors occur
- Optimizing performance for GPU acceleration
- Testing the system to ensure it meets requirements
- Documenting the implementation properly

Please begin by analyzing the PRD.md and ToDo.md documents, then help me start implementation with the first step in the checklist.
