# Release Notes

## Version 0.1.0

Initial release of the SymbolicAI + LightRAG GPT-4o Alternative.

### Features

- Neuro-Symbolic Reasoning via SymbolicAI
- Retrieval-Augmented Generation via LightRAG
- GPU-accelerated processing
- API compatibility with GPT-4o
- Docker deployment with GPU support
- Web interface for interactive use
- Performance monitoring and logging

### Known Issues

- Streaming responses may not work with all SymbolicAI configurations
- Document management is limited to text files
- Performance may vary depending on hardware configuration
- FAISS-GPU integration on Windows requires specific setup steps

## Utility Scripts

This release includes several utility scripts to help with environment setup and troubleshooting:

### Windows Environment Setup

- `scripts/setup_windows_env.ps1`: PowerShell script that automates the setup of a Conda environment with FAISS-GPU for Windows users. It checks for Miniconda, Visual C++ Redistributables, creates a Conda environment, and installs all dependencies including PyTorch and FAISS-GPU.

### FAISS-GPU Verification

- `scripts/verify_faiss_gpu.py`: Python script that verifies FAISS-GPU is correctly installed and functioning. It checks imports, CUDA availability, tests basic functionality, and benchmarks CPU vs GPU performance.

### FAISS-GPU Diagnostics and Fixes

- `scripts/fix_faiss_gpu.py`: Python script that diagnoses and attempts to fix common FAISS-GPU installation issues on Windows. Run with the `--fix` flag to automatically attempt fixes for identified issues.

## GPU Performance Benefits

Benchmarks conducted with FAISS-GPU show significant performance improvements over CPU-only operations:

| Dimension | CPU Search Time | GPU Search Time | Search Speedup |
|-----------|----------------|----------------|---------------|
| 64        | 0.0603s        | 0.0076s        | **7.94x**     |
| 128       | 0.0789s        | 0.0060s        | **13.07x**    |
| 256       | 0.1371s        | 0.0101s        | **13.56x**    |

These benchmarks were performed on a dataset of 100,000 vectors with 1,000 queries, demonstrating that:

1. GPU acceleration provides an average 11.5x speedup for vector search operations
2. Performance benefits increase with higher dimensions
3. Tasks that would take 1 hour on CPU can be completed in ~5 minutes on GPU
4. Results are mathematically identical between CPU and GPU implementations

For full benchmark details, run `python scripts/gpu_impact.py` after setting up your environment.

### Future Improvements

- Support for multi-modal inputs
- Enhanced document management
- Improved streaming response handling
- Better performance optimization
- More comprehensive OpenAI API compatibility
