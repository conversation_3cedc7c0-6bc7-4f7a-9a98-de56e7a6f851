["scripts/test_api.py::test_chat", "scripts/test_api.py::test_performance", "scripts/test_retrieval.py::test_retrieval", "scripts/test_symbolic.py::test_symbolic_reasoning", "tests/test_gpu.py::TestGPUSupport::test_cuda_tensor_operations", "tests/test_gpu.py::TestGPUSupport::test_retriever_gpu", "tests/test_gpu.py::TestGPUSupport::test_symbolic_reasoner_gpu", "tests/test_main.py::TestAPI::test_invalid_request", "tests/test_main.py::TestAPI::test_openai_chat_endpoint", "tests/test_main.py::TestAPI::test_performance_endpoint", "tests/test_main.py::TestAPI::test_streaming_endpoint", "tests/test_main.py::TestAPI::test_system_info_endpoint", "tests/test_monitoring.py::TestMonitoring::test_cache_metrics", "tests/test_monitoring.py::TestMonitoring::test_error_tracking", "tests/test_monitoring.py::TestMonitoring::test_metrics_filtering", "tests/test_monitoring.py::TestMonitoring::test_request_tracking", "tests/test_monitoring.py::TestMonitoring::test_system_metrics_collection", "tests/test_retrieval.py::TestRetriever::test_add_and_query_documents", "tests/test_retrieval.py::TestRetriever::test_batch_add_documents", "tests/test_retrieval.py::TestRetriever::test_initialization", "tests/test_retrieval.py::TestRetriever::test_system_info", "tests/test_symbolic_reasoning.py::TestSymbolicReasoner::test_batch_process_queries", "tests/test_symbolic_reasoning.py::TestSymbolicReasoner::test_initialization", "tests/test_symbolic_reasoning.py::TestSymbolicReasoner::test_process_query", "tests/test_symbolic_reasoning.py::TestSymbolicReasoner::test_system_info"]