# LibreChat configuration for Neural Symbolic Language Model integration
# This configuration enables LibreChat to use the Neural Symbolic Language Model
# as a custom endpoint with full feature support.

version: 1.0.5
cache: true

# Custom endpoint configuration for Neural Symbolic Language Model
endpoints:
  custom:
    - name: "Neural Symbolic LM"
      apiKey: "librechat-secure-key-2024"
      baseURL: "http://neural-symbolic-api:8080/v1"
      
      # Model configuration
      models:
        default: ["gemma3n:e2b"]
        fetch: false  # Don't fetch models dynamically
      
      # Conversation features
      titleConvo: true
      titleModel: "gemma3n:e2b"
      summarize: false
      summaryModel: "gemma3n:e2b"
      forcePrompt: false
      
      # UI customization
      modelDisplayLabel: "Neural Symbolic Language Model"
      iconURL: "https://raw.githubusercontent.com/neural-symbolic/assets/main/icon.png"
      
      # Advanced headers
      headers:
        "User-Agent": "LibreChat/1.0 (Neural-Symbolic-Integration)"
        "X-Client": "LibreChat"
      
      # Model parameters and limits
      modelOptions:
        temperature:
          range: [0, 2]
          step: 0.1
          default: 0.7
        top_p:
          range: [0, 1]
          step: 0.01
          default: 1.0
        max_tokens:
          range: [1, 4096]
          step: 1
          default: 1000
        presence_penalty:
          range: [-2, 2]
          step: 0.1
          default: 0.0
        frequency_penalty:
          range: [-2, 2]
          step: 0.1
          default: 0.0
      
      # Endpoint-specific settings
      dropParams: ["stop", "user", "frequency_penalty", "presence_penalty"]
      
      # Rate limiting (requests per minute)
      rateLimits:
        requests: 100
        tokens: 100000
      
      # Custom system prompts
      systemPrompts:
        - name: "Symbolic Reasoning Expert"
          prompt: "You are an expert in symbolic reasoning and logic. Provide clear, step-by-step explanations for your reasoning process. When solving problems, break them down into logical components and show your work."
        
        - name: "AI Research Assistant"
          prompt: "You are an AI research assistant with deep knowledge of machine learning, neural networks, and symbolic AI. Provide accurate, well-researched responses with citations when appropriate."
        
        - name: "Programming Tutor"
          prompt: "You are a programming tutor with expertise in multiple languages and paradigms. Explain concepts clearly, provide working code examples, and help debug issues step by step."
        
        - name: "Logic Puzzle Solver"
          prompt: "You are an expert at solving logic puzzles and mathematical problems. Approach each problem systematically, show your reasoning process, and verify your solutions."

# File upload and processing configuration
fileConfig:
  endpoints:
    - "Neural Symbolic LM"
  
  # Supported file types
  supportedMimeTypes:
    - "text/plain"
    - "text/markdown"
    - "application/pdf"
    - "text/csv"
    - "application/json"
    - "application/xml"
  
  # File size limits (in MB)
  fileSizeLimit: 10
  
  # Processing options
  processFiles: true
  extractText: true

# Search and RAG configuration
ragConfig:
  # Enable RAG features
  enabled: true
  
  # Vector store configuration
  vectorStore:
    provider: "local"
    chunkSize: 1000
    chunkOverlap: 200
  
  # Embedding configuration
  embeddings:
    provider: "ollama"
    model: "mxbai-embed-large"
    baseURL: "http://neural-symbolic-api:8080/v1"
  
  # Search configuration
  search:
    enabled: true
    provider: "meilisearch"
    host: "http://librechat-meilisearch:7700"
    apiKey: "librechat-meili-master-key-2024"

# Interface customization
interface:
  # Branding
  appTitle: "Neural Symbolic Chat"
  customFooter: "Powered by Neural Symbolic Language Model"
  
  # Theme
  defaultTheme: "dark"
  
  # Features
  showCode: true
  showTokenCount: true
  showModelSelect: true
  showTemperature: true
  
  # Privacy
  privacyPolicy:
    externalUrl: "https://your-domain.com/privacy"
    openNewTab: true
  
  termsOfService:
    externalUrl: "https://your-domain.com/terms"
    openNewTab: true

# Registration and authentication
registration:
  # Social login (disabled for this setup)
  socialLogins: []
  
  # Email domains (empty = allow all)
  allowedDomains: []

# Rate limiting configuration
rateLimits:
  # File uploads
  fileUploads:
    ipMax: 100
    ipWindowInMinutes: 60
    userMax: 50
    userWindowInMinutes: 60
  
  # Messages
  messages:
    ipMax: 1000
    ipWindowInMinutes: 60
    userMax: 500
    userWindowInMinutes: 60

# Cache configuration
cacheConfig:
  # Redis configuration (if using Redis)
  redis:
    enabled: false
  
  # Memory cache
  memory:
    enabled: true
    ttl: 3600  # 1 hour
    max: 1000  # Maximum cached items

# Logging configuration
logging:
  level: "info"
  
  # Log file configuration
  file:
    enabled: true
    filename: "/app/api/logs/librechat.log"
    maxsize: "10m"
    maxFiles: 5
  
  # Console logging
  console:
    enabled: true
    colorize: true

# Security configuration
security:
  # JWT configuration
  jwt:
    issuer: "LibreChat-Neural-Symbolic"
    audience: "LibreChat-Users"
  
  # CORS configuration
  cors:
    origin: true
    credentials: true
  
  # Content Security Policy
  csp:
    enabled: true
    directives:
      defaultSrc: ["'self'"]
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"]
      styleSrc: ["'self'", "'unsafe-inline'"]
      imgSrc: ["'self'", "data:", "https:"]
      connectSrc: ["'self'", "ws:", "wss:"]

# Plugin configuration (for future extensibility)
plugins:
  # Web search plugin
  - name: "web-search"
    enabled: false
  
  # Calculator plugin
  - name: "calculator"
    enabled: true
  
  # Code execution plugin
  - name: "code-interpreter"
    enabled: false

# Experimental features
experimental:
  # Streaming responses
  streaming: true
  
  # Function calling
  functionCalling: false
  
  # Multi-modal support
  multiModal: false
