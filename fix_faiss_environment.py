"""
FAISS-GPU Environment Fix Script

This script helps diagnose and fix common FAISS-GPU installation issues on Windows.
Run this from your Conda environment where you have FAISS-GPU installed.
"""

import os
import sys
import shutil
import subprocess
import site
import importlib.util
from pathlib import Path

def print_header(text):
    """Print a formatted header."""
    print("\n" + "=" * 80)
    print(f"  {text}")
    print("=" * 80)

def check_python_environment():
    """Check the current Python environment."""
    print_header("PYTHON ENVIRONMENT")
    
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    
    # Check if running in Conda environment
    conda_prefix = os.environ.get('CONDA_PREFIX')
    if conda_prefix:
        print(f"Conda environment: {os.path.basename(conda_prefix)}")
        print(f"Conda environment path: {conda_prefix}")
    else:
        print("WARNING: Not running in a Conda environment")
    
    # Get site-packages directory
    site_packages = site.getsitepackages()[0]
    print(f"Site-packages directory: {site_packages}")
    
    return site_packages

def check_faiss_installation(site_packages):
    """Check FAISS installation status."""
    print_header("FAISS INSTALLATION")
    
    # Check if FAISS is installed
    faiss_spec = importlib.util.find_spec("faiss")
    if faiss_spec:
        print(f"FAISS is installed at: {faiss_spec.origin}")
        
        # Try to import FAISS
        try:
            import faiss
            print(f"FAISS version: {faiss.__version__ if hasattr(faiss, '__version__') else 'Unknown'}")
            print(f"FAISS build with GPU support: {hasattr(faiss, 'StandardGpuResources')}")
            
            # Try to import problematic module
            try:
                import faiss.swigfaiss_avx2
                print("Successfully imported faiss.swigfaiss_avx2")
            except ImportError as e:
                print(f"ERROR importing faiss.swigfaiss_avx2: {e}")
                return False
                
        except ImportError as e:
            print(f"ERROR importing FAISS: {e}")
            return False
    else:
        print("FAISS is not installed in the current Python environment")
        return False
    
    # Check for problematic FAISS installations
    problematic_dirs = []
    for item in os.listdir(site_packages):
        if item.startswith('~faiss') or (item.startswith('faiss') and item.endswith('.dist-info')):
            full_path = os.path.join(site_packages, item)
            problematic_dirs.append(full_path)
    
    if problematic_dirs:
        print("\nWARNING: Found potentially problematic FAISS installation directories:")
        for dir_path in problematic_dirs:
            print(f"  - {dir_path}")
    else:
        print("\nNo problematic FAISS installation directories found.")
    
    return True

def check_vc_redistributable():
    """Check if Microsoft Visual C++ Redistributable is installed."""
    print_header("MICROSOFT VISUAL C++ REDISTRIBUTABLE")
    
    try:
        # Try to list installed Visual C++ Redistributables
        result = subprocess.run(
            'powershell "Get-ItemProperty HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\* | ' +
            'Select-Object DisplayName, DisplayVersion | Where-Object {$_.DisplayName -like \'*Visual C++*\'}"',
            capture_output=True, text=True, shell=True
        )
        
        if 'Microsoft Visual C++ 2015-2022' in result.stdout:
            print("Microsoft Visual C++ 2015-2022 Redistributable is installed.")
            return True
        else:
            print("WARNING: Microsoft Visual C++ 2015-2022 Redistributable may not be installed.")
            print("\nVisit the following URL to download and install the latest version:")
            print("https://aka.ms/vs/17/release/vc_redist.x64.exe")
            return False
    except Exception as e:
        print(f"Error checking for Visual C++ Redistributable: {e}")
        print("\nVisit the following URL to download and install the latest version:")
        print("https://aka.ms/vs/17/release/vc_redist.x64.exe")
        return False

def check_torch_compatibility():
    """Check PyTorch compatibility with other packages."""
    print_header("PYTORCH COMPATIBILITY")
    
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA version: {torch.version.cuda}")
            print(f"GPU name: {torch.cuda.get_device_name(0)}")
        
        # Check for vllm
        vllm_spec = importlib.util.find_spec("vllm")
        if vllm_spec:
            try:
                import vllm
                print(f"vllm version: {vllm.__version__ if hasattr(vllm, '__version__') else 'Unknown'}")
                print(f"Required torch version: 2.4.0 (as per your memory)")
                
                if torch.__version__ != '2.4.0':
                    print("\nWARNING: Potential torch version conflict with vllm")
                    print(f"Current torch: {torch.__version__}, vllm requires: 2.4.0")
            except ImportError:
                pass
    except ImportError:
        print("PyTorch is not installed")

def recommend_fix_steps():
    """Recommend steps to fix FAISS-GPU issues."""
    print_header("RECOMMENDED FIXES")
    
    print("Based on your environment, here are the recommended steps to fix FAISS-GPU issues:")
    
    print("\n1. Ensure you're using the Conda environment where FAISS-GPU is installed:")
    print("   - Open a new terminal")
    print("   - Run: conda activate your_environment_name")
    
    print("\n2. Install Microsoft Visual C++ Redistributable:")
    print("   - Download from: https://aka.ms/vs/17/release/vc_redist.x64.exe")
    print("   - Run the installer")
    
    print("\n3. Clean up any problematic FAISS installations:")
    print("   - If problematic directories were found above, delete them")
    print("   - Then reinstall FAISS-GPU: conda install -c conda-forge faiss-gpu")
    
    print("\n4. Fix dependency conflicts:")
    print("   - If torch version conflicts were detected, consider:")
    print("     conda install pytorch==2.4.0 -c pytorch")
    
    print("\n5. Run your application using the Conda environment:")
    print("   - With the Conda environment activated, start your API server:")
    print("     python src/main.py")
    print("   - Then run benchmarks from the same environment:")
    print("     python scripts/benchmark.py")

def main():
    """Main function."""
    print_header("FAISS-GPU ENVIRONMENT DIAGNOSTIC")
    print("This script helps diagnose and fix common FAISS-GPU installation issues on Windows.")
    
    site_packages = check_python_environment()
    faiss_installed = check_faiss_installation(site_packages)
    vc_redist_installed = check_vc_redistributable()
    check_torch_compatibility()
    recommend_fix_steps()
    
    print_header("DIAGNOSTIC SUMMARY")
    
    if not faiss_installed:
        print("❌ FAISS-GPU is not properly installed in the current Python environment")
    elif faiss_installed and vc_redist_installed:
        print("✅ FAISS-GPU appears to be properly installed")
    else:
        print("⚠️ FAISS-GPU is installed but there are potential issues")
    
    print("\nRun this script again after implementing the recommended fixes to verify the issues are resolved.")

if __name__ == "__main__":
    main()
