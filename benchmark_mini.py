"""
Mini FAISS CPU vs GPU Benchmark

A very small benchmark that runs quickly and prints clear results
"""

import time
import sys
import numpy as np

print(f"Python executable: {sys.executable}")

try:
    import faiss
    print(f"FAISS version: {faiss.__version__}")
    print(f"FAISS GPU support: {hasattr(faiss, 'StandardGpuResources')}")
except ImportError:
    print("FAISS not available")
    sys.exit(1)

# Test parameters - very small for quick results
dim = 64
size = 10000
queries = 100

print(f"\nRunning benchmark with:")
print(f"- {size} vectors")
print(f"- {dim} dimensions")
print(f"- {queries} queries")

# Create test data
np.random.seed(42)
vectors = np.random.random((size, dim)).astype('float32')
query_vectors = np.random.random((queries, dim)).astype('float32')

# CPU benchmark
print("\n=== CPU Benchmark ===")
cpu_index = faiss.IndexFlatL2(dim)

start = time.time()
cpu_index.add(vectors)
cpu_build_time = time.time() - start
print(f"CPU build time: {cpu_build_time:.4f} seconds")

start = time.time()
D1, I1 = cpu_index.search(query_vectors, 10)
cpu_search_time = time.time() - start
print(f"CPU search time: {cpu_search_time:.4f} seconds")

# GPU benchmark if available
if hasattr(faiss, 'StandardGpuResources'):
    try:
        print("\n=== GPU Benchmark ===")
        res = faiss.StandardGpuResources()
        gpu_index = faiss.index_cpu_to_gpu(res, 0, faiss.IndexFlatL2(dim))
        
        start = time.time()
        gpu_index.add(vectors)
        gpu_build_time = time.time() - start
        print(f"GPU build time: {gpu_build_time:.4f} seconds")
        
        start = time.time()
        D2, I2 = gpu_index.search(query_vectors, 10)
        gpu_search_time = time.time() - start
        print(f"GPU search time: {gpu_search_time:.4f} seconds")
        
        # Calculate speedup
        build_speedup = cpu_build_time / gpu_build_time
        search_speedup = cpu_search_time / gpu_search_time
        
        print("\n=== Performance Comparison ===")
        print(f"Build speedup: {build_speedup:.2f}x faster on GPU")
        print(f"Search speedup: {search_speedup:.2f}x faster on GPU")
        
        # Results match?
        match = np.allclose(D1, D2, rtol=1e-5, atol=1e-5)
        print(f"Results match: {match}")
        
    except Exception as e:
        print(f"GPU benchmark failed: {e}")
else:
    print("\nGPU support not available in FAISS")

# Let's try a slightly larger dataset for a more realistic comparison
if hasattr(faiss, 'StandardGpuResources'):
    print("\n\n=== Extended Benchmark (Larger Dataset) ===")
    large_size = 100000
    print(f"Running with {large_size} vectors, {dim} dimensions")
    
    # Create larger dataset
    large_vectors = np.random.random((large_size, dim)).astype('float32')
    
    # CPU benchmark
    print("\nCPU:")
    cpu_index = faiss.IndexFlatL2(dim)
    
    start = time.time()
    cpu_index.add(large_vectors)
    cpu_build_time = time.time() - start
    print(f"Build time: {cpu_build_time:.4f} seconds")
    
    start = time.time()
    D1, I1 = cpu_index.search(query_vectors, 10)
    cpu_search_time = time.time() - start
    print(f"Search time: {cpu_search_time:.4f} seconds")
    
    # GPU benchmark
    print("\nGPU:")
    res = faiss.StandardGpuResources()
    gpu_index = faiss.index_cpu_to_gpu(res, 0, faiss.IndexFlatL2(dim))
    
    start = time.time()
    gpu_index.add(large_vectors)
    gpu_build_time = time.time() - start
    print(f"Build time: {gpu_build_time:.4f} seconds")
    
    start = time.time()
    D2, I2 = gpu_index.search(query_vectors, 10)
    gpu_search_time = time.time() - start
    print(f"Search time: {gpu_search_time:.4f} seconds")
    
    # Calculate speedup
    build_speedup = cpu_build_time / gpu_build_time
    search_speedup = cpu_search_time / gpu_search_time
    
    print("\nSpeedup:")
    print(f"Build: {build_speedup:.2f}x faster on GPU")
    print(f"Search: {search_speedup:.2f}x faster on GPU")
    
    # Final impact assessment
    print("\n=== GPU Impact Assessment ===")
    print(f"For a dataset of {large_size} vectors with {dim} dimensions:")
    print(f"- Index building is {build_speedup:.1f}x faster with GPU")
    print(f"- Vector search is {search_speedup:.1f}x faster with GPU")
    print(f"- In absolute terms, the GPU saves {cpu_search_time - gpu_search_time:.4f} seconds per {queries} queries")
    
    # Extrapolate to 1 million queries
    time_saved_per_million = (cpu_search_time - gpu_search_time) * (1000000 / queries)
    print(f"- For 1 million queries, the GPU would save approximately {time_saved_per_million:.1f} seconds ({time_saved_per_million/60:.1f} minutes)")
