import numpy as np
import time
import faiss
import matplotlib.pyplot as plt
from tqdm import tqdm

def benchmark_faiss_gpu():
    print("FAISS GPU Benchmark with Large Dataset")
    print("==================================")
    
    # Print FAISS and GPU info
    print(f"FAISS version: {faiss.__version__}")
    print(f"Number of GPUs available: {faiss.get_num_gpus()}")
    
    # Parameters for different dataset sizes
    dimensions = 768  # Fixed dimension (e.g., BERT embeddings)
    dataset_sizes = [
        10_000,     # Small
        100_000,    # Medium
        1_000_000,  # Large
        5_000_000   # Very Large (may require significant GPU memory)
    ]
    
    query_size = 100  # Number of query vectors
    k = 10  # Number of nearest neighbors to retrieve
    
    # Initialize GPU resources
    res = faiss.StandardGpuResources()
    
    # Store timing results
    results = {
        'dataset_size': [],
        'cpu_time': [],
        'gpu_time': [],
        'speedup': []
    }
    
    for num_vectors in dataset_sizes:
        print(f"\n=== Testing with {num_vectors:,} vectors ===")
        
        # Generate random database vectors
        print(f"Generating {num_vectors:,} random vectors of dimension {dimensions}...")
        np.random.seed(123)  # For reproducibility
        database_vectors = np.random.random((num_vectors, dimensions)).astype('float32')
        
        # Generate random query vectors
        query_vectors = np.random.random((query_size, dimensions)).astype('float32')
        
        # Create and test CPU index
        print("Testing CPU index...")
        cpu_index = faiss.IndexFlatL2(dimensions)
        cpu_index.add(database_vectors)
        
        # Warm-up search
        _ = cpu_index.search(query_vectors, k)
        
        # Time CPU search
        start_time = time.time()
        cpu_distances, cpu_indices = cpu_index.search(query_vectors, k)
        cpu_time = time.time() - start_time
        
        print(f"CPU search time for {query_size} queries: {cpu_time:.4f} seconds")
        
        # Create and test GPU index
        print("Testing GPU index...")
        gpu_index = faiss.index_cpu_to_gpu(res, 0, faiss.IndexFlatL2(dimensions))
        gpu_index.add(database_vectors)
        
        # Warm-up search
        _ = gpu_index.search(query_vectors, k)
        
        # Time GPU search
        start_time = time.time()
        gpu_distances, gpu_indices = gpu_index.search(query_vectors, k)
        gpu_time = time.time() - start_time
        
        print(f"GPU search time for {query_size} queries: {gpu_time:.4f} seconds")
        
        # Calculate speedup
        speedup = cpu_time / gpu_time if gpu_time > 0 else float('inf')
        print(f"Speedup: {speedup:.2f}x")
        
        # Verify results (just check first query to save time)
        indices_match = np.array_equal(cpu_indices[0], gpu_indices[0])
        distances_close = np.allclose(cpu_distances[0], gpu_distances[0], rtol=1e-4)
        print(f"Results match: {indices_match and distances_close}")
        
        # Store results
        results['dataset_size'].append(num_vectors)
        results['cpu_time'].append(cpu_time)
        results['gpu_time'].append(gpu_time)
        results['speedup'].append(speedup)
    
    # Plot results
    print("\n=== Benchmark Results ===")
    print(f"{'Dataset Size':<12} | {'CPU Time (s)':<12} | {'GPU Time (s)':<12} | {'Speedup':<10}")
    print("-" * 60)
    for i in range(len(results['dataset_size'])):
        print(f"{results['dataset_size'][i]:<12,} | {results['cpu_time'][i]:<12.4f} | {results['gpu_time'][i]:<12.4f} | {results['speedup'][i]:<10.2f}x")
    
    # Create a plot
    plt.figure(figsize=(10, 6))
    
    # Plot CPU and GPU times
    plt.plot(results['dataset_size'], results['cpu_time'], 'o-', label='CPU')
    plt.plot(results['dataset_size'], results['gpu_time'], 's-', label='GPU')
    
    plt.xscale('log')
    plt.yscale('log')
    plt.xlabel('Number of Vectors (log scale)')
    plt.ylabel('Search Time (seconds, log scale)')
    plt.title('FAISS Search Performance: CPU vs GPU')
    plt.legend()
    plt.grid(True, which="both", ls="--")
    
    # Save the plot
    plot_filename = 'faiss_gpu_benchmark.png'
    plt.savefig(plot_filename)
    print(f"\nPlot saved as {plot_filename}")
    
    # Plot speedup
    plt.figure(figsize=(10, 4))
    plt.plot(results['dataset_size'], results['speedup'], 'o-', color='green')
    plt.xscale('log')
    plt.xlabel('Number of Vectors (log scale)')
    plt.ylabel('Speedup (CPU Time / GPU Time)')
    plt.title('FAISS GPU Speedup Over CPU')
    plt.grid(True, which="both", ls="--")
    
    # Add speedup values as text on the plot
    for i, size in enumerate(results['dataset_size']):
        plt.text(size, results['speedup'][i], f"{results['speedup'][i]:.1f}x", 
                 ha='center', va='bottom')
    
    # Save the speedup plot
    speedup_plot_filename = 'faiss_gpu_speedup.png'
    plt.savefig(speedup_plot_filename)
    print(f"Speedup plot saved as {speedup_plot_filename}")
    
    print("\nBenchmark completed!")

if __name__ == "__main__":
    benchmark_faiss_gpu()
