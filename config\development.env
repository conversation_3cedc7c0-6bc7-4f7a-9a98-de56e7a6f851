# Development Environment Configuration
# This file contains configuration settings for development environment

# Application Settings
APP_TITLE="Neural Symbolic Language Model (Development)"
APP_VERSION="0.1.0-dev"
APP_DEBUG=true
APP_ENVIRONMENT="development"
APP_HOST="127.0.0.1"
APP_PORT=8080
APP_WORKERS=1
APP_RELOAD=true

# Security Settings
SECURITY_API_KEYS_JSON='{"dev_key": {"key": "dev_api_key_12345", "permissions": ["read", "write"], "rate_limit": 1000, "created_at": "2025-06-29T00:00:00"}}'
SECURITY_RATE_LIMIT_REQUESTS=1000
SECURITY_RATE_LIMIT_WINDOW=60
SECURITY_MAX_REQUEST_SIZE=10485760
SECURITY_CORS_ORIGINS='["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"]'
SECURITY_BLOCK_DURATION=300
SECURITY_MAX_FAILED_ATTEMPTS=10

# Model Settings
MODEL_REASONING_ENGINE="local"
MODEL_REASONING_MODEL="llama"
MODEL_EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
MODEL_EMBEDDING_DIMENSION=384
MODEL_USE_GPU=false
MODEL_GPU_MEMORY_FRACTION=0.5
MODEL_OLLAMA_HOST="http://localhost:11434"
MODEL_OLLAMA_TIMEOUT=30
MODEL_VECTOR_DB_BACKEND="torch"

# Cache Settings
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
CACHE_BACKEND="memory"

# Logging Settings
LOG_LEVEL="DEBUG"
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE_ENABLED=true
LOG_FILE_PATH="logs"
LOG_MAX_FILE_SIZE=10485760
LOG_BACKUP_COUNT=5
LOG_STRUCTURED=false

# Development-specific settings
DEV_ENABLE_PROFILING=true
DEV_ENABLE_DEBUG_TOOLBAR=true
DEV_MOCK_EXTERNAL_APIS=true
DEV_SEED_DATA=true
